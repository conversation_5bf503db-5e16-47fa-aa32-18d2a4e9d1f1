package net.summerfarm.wms.application.materialManage.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:27
 * @version 1.0
 *
 */
@Data
public class WmsMaterialTaskDetailVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * 类型，96-领用，97-归还
	 */
	private Integer type;

	/**
	 * 物料任务编码
	 */
	private String materialTaskCode;

	/**
	 * 物料sku编码
	 */
	private String materialSku;

	/**
	 * 物料skuSaasId
	 */
	private Long materialSkuSaasId;

	/**
	 * 物料sku名称
	 */
	private String materialSkuName;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime updateTime;



}