package net.summerfarm.wms.infrastructure.repository.batch.converter;

import net.summerfarm.wms.domain.batch.domainobject.ProduceBatchRecord;
import net.summerfarm.wms.infrastructure.dao.batch.dataobject.WarehouseProduceBatchRecordDO;

import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  11:52
 */
public class ProduceBatchRecordConverter {

    /**
     *
     * @param produceBatchRecord
     * @return
     */
    public static WarehouseProduceBatchRecordDO batchRecordConverter(ProduceBatchRecord produceBatchRecord){

        if(Objects.isNull(produceBatchRecord)){
            return null;
        }
        WarehouseProduceBatchRecordDO batchRecordDO = WarehouseProduceBatchRecordDO.builder()
                .sourceId(produceBatchRecord.getSourceId())
                .remark(produceBatchRecord.getRemark())
                .quantity(produceBatchRecord.getQuantity())
                .warehouseProduceBatchId(produceBatchRecord.getWarehouseProduceBatchId())
                .warehouseNo(produceBatchRecord.getWarehouseNo())
                .sku(produceBatchRecord.getSku())
                .type(produceBatchRecord.getType())
                .recorder(produceBatchRecord.getRecorder())
                .tenantId(produceBatchRecord.getTenantId())
                .build();
        return batchRecordDO;
    }
}
