package net.summerfarm.wms.application.mission.stocktaking.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.api.h5.mission.stocktaking.req.StocktakingMissionCreateCommand;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StocktakingPdaCreateEvent implements Serializable {
    /**
     * 创建参数
     */
    StocktakingMissionCreateCommand command;
}
