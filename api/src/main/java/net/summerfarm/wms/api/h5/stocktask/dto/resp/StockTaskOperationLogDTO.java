package net.summerfarm.wms.api.h5.stocktask.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskOperationLogDTO implements Serializable {

    private static final long serialVersionUID = 4115438385454964219L;

    /**
     * 操作人id
     */
    String operatorId;

    /**
     * 操作人名称
     */
    String operatorName;

    /**
     * 操作类型
     */
    String operateType;

    /**
     * 操作详情
     */
    String operateInfo;

    /**
     * 操作时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date operateTime;
}
