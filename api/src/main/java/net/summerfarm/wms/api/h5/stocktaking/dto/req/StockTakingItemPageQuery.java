package net.summerfarm.wms.api.h5.stocktaking.dto.req;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.api.base.PageReq;

import java.io.Serializable;

/**
 * 盘点实例查询接口
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTakingItemPageQuery implements Serializable {
    private static final long serialVersionUID = 7462448445313227432L;

    /**
     * 仓库编码
     */
    Integer warehouseNo;

    /**
     * 任务id
     */
    Long taskId;

    /**
     * 分页参数
     */
    PageReq pageReq;

    /**
     * 查询多级类目
     */
    boolean queryLevelCategory = false;

    /**
     * 查询批次成本价格
     */
    boolean queryBatchCost = true;
}
