package net.summerfarm.wms.application.acl.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.wms.annotation.ExceptionListener;
import net.summerfarm.wms.api.h5.warehouse.StoreQueryService;
import net.summerfarm.wms.application.acl.provider.convert.StoreConvert;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.storage.StoreQueryProvider;
import net.summerfarm.wms.storage.dto.StoreDTO;
import net.summerfarm.wms.storage.req.QueryStoreByNoRequest;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/12/22 13:19
 **/
@DubboService
@Component
@Slf4j
@ExceptionListener
public class StoreQueryProviderImpl implements StoreQueryProvider {

    @Resource
    private StoreQueryService storeQueryService;

    @Override
    public DubboResponse<StoreDTO> queryStoreInfoByNo(QueryStoreByNoRequest request) {
        // 参数校验
        Integer storeNo = request.getStoreNo();
        ExceptionUtil.checkAndThrow(Objects.nonNull(storeNo) && storeNo > 0,
                "必传参数缺失-城配仓编码");
        // 查询城配仓信息
        WarehouseLogisticsCenter logisticsCenter = storeQueryService.queryStoreByNo(storeNo);
        ExceptionUtil.checkAndThrow(Objects.nonNull(logisticsCenter), "未查询到城配仓信息");
        return DubboResponse.getOK(StoreConvert.convertStoreDTO(logisticsCenter));
    }
}
