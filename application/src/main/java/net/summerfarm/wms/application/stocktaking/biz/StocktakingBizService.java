package net.summerfarm.wms.application.stocktaking.biz;

import net.summerfarm.wms.application.acl.dingding.bo.DingTriggerBO;
import net.summerfarm.wms.domain.stocktaking.domainobject.Stocktaking;

/**
 * 盘点biz
 *
 * <AUTHOR>
 */
public interface StocktakingBizService {
    /**
     * 盘点
     * 对象应该用bo，但是.....
     */
    void stocktaking(Stocktaking stocktaking);

    /**
     * 完成盘点
     */
    void finishAudit(Stocktaking stocktaking, DingTriggerBO dingTriggerBO);

    /**
     * 获取盘点维度类型
     *
     * @return dimension
     */
    int getType();
}
