package net.summerfarm.wms.manage.web.reposity;

import net.summerfarm.wms.manage.model.domain.StockTaskWaveSkuOccupyDO;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
public interface StockTaskWaveSkuOccupyRepository {

    List<StockTaskWaveSkuOccupyDO> selectListByStockTaskId(Integer stockTaskId,
                                                           List<String> skuList);

    int insert(StockTaskWaveSkuOccupyDO record);

    void insertList(List<StockTaskWaveSkuOccupyDO> record);

    int updatePickChange(Long id,
                         Integer remainOccupyQuantityReduce,
                         Integer remainNotOccupyQuantityReduce);

}
