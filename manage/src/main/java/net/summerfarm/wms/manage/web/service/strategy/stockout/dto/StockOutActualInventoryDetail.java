package net.summerfarm.wms.manage.web.service.strategy.stockout.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8
 */
@Data
public class StockOutActualInventoryDetail implements Serializable {

    private static final long serialVersionUID = 544038118618355865L;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 应出数量
     */
    private Integer shouldQuantity;

    /**
     * 出库数量
     */
    private Integer quantity;

}
