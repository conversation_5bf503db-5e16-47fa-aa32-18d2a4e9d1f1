package net.summerfarm.wms.domain.inventory.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryFlow;
import net.summerfarm.wms.domain.inventory.domainobject.query.CabinetBatchInventoryFlowQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description 库存批次库存Repository
 * @date 2023/5/3
 */
public interface CabinetBatchInventoryFlowRepository {

    /**
     * create
     *
     * @param cabinetBatchInventoryFlow * @return
     */
    Long create(CabinetBatchInventoryFlow cabinetBatchInventoryFlow);

    /**
     * batch create
     *
     * @param cabinetBatchInventoryFlows
     * @return
     */
    Integer creates(List<CabinetBatchInventoryFlow> cabinetBatchInventoryFlows);

    /**
     * update
     *
     * @param cabinetBatchInventoryFlow
     * @return
     */
    Boolean update(CabinetBatchInventoryFlow cabinetBatchInventoryFlow);

    /**
     * findById
     *
     * @param id
     * @return
     */
    CabinetBatchInventoryFlow findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<CabinetBatchInventoryFlow> findByIds(List<Long> ids);

    /**
     * count
     *
     * @param cabinetBatchInventoryFlow
     * @return
     */
    Long count(CabinetBatchInventoryFlowQuery cabinetBatchInventoryFlow);

    /**
     * findOne
     *
     * @param cabinetBatchInventoryFlow
     * @return
     */
    CabinetBatchInventoryFlow findOne(CabinetBatchInventoryFlowQuery cabinetBatchInventoryFlow);

    /**
     * list
     *
     * @param cabinetBatchInventoryFlow
     * @return
     */
    List<CabinetBatchInventoryFlow> list(CabinetBatchInventoryFlowQuery cabinetBatchInventoryFlow);

    PageInfo<CabinetBatchInventoryFlow> pageFlow(Integer pageIndex, Integer pageSize,
                                                 CabinetBatchInventoryFlowQuery cabinetBatchInventoryFlowQuery);
}
