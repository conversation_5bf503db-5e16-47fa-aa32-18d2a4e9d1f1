package net.summerfarm.wms.domain.batch.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @Description
 * @Date 2025/6/17 17:09
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierBatchInventoryQueryParam extends BasePageInput {

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    private String sku;

    /**
     * sku列表
     */
    private List<String> skuList;

}
