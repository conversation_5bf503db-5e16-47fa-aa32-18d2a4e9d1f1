package net.summerfarm.wms.application.instore.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.wms.api.h5.crosswarehouse.CrossWarehouseCommandService;
import net.summerfarm.wms.api.h5.instore.StockStorageCommandService;
import net.summerfarm.wms.api.h5.instore.dto.req.CargoArrivalCommand;
import net.summerfarm.wms.api.inner.instore.dto.StockStorageReqDTO;
import net.summerfarm.wms.application.instore.event.UpdateStockStorageStateEvent;
import net.summerfarm.wms.application.inventory.dto.SaleInventoryReleaseSkuDTO;
import net.summerfarm.wms.application.inventory.event.SaleInventoryReleaseEvent;
import net.summerfarm.wms.application.inventory.factory.SaleInventoryReleaseEventFactory;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.mqUtil.MqUtil;
import net.summerfarm.wms.common.mqUtil.base.enums.MType;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.instore.domainobject.*;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.enums.StockStoragePurchaseModeEnums;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageProcessStateEnums;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageReceivingStateEnum;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.*;
import net.summerfarm.wms.domain.mission.domainobject.ExecuteUnit;
import net.summerfarm.wms.domain.mission.domainobject.Mission;
import net.summerfarm.wms.domain.mission.enums.ExecuteUnitStateEnum;
import net.summerfarm.wms.domain.mission.enums.MissionTypeEnum;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.outstore.domainobject.WmsDamageStockItem;
import net.summerfarm.wms.domain.outstore.repository.WmsDamageStockItemRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.instore.dto.req.InBoundOrderDetailMqData;
import net.summerfarm.wms.instore.dto.req.StockStorageMqData;
import net.xianmu.rocketmq.retryable.producer.MqRetryableAfterTsProducer;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/25  15:22
 */
@Slf4j
@Component
public class StockStorageListener {

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private StockStorageItemRepository stockStorageItemRepository;
    @Resource
    private StockStorageItemDetailRepository stockStorageItemDetailRepository;
    @Resource
    private InBoundOrderQueryRepository inBoundOrderQueryRepository;
    @Resource
    private StockTaskStorageRepository stockTaskStorageRepository;
    @Resource
    private StockTaskAbnormalRecordRepository stockTaskAbnormalRecordRepository;
    @Resource
    private MqUtil mqUtil;
    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private WmsDamageStockItemRepository wmsDamageStockItemRepository;
    @Resource
    private MqProducer mqProducer;
    @Autowired
    private MqRetryableAfterTsProducer mqRetryableAfterTsProducer;
    @Resource
    private MissionRepository missionRepository;
    @Resource
    private StockStorageCommandService stockStorageCommandService;
    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;
    @Resource
    private CrossWarehouseCommandService crossWarehouseCommandService;

    @Resource
    private SaleInventoryReleaseEventFactory saleInventoryReleaseEventFactory;

    @EventListener
    public void handle(UpdateStockStorageStateEvent stockStorageStateEvent) {
        String source = (String) stockStorageStateEvent.getSource();
        log.info("入库单操作完成通知 data={}", source);

        // 任务状态变更
        StockStorageReqDTO data = JSON.parseObject(source, StockStorageReqDTO.class);

        run(data);
    }

    public void run(StockStorageReqDTO data) {
        log.info("执行入库处理入参：{}", JSON.toJSONString(data));
        Long stockStorageId = data.getStockStorageId();
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockStorageId);
        List<InBoundOrder> inBoundOrders = inBoundOrderQueryRepository.findInBoundOrders(data.getInboundOrderIds());
        Map<Long, InBoundOrder> orderMap = inBoundOrders.stream().collect(Collectors.toMap(InBoundOrder::getId, Function.identity(), (o1, o2) -> o1));

        List<InBoundOrderDetail> inBoundOrderDetailList = inBoundOrderQueryRepository.findInBoundOrderDetails(data.getInboundOrderIds());

        // 已经入库数量
        int actNum = stockStorageItems.stream().mapToInt(StockStorageItem::getActualQuantity).sum();
        int totalNum = stockStorageItems.stream().mapToInt(StockStorageItem::getQuantity).sum();
        int stockNum = inBoundOrderDetailList.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();

        // 库存仓
        Long warehouseNo = stockTaskStorage.getInWarehouseNo().longValue();
        // 释放销售库存数据
        List<SaleInventoryReleaseSkuDTO> saleInventoryReleaseSkuDTOList = new ArrayList<>();


        //查看任务是否完成，更新任务进度
        Map<String, List<InBoundOrderDetail>> skuInStoreMap =
                inBoundOrderDetailList.stream().collect(Collectors.groupingBy(InBoundOrderDetail::getSku));

        for (StockStorageItem stockStorageItem : stockStorageItems) {
            String sku = stockStorageItem.getSku();
            List<InBoundOrderDetail> inBoundOrderDetails = skuInStoreMap.get(sku);
            if (CollectionUtils.isEmpty(inBoundOrderDetails)) {
                continue;
            }
            // 本次生成的入库单的实入数量
            Integer sumQuantity = CollectionUtils.isEmpty(inBoundOrderDetails) ? 0 : inBoundOrderDetails.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();

            StockStorageItem updateStorageItem = StockStorageItem.builder().id(stockStorageItem.getId()).actualQuantity(sumQuantity).build();
            stockStorageItemRepository.updateStockStorageItem(updateStorageItem);

            // stockStorageItemDetail回写
            List<StockStorageItemDetail> stockStorageItemDetails = stockStorageItemDetailRepository.selectItemDetailBatch(Lists.newArrayList(stockStorageItem.getId()));
            Map<String, StockStorageItemDetail> existDetailMap = stockStorageItemDetails.stream().collect(Collectors.toMap(StockStorageItemDetail::dupKeyWithContainer, Function.identity(), (o1, o2) -> o1));
            Map<String, Integer> specifyMap = stockStorageItemDetails.stream()
                    .collect(Collectors.toMap(StockStorageItemDetail::dupKeyWithOutContainer, StockStorageItemDetail::getSpecify, (o1, o2) -> o1));
            inBoundOrderDetails.forEach(item -> {
                // 初始状态更新
                StockStorageItemDetail stockStorageItemDetail = existDetailMap.get(item.dpKeyWhenSkuOnly() + null);
                InBoundOrder inBoundOrder = orderMap.getOrDefault(item.getInBoundOrderId(), InBoundOrder.builder().build());
                if (stockStorageItemDetail != null) {
                    stockStorageItemDetail.setActualInQuantity(item.getStockNum() + stockStorageItemDetail.getActualInQuantity());
                    stockStorageItemDetail.setReceivingContainer(inBoundOrder.getContainerNo());
                    stockStorageItemDetailRepository.changeItemDetailQuantity(stockStorageItemDetail);
                    return;
                }
                // 追加更新
                StockStorageItemDetail stockStorageItemDetailWithCN = existDetailMap.get(item.dpKeyWhenSkuOnly() + inBoundOrder.getContainerNo());
                if (stockStorageItemDetailWithCN != null) {
                    stockStorageItemDetailWithCN.setActualInQuantity(item.getStockNum() + stockStorageItemDetailWithCN.getActualInQuantity());
                    stockStorageItemDetailWithCN.setReceivingContainer(inBoundOrder.getContainerNo());
                    stockStorageItemDetailRepository.changeItemDetailQuantity(stockStorageItemDetailWithCN);
                    return;
                }
                // 新增
                StockStorageItemDetail build = StockStorageItemDetail.builder()
                        .actualInQuantity(item.getStockNum())
                        .shouldQuantity(0)
                        .stockStorageItemId(stockStorageItem.getId())
                        .purchaseNo(item.getPurchaseNo())
                        .productionDate(DateUtil.toLocalDate(item.getProduceAt()))
                        .qualityDate(DateUtil.toLocalDate(item.getShelfLife()))
                        .tenantId(item.getTenantId())
                        .receivingContainer(inBoundOrder.getContainerNo())
                        .specify(specifyMap.getOrDefault(item.dpKeyWhenSkuOnly(), 1))
                        .build();
                stockStorageItemDetailRepository.saveStockStorageItemDetail(build);
            });
            // 释放销售库存数据组装
            SaleInventoryReleaseSkuDTO saleInventoryReleaseSkuDTO = saleInventoryReleaseEventFactory.buildDTO(warehouseNo, sku, sumQuantity);
            saleInventoryReleaseSkuDTOList.add(saleInventoryReleaseSkuDTO);
        }
        //入库数量 + 本次入库 + 异常操作 是否完成任务
        Integer state = StockTaskStorageStateEnum.FINISH.getId();
        Integer processState = StockTaskStorageProcessStateEnums.ALL_IN_STORE.getId();
        Integer stockTaskAbnormalNum = getStockTaskAbnormalNum(stockTaskStorage);
        log.info("totalNum= {},quantity={},sumQuantity={},入库任务：{}", totalNum, actNum, stockTaskAbnormalNum, stockStorageId);
        if (totalNum > actNum + stockTaskAbnormalNum + stockNum) {
            state = StockTaskStorageStateEnum.NORMAL.getId();
            processState = StockTaskStorageProcessStateEnums.PART_IN_OUT.getId();
        }

        List<InBoundOrderDetailMqData> detailBOList = inBoundOrderDetailList.stream()
                .map(inBoundOrderDetail -> InBoundOrderDetailMqData.builder()
                        .sku(inBoundOrderDetail.getSku())
                        .produceAt(inBoundOrderDetail.getProduceAt())
                        .shelfLife(inBoundOrderDetail.getShelfLife())
                        .purchaseNo(inBoundOrderDetail.getPurchaseNo())
                        .stockNum(inBoundOrderDetail.getStockNum())
                        .tenantId(inBoundOrderDetail.getTenantId())
                        .build()).collect(Collectors.toList());
        log.info("入库单状态更新开始 state={}，入库任务：{}", state, stockStorageId);
        StockTaskStorage updateTaskStorage = StockTaskStorage.builder()
                .processState(processState)
                .id(stockStorageId)
                .adminId(data.getAdminId())
                .operatorName(data.getOperatorName())
                .state(state).build();
        stockTaskStorageRepository.updateStockTaskStorage(updateTaskStorage);
        StockTask updateStockTask = StockTask.builder().id(stockTaskStorage.getStockTaskId())
                .processState(processState)
                .state(Objects.equals(state, StockTaskStorageStateEnum.FINISH.getId()) ? 2 : 1)
                .build();
        stockTaskRepository.updateState(updateStockTask);

        if (state.equals(StockTaskStorageStateEnum.FINISH.getId())) {
            Boolean open = warehouseConfigRepository.openCabinetManagement(stockTaskStorage.getInWarehouseNo());
            if (Boolean.TRUE.equals(open)) {
                if (StockTaskStorageReceivingStateEnum.CONFIRMED.getId().equals(stockTaskStorage.getReceivingState())) {
                    stockStorageCommandService.arrivalCargoFinish(CargoArrivalCommand.builder()
                            .warehouseNo(stockTaskStorage.getInWarehouseNo().longValue())
                            .stockStorageTaskId(stockStorageId)
                            .operator(data.getOperatorName())
                            .state(StockTaskStorageStateEnum.FINISH.getId())
                            .build());
                }
                // 更新收货任务对应的上架任务状态
                List<Mission> missions = missionRepository.findMissionByPMissionNo(stockStorageId.toString(), MissionTypeEnum.IN_STORE_TASK.getCode());
                List<String> missionNos = missions.stream().map(Mission::getMissionNo).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(missionNos)) {
                    List<ExecuteUnit> executeUnits = missionRepository.findExecuteUnits(missionNos);
                    executeUnits.forEach(item -> {
                        if (ExecuteUnitStateEnum.FINISH.getCode().equals(item.getState())) {
                            return;
                        }
                        item.setState(ExecuteUnitStateEnum.EXE.getCode());
                        missionRepository.changeExeUnit(item);
                    });
                }

                // 生成越库投线任务
                sendGenerateCrossMission(stockTaskStorage, data, StockTaskStorageStateEnum.FINISH.getId());
            }
        }

        //任务外部单号
        //任务id
        //任务类型
        StockStorageMqData dataBO = StockStorageMqData.builder()
                .sourceId(stockTaskStorage.getSourceId())
                .taskId(stockTaskStorage.getId())
                .stockTaskId(stockTaskStorage.getStockTaskId())
                .state(state)
                .inBoundOrderDetailBOList(detailBOList)
                .processState(processState)
                .adminId(data.getAdminId())
                .operatorName(data.getOperatorName())
                .tenantId(stockTaskStorage.getTenantId())
                .afterSaleOrderNo(stockTaskStorage.getAfterSaleOrder())
                .type(stockTaskStorage.getType())
                .operatorName(data.getOperatorName()).build();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                mqUtil.sendMqData(RocketMqMessageConstant.MALL_LIST, dataBO, MType.UPDATE_IN_STORE.name());
                log.info("入库单状态更新完成");
            }
        });
        // 如果是调拨入库则发送调拨在途批次库存更新消息
        if (StockTaskTypeEnum.STORE_ALLOCATION_IN.getId() == stockTaskStorage.getType()) {
            try {
                mqRetryableAfterTsProducer.sendOrderly(WmsConstant.TOPIC_INVENTORY_ORDERLY, WmsConstant.TAG_ALLOCATION_IN_UPDATE_ROAD_COST_BATCH,
                        data.getInboundOrderIds(), stockTaskStorage.getSourceId());
            } catch (Exception ex) {
                log.error("发送调拨在途批次库存更新消息失败", ex);
            }
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                @Override
//                public void afterCommit() {
//                    mqProducer.sendOrderly(WmsConstant.TOPIC_INVENTORY_ORDERLY, WmsConstant.TAG_ALLOCATION_IN_UPDATE_ROAD_COST_BATCH,
//                            data.getInboundOrderIds(), stockTaskStorage.getSourceId());
//                }
//            });
        }

        // 发送完成入库消息
        // 发送完成入库消息 越库入库 采购预提 或者有履约单号的订单
        if (Objects.nonNull(stockTaskStorage.getFulfillmentNo())) {
            sendCompleteInStoreMessage(stockTaskStorage, state);
        }

        // 多出入库类型发送释放销售冻结消息
        if (StockTaskTypeEnum.OUT_MORE_IN.getId() == stockTaskStorage.getType()) {
            // 幂等键拼接
            List<Integer> totalQuantityList = stockStorageItems.stream().map(StockStorageItem::getQuantity).collect(Collectors.toList());
            List<Integer> actualQuantityList = stockStorageItems.stream().map(StockStorageItem::getActualQuantity).collect(Collectors.toList());
            String totalJoin = Joiner.on("_").join(totalQuantityList);
            String actualJoin = Joiner.on("_").join(actualQuantityList);
            String idempotentNo = stockStorageId + "_" + totalJoin + "_" + actualJoin;
            // 释放销售库存实体组装
            SaleInventoryReleaseEvent saleInventoryReleaseEvent = saleInventoryReleaseEventFactory.buildEvent(stockTaskStorage, idempotentNo, saleInventoryReleaseSkuDTOList, data);
            log.info("入库完成发送库存冻结释放消息：{}", JSON.toJSONString(saleInventoryReleaseEvent));
            mqProducer.send(Global.MQ_TOPIC, WmsConstant.SALE_INVENTORY_RELEASE, saleInventoryReleaseEvent);
        }

        // 预提收货分配投线明细
        sendAllocationShelveCrossSort(stockTaskStorage, data);
    }

    /**
     * 发送生成越库投线任务
     *
     * @param stockTaskStorage 入库任务
     * @param data             收货消息
     */
    private void sendGenerateCrossMission(StockTaskStorage stockTaskStorage, StockStorageReqDTO data, Integer state) {
        // 不是越库入库任务不发送消息
        if (!Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())) {
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 前提是精细化仓库 生成对应的越库投线任务
                crossWarehouseCommandService.triggerGenerateMission(data.getStockStorageId(), null, data.getIsPda(), state);
                log.info("越库入库生成投线任务发送消息");
            }
        });
    }

    /**
     * 发送收货分配上架的明细
     *
     * @param stockTaskStorage 入库任务
     * @param data             收货详情内容
     */
    private void sendAllocationShelveCrossSort(StockTaskStorage stockTaskStorage, StockStorageReqDTO data) {
        // 采购入库 并且是预提类型的采购入库任务
        if (!(Objects.equals(OperationType.PURCHASE_IN.getId(), stockTaskStorage.getType())
                && StockStoragePurchaseModeEnums.PRE_ORDERED_GOODS.getCode().equals(stockTaskStorage.getPurchaseMode()))) {
            return;
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                mqProducer.send(Global.STOCK_TASK, "tag_cross_sort_allocation_shelve", data);
                log.info("发送采购预提上架分配数量消息");
            }
        });
    }

    /**
     * 获取异常操作
     *
     * @param stockTaskStorage
     * @return
     */
    private Integer getStockTaskAbnormalNum(StockTaskStorage stockTaskStorage) {


        List<StockTaskAbnormalRecord> recordList = stockTaskAbnormalRecordRepository.select(stockTaskStorage.getStockTaskId(), null);
        int stockTaskAbnormalNum = CollectionUtils.isEmpty(recordList) ? 0 : recordList.stream().mapToInt(StockTaskAbnormalRecord::getQuantity).sum();
        String sourceId = stockTaskStorage.getSourceId();

        //无外部单号，直接返回， 异常操作改造
        if (StringUtils.isEmpty(sourceId)) {
            return stockTaskAbnormalNum;
        }
        //回库
        StockTaskStorage queryStorage = StockTaskStorage.builder().sourceId(stockTaskStorage.getSourceId())
                .type(OperationType.ALLOCATION_ABNORMAL_IN.getId()).build();
        List<StockTaskStorage> stockTaskStorages = stockTaskStorageQueryRepository.queryStockTaskStorageBySourceId(queryStorage);
        List<InBoundOrderDetail> anInBoundOrderDetail = new ArrayList<>();
        stockTaskStorages.forEach(x -> {
            List<InBoundOrderDetail> inBoundOrderDetailByTaskId = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(x.getId()).build());
            anInBoundOrderDetail.addAll(inBoundOrderDetailByTaskId);
        });
        int inBoundOrderDetailNum = CollectionUtils.isEmpty(anInBoundOrderDetail) ? 0 : anInBoundOrderDetail.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
        //获取货损数据
        List<WmsDamageStockItem> stockItemList = wmsDamageStockItemRepository.selectByTaskNoAndType(stockTaskStorage.getSourceId(), OperationType.ALLOCATION_DAMAGE_OUT.getId());
        int wmsDamageNum = CollectionUtils.isEmpty(stockItemList) ? 0 : stockItemList.stream().mapToInt(WmsDamageStockItem::getActualQuantity).sum();

        return stockTaskAbnormalNum + inBoundOrderDetailNum + wmsDamageNum;
    }

    /**
     * 发送完成入库任务
     *
     * @param stockTaskStorage 入库任务详情
     * @param state            当前入库任务状态
     */
    private void sendCompleteInStoreMessage(StockTaskStorage stockTaskStorage, Integer state) {
        // 越库入库任务 采购预提入库 后置发送消息
        if (Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())
                || (Objects.equals(OperationType.PURCHASE_IN.getId(), stockTaskStorage.getType())
                && StockStoragePurchaseModeEnums.PRE_ORDERED_GOODS.getCode().equals(stockTaskStorage.getPurchaseMode()))) {
            return;
        }
        // 入库任务完成，发送消息
        if (state.equals(StockTaskStorageStateEnum.FINISH.getId())) {
            stockStorageCommandService.sendInStoreTaskFinishWithState(stockTaskStorage.getId(), StockTaskStorageStateEnum.FINISH.getId());
        }
    }
}
