package net.summerfarm.wms.domain.inventory.domainobject.enums;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库位库存操作类别
 */
public enum CabinetInventoryOperationType {


    STOCK_IN(11, "收货"),

    LOCKING(20, "锁库"),
    PICKING(21, "拣货"),
    REVIEW(22, "复核"),
    HANDOVER(23, "交接"),
    TRANSFER_IN(31, "入库"),
    TRANSFER_OUT(32,"出库"),
    LOSS(33,"盘亏"),
    PROFIT(34, "盘盈"),
//    MATERIAL_RECEIVE(35, "领料"),
//    MATERIAL_RETURN(36, "领料归还"),
//    PRODUCT(37, "加工"),

    SHELF_ON(42, "上架"),
    SHELF_OFF(41, "下架"),
//    MOVE_CABINET(43, "移库"),

    LINE_CAST(50, "投线"),

    PROCESSING_TASK_MATERIAL_REDUCE(86, "加工出库"),
    PROCESSING_TASK_MATERIAL_INCREASE(87, "加工回库"),
    PROCESSING_TASK_PRODUCT_INCREASE(88, "加工入库"),

    STOCKTAKING_RELEASE(90, "复盘"),

    MATERIAL_TASK_RECEIVE(96, "物料领用"),
    MATERIAL_TASK_RESTORE(97, "物料归还"),

    INVENTORY_LOCK(130, "库存锁定"),
    INVENTORY_UNLOCK(131, "库存锁定释放"),

    INIT_OPEN(181,"初始化开启"),
    INIT_CLOSE(182,"初始化关闭"),
    ;

    CabinetInventoryOperationType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    private  int id;

    private  String name;

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(int id) {
        for (CabinetInventoryOperationType operationType : CabinetInventoryOperationType.values()) {
            if (operationType.getId() == id) {
                return operationType.getName();
            }
        }
        return "";
    }

    public static Integer getIdByName(String name) {
        for (CabinetInventoryOperationType operationType : CabinetInventoryOperationType.values()) {
            if (operationType.getName().equals(name)) {
                return operationType.getId();
            }
        }
        return null;
    }

    public static CabinetInventoryOperationType getById(int id) {
        for (CabinetInventoryOperationType operationType : CabinetInventoryOperationType.values()) {
            if (operationType.getId() == id) {
                return operationType;
            }
        }
        return null;
    }

    public static Map<Integer, String> getAllType() {
        Map<Integer, String> map = new HashMap<>();
        for (CabinetInventoryOperationType operationType : CabinetInventoryOperationType.values()) {
            map.put(operationType.getId(), operationType.getName());
        }
        return map;
    }

    public static Map<Integer, String> getAllTypeByTypeName(List<CabinetInventoryOperationType> types) {
        Map<Integer, String> map = new HashMap<>();
        for (CabinetInventoryOperationType operationType : types) {
            map.put(operationType.getId(), operationType.getName());
        }
        return map;
    }
}
