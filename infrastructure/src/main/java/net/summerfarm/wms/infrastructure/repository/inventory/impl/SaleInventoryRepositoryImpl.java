package net.summerfarm.wms.infrastructure.repository.inventory.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.RedisCacheUtil;
import net.summerfarm.wms.domain.StoreRecord.domainobject.StoreRecord;
import net.summerfarm.wms.domain.StoreRecord.vo.SkuMinQualityDateVO;
import net.summerfarm.wms.domain.inventory.SaleInventoryRepository;
import net.summerfarm.wms.infrastructure.dao.batch.WarehouseProduceBatchDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.StoreRecordDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SkuMinQualityDateDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO;
import net.summerfarm.wms.infrastructure.repository.inventory.converter.SkuMinQualityDateVOConvert;
import net.summerfarm.wms.infrastructure.repository.inventory.converter.StoreRecordConvert;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class SaleInventoryRepositoryImpl implements SaleInventoryRepository {

    @Resource
    private StoreRecordDAO storeRecordDAO;
    @Resource
    private WarehouseProduceBatchDAO warehouseProduceBatchDAO;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    public List<StoreRecord> getPurchaseBatchInventory(List<Integer> warehouseNoList, List<String> skuList) {
        if (CollectionUtils.isEmpty(warehouseNoList) ||
                CollectionUtils.isEmpty(skuList)){
            return new ArrayList<>();
        }

        List<StoreRecordDO> storeRecordDOList = storeRecordDAO.getPurchaseBatchInventory(warehouseNoList, skuList);
        return StoreRecordConvert.INSTANCE.convertList(storeRecordDOList);
    }

    @Override
    public PageInfo<StoreRecord> getPurchaseBatchInventory(List<Integer> warehouseNoList, List<String> skuList,
                                                           Integer pageIndex, Integer pageSize) {
        if (pageSize == null || pageIndex == null){
            throw new BizException(ErrorCode.PARAM_ERROR);
        }
        PageInfo<StoreRecordDO> selectPageInfo = PageHelper.startPage(
                pageIndex, pageSize).doSelectPageInfo(() ->
                storeRecordDAO.getPurchaseBatchInventory(
                        warehouseNoList, skuList)
        );

        PageInfo<StoreRecord> storeRecordPageInfo = new PageInfo<>();
        storeRecordPageInfo.setTotal(selectPageInfo.getTotal());
        storeRecordPageInfo.setPages(selectPageInfo.getPages());
        storeRecordPageInfo.setList(StoreRecordConvert.INSTANCE.convertList(selectPageInfo.getList()));
        return storeRecordPageInfo;
    }

    @Override
    public List<SkuMinQualityDateVO> querySaleInventoryMinQualityDate(List<Integer> warehouseNoList, List<String> skuList) {
        if (CollectionUtils.isEmpty(warehouseNoList) ||
                CollectionUtils.isEmpty(skuList)){
            return new ArrayList<>();
        }

        List<SkuMinQualityDateDO> skuMinQualityDateDOList = warehouseProduceBatchDAO.querySaleInventoryMinQualityDate(warehouseNoList, skuList);
        return SkuMinQualityDateVOConvert.INSTANCE.convertList(skuMinQualityDateDOList);
    }
    @Override
    public List<SkuMinQualityDateVO> querySaleInventoryMinQualityDateForCache(List<Integer> warehouseNoList, List<String> skuList) {
        if (CollectionUtils.isEmpty(warehouseNoList) ||
                CollectionUtils.isEmpty(skuList)){
            return new ArrayList<>();
        }

        return redisCacheUtil.getCacheListValueRefreshByHalfTime(
                "wms:SaleInventoryRepository:querySaleInventoryMinQualityDateForCache:" +
                        JSONObject.toJSONString(warehouseNoList) + ":" +
                        JSONObject.toJSONString(skuList),
                60,
                () -> querySaleInventoryMinQualityDate(warehouseNoList, skuList),
                SkuMinQualityDateVO.class
        );
    }

    @Override
    public Map<String, SkuMinQualityDateVO> mapSaleInventoryMinQualityDate(List<Integer> warehouseNoList, List<String> skuList) {
        if (CollectionUtils.isEmpty(warehouseNoList) ||
                CollectionUtils.isEmpty(skuList)){
            return new HashMap<>();
        }

        List<SkuMinQualityDateVO> skuMinQualityDateVOList = querySaleInventoryMinQualityDate(warehouseNoList, skuList);
        return skuMinQualityDateVOList.stream()
                .collect(Collectors.toMap(SkuMinQualityDateVO::getSku, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, SkuMinQualityDateVO> mapSaleInventoryMinQualityDateForCache(List<Integer> warehouseNoList, List<String> skuList) {
        if (CollectionUtils.isEmpty(warehouseNoList) ||
                CollectionUtils.isEmpty(skuList)){
            return new HashMap<>();
        }

        List<SkuMinQualityDateVO> skuMinQualityDateVOList = querySaleInventoryMinQualityDateForCache(warehouseNoList, skuList);
        return skuMinQualityDateVOList.stream()
                .collect(Collectors.toMap(SkuMinQualityDateVO::getSku, Function.identity(), (a, b) -> a));
    }

}
