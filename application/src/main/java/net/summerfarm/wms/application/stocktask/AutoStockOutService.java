package net.summerfarm.wms.application.stocktask;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.wms.enums.StockTaskTypeEnum;
import net.summerfarm.wms.api.h5.inventory.BatchInventoryQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.batchInventory.BatchInventoryInput;
import net.summerfarm.wms.api.h5.inventory.dto.res.batchInventory.BatchInventoryVO;
import net.summerfarm.wms.application.external.factory.StockTaskSaleOutReqFactory;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItem;
import net.summerfarm.wms.manage.web.enums.StockTaskType;
import net.summerfarm.wms.manage.web.req.StockTaskSaleOutReq;
import net.summerfarm.wms.manage.web.service.StockTaskService;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCallbackItem;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCallbackReq;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/2 10:50
 * @Version 1.0
 */
@Slf4j
@Component
public class AutoStockOutService {

    @Autowired
    private StockTaskRepository stockTaskRepository;

    @Resource
    private StockTaskSaleOutReqFactory stockTaskSaleOutReqFactory;
    @Resource
    private BatchInventoryQueryService batchInventoryQueryService;
    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;

    /**
     * 自动出库，非库位精细化仓
     *
     * @param stockTaskId
     * @param stockTaskTypeEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoOutByStockOutIdAndTypeForNotCabinetManagement(
            Long stockTaskId, StockTaskTypeEnum stockTaskTypeEnum){
        log.info("自动出库，非库位精细化仓 {}", stockTaskId);
        if (stockTaskId == null || stockTaskTypeEnum == null){
            return;
        }

        StockTask stockTask = stockTaskRepository.findOutStockTask(stockTaskId);
        if (stockTask == null){
            return;
        }
        // 出库类型不一致返回
        if (!Integer.valueOf(stockTaskTypeEnum.getType()).equals(stockTask.getType())){
            log.error("\n出库类型不一致返回，无法自动出库 {} {} {}\n", stockTaskId, stockTaskTypeEnum, stockTask);
            return;
        }
        // 出库单非待出库状态
        if (!net.summerfarm.wms.application.stocktask.enums.StockTaskStateEnum.WAIT_IN_OUT.equalsId(stockTask.getState())){
            log.info("出库单非待出库状态，无法自动出库 {} {} {}", stockTaskId, stockTaskTypeEnum, stockTask);
            return;
        }
        // 库位精细化仓则返回
        Boolean open = warehouseConfigRepository.openCabinetManagement(stockTask.getAreaNo());
        if (open){
            log.error("\n请求到库位精细化仓，无法自动出库 {} {} {}\n", stockTaskId, stockTaskTypeEnum, stockTask);
            return;
        }

        // 待出库明细
        List<StockTaskItem> stockTaskItems = stockTaskRepository.queryStockTaskItem(
                Arrays.asList(stockTaskId.intValue()));

        // 批次的查询
        List<String> skuList = stockTaskItems.stream().map(StockTaskItem::getSku).collect(Collectors.toList());
        List<BatchInventoryVO> batchInventoryVOList = batchInventoryQueryService.queryBatchInventory(BatchInventoryInput.builder()
                .skus(skuList)
                .warehouseNo(Long.parseLong(stockTask.getAreaNo().toString()))
                .type(StockTaskType.SALE_OUT.getId())
                .storeQuantityMin(1)
                .build());
        log.info("获取批次库存数据：{}", JSON.toJSONString(batchInventoryVOList));
        if (org.springframework.util.CollectionUtils.isEmpty(batchInventoryVOList)) {
            throw new BizException("POP自动出库批次库存数据为空");
        }
        if (batchInventoryVOList.stream().anyMatch(batchInventoryVO -> CollectionUtils.isEmpty(batchInventoryVO.getBatches()))) {
            throw new BizException("POP自动出库，存在sku批次库存数据为空，会重试。");
        }

        // 参数的组装
        StockOutCallbackReq stockOutCallbackReq = new StockOutCallbackReq();
        List<StockOutCallbackItem> stockOutCallbackItems = new ArrayList<>();
        stockTaskItems.forEach(item ->{
            StockOutCallbackItem stockOutCallbackItem = new StockOutCallbackItem();
            stockOutCallbackItem.setSkuCode(item.getSku());
            stockOutCallbackItem.setActualQuantity(Math.max(item.getQuantity() - item.getActualQuantity(),0));

            stockOutCallbackItems.add(stockOutCallbackItem);
        });
        stockOutCallbackReq.setStockOutItems(stockOutCallbackItems);
        stockOutCallbackReq.setStockOutNo(String.valueOf(stockTask.getId()));
        stockOutCallbackReq.setOutWarehouseNo(String.valueOf(stockTask.getAreaNo()));

        // 执行出库
        List<StockTaskSaleOutReq> stockTaskSaleOutReqList = stockTaskSaleOutReqFactory.buildList(
                stockOutCallbackReq,
                batchInventoryVOList,
                stockTaskItems);
        log.info("POP转换成待出库数据：{}", JSON.toJSONString(stockTaskSaleOutReqList));
        //自动出库
        stockTaskService.inOutStore(stockTaskTypeEnum.getType(), JSON.toJSONString(stockTaskSaleOutReqList));
    }
}
