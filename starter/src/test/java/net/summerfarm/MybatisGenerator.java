package net.summerfarm;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.starter.WmsApplication;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.sql.Types;
import java.util.Collections;

/**
 * @Description
 * @Date 2023/9/7 15:20
 * @<AUTHOR>
 */
@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class MybatisGenerator{

    private static final String url = "**************************************************";
    private static final String username = "test";
    private static final String password = "xianmu619";

    @Test
    public void autoGenerator() throws ClassNotFoundException {
        Class.forName("com.mysql.jdbc.Driver");
        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    builder.author("cxh")
                            .disableOpenDir()
                            .commentDate("yyyy-MM-dd")
                            .dateType(DateType.ONLY_DATE)
                            .outputDir("C:/Users/<USER>/Documents/xianmu-project/summerfarm-wms/infrastructure/src/main/java"); // 指定输出目录
                            // String projectPath = System.getProperty("user.dir") + File.separator + "src" + File.separator + "main";
                })

                .packageConfig(builder -> {
                    builder.parent("net.summerfarm.wms.infrastructure.dao")
                            .moduleName("skucodetrace")     // 设置父包模块名 默认值:无
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, "C:/Users/<USER>/Documents/xianmu-project/summerfarm-wms/infrastructure/src/main/resources/mybatis/mapper")); // 设置mapperXml生成路径
                })


                /*.injectionConfig(consumer -> {
                    Map<String, String> customFile = new HashMap<>();
                    // DTO、VO
                    customFile.put("DTO.java", "/templates/entityDTO.java.ftl");
                    customFile.put("VO.java", "/templates/entityVO.java.ftl");

                    consumer.customFile(customFile);
                })*/

                .strategyConfig(builder -> {
                    builder.addInclude("sku_batch_code_trace_weight_log") // 设置需要生成的表名 可边长参数“user”, “user1”
                            .serviceBuilder()
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .entityBuilder()
                            .enableLombok()
                            .enableTableFieldAnnotation()
                            .controllerBuilder()
                            .formatFileName("%sController")
                            .enableRestStyle()
                            .mapperBuilder()
                            .formatMapperFileName("%sDAO")
                            .enableMapperAnnotation()
                            .formatXmlFileName("%sDAO");

                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();

    }


}
