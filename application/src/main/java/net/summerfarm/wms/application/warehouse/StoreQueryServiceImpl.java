package net.summerfarm.wms.application.warehouse;

import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wms.api.h5.warehouse.StoreQueryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/12/22 12:46
 **/
@Service
public class StoreQueryServiceImpl implements StoreQueryService {

    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;


    @Override
    public WarehouseLogisticsCenter queryStoreByNo(Integer storeNo) {
        return warehouseLogisticsService.selectByStoreNo(storeNo);
    }
}
