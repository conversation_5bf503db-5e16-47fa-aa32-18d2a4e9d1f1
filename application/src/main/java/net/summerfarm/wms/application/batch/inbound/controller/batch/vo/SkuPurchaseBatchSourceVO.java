package net.summerfarm.wms.application.batch.inbound.controller.batch.vo;

import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class SkuPurchaseBatchSourceVO  implements Serializable {

    /**
     * 来源批次号
     */
    private String originBatchNo;

    /**
     * 来源sku
     */
    private String originSku;


    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;


    /**
     * 保质期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productDate;

    /**
     * sku
     */
    private String sku;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 成本
     */
    private BigDecimal cost;


    /**
     * qms任务明细
     */
    private List<SkuBatchQmsInfoVO> qmsInfoVOList;

    /**
     * qms任务收货方式描述所有拼接
     */
    private String qmsTaskReceiveWayDescAll;
}
