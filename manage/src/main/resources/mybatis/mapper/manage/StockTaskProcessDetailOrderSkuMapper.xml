<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskProcessDetailOrderSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetailOrderSkuDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="stock_task_process_id" jdbcType="BIGINT" property="stockTaskProcessId" />
        <result column="stock_task_process_detail_id" jdbcType="BIGINT" property="stockTaskProcessDetailId" />
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="batch" jdbcType="VARCHAR" property="batch" />
        <result column="production_date" jdbcType="TIMESTAMP" property="productionDate" />
        <result column="quality_date" jdbcType="TIMESTAMP" property="qualityDate" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>
    <sql id="Base_Column_List">
        id, stock_task_id, stock_task_process_id, stock_task_process_detail_id, out_order_no,
    warehouse_no, sku, batch, production_date, quality_date, quantity, tenant_id, creator, operator,
    create_time, update_time, is_deleted
    </sql>

    <insert id="insert" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetailOrderSkuDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into wms_stock_task_process_detail_order_sku (stock_task_id, stock_task_process_id, stock_task_process_detail_id,
        out_order_no, warehouse_no, sku,
        batch, production_date, quality_date,
        quantity, tenant_id, creator, operator,
        create_time, is_deleted
        )
        values (#{stockTaskId,jdbcType=BIGINT}, #{stockTaskProcessId,jdbcType=BIGINT}, #{stockTaskProcessDetailId,jdbcType=BIGINT},
        #{outOrderNo,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR},
        #{batch,jdbcType=VARCHAR}, #{productionDate,jdbcType=TIMESTAMP}, #{qualityDate,jdbcType=TIMESTAMP},
        #{quantity,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
        now(), 0
        )
    </insert>

    <select id="selectListByProcessId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where stock_task_process_id = #{processId,jdbcType=BIGINT}
    </select>

    <select id="selectListByProcessIdAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where stock_task_process_id = #{processId,jdbcType=BIGINT}
        and sku = #{sku,jdbcType=VARCHAR}
    </select>

    <select id="selectOneByWarehouseNoAndSkuAndQualityDate" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        and sku = #{sku,jdbcType=VARCHAR}
        and production_date = #{productionDate,jdbcType=TIMESTAMP}
        and quality_date = #{qualityDate,jdbcType=TIMESTAMP}
        order by create_time desc
        limit 1
    </select>

    <select id="selectOneByOutOrderNoAndWarehouseNoAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        and sku = #{sku,jdbcType=VARCHAR}
        and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        order by create_time desc
        limit 1
    </select>

    <select id="selectOneByStockTaskIdAndWarehouseNoAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        and sku = #{sku,jdbcType=VARCHAR}
        and stock_task_id = #{stockTaskId,jdbcType=BIGINT}
        order by create_time desc
        limit 1
    </select>

    <select id="selectOneByWarehouseNoAndSkuAndThreeMonth" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_detail_order_sku
        where warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        and sku = #{sku,jdbcType=VARCHAR}
        and create_time >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        order by create_time desc
        limit 1
    </select>

</mapper>