package net.summerfarm.wms.application.materialManage.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialTaskVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskAutoPickService;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskCommandService;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingDetailQueryRepository;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingQueryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.summerfarm.wms.manage.model.vo.pms.AllocationOrderEntityVO;
import net.summerfarm.wms.manage.web.mapper.pms.OldAllocationOrderMapper;
import net.summerfarm.wms.mq.stocktask.StockTaskFinishMsgDTO;
import net.summerfarm.wms.outstore.dto.StockTaskProcessMsgDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WmsMaterialTaskAutoPickServiceImpl implements WmsMaterialTaskAutoPickService {

    @Resource
    private WmsMaterialTaskCommandService wmsMaterialTaskCommandService;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private WarehouseLogisticsFacade warehouseLogisticsFacade;
    @Resource
    private WmsMaterialBindingQueryRepository materialBindingQueryRepository;
    @Resource
    private WmsMaterialBindingDetailQueryRepository materialBindingDetailQueryRepository;
    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private OldAllocationOrderMapper oldAllocationOrderMapper;

    @Override
    public void handleAutoPickUpTask(StockTaskFinishMsgDTO stockTaskFinishMsgDTO) {
        if (stockTaskFinishMsgDTO == null) {
            return;
        }

        WarehouseStorageCenterEntity warehouseStorageCenter =
                warehouseStorageRepository.selectByWarehouseNo(stockTaskFinishMsgDTO.getWarehouseNo());
        if (warehouseStorageCenter == null) {
            return;
        }

        StockTask stockTask = stockTaskRepository.findOutStockTask(
                stockTaskFinishMsgDTO.getStockTaskId().longValue());
        if (stockTask == null) {
            return;
        }

        if (!StockTaskTypeEnum.SALE_OUT.equalCode(stockTask.getType()) &&
                !StockTaskTypeEnum.SUPPLY_AGAIN_TASK.equalCode(stockTask.getType()) &&
                !StockTaskTypeEnum.DEMO_OUT.equalCode(stockTask.getType()) &&
                !StockTaskTypeEnum.CROSS_OUT.equalCode(stockTask.getType()) &&
                !StockTaskTypeEnum.STORE_ALLOCATION_OUT.equalCode(stockTask.getType())) {
            return;
        }

        String destination = null;
        if (StockTaskTypeEnum.STORE_ALLOCATION_OUT.equalCode(stockTask.getType())) {
            AllocationOrderEntityVO listVO = oldAllocationOrderMapper.selectOne(stockTask.getTaskNo());
            if (listVO == null){
                log.info("出库消息中，调拨单不存在：{}", JSONObject.toJSONString(stockTaskFinishMsgDTO));
                return;
            }
            destination = listVO.getInStoreName();
        } else if (stockTask.getOutStoreNo() != null) {
            destination = warehouseLogisticsFacade.queryStoreName(stockTask.getOutStoreNo());
        }
        if (StringUtils.isEmpty(destination)) {
            log.info("出库消息中，目的地不存在：{}", JSONObject.toJSONString(stockTaskFinishMsgDTO));
            return;
        }

        // 物料一级类目
        Long materialFirstCategory = productRepository.getMaterialFirstCategory();

        List<String> skuList = stockTaskFinishMsgDTO.getOrderSkuList().stream()
                .map(StockTaskFinishMsgDTO.OrderSkuDTO::getSku)
                .distinct()
                .collect(Collectors.toList());

        // 绑定
        Map<String, List<WmsMaterialBindingEntity>> wmsMaterialBindingMap = materialBindingQueryRepository.mapByWnoAndSkuList(
                stockTask.getAreaNo(), skuList);
        List<Long> bindingIdList = wmsMaterialBindingMap.values().stream()
                .flatMap(Collection::stream)
                .map(WmsMaterialBindingEntity::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<WmsMaterialBindingDetailEntity>> bindingDetailMap = materialBindingDetailQueryRepository
                .mapByBindingIdList(bindingIdList);

        // 货品信息
        List<String> materialSkuList = bindingDetailMap.values().stream()
                .flatMap(Collection::stream)
                .map(WmsMaterialBindingDetailEntity::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                warehouseStorageCenter.getTenantId(), materialSkuList);

        // 库存信息
        Map<String, AreaStore> materialAreaStoreMap = areaStoreRepository.mapSkuStockBySku(
                stockTask.getAreaNo().longValue(), materialSkuList);

        List<WmsMaterialTaskDetailCommandInput> detailList = new ArrayList<>();
        for (StockTaskFinishMsgDTO.OrderSkuDTO orderSkuDTO : stockTaskFinishMsgDTO.getOrderSkuList()) {
            // 无物料配置
            List<WmsMaterialBindingEntity> wmsMaterialBindingEntityList = wmsMaterialBindingMap.get(orderSkuDTO.getSku());
            if (CollectionUtils.isEmpty(wmsMaterialBindingEntityList)) {
                continue;
            }

            for (WmsMaterialBindingEntity entity : wmsMaterialBindingEntityList) {
                handleDetail(materialFirstCategory,
                        bindingDetailMap,
                        materialGoodsInfoDTOMap,
                        materialAreaStoreMap,
                        detailList,
                        orderSkuDTO,
                        entity);
            }
        }
        if (CollectionUtils.isEmpty(detailList)){
            return;
        }

        // detailList，校验库存
        Map<String, WmsMaterialTaskDetailCommandInput> detailCommandInputMap = detailList.stream()
                .filter(s -> s.getMaterialSku()!= null)
                .collect(Collectors.toMap(WmsMaterialTaskDetailCommandInput::getMaterialSku, s -> s,
                        (a, b) -> {
                            a.setQuantity(a.getQuantity() + b.getQuantity());
                            return a;
                        }));
        detailList = new ArrayList<>(detailCommandInputMap.values());
        // 校验库存
        for (WmsMaterialTaskDetailCommandInput detailCommandInput : detailList) {
            AreaStore materialAreaStore = materialAreaStoreMap.get(detailCommandInput.getMaterialSku());
            if (materialAreaStore == null) {
                continue;
            }
            Integer maxQuantity = materialAreaStore.getQuantity() > materialAreaStore.getOnlineQuantity() ?
                    materialAreaStore.getOnlineQuantity() : materialAreaStore.getQuantity();
            maxQuantity = maxQuantity > detailCommandInput.getQuantity() ?
                    detailCommandInput.getQuantity() : maxQuantity;
            detailCommandInput.setQuantity(maxQuantity);
        }

        // 填充数据
        WmsMaterialTaskCommandInput input = new WmsMaterialTaskCommandInput();
        input.setCreateTime(LocalDateTime.now());
        input.setCreator(WmsConstant.SYSTEM);
        input.setUpdateTime(LocalDateTime.now());
        input.setUpdater(WmsConstant.SYSTEM);

        input.setWarehouseNo(stockTask.getAreaNo());
        input.setTenantId(warehouseStorageCenter.getTenantId());
        input.setSourceId(stockTask.getId());
        input.setSourceCode(stockTask.getTaskNo());
        input.setSourceType(stockTask.getType());

        input.setDestination(destination);
        input.setDetailList(detailList);
        try {
            CommonResult<WmsMaterialTaskVO> commonResult = wmsMaterialTaskCommandService.receive(input);
            if (commonResult == null || !ResultStatusEnum.OK.getStatus().equals(commonResult.getStatus())) {
                log.error("创建物料任务失败：{}", JSONObject.toJSONString(commonResult));
            }
        } catch (Exception ex) {
            log.error("创建物料任务失败", ex);
        }
    }

    private static void handleDetail(Long materialFirstCategory,
                                     Map<Long, List<WmsMaterialBindingDetailEntity>> bindingDetailMap,
                                     Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap,
                                     Map<String, AreaStore> materialAreaStoreMap,
                                     List<WmsMaterialTaskDetailCommandInput> detailList,
                                     StockTaskFinishMsgDTO.OrderSkuDTO orderSkuDTO,
                                     WmsMaterialBindingEntity entity) {
        List<WmsMaterialBindingDetailEntity> bindingDetailEntityList = bindingDetailMap.get(entity.getId());
        for (WmsMaterialBindingDetailEntity wmsMaterialBindingDetailEntity : bindingDetailEntityList) {
            GoodsInfoDTO materialGoodsInfoDTO = materialGoodsInfoDTOMap.get(wmsMaterialBindingDetailEntity.getMaterialSku());
            if (materialGoodsInfoDTO == null) {
                continue;
            }

            // 是否物料
            if (materialFirstCategory == null ||
                    !materialFirstCategory.equals(materialGoodsInfoDTO.getFirstCategoryId())) {
                continue;
            }

            // 是否自动出库
            String propertyValue = materialGoodsInfoDTO.getPropertyValueByEnum(ProductsPropertyEnum.AUTO_OUTBOUND);
            if (!"是".equals(propertyValue)) {
                continue;
            }

            // 仓库库存
            AreaStore materialAreaStore = materialAreaStoreMap.get(wmsMaterialBindingDetailEntity.getMaterialSku());
            if (materialAreaStore == null) {
                continue;
            }

            //最大出库数量
            if (entity.getSkuRatio() == null ||
                    entity.getSkuRatio().compareTo(BigDecimal.ZERO) <= 0 ||
                    wmsMaterialBindingDetailEntity.getMaterialSkuRatio() == null ||
                    wmsMaterialBindingDetailEntity.getMaterialSkuRatio().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            Integer materialOutQuantity = BigDecimal.valueOf(orderSkuDTO.getActualQuantity())
                    .multiply(wmsMaterialBindingDetailEntity.getMaterialSkuRatio())
                    .divide(entity.getSkuRatio(), 0, BigDecimal.ROUND_UP)
                    .intValue();
            Integer maxQuantity = materialAreaStore.getQuantity() > materialAreaStore.getOnlineQuantity() ?
                    materialAreaStore.getOnlineQuantity() : materialAreaStore.getQuantity();
            maxQuantity = maxQuantity > materialOutQuantity ? materialOutQuantity : maxQuantity;
            if (maxQuantity <= 0) {
                continue;
            }

            WmsMaterialTaskDetailCommandInput updateDto = new WmsMaterialTaskDetailCommandInput();
            updateDto.setMaterialSku(wmsMaterialBindingDetailEntity.getMaterialSku());
            updateDto.setMaterialSkuSaasId(materialGoodsInfoDTO.getSkuId());
            updateDto.setQuantity(maxQuantity);

            detailList.add(updateDto);
        }
    }
}
