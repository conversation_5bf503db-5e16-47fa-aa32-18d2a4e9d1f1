package net.summerfarm.wms.infrastructure.dao.inventory;

import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryWarnDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SafeInventoryWarnDAO {

    /**
     * create
     *
     * @param safeInventoryWarn
     * @return
     */
    Long create(SafeInventoryWarnDO safeInventoryWarn);

    /**
     * batch create
     *
     * @param safeInventoryWarns
     * @return
     */
    Integer creates(List<SafeInventoryWarnDO> safeInventoryWarns);

    /**
     * findById
     *
     * @param id
     * @return
     */
    SafeInventoryWarnDO findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<SafeInventoryWarnDO> findByIds(List<Long> ids);

    /**
     * update
     *
     * @param safeInventoryWarn
     * @return
     */
    Integer update(SafeInventoryWarnDO safeInventoryWarn);

    /**
     * count
     *
     * @param safeInventoryWarnQuery
     * @return
     */
    Long count(SafeInventoryWarnQuery safeInventoryWarnQuery);

    /**
     * findOne
     *
     * @param safeInventoryWarnQuery
     * @return
     */
    SafeInventoryWarnDO findOne(SafeInventoryWarnQuery safeInventoryWarnQuery);

    /**
     * list
     *
     * @param safeInventoryWarnQuery
     * @return
     */
    List<SafeInventoryWarnDO> list(SafeInventoryWarnQuery safeInventoryWarnQuery);

    /**
     * 查询安全库存的任务面板 - sku编码
     * @param warehouseNo 仓库编号
     * @param status warnStatus
     * @return 返回结果
     */
    List<SafeInventoryWarnDO> queryTaskPanelSkuCode(@Param("warehouseNo") Integer warehouseNo,
                                        @Param("status") Integer status);


}
