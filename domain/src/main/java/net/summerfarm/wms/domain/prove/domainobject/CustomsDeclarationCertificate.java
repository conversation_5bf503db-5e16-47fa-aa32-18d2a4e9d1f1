package net.summerfarm.wms.domain.prove.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.ValueObject;

/**
 * 报关证明
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomsDeclarationCertificate implements ValueObject {
    /**
     * 报关证明
     */
    String proofUrl;
}
