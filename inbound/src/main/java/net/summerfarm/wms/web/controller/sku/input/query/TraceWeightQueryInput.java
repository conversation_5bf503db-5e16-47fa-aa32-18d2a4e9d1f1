package net.summerfarm.wms.web.controller.sku.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Description: 称重分页查询<br/>
 * date: 2024/8/7 16:07<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceWeightQueryInput{
    /**
     * 分页条目
     */
    @NotNull(message = "请指定分页条目")
    private Integer pageIndex;

    /**
     * 分页数量
     */
    @NotNull(message = "请指定分页大小")
    private Integer pageSize;

    /**
     * 入库任务id
     */
    private Integer stockTaskStorageId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 货品名称
     */
    private String pdName;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 状态
     10：订单待分配
     20：路线待分配
     30：待称重
     40：已称重
     */
    private Integer state;

    /**
     * 开始时间
     */
    private LocalDateTime beginCreateTime;

    /**
     * 结束时间
     */
    private LocalDateTime endCreateTime;
}
