package net.summerfarm.wms.domain.inventory.domainobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryFlow;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryUpdate;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventoryFlow;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventoryUpdate;

import java.io.Serializable;
import java.util.List;

@Data
public class CabinetInventoryReduceAggregate implements Serializable {


    public List<CabinetBatchInventoryUpdate> updateCabinetBatchInventoryList;

    public List<CabinetBatchInventoryFlow> cabinetBatchInventoryFlowList;

    public List<CabinetInventoryUpdate> updateCabinetInventoryList;

    public List<CabinetInventoryFlow> cabinetInventoryFlowList;
}
