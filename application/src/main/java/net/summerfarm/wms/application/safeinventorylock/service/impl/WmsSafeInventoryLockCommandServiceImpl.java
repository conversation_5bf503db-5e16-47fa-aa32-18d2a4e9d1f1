package net.summerfarm.wms.application.safeinventorylock.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryOccupyDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryOccupyReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryReleaseDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryReleaseReq;
import net.summerfarm.wms.application.areastore.AreaStoreNewService;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreSafeStockUpdateDto;
import net.summerfarm.wms.application.bizLog.controller.bizLog.input.command.WmsBizLogCommandInput;
import net.summerfarm.wms.application.bizLog.service.bizLog.WmsBizLogCommandService;
import net.summerfarm.wms.application.safeinventorylock.assembler.WmsSafeInventoryLockAssembler;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockUnlockInput;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockCommandService;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockQueryService;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.bizLog.enums.WmsBizLogTypeEnum;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.inventory.domainobject.UpdateBatchInventoryLockRelation;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryLockCommandRepository;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.enums.LockStatusEnum;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockCommandRepository;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;


/**
 * 安全库存锁定表命令服务实现
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class WmsSafeInventoryLockCommandServiceImpl implements WmsSafeInventoryLockCommandService {

    @Autowired
    private WmsSafeInventoryLockCommandRepository wmsSafeInventoryLockCommandRepository;

    @Autowired
    private WmsSafeInventoryLockQueryService wmsSafeInventoryLockQueryService;

    @Autowired
    private WarehouseConfigRepository warehouseConfigRepository;

    @Autowired
    private CabinetInventoryCommandService cabinetInventoryCommandService;

    @Autowired
    private AreaStoreNewService areaStoreNewService;

    @Autowired
    private AreaStoreRepository areaStoreRepository;

    @Autowired
    private WmsBizLogCommandService wmsBizLogCommandService;

    @Autowired
    private CabinetBatchInventoryLockCommandRepository cabinetBatchInventoryLockCommandRepository;

    @Autowired
    private SerialNumberGenerator serialNumberGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> lock(WmsSafeInventoryLockLockInput input) {
        try {
            // 获取当前操作人
            String operator = LoginInfoThreadLocal.getCurrentUserName();

            // 设置租户ID
            if (input.getTenantId() == null) {
                input.setTenantId(LoginInfoThreadLocal.getTenantId());
            }

            // 转换参数并创建锁定记录
            String lockNo = serialNumberGenerator.createInternalNo(WmsConstant.INVENTORY_LOCK_PRE);;
            WmsSafeInventoryLockCommandParam param = WmsSafeInventoryLockAssembler.toWmsSafeInventoryLockCommandParam(input, operator, lockNo);
            wmsSafeInventoryLockCommandRepository.insertSelective(param);

            // 执行库存锁定逻辑
            executeInventoryLocking(input, operator, param.getLockNo());

            return CommonResult.ok(true);
        } catch (Exception e) {
            log.error("锁定安全库存失败", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "锁定失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> unlock(WmsSafeInventoryLockUnlockInput input) {
        try {
            // 获取当前操作人
            String operator = LoginInfoThreadLocal.getCurrentUserName();
            if (!StringUtils.hasText(operator)) {
                operator = "system";
            }

            // 检查锁定记录是否存在
            WmsSafeInventoryLockEntity entity = wmsSafeInventoryLockQueryService.getById(input.getId());
            if (entity == null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "锁定记录不存在");
            }

            // 检查是否已经释放
            if (LockStatusEnum.RELEASED.getCode().equals(entity.getLockStatus())) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该锁定记录已经释放");
            }

            // 执行库存释放逻辑
            executeInventoryUnlocking(entity, input, operator);

            // 释放锁定
            wmsSafeInventoryLockCommandRepository.updateLockQuantityById(entity.getId(), input.getUnlockQuantity(), operator, entity.getLockQuantity());
            if (entity.getLockQuantity() <= input.getUnlockQuantity()) {
                // 更新锁定状态
                wmsSafeInventoryLockCommandRepository.updateLockStatusByLockNo(entity.getId(), LockStatusEnum.RELEASED.getCode());
            }

            return CommonResult.ok(true);
        } catch (Exception e) {
            log.error("释放安全库存锁定失败", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "释放锁定失败，请稍后重试");
        }
    }

    /**
     * 执行库存锁定逻辑
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     */
    private void executeInventoryLocking(WmsSafeInventoryLockLockInput input, String operator, String lockNo) {
        try {
            // 检查是否为精细化仓
            Boolean isCabinetWarehouse = warehouseConfigRepository.openCabinetManagement(input.getWarehouseNo());

            if (Boolean.TRUE.equals(isCabinetWarehouse)) {
                // 精细化仓处理逻辑
                handleCabinetWarehouseLocking(input, operator, lockNo);
            } else {
                // 非精细化仓处理逻辑
                handleNoCabinetWarehouseLocking(input, operator, lockNo);
            }
        } catch (Exception e) {
            log.error("执行库存锁定逻辑失败，lockNo: {}, error: {}", lockNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理精细化仓库存锁定
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     */
    private void handleCabinetWarehouseLocking(WmsSafeInventoryLockLockInput input, String operator, String lockNo) {
        log.info("开始处理精细化仓库存锁定，lockNo: {}, sku: {}, warehouseNo: {}", lockNo, input.getSku(), input.getWarehouseNo());

        try {
            // 1. 占用库位批次库存
            occupyCabinetInventory(input, operator, lockNo);

            // 2. 操作安全库存
            updateSafeStock(input, operator);

            // 3. 记录操作日志
            recordOperationLog(input, operator, lockNo, "库存锁定");

            log.info("精细化仓库存锁定完成，lockNo: {}", lockNo);
        } catch (Exception e) {
            log.error("精细化仓库存锁定失败，lockNo: {}", lockNo, e);
            throw e;
        }
    }

    /**
     * 处理非精细化仓库存锁定
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     */
    private void handleNoCabinetWarehouseLocking(WmsSafeInventoryLockLockInput input, String operator, String lockNo) {
        log.info("开始处理非精细化仓库存锁定，lockNo: {}, sku: {}, warehouseNo: {}", lockNo, input.getSku(), input.getWarehouseNo());

        try {
            // 记录批次锁定记录
            recordBatchLockInventory(input, operator, lockNo);
            // 记录操作日志
            recordOperationLog(input, operator, lockNo, "库存锁定");

            log.info("非精细化仓库存锁定完成，lockNo: {}", lockNo);
        } catch (Exception e) {
            log.error("非精细化仓库存锁定失败，lockNo: {}, error: {}", lockNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 占用库位批次库存
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     */
    private void occupyCabinetInventory(WmsSafeInventoryLockLockInput input, String operator, String lockNo) {
        // 构建库位库存占用请求
        CabinetInventoryOccupyReq occupyReq = new CabinetInventoryOccupyReq();
        occupyReq.setTenantId(input.getTenantId());
        occupyReq.setWarehouseNo(input.getWarehouseNo());
        occupyReq.setOrderNo(lockNo);
        occupyReq.setOrderTypeName(CabinetInventoryChangeTypeEnum.INVENTORY_LOCK.getTypeName());
        occupyReq.setInternalNo(lockNo);
        occupyReq.setOperatorType(CabinetInventoryOperationType.INVENTORY_LOCK.getId());
        occupyReq.setOrderTypeName(operator);

        // 构建占用详情
        CabinetInventoryOccupyDetailReq detailReq = new CabinetInventoryOccupyDetailReq();
        detailReq.setSkuCode(input.getSku());
        detailReq.setOccupyQuantity(input.getLockQuantity());
        detailReq.setCabinetCode(input.getCabinetCode());
        detailReq.setSkuBatchCode(input.getBatchNo());
        if (input.getProduceDate() != null) {
            detailReq.setProduceDate(DateUtil.toDate(input.getProduceDate()));
        }
        if (input.getQualityDate() != null) {
            detailReq.setQualityDate(DateUtil.toDate(input.getQualityDate()));
        }

        occupyReq.setOccupyDetailReqList(Collections.singletonList(detailReq));

        // 执行库位库存占用
        cabinetInventoryCommandService.occupyCabinetInventory(occupyReq);
        log.info("库位库存占用完成，lockNo: {}, sku: {}, quantity: {}", lockNo, input.getSku(), input.getLockQuantity());
    }

    /**
     * 更新安全库存
     * @param input 锁定输入
     * @param operator 操作人
     */
    private void updateSafeStock(WmsSafeInventoryLockLockInput input, String operator) {
        // 查询当前安全库存
        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(input.getWarehouseNo().longValue(), input.getSku());
        if (areaStore == null) {
            log.warn("未找到库存记录，warehouseNo: {}, sku: {}", input.getWarehouseNo(), input.getSku());
            return;
        }

        // 计算新的安全库存数量（当前安全库存 + 锁定数量）
        Integer currentSafeQuantity = areaStore.getSafeQuantity() != null ? areaStore.getSafeQuantity() : 0;
        Integer newSafeQuantity = currentSafeQuantity + input.getLockQuantity();

        // 构建更新请求
        AreaStoreSafeStockUpdateDto updateDto = new AreaStoreSafeStockUpdateDto();
        updateDto.setWarehouseNo(input.getWarehouseNo().longValue());
        updateDto.setSkuCode(input.getSku());
        updateDto.setSafeQuantity(newSafeQuantity);
        updateDto.setRemark(input.getLockReason());
        updateDto.setTenantId(input.getTenantId());
        updateDto.setOperator(operator);
        updateDto.setOperatorId(LoginInfoThreadLocal.getCurrentUserId());

        // 执行安全库存更新
        CommonResult<String> result = areaStoreNewService.updateSafeStock(updateDto);
        if (!ResultStatusEnum.OK.getStatus().equals(result.getStatus())) {
            throw new RuntimeException("更新安全库存失败");
        }

        log.info("安全库存更新完成，sku: {}, 原安全库存: {}, 新安全库存: {}", input.getSku(), currentSafeQuantity, newSafeQuantity);
    }

    /**
     * 记录操作日志
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     * @param title 日志标题
     */
    private void recordOperationLog(WmsSafeInventoryLockLockInput input, String operator, String lockNo, String title) {
        WmsBizLogCommandInput logInput = new WmsBizLogCommandInput();
        logInput.setTenantId(input.getTenantId());
        logInput.setBizType(WmsBizLogTypeEnum.INVENTORY_LOCK.getCode());
        logInput.setBizNo(lockNo);
        logInput.setTitle(title);
        logInput.setContent(String.format("锁定库存 - SKU: %s, 数量: %d, 原因: %s",
                input.getSku(), input.getLockQuantity(), input.getLockReason()));
        logInput.setOperatorName(operator);
        logInput.setCreateTime(LocalDateTime.now());
        logInput.setUpdateTime(LocalDateTime.now());

        wmsBizLogCommandService.insert(logInput);
        log.info("操作日志记录完成，lockNo: {}", lockNo);
    }

    /**
     * 记录批次锁定记录（非精细化仓）
     * @param input 锁定输入
     * @param operator 操作人
     * @param lockNo 锁定编号
     */
    private void recordBatchLockInventory(WmsSafeInventoryLockLockInput input, String operator, String lockNo) {
        UpdateBatchInventoryLockRelation lockRelation = new UpdateBatchInventoryLockRelation();
        lockRelation.setWarehouseNo(input.getWarehouseNo().longValue());
        lockRelation.setSku(input.getSku());
        lockRelation.setBatch(input.getBatchNo());
        lockRelation.setCabinetNo(input.getCabinetCode());
        if (input.getProduceDate() != null) {
            lockRelation.setProduceDate(DateUtil.toDate(input.getProduceDate()));
        }
        if (input.getQualityDate() != null) {
            lockRelation.setQualityDate(DateUtil.toDate(input.getQualityDate()));
        }
        lockRelation.setChangeQuantity(input.getLockQuantity());
        lockRelation.setBizId(lockNo);
        lockRelation.setBizType(CabinetInventoryOperationType.INVENTORY_LOCK.getId());
        lockRelation.setBizName(CabinetInventoryOperationType.INVENTORY_LOCK.getName());
        lockRelation.setOperator(operator);

        cabinetBatchInventoryLockCommandRepository.updateInsert(lockRelation);
        log.info("批次锁定记录完成，lockNo: {}, sku: {}, quantity: {}", lockNo, input.getSku(), input.getLockQuantity());
    }

    /**
     * 执行库存释放逻辑
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void executeInventoryUnlocking(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        try {
            // 检查是否为精细化仓
            Boolean isCabinetWarehouse = warehouseConfigRepository.openCabinetManagement(entity.getWarehouseNo());

            if (Boolean.TRUE.equals(isCabinetWarehouse)) {
                // 精细化仓处理逻辑
                handleCabinetWarehouseUnlocking(entity, input, operator);
            } else {
                // 非精细化仓处理逻辑
                handleNoCabinetWarehouseUnlocking(entity, input, operator);
            }
        } catch (Exception e) {
            log.error("执行库存释放逻辑失败，lockNo: {}, error: {}", entity.getLockNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理精细化仓库存释放
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void handleCabinetWarehouseUnlocking(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        log.info("开始处理精细化仓库存释放，lockNo: {}, sku: {}, warehouseNo: {}", entity.getLockNo(), entity.getSku(), entity.getWarehouseNo());

        try {
            // 1. 释放库位批次库存
            releaseCabinetInventory(entity, input, operator);

            // 2. 操作安全库存（减少安全库存）
            reduceSafeStock(entity, input, operator);

            // 3. 记录操作日志
            recordUnlockOperationLog(entity, input, operator, "库存锁定释放");

            log.info("精细化仓库存释放完成，lockNo: {}", entity.getLockNo());
        } catch (Exception e) {
            log.error("精细化仓库存释放失败，lockNo: {}, error: {}", entity.getLockNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理非精细化仓库存释放
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void handleNoCabinetWarehouseUnlocking(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        log.info("开始处理非精细化仓库存释放，lockNo: {}, sku: {}, warehouseNo: {}", entity.getLockNo(), entity.getSku(), entity.getWarehouseNo());

        try {
            // 1. 操作批次锁定记录进行释放
            releaseBatchLockInventory(entity, input, operator);

            // 2. 记录操作日志
            recordUnlockOperationLog(entity, input, operator, "库存锁定释放");

            log.info("非精细化仓库存释放完成，lockNo: {}", entity.getLockNo());
        } catch (Exception e) {
            log.error("非精细化仓库存释放失败，lockNo: {}, error: {}", entity.getLockNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 释放库位批次库存
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void releaseCabinetInventory(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        // 构建库位库存释放请求
        CabinetInventoryReleaseReq releaseReq = new CabinetInventoryReleaseReq();
        releaseReq.setTenantId(entity.getTenantId());
        releaseReq.setWarehouseNo(entity.getWarehouseNo());
        releaseReq.setOrderNo(entity.getLockNo());
        releaseReq.setOrderTypeName(CabinetInventoryOperationType.INVENTORY_UNLOCK.getName());
        releaseReq.setOperatorType(CabinetInventoryOperationType.INVENTORY_UNLOCK.getId());

        // 构建释放详情
        CabinetInventoryReleaseDetailReq detailReq = new CabinetInventoryReleaseDetailReq();
        detailReq.setSkuCode(entity.getSku());
        detailReq.setReleaseQuantity(input.getUnlockQuantity());
        detailReq.setCabinetCode(entity.getCabinetCode());
        detailReq.setSkuBatchCode(entity.getBatchNo());
        if (entity.getProduceDate() != null) {
            detailReq.setProduceDate(DateUtil.toDate(entity.getProduceDate()));
        }
        if (entity.getQualityDate() != null) {
            detailReq.setQualityDate(DateUtil.toDate(entity.getQualityDate()));
        }

        releaseReq.setReleaseDetailReqList(Collections.singletonList(detailReq));

        // 执行库位库存释放
        cabinetInventoryCommandService.releaseCabinetInventory(releaseReq);
        log.info("库位库存释放完成，lockNo: {}, sku: {}, quantity: {}", entity.getLockNo(), entity.getSku(), input.getUnlockQuantity());
    }

    /**
     * 减少安全库存
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void reduceSafeStock(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        // 查询当前安全库存
        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(entity.getWarehouseNo().longValue(), entity.getSku());
        if (areaStore == null) {
            log.warn("未找到库存记录，warehouseNo: {}, sku: {}", entity.getWarehouseNo(), entity.getSku());
            return;
        }

        // 计算新的安全库存数量（当前安全库存 - 释放数量）
        Integer currentSafeQuantity = areaStore.getSafeQuantity() != null ? areaStore.getSafeQuantity() : 0;
        Integer newSafeQuantity = Math.max(0, currentSafeQuantity - input.getUnlockQuantity());

        // 构建更新请求
        AreaStoreSafeStockUpdateDto updateDto = new AreaStoreSafeStockUpdateDto();
        updateDto.setWarehouseNo(entity.getWarehouseNo().longValue());
        updateDto.setSkuCode(entity.getSku());
        updateDto.setSafeQuantity(newSafeQuantity);
        updateDto.setRemark(input.getUnlockReason());
        updateDto.setTenantId(entity.getTenantId());
        updateDto.setOperator(operator);
        updateDto.setOperatorId(LoginInfoThreadLocal.getCurrentUserId());

        // 执行安全库存更新
        CommonResult<String> result = areaStoreNewService.updateSafeStock(updateDto);
        if (!ResultStatusEnum.OK.getStatus().equals(result.getStatus())) {
            throw new RuntimeException("更新安全库存失败");
        }

        log.info("安全库存减少完成，sku: {}, 原安全库存: {}, 新安全库存: {}", entity.getSku(), currentSafeQuantity, newSafeQuantity);
    }

    /**
     * 释放批次锁定记录（非精细化仓）
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     */
    private void releaseBatchLockInventory(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator) {
        UpdateBatchInventoryLockRelation lockRelation = new UpdateBatchInventoryLockRelation();
        lockRelation.setWarehouseNo(entity.getWarehouseNo().longValue());
        lockRelation.setSku(entity.getSku());
        lockRelation.setBatch(entity.getBatchNo());
        lockRelation.setCabinetNo(entity.getCabinetCode());
        if (entity.getProduceDate() != null) {
            lockRelation.setProduceDate(DateUtil.toDate(entity.getProduceDate()));
        }
        if (entity.getQualityDate() != null) {
            lockRelation.setQualityDate(DateUtil.toDate(entity.getQualityDate()));
        }
        // 释放时使用负数表示减少锁定数量
        lockRelation.setChangeQuantity(-input.getUnlockQuantity());
        lockRelation.setBizId(entity.getLockNo());
        lockRelation.setBizType(CabinetInventoryChangeTypeEnum.INVENTORY_LOCK.getType());
        lockRelation.setBizName(CabinetInventoryChangeTypeEnum.INVENTORY_LOCK.getTypeName());
        lockRelation.setOperator(operator);

        cabinetBatchInventoryLockCommandRepository.updateInsert(lockRelation);
        log.info("批次锁定释放记录完成，lockNo: {}, sku: {}, quantity: {}", entity.getLockNo(), entity.getSku(), input.getUnlockQuantity());
    }

    /**
     * 记录释放操作日志
     * @param entity 锁定实体
     * @param input 释放输入
     * @param operator 操作人
     * @param title 日志标题
     */
    private void recordUnlockOperationLog(WmsSafeInventoryLockEntity entity, WmsSafeInventoryLockUnlockInput input, String operator, String title) {
        WmsBizLogCommandInput logInput = new WmsBizLogCommandInput();
        logInput.setTenantId(entity.getTenantId());
        logInput.setBizType(WmsBizLogTypeEnum.INVENTORY_UNLOCK.getCode());
        logInput.setBizNo(entity.getLockNo());
        logInput.setTitle(title);
        logInput.setContent(String.format("库存锁定释放 - SKU: %s, 释放数量: %d", entity.getSku(), input.getUnlockQuantity()));
        logInput.setOperatorName(operator);
        logInput.setCreateTime(LocalDateTime.now());
        logInput.setUpdateTime(LocalDateTime.now());

        wmsBizLogCommandService.insert(logInput);
        log.info("释放操作日志记录完成，lockNo: {}", entity.getLockNo());
    }

}
