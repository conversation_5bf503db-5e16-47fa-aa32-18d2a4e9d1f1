package net.summerfarm.wms.domain.mission.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;

import java.util.Arrays;
import java.util.Set;

/**
 * 任务类型，10-上架任务，20-移库任务，30-入库任务（非收货,兼容），40-拣货任务，50-盘点任务，60-越库投线任务,70-货检任务,999-未知
 */
@Getter
@AllArgsConstructor
public enum MissionTypeEnum {
    SHELVING(10, "上架任务", "SJ", DefaultCabinetCodeEnum.SH.getCode()),
    MOVE_CABINET(20, "移库任务", "YK",""),
    IN_STORE_TASK(30, "入库任务（非收货,兼容）", "SH",""),
    ORDER_PICKING(40, "拣货任务", "JH",""),
    STOCKTAKING(50, "盘点任务", "PD",""),
    CROSS_WAREHOUSE_SORT(60, "越库投线任务", "TX",""),
    INSPECT_TASK(70, "货检任务", "HJ",""),
    UNKNOWN(999, "未知", "未知",""),
    ;
    // 需要展示来源容器
    public static Set<Integer> sourceContainerNo = Sets.newHashSet(SHELVING.code);
    // 需要展示来源库位
    public static Set<Integer> sourceCabinetNo = Sets.newHashSet(MOVE_CABINET.code);
    // 记录时效的任务
    public static Set<Integer> ageingMission = Sets.newHashSet(ORDER_PICKING.code);

    public static String convertAbbreviation(Integer code) {
        return Arrays.stream(MissionTypeEnum.values())
                .filter(o -> o.getCode().equals(code))
                .findFirst().orElse(MissionTypeEnum.UNKNOWN).getAbbreviation();
    }

    public static MissionTypeEnum convertEnum(Integer code) {
        return Arrays.stream(MissionTypeEnum.values())
                .filter(o -> o.getCode().equals(code))
                .findFirst().orElse(MissionTypeEnum.UNKNOWN);
    }

    public boolean equalsCode(Integer input){
        return this.code.equals(input);
    }

    Integer code;
    String desc;
    String abbreviation;
    String defaultSourceCabinetNo;
}
