package net.summerfarm.wms.infrastructure.repository.instore.impl;

import com.google.common.collect.Lists;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItem;
import net.summerfarm.wms.domain.instore.valueObject.StockStorageItemActualQuantityValueObject;
import net.summerfarm.wms.domain.instore.valueObject.StockStorageItemDetailBatchValueObject;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItemCount;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.infrastructure.dao.instore.StockStorageItemDAO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemCountDO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDO;
import net.summerfarm.wms.infrastructure.repository.instore.converter.StockStorageItemConverter;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/16  10:14
 */
@Repository
public class StockStorageItemRepositoryImpl implements StockStorageItemRepository {

    @Resource
    private StockStorageItemDAO stockStorageItemDAO;

    @Override
    public Long saveStockStorageItem(StockStorageItem stockStorageItem) {
        StockStorageItemDO saveStockStorageItemDO = StockStorageItemConverter.INSTANCE.converter(stockStorageItem);
        stockStorageItemDAO.saveStockStorageItem(saveStockStorageItemDO);
        return saveStockStorageItemDO.getId();
    }

    @Override
    public void saveBatchList(List<StockStorageItem> stockStorageItem) {
        List<StockStorageItemDO> saveBatchItem = stockStorageItem.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
        stockStorageItemDAO.saveBatchStockStorageItem(saveBatchItem);
    }

    @Override
    public List<StockStorageItem> queryStockStorageItem(Long stockTaskStorageId) {
        StockStorageItemDO queryItem = StockStorageItemDO.builder().
                stockTaskStorageId(stockTaskStorageId).build();
        List<StockStorageItemDO> stockStorageItemDOS = stockStorageItemDAO.queryStockStorageItem(queryItem);
        return stockStorageItemDOS.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
    }

    @Override
    public List<StockStorageItem> queryStockStorageItems(List<Long> stockTaskStorageIds) {
        if (CollectionUtils.isEmpty(stockTaskStorageIds)){
            return Lists.newArrayList();
        }
        List<StockStorageItemDO> stockStorageItemDOS = stockStorageItemDAO.queryStockStorageItemByStorageIds(stockTaskStorageIds);
        return stockStorageItemDOS.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
    }

    @Override
    public StockStorageItem queryStockStorageItemByIdAndSku(Long stockTaskStorageId, String sku) {
        StockStorageItemDO stockStorageItemDO = stockStorageItemDAO.queryStockStorageItemByIdAndSku(stockTaskStorageId, sku);
        return StockStorageItemConverter.INSTANCE.converter(stockStorageItemDO);
    }

    @Override
    public List<StockStorageItem> queryStockStorageItem(Long taskId, List<String> skuCodes, String category) {
        List<StockStorageItemDO> stockStorageItemDOList = stockStorageItemDAO.queryStockStorageItemByCondition(taskId, skuCodes, category);
        if (CollectionUtils.isNotEmpty(stockStorageItemDOList)) {
            return stockStorageItemDOList.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<StockStorageItem> batchStockStorageItem(List<Long> stockTaskStorageIds) {
        if (CollectionUtils.isEmpty(stockTaskStorageIds)) {
            //err todo
            throw new DefaultServiceException("查询为空");
        }
        List<StockStorageItemDO> stockStorageItemDOS = stockStorageItemDAO.queryStockStorageItemBatch(stockTaskStorageIds);
        return stockStorageItemDOS.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
    }

    @Override
    public void updateStockStorageItem(StockStorageItem updateItem) {
        StockStorageItemDO updateItemDO = StockStorageItemDO.builder().id(updateItem.getId())
                .actualQuantity(updateItem.getActualQuantity())
                .quantity(updateItem.getQuantity())
                .stockTaskStorageId(updateItem.getStockTaskStorageId())
                .pdName(updateItem.getPdName())
                .packaging(updateItem.getPackaging())
                .specification(updateItem.getSpecification())
                .categoryType(updateItem.getCategoryType())
                .skuType(updateItem.getSkuType())
                .build();
        int update = stockStorageItemDAO.updateStockStorageItem(updateItemDO);
        if (update <= 0) {
            throw new BizException("请检查收货数量");
        }
    }

    @Override
    public List<StockStorageItem> itemIdByStockTaskId(Long stockTaskId) {
        List<StockStorageItemDO> stockStorageItemDOS = stockStorageItemDAO.queryStockStorageItemByStockTaskId(stockTaskId);
        return stockStorageItemDOS.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
    }

    @Override
    public void updateStockStorageItemAddTime(StockStorageItem updateItem) {
        StockStorageItemDO updateItemDO = StockStorageItemDO.builder().id(updateItem.getId())
                .addTime(updateItem.getAddTime())
                .build();
        stockStorageItemDAO.updateStockStorageItemAddTime(updateItemDO);
    }

    @Override
    public List<StockStorageItemDetailBatchValueObject> selectStockStorageBatchByTaskNoAndType(List<String> taskNos, Integer type, String sku) {
        ExceptionUtil.checkAndThrow(type != null, "type不能为空");
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(taskNos), "taskNos不能为空");
        return stockStorageItemDAO.selectStockStorageItemDetailBatchByTaskNoAndType(taskNos, type, sku);
    }

    @Override
    public List<StockStorageItemActualQuantityValueObject> queryItemActualQuantityByStockStorageIdAndSku(List<Long> stockStorageIdList, List<String> skuList) {
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(stockStorageIdList), "stockStorageIdList不能为空");
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(skuList), "skuList不能为空");
        return stockStorageItemDAO.queryItemActualQuantityByStockStorageIdAndSku(stockStorageIdList, skuList);
    }

    /**
     * 批量查询条目信息id
     *
     * @param stockTaskStorageIds 出库任务id
     * @param sku                 sku
     * @return 返回入库任务条目
     */
    @Override
    public List<StockStorageItem> batchQueryStockStorageItemByIdAndSku(List<Long> stockTaskStorageIds, String sku) {
        if (CollectionUtils.isEmpty(stockTaskStorageIds) || StringUtils.isBlank(sku)) {
            return Lists.newArrayList();
        }
        List<StockStorageItemDO> stockStorageItemDOList = stockStorageItemDAO.queryItemByStockStorageIdAndSku(stockTaskStorageIds, sku);
        if (CollectionUtils.isEmpty(stockStorageItemDOList)) {
            return Lists.newArrayList();
        }
        return stockStorageItemDOList.stream().map(StockStorageItemConverter.INSTANCE::converter).collect(Collectors.toList());
    }

    /**
     * 移除对应的明细信息
     *
     * @param id       明细id
     * @param quantity 数量
     * @return 返回移除的行数内容
     */
    @Override
    public int removeItemByIdAndQuantity(Long id, Integer quantity) {
        if (Objects.isNull(id) || Objects.isNull(quantity)) {
            return 0;
        }
        return stockStorageItemDAO.removeItemByIdAndQuantity(id, quantity);
    }

    /**
     * 查询入库任务对应的条目数量
     *
     * @param stockStorageIdList 入库任务id列表
     * @return 返回入库任务对应的条目数量列表
     */
    @Override
    public List<StockStorageItemCount> countItemByStockStorageIdList(List<Long> stockStorageIdList) {
        if (CollectionUtils.isEmpty(stockStorageIdList)) {
            return Lists.newArrayList();
        }
        // 查询
        List<StockStorageItemCountDO> storageItemCountDOList
                = stockStorageItemDAO.countItemByStockStorageIdList(stockStorageIdList);
        if (CollectionUtils.isEmpty(stockStorageIdList)) {
            return Lists.newArrayList();
        }
        return storageItemCountDOList.stream().map(it -> {
            return StockStorageItemCount.builder()
                    .stockStorageId(it.getStockStorageId())
                    .itemNum(it.getItemNum())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 更新对应的明细信息
     *
     * @param itemId      明细id
     * @param quantity    数量
     * @param oldQuantity 旧数量
     * @return 返回修改的行数内容
     */
    @Override
    public int updateQuantityByIdAndQuantity(Long itemId, Integer quantity, Integer oldQuantity) {
        if (Objects.isNull(itemId) || Objects.isNull(quantity) || Objects.isNull(oldQuantity)) {
            return 0;
        }
        // 修改数量为空
        if (quantity.equals(oldQuantity)) {
            return 0;
        }
        return stockStorageItemDAO.updateQuantityByIdAndQuantity(itemId, quantity, oldQuantity);
    }

    /**
     * 查询应收数量
     * @param stockTaskStorageId 入库任务id
     * @return 返回应入库数量
     */
    @Override
    public int sumQuantityByStockStorageId(Long stockTaskStorageId) {
        if (stockTaskStorageId == null) {
            return 0;
        }
        Integer sum = stockStorageItemDAO.sumQuantityByStockStorageId(stockTaskStorageId);
        if (sum == null) {
            return 0;
        }
        return sum;
    }

    @Override
    public BigDecimal countCapacity(List<Integer> stockTaskIdList) {
        if (CollectionUtils.isEmpty(stockTaskIdList)) {
            return null;
        }
        return stockStorageItemDAO.countCapacity(stockTaskIdList);
    }
}
