package net.summerfarm.wms.api.base;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

@Data
public class PageRes<T> implements Iterable<T>, Serializable {

    private static final long serialVersionUID = 2098774814225164415L;

    private List<T> content = Collections.emptyList();

    private long pageSize;

    private long pageNum;

    private long total = 0L;

    public PageRes() {
    }

    public PageRes(List<T> content, long pageSize, long pageNum, long total) {
        this.content = content == null ? Collections.emptyList() : content;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
    }

    @Override
    public Iterator<T> iterator() {
        return content.iterator();
    }
}
