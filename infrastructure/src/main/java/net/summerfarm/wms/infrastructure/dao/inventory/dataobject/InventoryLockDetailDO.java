package net.summerfarm.wms.infrastructure.dao.inventory.dataobject;

import java.util.Date;
import java.io.Serializable;

/**
 * 库存锁定明细表(InventoryLockDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-04-04 18:16:36
 */
public class InventoryLockDetailDO implements Serializable {
    private static final long serialVersionUID = -69593446569238149L;
    /**
     * primary key
     */
    private Long id;
    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
    /**
     * 库存仓编号
     */
    private Long warehouseNo;
    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    private Long warehouseTenantId;
    /**
     * sku编码
     */
    private String skuCode;
    /**
     * 业务单号
     */
    private String orderNo;
    /**
     * 库存变动类型
     */
    private String orderTypeName;
    /**
     * 锁库数量
     */
    private Integer lockQuantity;
    /**
     * 剩余数量
     */
    private Integer remainQuantity;
    /**
     * 释放数量
     */
    private Integer releaseQuantity;
    /**
     * 扣减数量
     */
    private Integer reduceQuantity;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;

    /**
     * 业务子单号
     */
    private String orderSubNo;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getWarehouseNo() {
        return warehouseNo;
    }

    public void setWarehouseNo(Long warehouseNo) {
        this.warehouseNo = warehouseNo;
    }

    public Long getWarehouseTenantId() {
        return warehouseTenantId;
    }

    public void setWarehouseTenantId(Long warehouseTenantId) {
        this.warehouseTenantId = warehouseTenantId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public Integer getLockQuantity() {
        return lockQuantity;
    }

    public void setLockQuantity(Integer lockQuantity) {
        this.lockQuantity = lockQuantity;
    }

    public Integer getRemainQuantity() {
        return remainQuantity;
    }

    public void setRemainQuantity(Integer remainQuantity) {
        this.remainQuantity = remainQuantity;
    }

    public Integer getReleaseQuantity() {
        return releaseQuantity;
    }

    public void setReleaseQuantity(Integer releaseQuantity) {
        this.releaseQuantity = releaseQuantity;
    }

    public Integer getReduceQuantity() {
        return reduceQuantity;
    }

    public void setReduceQuantity(Integer reduceQuantity) {
        this.reduceQuantity = reduceQuantity;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getOrderSubNo() {
        return orderSubNo;
    }

    public void setOrderSubNo(String orderSubNo) {
        this.orderSubNo = orderSubNo;
    }
}

