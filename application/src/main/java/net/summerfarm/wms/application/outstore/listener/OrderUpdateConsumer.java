package net.summerfarm.wms.application.outstore.listener;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.manage.event.OrderUpdateEvent;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.order.OrderRepository;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Component
@MqListener(topic = Global.MQ_TOPIC,
        tag = WmsConstant.ORDER_UPDATE_TAG,
        consumerGroup = WmsConstant.MQ_WMS_GID, maxReconsumeTimes = 6)
public class OrderUpdateConsumer extends AbstractMqListener<OrderUpdateEvent> {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void process(OrderUpdateEvent orderUpdateEvent) {
        if (CollectionUtils.isEmpty(orderUpdateEvent.getOrders())) {
            return;
        }
        applicationContext.getBean(OrderUpdateConsumer.class).updateOrders(orderUpdateEvent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrders(OrderUpdateEvent orderUpdateEvent) {
        orderRepository.batchUpdateByOrderNoList(orderUpdateEvent.getOrders(), 1);
    }
}
