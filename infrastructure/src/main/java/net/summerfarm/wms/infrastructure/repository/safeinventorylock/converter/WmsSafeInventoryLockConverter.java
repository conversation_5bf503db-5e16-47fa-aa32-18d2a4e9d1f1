package net.summerfarm.wms.infrastructure.repository.safeinventorylock.converter;

import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;
import net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 安全库存锁定表转换器
 * <AUTHOR>
 * @date 2025-07-30
 */
public class WmsSafeInventoryLockConverter {

    /**
     * DataObject转Entity
     */
    public static WmsSafeInventoryLockEntity toWmsSafeInventoryLockEntity(WmsSafeInventoryLock wmsSafeInventoryLock) {
        if (wmsSafeInventoryLock == null) {
            return null;
        }
        WmsSafeInventoryLockEntity entity = new WmsSafeInventoryLockEntity();
        entity.setId(wmsSafeInventoryLock.getId());
        entity.setCreateTime(wmsSafeInventoryLock.getCreateTime());
        entity.setUpdateTime(wmsSafeInventoryLock.getUpdateTime());
        entity.setWarehouseNo(wmsSafeInventoryLock.getWarehouseNo());
        entity.setSku(wmsSafeInventoryLock.getSku());
        entity.setBatchNo(wmsSafeInventoryLock.getBatchNo());
        entity.setProduceDate(wmsSafeInventoryLock.getProduceDate());
        entity.setQualityDate(wmsSafeInventoryLock.getQualityDate());
        entity.setCabinetCode(wmsSafeInventoryLock.getCabinetCode());
        entity.setLockNo(wmsSafeInventoryLock.getLockNo());
        entity.setInitQuantity(wmsSafeInventoryLock.getInitQuantity());
        entity.setLockQuantity(wmsSafeInventoryLock.getLockQuantity());
        entity.setLockType(wmsSafeInventoryLock.getLockType());
        entity.setLockStatus(wmsSafeInventoryLock.getLockStatus());
        entity.setLockReason(wmsSafeInventoryLock.getLockReason());
        entity.setCreateOperator(wmsSafeInventoryLock.getCreateOperator());
        entity.setUpdateOperator(wmsSafeInventoryLock.getUpdateOperator());
        entity.setTenantId(wmsSafeInventoryLock.getTenantId());
        return entity;
    }

    /**
     * DataObject列表转Entity列表
     */
    public static List<WmsSafeInventoryLockEntity> toWmsSafeInventoryLockEntityList(List<WmsSafeInventoryLock> wmsSafeInventoryLockList) {
        if (wmsSafeInventoryLockList == null || wmsSafeInventoryLockList.isEmpty()) {
            return Collections.emptyList();
        }
        List<WmsSafeInventoryLockEntity> entityList = new ArrayList<>();
        for (WmsSafeInventoryLock wmsSafeInventoryLock : wmsSafeInventoryLockList) {
            entityList.add(toWmsSafeInventoryLockEntity(wmsSafeInventoryLock));
        }
        return entityList;
    }

    /**
     * CommandParam转DataObject
     */
    public static WmsSafeInventoryLock toWmsSafeInventoryLock(WmsSafeInventoryLockCommandParam param) {
        if (param == null) {
            return null;
        }
        WmsSafeInventoryLock wmsSafeInventoryLock = new WmsSafeInventoryLock();
        wmsSafeInventoryLock.setId(param.getId());
        wmsSafeInventoryLock.setCreateTime(param.getCreateTime());
        wmsSafeInventoryLock.setUpdateTime(param.getUpdateTime());
        wmsSafeInventoryLock.setWarehouseNo(param.getWarehouseNo());
        wmsSafeInventoryLock.setSku(param.getSku());
        wmsSafeInventoryLock.setBatchNo(param.getBatchNo());
        wmsSafeInventoryLock.setProduceDate(param.getProduceDate());
        wmsSafeInventoryLock.setQualityDate(param.getQualityDate());
        wmsSafeInventoryLock.setCabinetCode(param.getCabinetCode());
        wmsSafeInventoryLock.setLockNo(param.getLockNo());
        wmsSafeInventoryLock.setInitQuantity(param.getInitQuantity());
        wmsSafeInventoryLock.setLockQuantity(param.getLockQuantity());
        wmsSafeInventoryLock.setLockType(param.getLockType());
        wmsSafeInventoryLock.setLockStatus(param.getLockStatus());
        wmsSafeInventoryLock.setLockReason(param.getLockReason());
        wmsSafeInventoryLock.setCreateOperator(param.getCreateOperator());
        wmsSafeInventoryLock.setUpdateOperator(param.getUpdateOperator());
        wmsSafeInventoryLock.setTenantId(param.getTenantId());
        return wmsSafeInventoryLock;
    }
}
