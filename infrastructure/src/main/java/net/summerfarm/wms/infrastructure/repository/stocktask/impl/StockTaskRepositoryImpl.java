package net.summerfarm.wms.infrastructure.repository.stocktask.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.common.enums.TaskPanelQueryTypeEnum;
import net.summerfarm.wms.domain.StoreRecord.enums.StoreRecordType;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.purchase.domainobject.AllocationOrderItem;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.*;
import net.summerfarm.wms.infrastructure.dao.instore.*;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.*;
import net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskDAO;
import net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskItemCabinetOccupyDAO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.WmsStockTaskItemCabinetOccupyDO;
import net.summerfarm.wms.infrastructure.repository.stocktask.converter.AllocationOrderItemPeriodConvert;
import net.summerfarm.wms.infrastructure.repository.stocktask.converter.StockTaskConvert;
import net.summerfarm.wms.infrastructure.repository.stocktask.converter.StockTaskItemCabinetOccupyConvert;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/28  13:28
 */
@Slf4j
@Repository
public class StockTaskRepositoryImpl implements StockTaskRepository {

    @Resource
    private StockTaskDAO stockTaskDAO;

    @Resource
    private StockTaskProcessDAO stockTaskProcessDAO;

    @Resource
    private StockTaskProcessDetailDAO stockTaskProcessDetailDAO;

    @Resource
    private StockTaskProcessExpandDAO stockTaskProcessExpandDAO;

    @Resource
    private StockTaskItemDAO stockTaskItemDAO;

    @Resource
    private StockTaskItemDetailDAO stockTaskItemDetailDAO;
    @Resource
    private StockTaskItemCabinetOccupyDAO stockTaskItemCabinetOccupyDAO;


    @Override
    public void updateState(StockTask stockTask) {
        if (Objects.isNull(stockTask.getId())) {
            return;
        }
        StockTaskDO updateStockTask = StockTaskDO.builder().id(stockTask.getId())
                .processState(stockTask.getProcessState())
                .updatetime(LocalDateTime.now())
                .state(stockTask.getState()).build();
        stockTaskDAO.update(updateStockTask);

    }

    @Override
    public void updateState(Long id, Integer state) {
        if (null == id || null == state) {
            return;
        }
        stockTaskDAO.updateState(id, state);
    }

    @Override
    public void updateStateNotUpdateTime(StockTask stockTask) {
        if (Objects.isNull(stockTask.getId())) {
            return;
        }
        StockTaskDO updateStockTask = StockTaskDO.builder().id(stockTask.getId())
                .processState(stockTask.getProcessState())
                .state(stockTask.getState()).build();
        stockTaskDAO.update(updateStockTask);
    }

    @Override
    public void saveProcessExt(StockTaskProcessExpand stockTaskProcessExpand) {
        stockTaskProcessExpandDAO.insert(StockTaskProcessExpandDO.builder()
                .isComplete(stockTaskProcessExpand.getIsComplete())
                .remark(stockTaskProcessExpand.getRemark())
                .stockTaskProcessDetailId(stockTaskProcessExpand.getStockTaskProcessDetailId())
                .build());
    }

    @Override
    public Long findStockTaskProcessId(Long inBoundOrderId) {
        StockTaskProcessDO stockTaskProcessDO = stockTaskProcessDAO.selectByInBoundId(inBoundOrderId);
        if (Objects.isNull(stockTaskProcessDO)) {
            return 0L;
        }
        return stockTaskProcessDO.getId();
    }

    @Override
    public StockTaskProcess findProcess(Long inBoundOrderId) {
        StockTaskProcessDO stockTaskProcessDO = stockTaskProcessDAO.selectByInBoundId(inBoundOrderId);
        return StockTaskConvert.INSTANCE.convert(stockTaskProcessDO);
    }

    @Override
    public List<StockTaskProcess> findTaskProcess(Long taskId, String sku, String purchaseNo, LocalDate produceAt) {
        List<StockTaskProcessDO> stockTaskProcessDOS = stockTaskProcessDAO.selectByTaskIdAndSkuAndPurchaseNo(taskId, sku, purchaseNo, produceAt);
        return stockTaskProcessDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public StockTask findOutStockTask(String listNo) {
        StockTaskDO stockTaskDO = stockTaskDAO.selectOne(listNo, StoreRecordType.STORE_ALLOCATION_OUT.getId());
        return StockTaskConvert.INSTANCE.convert(stockTaskDO);
    }

    @Override
    public StockTask findOutStockTask(Long id) {
        StockTaskDO stockTaskDO = stockTaskDAO.selectById(id);
        return StockTaskConvert.INSTANCE.convert(stockTaskDO);
    }

    @Override
    public List<StockTask> findOutStockTaskList(List<Long> ids) {
        List<StockTaskDO> stockTaskDO = stockTaskDAO.selectByIds(ids);
        return StockTaskConvert.INSTANCE.convertList(stockTaskDO);
    }

    @Override
    public List<StockTaskProcessDetail> findProcessDetail(Long processId) {
        List<StockTaskProcessDetailDO> stockTaskProcessDetailDOS = stockTaskProcessDetailDAO.listByProcessId(processId);
        return stockTaskProcessDetailDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public Long saveStockTask(StockTask stockTask) {
        StockTaskDO insert = StockTaskDO.builder().areaNo(stockTask.getAreaNo())
                .type(stockTask.getType())
                .state(0)
                .adminId(stockTask.getAdminId())
                .tenantId(stockTask.getTenantId())
                .addtime(LocalDateTime.now())
                .systemSource(stockTask.getSystemSource())
                .taskNo(stockTask.getTaskNo())
                .expectTime(stockTask.getExpectTime())
                .build();
        stockTaskDAO.insert(insert);
        return insert.getId();
    }

    @Override
    public Long insertStockTask(StockTask stockTask) {
        StockTaskDO stockTaskDO = StockTaskConvert.INSTANCE.convert2StockTaskDO(stockTask);
        stockTaskDAO.insert(stockTaskDO);
        return stockTaskDO.getId();
    }


    @Override
    public List<StockTaskItem> queryStockTaskItem(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<>();
        }
        List<StockTaskItemDO> stockTaskItemDOS = stockTaskItemDAO.selectByStockTaskId(taskId);
        return stockTaskItemDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<StockTaskItem> queryStockTaskItem(List<Integer> taskIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(taskIds)) {
            return Lists.newArrayList();
        }
        List<StockTaskItemDO> stockTaskItemDOS = stockTaskItemDAO.selectByStockTaskIds(taskIds);
        return stockTaskItemDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<StockTaskItem> queryStockTaskItem(Integer taskId, List<String> skus) {
        if (taskId == null) {
            return new ArrayList<>();
        }
        List<StockTaskItemDO> stockTaskItemDOS = stockTaskItemDAO.selectByStockTaskIdAndSkus(taskId, skus);
        return stockTaskItemDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public Integer queryStockTaskItemQuantity(Integer stockTaskId, String sku) {
        if (stockTaskId == null || StringUtils.isEmpty(sku)) {
            return 0;
        }

        return stockTaskItemDAO.queryStockTaskItemQuantity(stockTaskId, sku);
    }

    @Override
    public List<StockTaskItemDetail> queryStockTaskItemDetail(List<Integer> itemTaskId) {
        if (CollectionUtils.isEmpty(itemTaskId)) {
            return new ArrayList<>();
        }
        List<StockTaskItemDetailDO> stockTaskItemDetailDOS = stockTaskItemDetailDAO.selectByItemId(itemTaskId);
        return stockTaskItemDetailDOS.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());

    }

    @Override
    public StockTaskItemDetail queryDetailByTaskIdAndSku(Integer taskId, String sku, LocalDate produceDate, LocalDate qualityDate) {
        StockTaskItemDO stockTaskItemDO = stockTaskItemDAO.selectByStockTaskIdAndSku(taskId, sku);
        StockTaskItemDetailDO stockTaskItemDetailDO = stockTaskItemDetailDAO.selectByItemIdAndSku(stockTaskItemDO.getId(), sku, produceDate, qualityDate);
        return StockTaskConvert.INSTANCE.convert(stockTaskItemDetailDO);
    }

    @Override
    public Integer queryWaitOutNumByType(Long warehouseNo, String sku, LocalDate expectTime) {
        if (Objects.isNull(warehouseNo) || StringUtils.isEmpty(sku)) {
            throw new BizException("仓库号和sku不可为空");
        }
        Integer num = stockTaskItemDAO.queryWaitOutNumByType(warehouseNo, sku, OperationType.outType, expectTime);
        return num == null ? 0 : num;
    }

    @Override
    public Map<String, Integer> mapWaitOutNumByType(Long warehouseNo, List<String> skus, LocalDate expectTime) {
        if (Objects.isNull(warehouseNo) || CollectionUtils.isEmpty(skus)) {
            throw new BizException("仓库号和sku不可为空");
        }
        List<UnOutStoreNumDO> unOutStoreNumDOS = stockTaskItemDAO.listWaitOutNumByType(warehouseNo, skus, OperationType.outType, expectTime);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(unOutStoreNumDOS)) {
            return Maps.newHashMap();
        }
        return unOutStoreNumDOS.stream()
                .collect(Collectors.toMap(UnOutStoreNumDO::getSku, UnOutStoreNumDO::getNum, (o1, o2) -> o1));
    }

    @Override
    public List<AllocationOrderItem> queryStockTaskItemByAllocate(String listNo) {
        if (StringUtils.isEmpty(listNo)) {
            return new ArrayList<>();
        }
        List<AllocationOrderItemDO> stockTaskItemDO = stockTaskItemDAO.queryStockTaskItemByAllocate(listNo);
        if (CollectionUtils.isEmpty(stockTaskItemDO)) {
            return new ArrayList<>();
        }
        List<Integer> itemIdList = stockTaskItemDO.stream()
                .map(AllocationOrderItemDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<AllocationOrderItemPeriodDO> stockTaskItemDetailDOS = stockTaskItemDetailDAO.queryStockTaskItemPeriodAllNoDetail(itemIdList);
        Map<Integer, List<AllocationOrderItemPeriodDO>> itemDetailMap = stockTaskItemDetailDOS.stream()
                .collect(Collectors.groupingBy(AllocationOrderItemPeriodDO::getStockAllocationItemId));

        List<AllocationOrderItem> result = AllocationOrderItemPeriodConvert.INSTANCE.convertList(stockTaskItemDO);

        for (AllocationOrderItem allocationOrderItemDO : result) {
            List<AllocationOrderItemPeriodDO> list = itemDetailMap.get(allocationOrderItemDO.getId());
            if (!CollectionUtils.isEmpty(list)) {
                allocationOrderItemDO.setItemPeriodList(
                        AllocationOrderItemPeriodConvert.INSTANCE.convertList1(list)
                );
            }
        }

        return result;
    }

    @Override
    public Integer queryStockTaskItemQuantityByAllocate(String listNo, String sku) {
        if (StringUtils.isEmpty(listNo) || StringUtils.isEmpty(sku)) {
            return 0;
        }

        return stockTaskItemDAO.queryStockTaskItemQuantityByAllocate(listNo, sku);
    }

    @Override
    public Integer createStockTaskItem(StockTaskItem stockTaskItem) {
        StockTaskItemDO stockTaskItemDO = StockTaskConvert.INSTANCE.convert2DO(stockTaskItem);
        stockTaskItemDAO.insert(stockTaskItemDO);
        return stockTaskItemDO.getId();
    }

    @Override
    public List<StockTask> listByWareNoAndInventoryLocked500(Integer warehouseNo,
                                                             LocalDate deliveryDateStart,
                                                             LocalDate deliveryDateEnd,
                                                             LocalDate addTimeStart,
                                                             List<Integer> typeList) {
        if (warehouseNo == null || CollectionUtils.isEmpty(typeList)) {
            return new ArrayList<>();
        }

        List<StockTaskDO> stockTaskDOList = stockTaskDAO.listByWareNoAndInventoryLocked500(
                warehouseNo, typeList, deliveryDateStart, deliveryDateEnd, addTimeStart);
        return stockTaskDOList.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public int createStockTaskItemCabinetOccupy(StockTaskItemCabinetOccupy stockTaskItemCabinetOccupy) {
        WmsStockTaskItemCabinetOccupyDO cabinetOccupyDO = StockTaskItemCabinetOccupyConvert.INSTANCE.convert2DO(stockTaskItemCabinetOccupy);
        return stockTaskItemCabinetOccupyDAO.insert(cabinetOccupyDO);
    }

    @Override
    public StockTask queryStockTaskById(Integer stockTaskId) {
        StockTaskDO stockTaskDO = stockTaskDAO.selectByPrimaryKey(stockTaskId);
        return StockTaskConvert.INSTANCE.convert(stockTaskDO);
    }

    @Override
    public Long countPartFinishedTask(Integer warehouseNo) {
        return stockTaskDAO.countPartFinishedTask(warehouseNo);
    }

    @Override
    public void updateInventoryLocked(Integer stockTaskId, Integer inventoryLocked) {
        Integer count = stockTaskDAO.updateInventoryLocked(stockTaskId, inventoryLocked);
        if (count <= 0) {
            log.warn("锁库异常，发生并发，出库任务id：{}", stockTaskId);
//            throw new BizException("锁库异常，发生并发请重试");
        }
    }

    @Override
    public StockTaskItem queryByTaskIdAndSku(String sku, Integer taskId) {
        StockTaskItemDO stockTaskItemDO = stockTaskItemDAO.selectByStockTaskIdAndSku(taskId, sku);
        return StockTaskConvert.INSTANCE.convert(stockTaskItemDO);
    }

    @Override
    public void updateExpectTime(StockTask stockTask) {
        if (Objects.isNull(stockTask.getId())) {
            return;
        }
        StockTaskDO updateStockTask = StockTaskDO.builder()
                .id(stockTask.getId())
                .expectTime(stockTask.getExpectTime())
                .updater(stockTask.getUpdater())
                // .updatetime(LocalDateTime.now())
                .build();
        stockTaskDAO.updateExpectTime(updateStockTask);
    }

    @Override
    public void updateExternalWarehouseNo(StockTask stockTask) {
        if (Objects.isNull(stockTask.getId())) {
            return;
        }
        StockTaskDO updateStockTask = StockTaskDO.builder()
                .id(stockTask.getId())
                .externalWarehouseNo(stockTask.getExternalWarehouseNo())
                .build();
        stockTaskDAO.updateExternalWarehouseNo(updateStockTask);
    }

    @Override
    public StockTask selectByTaskNo(String outOrderNo, Integer type) {
        StockTaskDO stockTaskDO = stockTaskDAO.selectOneByTaskNo(outOrderNo, type);
        return StockTaskConvert.INSTANCE.convert(stockTaskDO);
    }

    @Override
    public List<StockTask> selectListByTaskNo(List<String> taskNoList, Integer type) {
        if (CollectionUtils.isEmpty(taskNoList) || Objects.isNull(type)) {
            return Lists.newArrayList();
        }
        List<StockTaskDO> stockTaskDOList = stockTaskDAO.selectListByTaskNo(taskNoList, type);
        return stockTaskDOList.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public StockTask selectByWarehouseNoAndStoreNo(Long warehouseNo, Long storeNo) {
        if (warehouseNo == null || storeNo == null) {
            throw new BizException("仓库号和城配仓不可为空");
        }
        StockTaskDO stockTaskDO = stockTaskDAO.selectByWarehouseNoAndStoreNo(warehouseNo, storeNo);
        return StockTaskConvert.INSTANCE.convert(stockTaskDO);
    }

    @Override
    public Boolean hasUnFinishTask(Integer warehouseNo, List<String> skuList) {
        if (warehouseNo == null || CollectionUtils.isEmpty(skuList)) {
            return false;
        }

        Long count = stockTaskDAO.countUnFinishTask(warehouseNo, skuList);
        return count > 0;
    }

    @Override
    public void cancel(StockTask stockTask) {
        if (Objects.isNull(stockTask.getId())) {
            return;
        }
        StockTaskDO updateStockTask = StockTaskDO.builder()
                .id(stockTask.getId())
                .taskNo(stockTask.getTaskNo())
                .processState(stockTask.getProcessState())
                .updatetime(LocalDateTime.now())
                .state(stockTask.getState()).build();
        stockTaskDAO.update(updateStockTask);
    }

    @Override
    public List<StockTask> selectExpectTask(LocalDateTime expectStartTime, LocalDateTime expectEndTime, Integer areaNo) {
        if (Objects.isNull(expectStartTime) || Objects.isNull(expectEndTime)
                || Objects.isNull(areaNo)) {
            return Lists.newArrayList();
        }
        List<StockTaskDO> stockTaskDOList = stockTaskDAO.selectExpectTask(expectStartTime, expectEndTime, areaNo);
        return stockTaskDOList.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public BigDecimal countCapacity(List<Integer> stockTaskIdList) {
        if (CollectionUtils.isEmpty(stockTaskIdList)) {
            return null;
        }
        return stockTaskItemDAO.countCapacity(stockTaskIdList);
    }

    /**
     * 统计越库出库任务数量
     *
     * @param type        任务类型 必填
     * @param warehouseNo 仓库编号 必填
     * @param expectTime  期望出库时间 必填
     * @param storeNo     城配仓
     * @return 返回越库出库任务数量
     */
    @Override
    public Long countCrossTaskByType(Integer type, Integer warehouseNo, Integer storeNo, LocalDateTime expectTime) {
        if (type == null || warehouseNo == null || expectTime == null) {
            return 0L;
        }
        return stockTaskDAO.countCrossTaskByType(type, warehouseNo, storeNo, expectTime);
    }

    @Override
    public List<StockTask> queryList(Integer type, LocalDate expectTime, Integer state, Integer outboundCategory) {
        if(type == null || expectTime == null || state == null || outboundCategory == null){
            throw new BizException("查询条件不能为空");
        }
        List<StockTaskDO> stockTaskDOList = stockTaskDAO.queryByTypeAndExpectTimeAndState(type, expectTime, state, outboundCategory);
        return stockTaskDOList.stream().map(StockTaskConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, List<Integer> stateList, List<Integer> typeList) {
        if (warehouseNo == null || createTimeStart == null || createTimeEnd == null || CollectionUtils.isEmpty(stateList) || CollectionUtils.isEmpty(typeList)) {
            throw new ParamsException("查询条件不能为空");
        }
        return stockTaskDAO.queryTaskPanelQuantity(warehouseNo, stateList, typeList, createTimeStart, createTimeEnd);
    }

    @Override
    public PageInfo<String> pagePanelWindowData(Integer taskPanelQueryTypeEnum,
                                                Integer pageNum,
                                                Integer pageSize,
                                                Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, List<Integer> stateList, List<Integer> typeList) {
        if (taskPanelQueryTypeEnum == null || warehouseNo == null || createTimeStart == null || createTimeEnd == null || CollectionUtils.isEmpty(stateList) || CollectionUtils.isEmpty(typeList)) {
            throw new ParamsException("查询条件不能为空");
        }
        if (TaskPanelQueryTypeEnum.OUTBOUND_TASK.getCode().equals(taskPanelQueryTypeEnum)) {
            return PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() ->
                    stockTaskDAO.queryTaskPanelNotFinishTask(warehouseNo, stateList, typeList, createTimeStart, createTimeEnd)
            );
        }
        if (TaskPanelQueryTypeEnum.OUTBOUND_TASK_NOT_IN_TIME.getCode().equals(taskPanelQueryTypeEnum)) {
            return PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() ->
                    stockTaskDAO.queryTaskPanelNotFinishInTimeTask(warehouseNo, stateList, typeList, createTimeStart, createTimeEnd)
            );
        }
        return null;
    }

    @Override
    public Integer updateCustomerSkuCode(String customerSkuCode, Long id) {
        if (StringUtils.isEmpty(customerSkuCode) || id == null) {
            return 0;
        }
        return stockTaskItemDAO.updateCustomerSkuCode(customerSkuCode, id);
    }

    @Override
    public List<Long> queryOutTaskNeedNoticeKingdee(Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
        if (null == warehouseNo) {
            return Lists.newArrayList();
        }
        return stockTaskDAO.queryOutTaskNeedNoticeKingdee(warehouseNo, createTimeStart, createTimeEnd);
    }

    @Override
    public List<Long> queryPurchaseOutTaskNeedNoticeKingdee(Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
        if (null == warehouseNo) {
            return Lists.newArrayList();
        }
        return stockTaskDAO.queryPurchaseOutTaskNeedNoticeKingdee(warehouseNo, createTimeStart, createTimeEnd);
    }

}
