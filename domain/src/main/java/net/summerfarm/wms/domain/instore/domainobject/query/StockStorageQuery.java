package net.summerfarm.wms.domain.instore.domainobject.query;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wms.common.constant.WmsConstant;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/17  17:35
 */
@Data
@Builder
public class StockStorageQuery {


    /**
     * 任务id
     */
    private Long id;
    /**
     *
     */
    private String sourceId;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;
    /**
     * 入库仓编号
     */
    private Integer inWarehouseNo;

    /**
     * 出库仓信息
     */
    private Integer outWarehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplier;

    /**
     * 状态列表
     */
    private List<Integer> states;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 入库任务类型
     */
    private Integer type;

    /**
     * 入库任务id
     */
    private List<Long> ids;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 页数
     */
    private Integer pageNum;


    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 进度
     */
    private Integer processState;

    /**
     * 进度集合
     */
    private List<Integer> processStates;

    /**
     * 预约时间
     */
    private String startExpectTime;
    /**
     * 预约时间
     */
    private String endExpectTime;

    private List<Integer> types;

    /**
     * 商品名称
     */
    private Long pdId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 归属人
     */
    private String merchantName;

    /**
     * 任务id
     */
    private Long stockTaskId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 当前租户的仓库号列表
     */
    private List<Integer> tenantWarehouseNoList;

    /**
     *  任务号/订单号
     */
    private String bizNo;

    /**
     * 货主
     */
    private String cargoOwner;

    /**
     * 系统来源 0-内部 1-外部
     */
    private Integer systemSource;

    /**
     * 关联单号
     */
    private String relationNo;

    /**
     * 扩展标志位
     */
    private Long optionFlag;

    /**
     * 采购模式 1: 代销不入仓越库采购 2: 备货采购单 3: 预提采购单 4: POP采购单 5：POP采购单T+2
     */
    private Integer purchaseMode;
    /**
     * 来源订单
     */
    private String sourceOrderNo;

    /**
     * 外部仓
     */
    private String externalWarehouseNo;

    public Long getTenantId() {
        if (tenantId == null) {
            return WmsConstant.XIANMU_TENANT_ID;
        }
        return tenantId;
    }

}
