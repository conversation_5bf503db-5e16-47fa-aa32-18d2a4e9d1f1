package net.summerfarm.wms.infrastructure.dao.inventory.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2023/4/15 11:21
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryRelationDO {

    private String sku;

    private Integer createType;

    private String pdName;

    private String realName;
    private String weight;
    private Integer type;
    private Long categoryId;
    private Integer categoryType;
    private String volume;
    private BigDecimal weightNum;
    private String nameRemakes;
    private Long pdId;
    private String pdNo;
    private String origin;
    private Integer isDomestic;
    private Integer baseSaleQuantity;
    private String baseSaleUnit;
    private String storageLocation;
    private Integer samplePool;
    private Integer extType;;
    private Integer qualityTime;
    private String qualityTimeUnit;
    private String skuPic;
    private String picturePath;
    private String categoryName;
    private String pack;
    private String unit;
    private Integer qualityTimeType;
    private Integer warnTime;

    /**
     * 仓库库存
     */
    private Integer quantity;

    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;

    /**
     * 锁定库存
     */
    private Integer lockQuantity;

    /**
     * 安全库存
     */
    private Integer safeQuantity;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * SKU二级类型
     */
    private Integer skuSubType;

}
