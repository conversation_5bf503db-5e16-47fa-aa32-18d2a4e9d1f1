package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *  入库单拓展实体类
 * <AUTHOR>
 * @date 2022/07/07
 */
@Data
@Deprecated
@Builder
@AllArgsConstructor
public class StockTaskProcessExpand {

  private Integer id;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;
  /**
   * 出入库单明细id
   */
  private Integer stockTaskProcessDetailId;
  /**
   * 供应商证件是否齐全0 不齐 1 齐
   */
  private Integer isComplete;

  private String remark;

}
