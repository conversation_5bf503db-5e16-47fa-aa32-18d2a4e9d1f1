package net.summerfarm.wms.infrastructure.dao.instore.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  15:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockStorageItemDetailDO {

    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 入库条目Id
     */
    private Long stockStorageItemId;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 实入数量
     * 数据库映射actual_in_quantity
     */
    private Integer shouldQuantity;

    /**
     * 应入数量
     * 数据库映射should_in_quantity
     */
    private Integer actualInQuantity;

    /**
     * 保质期 时间戳
     */
    private LocalDate qualityDate;

    /**
     * 生产期 时间戳
     */
    private LocalDate productionDate;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 收货容器
     */
    private String receivingContainer;

    /**
     * 是否上游指定效期
     * 0:指定,1:非指定
     */
    @Builder.Default
    private Integer specify = 0;
}

