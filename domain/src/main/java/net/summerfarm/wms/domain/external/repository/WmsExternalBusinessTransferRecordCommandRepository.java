package net.summerfarm.wms.domain.external.repository;

import net.summerfarm.wms.domain.external.entity.WmsExternalBusinessTransferRecordEntity;

/**
 * <AUTHOR>
 * @Date 2024/5/20 11:32
 * @Version 1.0
 */
public interface WmsExternalBusinessTransferRecordCommandRepository {
    /**
     * 保存下发状态记录
     *
     * @param recordEntity  仓库外部路由下发状态
     */
     void insert(WmsExternalBusinessTransferRecordEntity recordEntity);

    void deleteById(Long id);
}
