package net.summerfarm.wms.domain.qms.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-04-02 15:05:02
 * @version 1.0
 *
 */
@Data
public class QmsInspectionScaleEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 货品加工类型，1-成品，2-原料
	 * @see net.summerfarm.wms.domain.qms.enums.QmsInspectScaleProcessTypeEnum
	 */
	private Integer goodsProcessType;

	/**
	 * 预约/库存数最小值
	 */
	private Integer preQuantityGt;

	/**
	 * 预约/库存数最大值
	 */
	private Integer preQuantityLt;

	/**
	 * 货检量
	 */
	private Integer inspQuantity;

	/**
	 * 货检量范围规则类型, 1-全检,2-/、3-最多、4-最少、5-百分比
	 * @see net.summerfarm.wms.domain.qms.enums.QmsInspectScaleRuleTypeEnum
	 */
	private Integer inspType;

	/**
	 * 复检量
	 */
	private Integer reInspQuantity;

	/**
	 * 复检量范围规则类型, 1-全检,2-/、3-最多、4-最少、5-百分比
	 * @see net.summerfarm.wms.domain.qms.enums.QmsInspectScaleRuleTypeEnum
	 */
	private Integer reInspType;

	/**
	 * 破坏性货检量
	 */
	private Integer destoryQuantity;

	/**
	 * 破坏性货检量范围规则类型, 1-全检,2-/、3-最多、4-最少、5-百分比
	 * @see net.summerfarm.wms.domain.qms.enums.QmsInspectScaleRuleTypeEnum
	 */
	private Integer destoryType;

	/**
	 * 删除时间
	 */
	private Long deletedAt;

	

	
}