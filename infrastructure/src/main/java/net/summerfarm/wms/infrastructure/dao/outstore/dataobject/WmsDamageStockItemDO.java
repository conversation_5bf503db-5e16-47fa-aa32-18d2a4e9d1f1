package net.summerfarm.wms.infrastructure.dao.outstore.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/11/28  17:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsDamageStockItemDO {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 货损任务编号
     */
    private Long damageStockTaskId;

    /**
     * sku
     */
    private String sku;

    /**
     * 采购单号
     */
    private String listNo;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 实际数量
     */
    private Integer actualQuantity;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 货位编号
     */
    private String glNo;

    /**
     * 应出数量
     */
    private Integer shouldQuantity;

    /**
     * 出库数量
     */
    private Integer outStoreQuantity;

    /**
     * 货损类型
     */
    private String reasonType;

    /**
     * 货损凭证
     */
    private String reason;
    /**
     * 批次
     */
    private String batch;
}
