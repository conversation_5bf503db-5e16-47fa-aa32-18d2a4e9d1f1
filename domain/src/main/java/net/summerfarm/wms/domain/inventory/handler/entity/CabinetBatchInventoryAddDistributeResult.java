package net.summerfarm.wms.domain.inventory.handler.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 库位批次库存增加分配结果
 * @date 2023/6/5
 */
@Data
public class CabinetBatchInventoryAddDistributeResult implements Serializable {

    private static final long serialVersionUID = -4084997413372624892L;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 批次
     */
    private String skuBatchCode;

    /**
     * 增加库存
     */
    private Integer addQuantity;

}
