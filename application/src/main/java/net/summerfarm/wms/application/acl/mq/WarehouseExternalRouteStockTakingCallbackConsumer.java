package net.summerfarm.wms.application.acl.mq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.event.WarehouseExternalRouteStockOutCallbackEvent;
import net.summerfarm.wms.application.external.event.WarehouseExternalRouteStockTakingCallbackEvent;
import net.summerfarm.wms.application.external.factory.WarehouseExternalRouteCommandFactory;
import net.summerfarm.wms.application.external.input.WarehouseExternalRouteCommandInput;
import net.summerfarm.wms.application.external.service.WarehouseExternalRouteCommandService;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalRouteQueryRepository;
import net.summerfarm.wms.domain.stocktaking.domainobject.Stocktaking;
import net.summerfarm.wms.domain.stocktaking.repository.StocktakingRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCallbackReq;
import net.summerfarm.wms.openapi.stocktaking.xm.req.StockTakingCallbackReq;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.util.MessageExtUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 外部路由盘点回告消费
 * @date 2024/2/22
 */
@Slf4j
@Component
@MqListener(topic = Global.MQ_EXTERNAL_TOPIC,
        tag = WmsConstant.EXTERNAL_ROUTE_STOCK_TAKING_CALLBACK_INVOKE,
        consumerGroup = WmsConstant.MQ_WMS_EXTERNAL_GID,
        maxReconsumeTimesWarnLog = 5,
        maxReconsumeTimes = 5)
public class WarehouseExternalRouteStockTakingCallbackConsumer extends AbstractMqListener<WarehouseExternalRouteStockTakingCallbackEvent> {

    @Resource
    private WarehouseExternalRouteQueryRepository warehouseExternalRouteQueryRepository;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private StocktakingRepository stocktakingRepository;
    @Resource
    private WarehouseExternalRouteCommandFactory warehouseExternalRouteCommandFactory;
    @Resource
    private WarehouseExternalRouteCommandService warehouseExternalRouteCommandService;

    @Override
    public void process(WarehouseExternalRouteStockTakingCallbackEvent warehouseExternalRouteStockTakingCallbackEvent) {
        log.info("仓库外部对接盘点回告路由调用入参：{}", JSON.toJSONString(warehouseExternalRouteStockTakingCallbackEvent));
        try {
            AdminUtil.setDefaultUserBase();
            log.info("获取默认登陆信息，{}", JSON.toJSONString(AdminUtil.USER_BASE.get()));
            if (null == warehouseExternalRouteStockTakingCallbackEvent) {
                return;
            }
            if (null == warehouseExternalRouteStockTakingCallbackEvent.getWarehouseExternalRouteId() || null == warehouseExternalRouteStockTakingCallbackEvent.getStockTakingId()) {
                return;
            }
            redisUtil.mqOnceLockExecute(MessageExtUtil.getMessageExt().getMsgId(),
                    () -> {
                        if (StringUtils.isBlank(warehouseExternalRouteStockTakingCallbackEvent.getIdempotentNo())) {
                            doInvoke(warehouseExternalRouteStockTakingCallbackEvent);
                        } else {
                            String key = RedisKeys.buildWarehouseExternalRouteStockTakingCallbackConsumeExKey(warehouseExternalRouteStockTakingCallbackEvent.getIdempotentNo());
                            redisUtil.doLock(key, 5L, TimeUnit.MINUTES, 0L, () -> {
                                doInvoke(warehouseExternalRouteStockTakingCallbackEvent);
                            });
                        }
                    });
        } finally {
            AdminUtil.USER_BASE.remove();
            log.info("获取默认登陆清空信息，{}", JSON.toJSONString(AdminUtil.USER_BASE.get()));
        }
    }

    public void doInvoke(WarehouseExternalRouteStockTakingCallbackEvent warehouseExternalRouteStockTakingCallbackEvent) {
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findById(warehouseExternalRouteStockTakingCallbackEvent.getWarehouseExternalRouteId());
        if (null == warehouseExternalRoute) {
            return;
        }
        if (null == warehouseExternalRouteStockTakingCallbackEvent.getStockTakingId()) {
            return;
        }
        Stocktaking stocktaking = stocktakingRepository.getStocktaking(warehouseExternalRouteStockTakingCallbackEvent.getStockTakingId());
        if (null == stocktaking) {
            return;
        }
        if (StringUtils.isBlank(warehouseExternalRouteStockTakingCallbackEvent.getCallbackJson())) {
            return;
        }
        StockTakingCallbackReq stockTakingCallbackReq = JSON.parseObject(warehouseExternalRouteStockTakingCallbackEvent.getCallbackJson(), StockTakingCallbackReq.class);
        WarehouseExternalRouteCommandInput<StockTakingCallbackReq> warehouseExternalRouteCommandInput = warehouseExternalRouteCommandFactory.buildStockTakingCallbackCommand(stockTakingCallbackReq, warehouseExternalRoute);
        warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
        log.info("仓库外部对接盘点回告路由调用完成：{}", JSON.toJSONString(warehouseExternalRouteStockTakingCallbackEvent));
    }

}
