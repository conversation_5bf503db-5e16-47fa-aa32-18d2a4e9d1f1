package net.summerfarm.wms.facade.wnc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.facade.wnc.converter.WarehouseStorageConverter;
import net.summerfarm.wms.facade.wnc.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBaseInfoByNoReq;
import net.summerfarm.wnc.client.req.WarehouseStorageQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageCenterResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WarehouseStorageFacade {
    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;

    /**
     * 根据仓库号查询仓库信息
     *
     * @param warehouseNo
     * @return
     */
    public WarehouseStorageDTO queryWarehouseStorage(Integer warehouseNo) {
        if (warehouseNo == null) {
            return null;
        }
        WarehouseStorageQueryReq req = new WarehouseStorageQueryReq();
        req.setWarehouseNo(warehouseNo);
        DubboResponse<WarehouseStorageResp> dubboResponse = warehouseStorageQueryProvider.queryOneWarehouseStorage(req);
        ExceptionUtil.checkDubboResponse(dubboResponse);
        return WarehouseStorageConverter.convertToWarehouseStorageDTO(dubboResponse.getData());
    }

    /**
     * 获取仓库租户
     * @param warehouseNo
     * @return
     */
    public Long getWarehouseTenantId(Integer warehouseNo) {
        WarehouseStorageDTO warehouseStorageDTO = this.queryWarehouseStorage(warehouseNo);
        if (Objects.isNull(warehouseStorageDTO)) {
            return null;
        }
        return warehouseStorageDTO.getTenantId();
    }

    /**
     * 根据仓库号查询仓库名称
     *
     * @param warehouseNo 库存仓号
     * @return 库存仓名称
     */
    public String queryWarehouseStorageName(Integer warehouseNo) {
        if (warehouseNo == null) {
            return "";
        }
        try {
            WarehouseStorageDTO warehouseStorageDTO = this.queryWarehouseStorage(warehouseNo);
            return warehouseStorageDTO.getWarehouseName();
        } catch (Exception e) {
            log.error("获取库存仓名称异常 >>> {}", warehouseNo, e);
            return warehouseNo.toString();
        }
    }

    /**
     * 根据仓库号列表查询仓库信息
     *
     * @param warehouseNoList 仓库号列表
     * @return 仓库号与仓库名称的映射
     */
    public Map<Integer, String> queryWarehouseStorageList(List<Integer> warehouseNoList) {
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return null;
        }
        warehouseNoList.removeIf(Objects::isNull);
        warehouseNoList = warehouseNoList.stream().distinct().collect(Collectors.toList());
        WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq = new WarehouseBaseInfoByNoReq();
        warehouseBaseInfoByNoReq.setWarehouseNos(warehouseNoList);
        DubboResponse<List<WarehouseBaseInfoByNoResp>> dubboResponse = warehouseStorageQueryProvider.queryBaseInfoByWarehouseNo(warehouseBaseInfoByNoReq);
        ExceptionUtil.checkDubboResponse(dubboResponse);
        Map<Integer, String> warehouseNoNameMap = new HashMap<>();
        for (WarehouseBaseInfoByNoResp warehouseInfo : dubboResponse.getData()) {
            warehouseNoNameMap.put(warehouseInfo.getWarehouseNo(), warehouseInfo.getWarehouseName());
        }
        return warehouseNoNameMap;
    }

    /**
     * 查询所有的POP仓库
     * @return POP仓库号
     */
    public List<Integer> queryAllPopWarehouseStorage(){
        DubboResponse<List<WarehouseStorageCenterResp>> dubboResponse = warehouseStorageQueryProvider.queryAllPopWarehouseList();
        ExceptionUtil.checkDubboResponse(dubboResponse);
        List<WarehouseStorageCenterResp> warehouseStorageCenterResps = dubboResponse.getData();
        if(CollectionUtils.isEmpty(warehouseStorageCenterResps)){
            return Collections.emptyList();
        }

        return warehouseStorageCenterResps.stream()
                .map(WarehouseStorageCenterResp::getWarehouseNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
