package net.summerfarm.wms.domain.stockTransfer.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.Entity;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOp implements Entity {
    /**
     * 主键id
     */
    Long id;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 关联表id
     */
    Long stockTransferItemId;

    /**
     * 转换类型
     * 0-单一，1-混合
     */
    @Builder.Default
    Integer type = 0;

    /**
     * 转换比例
     */
    String transferRatio;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转入批次的生产日期
     */
    Long produceDate;

    /**
     * 转入批次的保质期
     */
    Long shelfLife;

    /**
     * 操作人
     */
    String operator;
}
