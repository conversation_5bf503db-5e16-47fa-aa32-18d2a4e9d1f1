package net.summerfarm.wms.manage.web.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.wms.manage.web.req.StockTaskReq;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
public interface StockTaskPickService {

    AjaxResult selectTaskPick(Integer pageIndex, Integer pageSize, StockTaskReq req);


    AjaxResult createDown(Integer storeNo, LocalDate deliveryTime, Integer type, String closeOrderTime, HttpServletResponse response);

    /**
     * 生成捡货任务
     */
    void createStockTaskPick(String closeTime);
}
