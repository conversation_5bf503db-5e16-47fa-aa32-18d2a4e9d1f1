package net.summerfarm.wms.application.inventory.biz.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.UnsafeUtil;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryWarnCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryWarnCommand;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryUnlockInput;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockCommandService;
import net.summerfarm.wms.domain.areaStore.QuantityChangeRecordRepository;
import net.summerfarm.wms.domain.areaStore.enums.OtherStockChangeTypeEnum;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.AreaStoreService;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.areaStore.domainobject.QuantityChangeRecord;
import net.summerfarm.wms.domain.areaStore.domainobject.StockChangeType;
import net.summerfarm.wms.domain.safeinventorylock.enums.SafeInventoryLockTypeEnum;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/8
 */
@Service
@Slf4j
public class SafeInventoryCommandServiceImpl implements SafeInventoryCommandService {

    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private SafeInventoryWarnCommandService safeInventoryWarnCommandService;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private QuantityChangeRecordRepository quantityChangeRecordRepository;
    @Resource
    private WmsSafeInventoryLockCommandService wmsSafeInventoryLockCommandService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void updateSafeInventory(SafeInventoryCommand safeInventoryCommand) {
        if (Objects.isNull(safeInventoryCommand.getSafeQuantity())) {
            throw new BizException("请填写安全库存数量！");
        }
        if (Objects.isNull(safeInventoryCommand.getSku()) || Objects.isNull(safeInventoryCommand.getWarehouseNo())) {
            throw new BizException("传参错误！");
        }
        if (StringUtils.isBlank(safeInventoryCommand.getRemark())) {
            throw new BizException("请备注修改原因！");
        }
        if (safeInventoryCommand.getRemark().length() > Global.LIMIT_QUANTITY) {
            throw new BizException("抱歉，备注内容超过300字咯！");
        }
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        String key = RedisKeys.buildSafeInventoryLockConsumeExKey(safeInventoryCommand.getWarehouseNo(), safeInventoryCommand.getSku());
        try {
            redisUtil.doLock(key, 5L, TimeUnit.MINUTES, 0L, () -> {
                doUpdateSafeInventory(safeInventoryCommand, OtherStockChangeTypeEnum.SAFE_STOCK_CHANGE, recordMap);
                quantityChangeRecordRepository.insertRecord(recordMap);
            });
        } catch (Exception e) {
            log.warn("安全库存锁定异常，{}", JSON.toJSONString(safeInventoryCommand), e);
            throw e;
        }
    }

    private void doUpdateSafeInventory(SafeInventoryCommand safeInventoryCommand, StockChangeType changeType, Map<String, QuantityChangeRecord> recordMap) {
        // 原始记录
        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(safeInventoryCommand.getWarehouseNo(), safeInventoryCommand.getSku());
        Integer safeQuantity = areaStore.getSafeQuantity();

        if (null == recordMap) {
            recordMap = new HashMap<>();
        }
        String operator = Boolean.TRUE.equals(safeInventoryCommand.getSystemExe()) ? "系统" : safeInventoryCommand.getOperator();
        areaStoreService.updateSafeInventory(safeInventoryCommand.getSafeQuantity(), safeInventoryCommand.getSku(), safeInventoryCommand.getWarehouseNo(), changeType, null, operator, recordMap, safeInventoryCommand.getRemark());
        QuantityChangeRecord record = recordMap.get(safeInventoryCommand.getSku() + ":" + safeInventoryCommand.getWarehouseNo());
        if (record == null) {
            record = new QuantityChangeRecord(safeInventoryCommand.getWarehouseNo(), safeInventoryCommand.getSku(), operator, changeType.getTypeName(), null, safeInventoryCommand.getRemark());
        }
        UnsafeUtil.unsafe.compareAndSwapObject(record, QuantityChangeRecord.oldSafeQuantityOffset, null, areaStore.getSafeQuantity());
        recordMap.put(safeInventoryCommand.getSku() + ":" + safeInventoryCommand.getWarehouseNo(), record);
        // 变动数量
        int changeQuantity = safeInventoryCommand.getSafeQuantity() - safeQuantity;
        // 锁定
        if (changeQuantity > 0) {
            // 调用lock
            WmsSafeInventoryLockInput lockInput = WmsSafeInventoryLockInput.builder()
                    .lockType(SafeInventoryLockTypeEnum.INVENTORY_LOCK.getCode())
                    .warehouseNo(safeInventoryCommand.getWarehouseNo().intValue())
                    .sku(safeInventoryCommand.getSku())
                    .lockQuantity(changeQuantity)
                    .lockReason(safeInventoryCommand.getRemark())
                    .tenantId(safeInventoryCommand.getTenantId())
                    .operator(safeInventoryCommand.getOperator())
                    .oldSafeInventoryUpdate(safeInventoryCommand.getOldSafeInventoryUpdate())
                    .build();
            wmsSafeInventoryLockCommandService.lockForOldSafeUpdate(lockInput);
            // 释放
        } else if (changeQuantity < 0) {
            // 调用unlock
            WmsSafeInventoryUnlockInput unlockInput = WmsSafeInventoryUnlockInput.builder()
                    .lockType(SafeInventoryLockTypeEnum.INVENTORY_LOCK.getCode())
                    .warehouseNo(safeInventoryCommand.getWarehouseNo().intValue())
                    .sku(safeInventoryCommand.getSku())
                    .unlockQuantity(-changeQuantity)
                    .tenantId(safeInventoryCommand.getTenantId())
                    .operator(safeInventoryCommand.getOperator())
                    .oldSafeInventoryUpdate(safeInventoryCommand.getOldSafeInventoryUpdate())
                    .build();
            wmsSafeInventoryLockCommandService.unlockForOldSafeUpdate(unlockInput);
        }
        // 预警记录
        try {
            // 锁定
            if (safeInventoryCommand.getSafeQuantity() > safeQuantity) {
                SafeInventoryWarnCommand safeInventoryWarnCommand = SafeInventoryWarnCommand.builder()
                        .warehouseNo(safeInventoryCommand.getWarehouseNo().intValue())
                        .sku(safeInventoryCommand.getSku())
                        .lockReason(safeInventoryCommand.getRemark())
                        .lockTime(new Date())
                        .lockQuantity(safeInventoryCommand.getSafeQuantity() - safeQuantity)
                        .createOperator(safeInventoryCommand.getOperator())
                        .createOperatorId(safeInventoryCommand.getOperatorId())
                        .tenantId(safeInventoryCommand.getTenantId())
                        .build();
                safeInventoryWarnCommandService.lockWarn(safeInventoryWarnCommand);
            } else if (safeInventoryCommand.getSafeQuantity() < safeQuantity) {
                // 释放
                SafeInventoryWarnCommand safeInventoryWarnCommand = SafeInventoryWarnCommand.builder()
                        .warehouseNo(safeInventoryCommand.getWarehouseNo().intValue())
                        .sku(safeInventoryCommand.getSku())
                        .releaseReason(safeInventoryCommand.getRemark())
                        .lockTime(new Date())
                        .releaseQuantity(safeQuantity - safeInventoryCommand.getSafeQuantity())
                        .createOperator(safeInventoryCommand.getOperator())
                        .createOperatorId(safeInventoryCommand.getOperatorId())
                        .tenantId(safeInventoryCommand.getTenantId())
                        .build();
                safeInventoryWarnCommandService.releaseWarn(safeInventoryWarnCommand);
            }
        } catch (Exception e) {
            log.error("安全库存预警记录异常，仓：{}，sku：{}", safeInventoryCommand.getWarehouseNo(), safeInventoryCommand.getSku(), e);
        }
    }

}
