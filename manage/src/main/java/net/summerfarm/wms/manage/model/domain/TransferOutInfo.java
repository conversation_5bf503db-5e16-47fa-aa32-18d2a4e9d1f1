package net.summerfarm.wms.manage.model.domain;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.util.DateUtil;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class TransferOutInfo implements Serializable, Cloneable {
    private static final long serialVersionUID = 4385381709133371345L;

    /**
     * 转出名称
     */
    String transferOutGoodsName;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转出批次
     */
    String transferOutBatch;

    /**
     * 转出库位
     */
    String transferOutCabinetNo;

    /**
     * 转出数量
     */
    Long transferOutNum;

    /**
     * 转出批次的生产日期
     */
    Long produceTime;

    /**
     * 转出批次的保质期
     */
    Long shelfLife;

    /**
     * 存储区域
     */
    String storageArea;

    /**
     * 包装
     */
    String packaging;

    /**
     * 规格
     */
    String specification;

    /**
     * 外部回告转入数量
     */
    Integer externalTransferInNum;

    public String fourElementKey() {
        return transferOutCabinetNo + transferOutSku + DateUtil.toLocalDate(produceTime) + DateUtil.toLocalDate(shelfLife);
    }

    @Override
    public Object clone() {
        TransferOutInfo transferOutInfo = null;
        try {
            transferOutInfo = (TransferOutInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            log.info("stock对象clone异常", e);
        }
        return transferOutInfo;
    }
}
