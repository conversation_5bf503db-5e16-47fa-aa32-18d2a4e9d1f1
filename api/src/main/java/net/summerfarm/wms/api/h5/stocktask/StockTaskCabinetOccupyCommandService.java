package net.summerfarm.wms.api.h5.stocktask;

import net.summerfarm.wms.api.h5.stocktask.dto.req.MultiStockTaskOccupyQuery;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCabinetOccupyCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCabinetOccupyOneKeyCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MultiStockTaskOccupyResp;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.StockTaskCabinetOccupyResp;

public interface StockTaskCabinetOccupyCommandService {


    /**
     * 根据出库任务编码占用库位库存
     *
     * <AUTHOR>
     * @date 2023/5/22 11:02
     * @param stockTaskId 出库任务编码
     */
    void occupyCabinetInventoryForNormalTask(Integer stockTaskId);


    void occupyCabinetInventoryForAllocateTask(Integer stockTaskId);

    void occupyCabinetInventoryForPurchaseBackTask(Integer stockTaskId);

    /**
     * 越库出库任务自动锁库
     * @param stockTaskId
     */
    void occupyCabinetInventoryForCrossTask(Integer stockTaskId);

    /**
     * 出库任务占用库位库存
     *
     * <AUTHOR>
     * @date 2023/5/24 15:09
     * @return java.lang.Boolean
     */
    Boolean stockTaskOccupyCabinetInventory(StockTaskCabinetOccupyCommand occupyCommand);

    /**
     * 出库任务占用库位库存，一键
     * @param cabinetOccupyCommand
     */
    Boolean stockTaskOccupyCabinetInventoryOneKey(StockTaskCabinetOccupyOneKeyCommand cabinetOccupyCommand);

    /**
     * 查询出库任务占用库存库存明细
     *
     * <AUTHOR>
     * @date 2023/5/24 18:03
     * @param stockTaskId 出库任务编码
     * @return net.summerfarm.wms.api.h5.stocktask.dto.resp.StockTaskCabinetOccupyResp
     */
    StockTaskCabinetOccupyResp queryStockTaskCabinetOccupy(Integer stockTaskId);

    void releaseCabinetInventoryForAllocateTask(Integer stockTaskId);

    void releaseCabinetInventoryForPurchaseBackTask(Integer stockTaskId);

    void releaseCabinetInventoryForNormalTask(Integer stockTaskId);

    /**
     * 查询多个出库任务占用明细
     *
     * @param query 查询条件
     * @return 返回出库任务占用的明细
     */
    MultiStockTaskOccupyResp queryStockTaskCabinetOccupyMulti(MultiStockTaskOccupyQuery query);
}
