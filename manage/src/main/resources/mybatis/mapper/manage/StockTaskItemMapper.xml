<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskItemMapper">

    <resultMap id="withDetail" type="net.summerfarm.wms.manage.model.vo.StockTaskItemVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="INTEGER"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="actual_quantity" property="actualQuantity" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="name_remakes" property="nameRemakes"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="pd_id" property="pdId"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="categoryType" property="categoryType"/>
        <result column="skuType" property="skuType"/>
        <result column="extType" property="extType"/>
        <result column="weight_num" property="weightNum"/>
        <result column="is_domestic" property="isDomestic"/>
        <result column="customer_sku_code" property="customerSkuCode"/>
        <collection property="stockTaskItemDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.wms.manage.web.mapper.manage.StockTaskItemDetailMapper.selectByItemId"/>
    </resultMap>

    <select id="selectById" resultMap="withDetail">
        /*FORCE_MASTER*/
        SELECT sti.id,
               sti.stock_task_id,
               sti.sku,
               sti.quantity,
               sti.actual_quantity,
               sti.customer_sku_code,
               ar.status
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
                 LEFT JOIN warehouse_stock_ext ar ON st.area_no = ar.warehouse_no AND sti.sku = ar.sku
        WHERE st.id = #{stockTaskId,jdbcType=INTEGER}
    </select>

    <select id="selectByStockTaskIdAndGoods" resultMap="withDetail">
        /*FORCE_MASTER*/
        SELECT sti.id,sti.stock_task_id,sti.sku,sti.quantity,sti.actual_quantity,sti.customer_sku_code,ar.status
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
        LEFT JOIN warehouse_stock_ext ar ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        WHERE st.id = #{stockTaskId,jdbcType=INTEGER}
        <if test="sku != null and sku.length > 0">
            and sti.sku = #{sku}
        </if>
        <if test="querySkus != null and querySkus.size > 0">
            and sti.sku in
            <foreach collection="querySkus" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by sti.id desc
    </select>


    <select id="selectByIds" resultMap="withDetail">
        SELECT sti.id,sti.stock_task_id,sti.sku,sti.quantity,sti.actual_quantity,sti.customer_sku_code,ar.status
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
        LEFT JOIN warehouse_stock_ext ar ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        WHERE st.id IN
        <foreach collection="stockTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="select" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskItem"
            resultType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        SELECT id,stock_task_id stockTaskId,sku,quantity,actual_quantity actualQuantity,customer_sku_code customerSkuCode
        FROM stock_task_item
        <where>
            <if test="stockTaskId != null">
                AND stock_task_id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="skuList != null and skuList.size() > 0">
                AND sku IN
                <foreach collection="skuList" open="(" close=")" separator="," item="sku">
                    #{sku}
                </foreach>
            </if>
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        UPDATE stock_task_item
        <set>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity},
            </if>
            <if test="oldQuantity != null">
                old_quantity = #{oldQuantity},
            </if>
            <if test="customerSkuCode != null">
                customer_sku_code = #{customerSkuCode},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateActualQuantity" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        UPDATE stock_task_item
        SET actual_quantity = actual_quantity + #{actualQuantity}
        WHERE id = #{id,jdbcType=INTEGER}
        AND quantity >= actual_quantity + #{actualQuantity}
    </update>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        INSERT INTO stock_task_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="actualQuantity != null">
                actual_quantity,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="customerSkuCode != null">
                customer_sku_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                #{stockTaskId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="actualQuantity != null">
                #{actualQuantity},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="customerSkuCode!= null">
                #{customerSkuCode},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_task_item (stock_task_id,sku,quantity,actual_quantity,tenant_id,customer_sku_code)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTaskId},#{item.sku},#{item.quantity},#{item.actualQuantity},#{item.tenantId},#{item.customerSkuCode})
        </foreach>
    </insert>

    <select id="queryStockTaskItem" resultType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        SELECT id,stock_task_id stockTaskId,sku,quantity,actual_quantity actualQuantity,customer_sku_code customerSkuCode
        FROM stock_task_item
        where stock_task_id = #{stockTaskId} and sku =#{sku}
    </select>

    <select id="selectByStockTaskId" resultMap="withDetail">
        /*FORCE_MASTER*/
        SELECT sti.id,sti.stock_task_id,sti.sku,sti.quantity,sti.actual_quantity,sti.customer_sku_code,ar.status
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
                 LEFT JOIN warehouse_stock_ext  ar	ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        WHERE st.id = #{stockTaskId,jdbcType=INTEGER}
        order by sti.id desc
    </select>
</mapper>