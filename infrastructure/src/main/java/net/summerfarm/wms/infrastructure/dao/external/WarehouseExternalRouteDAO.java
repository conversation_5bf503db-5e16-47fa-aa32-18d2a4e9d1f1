package net.summerfarm.wms.infrastructure.dao.external;

import net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalRouteDO;
import net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 仓库外部路由DAO
 * @date 2024/4/28
 */
public interface WarehouseExternalRouteDAO {

    /**
     * create
     *
     * @param warehouseExternalRoute
     * @return
     */
    Long create(WarehouseExternalRouteDO warehouseExternalRoute);

    /**
     * batch create
     *
     * @param warehouseExternalRoutes
     * @return
     */
    Integer creates(List<WarehouseExternalRouteDO> warehouseExternalRoutes);

    /**
     * findById
     *
     * @param id
     * @return
     */
    WarehouseExternalRouteDO findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<WarehouseExternalRouteDO> findByIds(List<Long> ids);

    /**
     * update
     *
     * @param warehouseExternalRoute
     * @return
     */
    Integer update(WarehouseExternalRouteDO warehouseExternalRoute);

    /**
     * count
     *
     * @param warehouseExternalRouteQueryParam
     * @return
     */
    Long count(WarehouseExternalRouteQueryParam warehouseExternalRouteQueryParam);

    /**
     * findOne
     *
     * @param warehouseExternalRouteQueryParam
     * @return
     */
    WarehouseExternalRouteDO findOne(WarehouseExternalRouteQueryParam warehouseExternalRouteQueryParam);

    /**
     * list
     *
     * @param warehouseExternalRouteQueryParam
     * @return
     */
    List<WarehouseExternalRouteDO> list(WarehouseExternalRouteQueryParam warehouseExternalRouteQueryParam);

    List<Long> queryDistinctWarehouseNo(@Param("externalAppKey") String externalAppKey,
                                        @Param("routeStatus") Integer routeStatus);
}
