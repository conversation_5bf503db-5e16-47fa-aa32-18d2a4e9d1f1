package net.summerfarm.wms.api.h5.stocktaking.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/17
 */
@Data
public class StockTakingCallbackCommand implements Serializable {

    private static final long serialVersionUID = 1144989499990141663L;

    /**
     * 对接方提供，鲜沐侧使用此单号做幂等
     */
    private String idempotentNo;

    /**
     * 盘点单号
     */
    private String stockTakingNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 完成时间（YYYY-MM-DD HH:mm:ss）
     */
    private String finishTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 盘点类型（0：sku盘点，1：批次盘点）
     */
    private Integer takingType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点明细
     */
    private List<StockTakingCallbackCommandItem> stockTakingItems;

}
