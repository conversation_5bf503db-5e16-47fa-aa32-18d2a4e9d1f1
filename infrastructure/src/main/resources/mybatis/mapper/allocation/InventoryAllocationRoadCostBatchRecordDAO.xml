<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.allocation.InventoryAllocationRoadCostBatchRecordDAO">
    <!-- 结果集映射 -->
    <resultMap id="inventoryAllocationRoadCostBatchRecordResultMap" type="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.InventoryAllocationRoadCostBatchRecordDO">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="list_no" property="listNo" jdbcType="VARCHAR"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
		<result column="production_date" property="productionDate" jdbcType="DATE"/>
		<result column="quality_date" property="qualityDate" jdbcType="DATE"/>
		<result column="old_road_quantity" property="oldRoadQuantity" jdbcType="INTEGER"/>
		<result column="new_road_quantity" property="newRoadQuantity" jdbcType="INTEGER"/>
		<result column="record_type" property="recordType" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="inventoryAllocationRoadCostBatchRecordColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.list_no,
          t.warehouse_no,
          t.sku,
          t.batch_no,
          t.production_date,
          t.quality_date,
          t.old_road_quantity,
          t.new_road_quantity,
          t.record_type
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="inventoryAllocationRoadCostBatchRecordResultMap" >
        SELECT <include refid="inventoryAllocationRoadCostBatchRecordColumns" />
        FROM inventory_allocation_road_cost_batch_record t
		WHERE t.id = #{id}
    </select>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_allocation_road_cost_batch_record (
          list_no, warehouse_no, sku, batch_no, production_date, quality_date, old_road_quantity, new_road_quantity, record_type
        ) values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.listNo}, #{item.warehouseNo}, #{item.sku}, #{item.batchNo}, #{item.productionDate}, #{item.qualityDate},
            #{item.oldRoadQuantity}, #{item.newRoadQuantity}, #{item.recordType}
            )
        </foreach>
    </insert>

</mapper>