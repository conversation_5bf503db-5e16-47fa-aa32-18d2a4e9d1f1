package net.summerfarm.wms.infrastructure.dao.stockTask.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description
 * @Date 2023/5/22 10:10
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsStockTaskItemCabinetOccupyDO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 出库任务编码
     */
    private Long stockTaskId;

    /**
     * 出库任务明细编码
     */
    private Long stockTaskItemId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 占用数量
     */
    private Integer occupyQuantity;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 是否软删 0-正常 1-软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 拣货数量
     */
    private Integer pickQuantity;

    /**
     * 释放数量
     */
    private Integer releaseQuantity;

    /**
     * 需要拣货数量
     */
    private Integer shouldPickQuantity;

    /**
     * 实际拣货数量
     */
    private Integer actualPickQuantity;

    private Integer abnormalQuantity;
}
