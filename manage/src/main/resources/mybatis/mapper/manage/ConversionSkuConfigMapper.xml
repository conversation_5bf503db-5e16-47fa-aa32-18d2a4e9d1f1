<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.ConversionSkuConfigMapper">

    <select id="selectConfig" parameterType="net.summerfarm.wms.manage.model.domain.ConversionSkuConfig"
            resultType="net.summerfarm.wms.manage.model.domain.ConversionSkuConfig">
        select
        id,
        warehouse_no warehouseNo,
        pd_id pdId,
        in_sku inSku,
        out_sku outSku,
        admin_id adminId,
        status,
        rates
        from conversion_sku_config
        <where>
            <if test="inSku != null">
                AND in_sku = #{inSku}
            </if>
            <if test="outSku != null">
                AND out_sku = #{outSku}
            </if>
            <if test="warehouseNo != null">
                AND warehouse_no = #{warehouseNo}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="selectConfigDetail"  resultType="net.summerfarm.wms.manage.model.domain.ConversionSkuConfig">

        select
            id,
            warehouse_no warehouseNo,
            pd_id pdId,
            in_sku inSku,
            out_sku outSku,
            admin_id adminId,
            status,
            rates
        from conversion_sku_config
        where id = #{id}
    </select>
</mapper>