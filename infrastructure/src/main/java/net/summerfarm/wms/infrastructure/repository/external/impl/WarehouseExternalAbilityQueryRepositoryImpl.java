package net.summerfarm.wms.infrastructure.repository.external.impl;

import net.summerfarm.wms.domain.external.entity.WarehouseExternalAbilityEntity;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalAbilityQueryRepository;
import net.summerfarm.wms.infrastructure.repository.external.converter.WarehouseExternalAbilityConverter;
import net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalAbilityDO;
import net.summerfarm.wms.infrastructure.dao.external.WarehouseExternalAbilityDAO;
import net.summerfarm.wms.domain.external.param.WarehouseExternalAbilityQueryParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 仓库外部能力点
 * @date 2024/4/28
 */
@Repository
public class WarehouseExternalAbilityQueryRepositoryImpl implements WarehouseExternalAbilityQueryRepository {

    @Resource
    private WarehouseExternalAbilityDAO warehouseExternalAbilityDAO;

    /**
     * findById
     *
     * @param id
     * @return
     */
    @Override
    public WarehouseExternalAbilityEntity findById(Long id) {
        if (null == id) {
            return null;
        }
        return WarehouseExternalAbilityConverter.INSTANCE.convert(warehouseExternalAbilityDAO.findById(id));
    }

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    @Override
    public List<WarehouseExternalAbilityEntity> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<WarehouseExternalAbilityDO> warehouseExternalAbilityDOList = warehouseExternalAbilityDAO.findByIds(ids);
        if (CollectionUtils.isEmpty(warehouseExternalAbilityDOList)) {
            return new ArrayList<>();
        }
        return warehouseExternalAbilityDOList.stream().map(WarehouseExternalAbilityConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    /**
     * count
     *
     * @param warehouseExternalAbilityQueryParam
     * @return
     */
    @Override
    public Long count(WarehouseExternalAbilityQueryParam warehouseExternalAbilityQueryParam) {
        if (null == warehouseExternalAbilityQueryParam) {
            return 0L;
        }
        return warehouseExternalAbilityDAO.count(warehouseExternalAbilityQueryParam);
    }

    /**
     * findOne
     *
     * @param warehouseExternalAbilityQueryParam
     * @return
     */
    @Override
    public WarehouseExternalAbilityEntity findOne(WarehouseExternalAbilityQueryParam warehouseExternalAbilityQueryParam) {
        if (null == warehouseExternalAbilityQueryParam) {
            return null;
        }
        WarehouseExternalAbilityDO warehouseExternalAbilityDO = warehouseExternalAbilityDAO.findOne(warehouseExternalAbilityQueryParam);
        if (null == warehouseExternalAbilityDO) {
            return null;
        }
        return WarehouseExternalAbilityConverter.INSTANCE.convert(warehouseExternalAbilityDO);
    }

    /**
     * list
     *
     * @param warehouseExternalAbilityQueryParam
     * @return
     */
    @Override
    public List<WarehouseExternalAbilityEntity> list(WarehouseExternalAbilityQueryParam warehouseExternalAbilityQueryParam) {
        if (null == warehouseExternalAbilityQueryParam) {
            return new ArrayList<>();
        }
        List<WarehouseExternalAbilityDO> warehouseExternalAbilityDOList = warehouseExternalAbilityDAO.list(warehouseExternalAbilityQueryParam);
        if (CollectionUtils.isEmpty(warehouseExternalAbilityDOList)) {
            return new ArrayList<>();
        }
        return warehouseExternalAbilityDOList.stream().map(WarehouseExternalAbilityConverter.INSTANCE::convert).collect(Collectors.toList());
    }
}
