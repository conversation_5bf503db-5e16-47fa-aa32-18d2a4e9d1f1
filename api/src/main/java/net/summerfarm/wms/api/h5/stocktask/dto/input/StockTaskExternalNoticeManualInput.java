package net.summerfarm.wms.api.h5.stocktask.dto.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskExternalNoticeManualInput implements Serializable {

    private static final long serialVersionUID = -6222432810931628364L;

    /**
     * 出库任务id
     */
    @NotNull(message = "任务id不能为空")
    private Integer stockTaskId;
}
