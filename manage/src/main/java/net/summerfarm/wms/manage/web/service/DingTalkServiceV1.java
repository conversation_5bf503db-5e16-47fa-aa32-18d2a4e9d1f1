package net.summerfarm.wms.manage.web.service;

import java.util.StringJoiner;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
public interface DingTalkServiceV1 {

    /**
     * 创建审批流
     */
    void createProcessInstance(String sku, Integer areaNo);

    /**
     * 发送盘盈盘亏预警提醒
     * @param taskId
     * @param type
     * @param msg
     * @return
     */
    void sendCargoDamageMsg(Integer taskId, String type, StringJoiner msg, Integer auditId);
}
