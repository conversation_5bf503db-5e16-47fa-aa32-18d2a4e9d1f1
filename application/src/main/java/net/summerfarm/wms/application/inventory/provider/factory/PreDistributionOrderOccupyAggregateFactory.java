package net.summerfarm.wms.application.inventory.provider.factory;

import net.summerfarm.wms.domain.inventory.domainobject.aggregate.PreDistributionOrderOccupyAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.valueObject.OrderOccupyDetail;
import net.summerfarm.wms.saleinventory.dto.req.PreDistributionOrderOccupyReqDTO;
import net.summerfarm.wms.saleinventory.dto.req.PreDistributionSkuDetailReqDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class PreDistributionOrderOccupyAggregateFactory {


    public static PreDistributionOrderOccupyAggregate newInstance(
            PreDistributionOrderOccupyReqDTO preDistributionOrderOccupyReqDTO) {

        PreDistributionOrderOccupyAggregate aggregate = new PreDistributionOrderOccupyAggregate();
        aggregate.setTenantId(preDistributionOrderOccupyReqDTO.getTenantId());
        aggregate.setWarehouseTenantId(preDistributionOrderOccupyReqDTO.getWarehouseTenantId());
        aggregate.setProvince(preDistributionOrderOccupyReqDTO.getProvince());
        aggregate.setCity(preDistributionOrderOccupyReqDTO.getCity());
        aggregate.setArea(preDistributionOrderOccupyReqDTO.getArea());
        aggregate.setAddress(preDistributionOrderOccupyReqDTO.getAddress());
        aggregate.setPoi(preDistributionOrderOccupyReqDTO.getPoi());
        aggregate.setStoreNo(preDistributionOrderOccupyReqDTO.getStoreNo());
        aggregate.setContactId(preDistributionOrderOccupyReqDTO.getContactId());
        aggregate.setMerchantId(preDistributionOrderOccupyReqDTO.getMerchantId());
        aggregate.setSource(preDistributionOrderOccupyReqDTO.getSource());
        aggregate.setAddOrderFlag(preDistributionOrderOccupyReqDTO.getAddOrderFlag());

        aggregate.setOrderOccupyDetailList(newInstanceList(
                preDistributionOrderOccupyReqDTO.getPreDistributionSkuDetailReqDTOS()));

        return aggregate;
    }

    private static List<OrderOccupyDetail> newInstanceList(List<PreDistributionSkuDetailReqDTO> preDistributionSkuDetailReqDTOS) {
        if (CollectionUtils.isEmpty(preDistributionSkuDetailReqDTOS)){
            return new ArrayList<>();
        }

        List<OrderOccupyDetail> result = new ArrayList<>();
        for (PreDistributionSkuDetailReqDTO preDistributionSkuDetailReqDTO : preDistributionSkuDetailReqDTOS) {
            OrderOccupyDetail orderOccupyDetail = new OrderOccupyDetail();
            orderOccupyDetail.setSkuCode(preDistributionSkuDetailReqDTO.getSkuCode());
            orderOccupyDetail.setOccupyQuantity(preDistributionSkuDetailReqDTO.getOccupyQuantity());

            result.add(orderOccupyDetail);
        }

        return result;
    }
}
