package net.summerfarm.wms.domain.pickuptask.domainobject.entity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 投线任务订单信息
 * <AUTHOR>
 * @date 2023/02/20
 */
@Data
@Accessors(chain = true)
public class PickUpTaskOrderInfo {

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 喜茶订单编号
     */
    private String htOrderCode;
    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 城配仓编码
     */
    private Integer storeNo;

    /**
     * 客户id
     */
    private Integer adminId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SKU数量
     */
    private Integer amount;
}
