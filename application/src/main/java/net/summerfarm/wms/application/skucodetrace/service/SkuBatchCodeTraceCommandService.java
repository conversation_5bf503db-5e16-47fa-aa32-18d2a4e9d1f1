package net.summerfarm.wms.application.skucodetrace.service;

import net.summerfarm.wms.application.skucodetrace.inbound.input.command.FulfillmentOrderInfoInput;
import net.summerfarm.wms.application.skucodetrace.inbound.input.command.ThresholdUpdateInput;
import net.summerfarm.wms.application.skucodetrace.inbound.vo.ThresholdUpdateResultVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * Description: 唯一溯源码操作服务<br/>
 * date: 2024/8/7 14:35<br/>
 *
 * <AUTHOR> />
 */
public interface SkuBatchCodeTraceCommandService {

    /**
     * 创建批次码和溯源码
     * @param stockStorageTaskId 批次码任务ID
     */
    void createBatchCodeAndTrace(Long stockStorageTaskId);

    /**
     * 更新重量
     * @param input
     * @return
     */
    CommonResult<ThresholdUpdateResultVO> upsetWeight(ThresholdUpdateInput input);


    /**
     * 更新履约订单信息
     * @param fulfillmentOrderInfoInputList 履约订单溯源码信息
     */
    void updateSkuBatchCodeTraceFulfillmentOrderInfo(List<FulfillmentOrderInfoInput> fulfillmentOrderInfoInputList);
}
