package net.summerfarm.wms.infrastructure.dao.stockTransfer;

import net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferItemOpDetailDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferItemOpDetailDAO {

    /**
     * 根据op表主键列表查询
     */
    List<StockTransferItemOpDetailDO> listByOpIds(@Param("opIds") List<Long> opIds);

    /**
     * 根据实例操作id和转出批次查询
     */
    StockTransferItemOpDetailDO selectByBatch(@Param("opId") Long stockTransferItemOpId, @Param("batch") String batch);

    int insert(StockTransferItemOpDetailDO stockTransferItemOpDetailDO);

    void batchInsert(@Param("items") List<StockTransferItemOpDetailDO> stockTransferItemOpDetailDos);

    /**
     * 获取转换批次信息
     */
    List<StockTransferItemOpDetailDO> listByTransferNo(String transferNo);

    int updateExternalTransferInNum(@Param("id") Long opDetailId, @Param("externalTransferInNum") Integer externalTransferInNum);

}
