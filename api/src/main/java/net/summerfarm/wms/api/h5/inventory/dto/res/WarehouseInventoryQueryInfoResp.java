package net.summerfarm.wms.api.h5.inventory.dto.res;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WarehouseInventoryQueryInfoResp implements Serializable {

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    @ExcelIgnore
    private Long warehouseNo;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    @ExcelProperty(value = "仓库", index = 0)
    private String warehouseName;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @ExcelProperty(value = "商品名称", index = 1)
    private String productName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @ExcelProperty(value = "sku", index = 2)
    private String skuCode;

    /**
     * 一级类目，鲜果、非鲜果
     */
    @ExcelProperty(value = "一级类目", index = 3)
    private String firstLevelCategory;

    /**
     * 二级类目
     */
    @ExcelProperty(value = "二级类目", index = 4)
    private String secondLevelCategory;

    /**
     * 商品归属
     */
    @ExcelProperty(value = "商品归属", index = 5)
    private String goodsBelong;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    @ExcelProperty(value = "规格", index = 6)
    private String weightSpec;


    /**
     * 存储区域
     */
    @ApiModelProperty(value = "储存区域")
    @ExcelProperty(value = "储存区域", index = 7)
    private String storageLocation;

    /**
     * 存储区域id
     */
    @ExcelIgnore
    private Integer storageLocationId;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    @ExcelProperty(value = "包装", index = 8)
    private String packaging;

    /**
     * 进口/外包
     */
    @ApiModelProperty(value = "进口/外包")
    @ExcelProperty(value = "进口/外包", index = 9)
    private String origin;

    /**
     * 仓库库存数量
     */
    @ApiModelProperty(value = "仓库库存")
    @ExcelProperty(value = "仓库库存", index = 10)
    private Integer quantity;


    /**
     * 安全库存
     */
    @ApiModelProperty(value = "安全库存")
    @ExcelProperty(value = "安全库存", index = 11)
    private Integer safeQuantity;

    /**
     * 冻结库存
     */
    @ApiModelProperty(value = "冻结库存")
    @ExcelProperty(value = "冻结库存", index = 12)
    private Integer lockQuantity;

    /**
     * 在途库存
     */
    @ApiModelProperty(value = "在途库存")
    @ExcelProperty(value = "在途库存", index = 13)
    private Integer roadQuantity;

    /**
     * 销售冻结库存
     */
    @ApiModelProperty(value = "销售冻结库存")
    @ExcelIgnore
    private Integer saleLockQuantity;

    /**
     * 可用库存
     */
    @ApiModelProperty(value = "可用库存")
    @ExcelProperty(value = "可用库存", index = 14)
    private Integer availableQuantity;

    /**
     * 虚拟库存
     */
    @ApiModelProperty(value = "虚拟库存")
    @ExcelProperty(value = "虚拟库存", index = 15)
    private Integer onlineQuantity;
}

