package net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetBatchInventoryAddReq implements Serializable {

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private Integer warehouseNo;

    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号")
    private String orderNo;

    /**
     * 变动类型
     *
     * @see net.summerfarm.wms.domain.inventory.enums.CabinetInventoryChangeTypeEnum
     */
    @ApiModelProperty(value = "变动类型")
    private String orderTypeName;


    /**
     * 内部流转单号
     */
    @ApiModelProperty(value = "内部流转单号")
    private String internalNo;

    /**
     * 操作类别
     *
     * @see net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType
     */
    @ApiModelProperty(value = "操作类别")
    private Integer operatorType;

    /**
     * 容器编码
     */
    @ApiModelProperty(value = "容器编码")
    private String containerCode;

    /**
     * 增加库存
     */
    private List<CabinetBatchInventoryAddDetailReq> addDetailReqList;

    /**
     * 操作人名称
     */
    private String operatorName;
}
