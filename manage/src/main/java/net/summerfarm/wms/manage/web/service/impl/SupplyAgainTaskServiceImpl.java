package net.summerfarm.wms.manage.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wms.api.h5.common.StoreTaskLogCommandService;
import net.summerfarm.wms.api.h5.goods.SkuConvertCommandService;
import net.summerfarm.wms.api.h5.instore.even.OutMoreInStockStorageChangeEvent;
import net.summerfarm.wms.api.h5.stocktask.MissionTaskItemMappingCommandService;
import net.summerfarm.wms.api.h5.stocktask.MissionTaskItemMappingQueryService;
import net.summerfarm.wms.api.h5.stocktask.StockOutWarehouseQueryService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.*;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingCountResp;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingDTO;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingResp;
import net.summerfarm.wms.api.inner.batch.ProduceBatchInnerService;
import net.summerfarm.wms.api.inner.batch.req.ProduceBatchQueryReqDTO;
import net.summerfarm.wms.api.inner.batch.req.ProduceBatchUpdateReqDTO;
import net.summerfarm.wms.api.inner.batch.res.ProduceBatchResDTO;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.initconfig.ProduceBatchSwitchConfig;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.stocktask.OutNoticeAbnormalDetailQueryRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderAbnormalDetail;
import net.summerfarm.wms.domain.stocktask.enums.StorageLocationEnum;
import net.summerfarm.wms.inventory.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryOccupyReduceReqDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryReleaseDetailReqDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryReleaseReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryOccupyReduceBatchRespDTO;
import net.summerfarm.wms.manage.model.domain.*;
import net.summerfarm.wms.manage.model.dto.MailWorkBookDTO;
import net.summerfarm.wms.manage.model.dto.StockTaskItemDetailPrintDTO;
import net.summerfarm.wms.manage.model.dto.StockTaskItemPrintDTO;
import net.summerfarm.wms.manage.model.dto.StockTaskPickDetailDTO;
import net.summerfarm.wms.manage.model.vo.StockTaskItemDetailVO;
import net.summerfarm.wms.manage.model.vo.StockTaskItemVO;
import net.summerfarm.wms.manage.web.config.StockOutBranchConfig;
import net.summerfarm.wms.manage.web.enums.*;
import net.summerfarm.wms.manage.web.enums.impl.SaleStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.enums.impl.StoreRecordType;
import net.summerfarm.wms.manage.web.facade.CabinetInventoryFacade;
import net.summerfarm.wms.manage.web.factory.StockOutCommonCommandInputFactory;
import net.summerfarm.wms.manage.web.factory.StockTaskItemDetailUpdateFactory;
import net.summerfarm.wms.manage.web.mapper.manage.*;
import net.summerfarm.wms.manage.web.reposity.StockTaskItemCabinetOccupyRepositoryV1;
import net.summerfarm.wms.manage.web.reposity.StockTaskWaveRepositoryV1;
import net.summerfarm.wms.manage.web.reposity.StockTaskWaveSkuOccupyRepository;
import net.summerfarm.wms.manage.web.req.StockTaskSaleOutReq;
import net.summerfarm.wms.manage.web.resp.StockOutTaskPrintResp;
import net.summerfarm.wms.manage.web.resp.StockTaskDetailOutStatusResp;
import net.summerfarm.wms.manage.web.resp.StockTaskResult;
import net.summerfarm.wms.manage.web.service.*;
import net.summerfarm.wms.manage.web.service.strategy.stockout.init.StockOutCommonServiceFactory;
import net.summerfarm.wms.manage.web.service.strategy.stockout.input.StockOutCommonCommandInput;
import net.summerfarm.wms.manage.web.service.tms.TmsTrunkService;
import net.summerfarm.wms.manage.web.utils.OptionFlagUtil;
import net.summerfarm.wms.manage.web.utils.SaasThreadLocalUtil;
import net.summerfarm.wms.manage.web.utils.SkuUtil;
import net.summerfarm.wms.manage.web.utils.SystemLocalUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.rocketmq.support.producer.SendResultDTO;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Service
@Slf4j
public class SupplyAgainTaskServiceImpl implements SupplyAgainTaskService {

    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private AdminUtil adminUtil;
    @Resource
    private WmsBuilderService wmsBuilderService;
    @Resource
    private SkuConvertCommandService skuConvertCommandService;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private StockTaskItemCabinetOccupyRepositoryV1 stockTaskItemCabinetOccupyRepositoryV1;
    @Resource
    private StockTaskMsgService stockTaskMsgService;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private StockTaskOrderCancelService stockTaskOrderCancelService;
    @Resource
    private BaseService baseService;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private StockTaskProcessLogisticsOrderMapper logisticsOrderMapper;
    @Resource
    private AreaStoreServiceV1 areaStoreServiceV1;
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private TmsTrunkService tmsTrunkService;
    @Resource
    private StockTaskMapper stockTaskMapper;
    private SupplyAgainTaskService selfService;
    @Resource
    private StockTaskWaveRepositoryV1 stockTaskWaveRepositoryV1;
    @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;
    @Resource
    private QuantityChangeRecordServiceV1 quantityChangeRecordServiceV1;
    @Resource
    private StockTaskProcessOrderSkuService stockTaskProcessOrderSkuService;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private MissionTaskItemMappingQueryService missionTaskItemMappingQueryService;
    @Resource
    private MissionTaskItemMappingCommandService missionTaskItemMappingCommandService;
    @Resource
    private MqProducer mqProducer;

    @Resource
    private WarehouseStorageServiceV1 warehouseStorageServiceV1;
    @Resource
    private StoreTaskLogCommandService storeTaskLogCommandService;
    @Resource
    private StockTaskWaveSkuOccupyRepository stockTaskWaveSkuOccupyRepository;
    @Resource
    private StockOutWarehouseQueryService stockOutWarehouseQueryService;
    @Resource
    private OutNoticeAbnormalDetailQueryRepository outNoticeAbnormalDetailQueryRepository;
    @Resource
    private ProduceBatchInnerService produceBatchInnerService;
    @Resource
    private ProduceBatchSwitchConfig produceBatchSwitchConfig;
    @Resource
    private StockOutBranchConfig stockOutBranchConfig;
    @Resource
    private StockOutCommonCommandInputFactory stockOutCommonCommandInputFactory;
    @Resource
    private StockOutCommonServiceFactory stockOutCommonServiceFactory;

    @PostConstruct
    private void setSelf() {
        selfService = applicationContext.getBean(SupplyAgainTaskService.class);
    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        return stockTaskDetail(result, null, null);
    }

    @Override
    public StockTaskDetailOutStatusResp outStatusQuery(StockTask stockTask) {
        List<StockTaskItemVO> stockTaskItemVOS = stockTaskItemMapper.selectById(stockTask.getId().intValue());
        if (CollectionUtils.isEmpty(stockTaskItemVOS)) {
            // 没有查询到明细信息
            return StockTaskDetailOutStatusResp.builder().build();
        }
        // 校验是否已完成拣货
        StockTaskDetailOutStatusResp outStatusResp = StockTaskDetailOutStatusResp.builder().build();
        MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
        missionTaskItemMappingSkuQuery.setStockTaskId(stockTask.getId().toString());
        MissionTaskItemMappingCountResp missionTaskItemMappingCountResp = missionTaskItemMappingQueryService.countMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
        if (missionTaskItemMappingCountResp.getPickCreateNum() > 0) {
            // 未完成拣货
            outStatusResp.setPickStatus(1);
        } else if (missionTaskItemMappingCountResp.getPickCreateNum() == 0
                && missionTaskItemMappingCountResp.getPickCompleteNum() == 0
                && missionTaskItemMappingCountResp.getOutConfirmNum() == 0
                && missionTaskItemMappingCountResp.getDiffConfirmNum() == 0) {
            outStatusResp.setPickStatus(2);
        } else if (missionTaskItemMappingCountResp.getPickCreateNum() == 0
                && missionTaskItemMappingCountResp.getPickCompleteNum() == 0) {
            // 完成拣货并且完成出库
            outStatusResp.setPickStatus(3);
        } else {
            // 已完成拣货
            outStatusResp.setPickStatus(0);
        }
        if (missionTaskItemMappingCountResp.getPickCompleteNum() > 0) {
            outStatusResp.setCompleteOutStoreStatus(1);
        } else {
            outStatusResp.setCompleteOutStoreStatus(0);
            // 总数量
            int sum = stockTaskItemVOS.stream().filter(it -> Objects.nonNull(it.getOutQuantity()))
                    .mapToInt(StockTaskItemVO::getOutQuantity).sum();
            // 已出数量
            AtomicInteger actualNumTotal = new AtomicInteger();
            stockTaskItemVOS.forEach(it -> {
                List<StockTaskItemDetailVO> itemDetails = it.getStockTaskItemDetailVOS();
                if (!CollectionUtils.isEmpty(itemDetails)) {
                    int actualNum = itemDetails.stream().filter(item -> Objects.nonNull(item.getOutStoreQuantity()))
                            .mapToInt(StockTaskItemDetailVO::getOutStoreQuantity).sum();
                    actualNumTotal.set(actualNumTotal.get() + actualNum);
                }
            });
            if (sum > actualNumTotal.get() && actualNumTotal.get() > 0) {
                // 部分出库
                outStatusResp.setCompleteOutStoreStatus(2);
            }
        }
        return outStatusResp;
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result, String sku, Long pdId) {
        List<Product> productsByPdName = Objects.isNull(pdId) ? com.alibaba.nacos.shaded.com.google.common.collect.Lists.newArrayList() : productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                .warehouseNo(result.getAreaNo().longValue()).pdIds(Lists.newArrayList(pdId)).build());
        List<String> querySkus = productsByPdName.stream().map(Product::getSku).collect(Collectors.toList());
        List<StockTaskItemVO> stockTaskItemVOs = stockTaskItemMapper.selectByStockTaskIdAndGoods(result.getId(), sku, querySkus);
        List<String> skus = stockTaskItemVOs.stream().map(StockTaskItemVO::getSku).collect(Collectors.toList());

        Lists.partition(skus, WmsConstant.PARTITION_SIZE).forEach(list -> {
            HashSet<String> skuSet = new HashSet<>(list);
            List<Product> productsFromGoodsCenter1 = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                    .warehouseNo(result.getAreaNo().longValue())
                    .skus(list).build());
            Map<String, Product> productMap = productsFromGoodsCenter1.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));

            stockTaskItemVOs.stream()
                    .filter(item -> skuSet.contains(item.getSku()))
                    .forEach(item -> {
                        Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                        item.setWeight(product.getSpecification());
                        item.setUnit(product.getPackaging());
                        item.setCategoryId(Objects.isNull(product.getCategoryId()) ? null : product.getCategoryId().intValue());
                        item.setPdId(Objects.isNull(product.getId()) ? null : product.getId().intValue());
                        item.setStorageLocation(product.getStorageLocation());
                        item.setCategoryType(product.getCategoryType());
                        item.setSkuType(product.getSkuType());
                        item.setExtType(product.getSkuExeType());
                        item.setPdName(product.getPdName());
                        item.setVolume(product.getVolume());
                        item.setWeightNum(product.getWeightNum());
                        item.setCategory(product.getCategory());
                        item.setIsDomestic(product.getIsDomestic());
                        item.setNameRemakes(adminUtil.getNameRemarks(product.getInventoryAdminId()));
                        item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                        item.setStorageArea(StorageLocationEnum.getDescByCode(product.getTemperature()));
                        item.setPacking(product.getPackaging());
                        item.setSku(product.getSku());
                        item.setCategoryType(product.getCategoryType());
                        item.setSecondLevelCategory(product.queryFirstCategoryName() + product.querySecondCategoryName()
                                + product.queryThirdCategoryName());
                        item.setStorageLocation(product.getStorageLocation());
                    });
        });
        List<StockTaskItemVO> stockTaskItemVOS = stockTaskItemVOs.stream()
                .sorted(Comparator.comparing(StockTaskItemVO::compareCategoryType)
                        .thenComparing(StockTaskItemVO::getId).reversed())
                .collect(Collectors.toList());

        boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(result.getAreaNo());
        result.setOpenCabinetManagement(openCabinetManagement);
        if (CollectionUtils.isEmpty(stockTaskItemVOS)) {
            result.setList(Lists.newArrayList());
            return AjaxResult.getOK(result);
        }

        // saas端转换skuId
        if (!WmsConstant.XIANMU_TENANT_ID.equals(result.getTenantId())) {
            skuConvertCommandService.setSaasSkuIdForList(stockTaskItemVOS);
        }
        result.setList(stockTaskItemVOS);
        if (openCabinetManagement) {
            List<String> skuCodeList = stockTaskItemVOS.stream()
                    .map(StockTaskItemVO::getSku)
                    .distinct()
                    .collect(Collectors.toList());

            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepositoryV1
                    .selectListByStockTaskIdOccupyedExcludedJJV1(result.getId(), skuCodeList);
            Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> s.getSku()));

            for (StockTaskItemVO stockTaskItemVO : stockTaskItemVOS) {
                List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = skuQualityDateOrderMap.get(
                        stockTaskItemVO.getSku());
                if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                    stockTaskItemVO.setCabinetOccupyDOList(stockTaskItemCabinetOccupyDOList);
                    int sum = stockTaskItemCabinetOccupyDOList.stream()
                            .map(StockTaskItemCabinetOccupyDO::getShouldPickQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    stockTaskItemVO.setActualGenPickQuantity(sum);
                }
            }
        } else {
            stockTaskItemVOS.forEach(it -> {
                List<StockTaskItemDetailVO> itemDetails = it.getStockTaskItemDetailVOS();
                if (!CollectionUtils.isEmpty(itemDetails)) {
                    int sum = itemDetails.stream().map(StockTaskItemDetailVO::getOutStoreQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    it.setActualGenPickQuantity(sum);
                }
            });
        }
        return AjaxResult.getOK(result);
    }

    @Override
    public List<StockTaskPickDetailDTO> stockTaskDetailListByTaskIdList(List<StockTask> stockTaskList) {
        List<Integer> stockIdList = stockTaskList.stream().map(StockTask::getId).collect(Collectors.toList());
        Integer areaNo = stockTaskList.get(0).getAreaNo();
        List<StockTaskItemVO> stockTaskItemList = selectItems(areaNo.longValue(), stockIdList);

        if (CollectionUtils.isEmpty(stockTaskItemList)) {
            return Lists.newArrayList();
        }
        boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(areaNo);

        if (openCabinetManagement) {
            List<String> skuCodeList = stockTaskItemList.stream()
                    .map(StockTaskItemVO::getSku)
                    .distinct()
                    .collect(Collectors.toList());

            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyList = stockTaskItemCabinetOccupyRepositoryV1
                    .selectListByStockTaskIdOccupyedExcludedJJ(stockIdList, skuCodeList);
            Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = stockTaskItemCabinetOccupyList.stream()
                    .collect(Collectors.groupingBy(s -> s.getStockTaskId() + "_" + s.getSku()));

            for (StockTaskItemVO stockTaskItemVO : stockTaskItemList) {
                List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = skuQualityDateOrderMap.get(
                        stockTaskItemVO.getStockTaskId() + "_" + stockTaskItemVO.getSku());
                if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                    stockTaskItemVO.setCabinetOccupyDOList(stockTaskItemCabinetOccupyDOList);
                    int sum = stockTaskItemCabinetOccupyDOList.stream()
                            .map(StockTaskItemCabinetOccupyDO::getShouldPickQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    stockTaskItemVO.setActualGenPickQuantity(sum);
                }
            }
        } else {
            stockTaskItemList.forEach(it -> {
                List<StockTaskItemDetailVO> itemDetails = it.getStockTaskItemDetailVOS();
                if (!CollectionUtils.isEmpty(itemDetails)) {
                    int sum = itemDetails.stream().map(StockTaskItemDetailVO::getOutStoreQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    it.setActualGenPickQuantity(sum);
                    itemDetails.forEach(item -> {
                        String cabinetCode = item.getCabinetCode();
                        if (!StringUtils.isEmpty(cabinetCode)) {
                            String zoneCode = cabinetCode.split("-")[0];
                            item.setZoneCode(zoneCode);
                        }
                    });
                }
            });
        }
        // 获取单据类型列表
        Map<Integer, StockTask> stockTaskMap = stockTaskList.stream().collect(Collectors.toMap(StockTask::getId, Function.identity(), (a, b) -> a));
        return stockTaskItemList.stream().map(it -> {
            List<StockTaskItemDetailVO> itemDetails = it.getStockTaskItemDetailVOS();
            int actualOutQuantity = 0;
            if (!CollectionUtils.isEmpty(itemDetails)) {
                actualOutQuantity = itemDetails.stream().map(StockTaskItemDetailVO::getOutStoreQuantity)
                        .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
            }
            // 单据类型
            Integer type = stockTaskMap.get(it.getStockTaskId()).getType();
            // 城配仓
            Integer storeNo = stockTaskMap.get(it.getStockTaskId()).getOutStoreNo();
            LocalDateTime expectTime = stockTaskMap.get(it.getStockTaskId()).getExpectTime();
            return StockTaskPickDetailDTO.builder()
                    .cabinetOccupyList(it.getCabinetOccupyDOList())
                    .pdId(Long.valueOf(it.getPdId()))
                    .pdName(it.getPdName())
                    .packing(it.getPacking())
                    .weight(it.getWeight())
                    .isDomestic(it.getIsDomestic())
                    .skuType(it.getSkuType())
                    .firstLevelCategory(it.getFirstLevelCategory())
                    .secondLevelCategory(it.getSecondLevelCategory())
                    .shouldQuantity(it.getQuantity())
                    .actualGenPickQuantity(it.getActualGenPickQuantity())
                    .stockTaskItemId(it.getId())
                    .stockTaskId(it.getStockTaskId())
                    .sku(it.getSku())
                    .areaNo(areaNo)
                    .storeNo(storeNo)
                    .expectTime(expectTime)
                    .storageArea(it.getStorageArea())
                    .taskNo("")
                    .actualOutQuantity(actualOutQuantity)
                    .type(type)
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 打印任务详情信息
     *
     * @param stockTask 任务内容
     * @return 返回出库单打印详情
     */
    @Override
    public StockOutTaskPrintResp printTaskDetail(StockTask stockTask) {
        StockOutTaskPrintResp resp = StockOutTaskPrintResp.builder().build();
        // 出库时间 入库仓  出库仓
        resp.setExpectTime(stockTask.getExpectTime());
        resp.setStockTaskId(Long.valueOf(stockTask.getId()));
        resp.setType(StockTaskType.getNameById(stockTask.getType()));
        resp.setTaskType(stockTask.getType());
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageServiceV1.selectByWarehouseNo(stockTask.getAreaNo());
        resp.setOutStoreName(warehouseStorageCenter.getWarehouseName());
        resp.setReceiveName(Global.storeMap.getOrDefault(stockTask.getOutStoreNo(), ""));
        List<StockTaskItemVO> stockTaskItemVOS = selectItems(stockTask.getAreaNo().longValue(), stockTask.getId());
        boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        if (CollectionUtils.isEmpty(stockTaskItemVOS)) {
            return resp;
        }
        if (openCabinetManagement) {
            List<String> skuCodeList = stockTaskItemVOS.stream()
                    .map(StockTaskItemVO::getSku)
                    .distinct()
                    .collect(Collectors.toList());

            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepositoryV1
                    .selectListByStockTaskIdOccupyedExcludedJJV1(stockTask.getId(), skuCodeList);
            Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(StockTaskItemCabinetOccupyDO::getSku));

            for (StockTaskItemVO stockTaskItemVO : stockTaskItemVOS) {
                List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = skuQualityDateOrderMap.get(
                        stockTaskItemVO.getSku());
                if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                    int sum = stockTaskItemCabinetOccupyDOList.stream()
                            .map(StockTaskItemCabinetOccupyDO::getShouldPickQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    stockTaskItemVO.setActualGenPickQuantity(sum);
                }
            }
        } else {
            stockTaskItemVOS.forEach(it -> {
                List<StockTaskItemDetailVO> itemDetails = it.getStockTaskItemDetailVOS();
                if (!CollectionUtils.isEmpty(itemDetails)) {
                    int sum = itemDetails.stream().map(StockTaskItemDetailVO::getOutStoreQuantity)
                            .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum();
                    it.setActualGenPickQuantity(sum);
                }
            });
        }
        // 返回数据拼装
        List<StockTaskItemPrintDTO> items = handlerTaskPrintDataConvert(stockTaskItemVOS);
        resp.setItems(items);
        resp.setOpenCabinetManagement(openCabinetManagement);
        return resp;
    }

    private List<StockTaskItemVO> selectItems(Long warehouseNo, Integer id) {
        return selectItems(warehouseNo, com.alibaba.nacos.shaded.com.google.common.collect.Lists.newArrayList(id));
    }

    private List<StockTaskItemVO> selectItems(Long warehouseNo, List<Integer> ids) {
        List<StockTaskItemVO> itemVOs = stockTaskItemMapper.selectByIds(ids);
        List<String> skus = itemVOs.stream().map(StockTaskItemVO::getSku).distinct().collect(Collectors.toList());

        Lists.partition(skus, WmsConstant.PARTITION_SIZE).forEach(list -> {
            HashSet<String> skuSet = new HashSet<>(list);
            List<Product> productsFromGoodsCenter = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                    .warehouseNo(warehouseNo)
                    .skus(list).build());
            Map<String, Product> productMap = productsFromGoodsCenter.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));

            itemVOs.stream()
                    .filter(item -> skuSet.contains(item.getSku()))
                    .forEach(item -> {
                        Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                        item.setWeight(product.getSpecification());
                        item.setUnit(product.getPackaging());
                        item.setCategoryId(Objects.isNull(product.getCategoryId()) ? null : product.getCategoryId().intValue());
                        item.setPdId(Objects.isNull(product.getId()) ? null : product.getId().intValue());
                        item.setStorageLocation(product.getStorageLocation());
                        item.setCategoryType(product.getCategoryType());
                        item.setSkuType(product.getSkuType());
                        item.setPdName(product.getPdName());
                        item.setExtType(product.getSkuExeType());
                        item.setVolume(product.getVolume());
                        item.setWeightNum(product.getWeightNum());
                        item.setCategory(product.getCategory());
                        item.setIsDomestic(product.getIsDomestic());
                        item.setNameRemakes(adminUtil.getNameRemarks(product.getInventoryAdminId()));
                        item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                        item.setStorageArea(StorageLocationEnum.getDescByCode(product.getTemperature()));
                        item.setPacking(product.getPackaging());
                        item.setSku(product.getSku());
                        item.setCategoryType(product.getCategoryType());
                        item.setSecondLevelCategory(product.queryFirstCategoryName() + product.querySecondCategoryName()
                                + product.queryThirdCategoryName());
                        item.setStorageLocation(product.getStorageLocation());
                    });
        });

        return itemVOs.stream()
                .sorted(Comparator.comparing(StockTaskItemVO::compareCategoryType)
                        .thenComparing(StockTaskItemVO::getId).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 处理任务打印数据的转化
     *
     * @param stockTaskItemList 数据信息详情
     * @return 返回转化后的数据结构
     */
    private List<StockTaskItemPrintDTO> handlerTaskPrintDataConvert(List<StockTaskItemVO> stockTaskItemList) {
        return stockTaskItemList.stream().map(it -> {
            StockTaskItemPrintDTO itemPrint = StockTaskItemPrintDTO.builder()
                    .sku(it.getSku())
                    .pdName(it.getPdName())
                    .packaging(it.getPacking())
                    .specification(it.getWeight())
                    .shouldOutQuantity(it.getQuantity())
                    .actualGenPickQuantity(it.getActualGenPickQuantity())
                    .skuType(it.getSkuType())
                    .temperature(it.getStorageArea())
                    .origin(Objects.equals(NumberUtils.INTEGER_ZERO, it.getIsDomestic()) ? "进口" : "国产")
                    .build();

            // 库位相关信息
            List<StockTaskItemDetailVO> stockTaskItemDetailList = it.getStockTaskItemDetailVOS();
            if (CollectionUtils.isEmpty(stockTaskItemDetailList)) {
                return itemPrint;
            }
            List<StockTaskItemDetailPrintDTO> itemDetailPrintList = stockTaskItemDetailList.stream().map(item ->
                    StockTaskItemDetailPrintDTO.builder()
                            .cabinetNo(item.getCabinetCode())
                            .batchNo(item.getListNo())
                            .outQuantity(item.getOutStoreQuantity())
                            .produceTime(item.getProductionDate())
                            .shelfLife(item.getQualityDate())
                            .build()).collect(Collectors.toList());
            itemPrint.setDetails(itemDetailPrintList);
            return itemPrint;
        }).collect(Collectors.toList());
    }

    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {

    }

    /**
     * 生成excel表
     *
     * @param areaNo   库存仓
     * @param type     出库任务类型
     * @param taskList 出库任务列表
     * @return 生成的excel(文件名 + 文件实体)
     */
    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        // 返回的结果list
        List<MailWorkBookDTO> resultList = new ArrayList<>();
        // 根据不同出库任务的规则做最细粒度的拆分
        Map<String, List<StockTask>> tasks = taskList.stream()
                .collect(groupingBy(a -> a.getOutStoreNo() + Global.SEPARATING_SYMBOL +
                        LocalDateTime.of(a.getExpectTime().toLocalDate(), LocalTime.MIN).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        for (Map.Entry<String, List<StockTask>> entry : tasks.entrySet()) {
            Workbook workbook = new HSSFWorkbook();
            Sheet loadingSheet = workbook.createSheet("销售出库");
            List<StockTask> taskValue = entry.getValue();
            // 查询出库任务对应的item信息
            List<Integer> taskIdList = taskValue.stream().map(StockTask::getId).collect(Collectors.toList());
            List<StockTaskItemVO> itemVOList = selectItems(areaNo.longValue(), taskIdList);
            if (!CollectionUtils.isEmpty(itemVOList)) {
                itemVOList = wmsBuilderService.sortedFruitPriority(itemVOList);
            }

            // 设置实际出库数量
            itemVOList.forEach(item -> {
                List<StockTaskItemDetailVO> stockTaskItemDetailVOS = item.getStockTaskItemDetailVOS();
                int quantity = 0;
                if (!CollectionUtils.isEmpty(stockTaskItemDetailVOS)) {
                    quantity = stockTaskItemDetailVOS.stream().mapToInt(StockTaskItemDetailVO::getQuantity).sum();
                }
                item.setDetailQuantity(quantity);
            });

            // 合并SKU信息
            List<StockTaskItemVO> itemVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(itemVOList)) {
                itemVOS = new ArrayList<>(itemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, a -> a, (o1, o2) -> {
                    o1.setQuantity(o1.getQuantity() + o2.getQuantity());
                    o1.setDetailQuantity(o1.getDetailQuantity() + o2.getDetailQuantity());
                    return o1;
                })).values());
            }
            // 加载SKU信息
            loadingGoods(loadingSheet, type, taskValue, itemVOS);
            // 生成文件名
            StockTask stockTask = taskValue.get(0);
            String typeName = StockTaskType.getNameById(type);
            Long tmpAreaNo = null;
            if (Objects.nonNull(stockTask.getAreaNo())) {
                tmpAreaNo = Long.valueOf(stockTask.getAreaNo());
            }
            String fileName = stockTask.getOutStoreNo() == null ?
                    Global.warehouseMap.get(tmpAreaNo)
                            + typeName
                            + Global.WHITE_SPACE
                            + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            + Global.SEPARATING_SYMBOL
                            + System.currentTimeMillis()
                            + ".xls" :
                    Global.warehouseMap.get(tmpAreaNo)
                            + "至" + Global.storeMap.get(stockTask.getOutStoreNo())
                            + typeName
                            + Global.WHITE_SPACE
                            + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            + Global.SEPARATING_SYMBOL
                            + System.currentTimeMillis()
                            + ".xls";
            MailWorkBookDTO mailWorkBookDTO = new MailWorkBookDTO(fileName, workbook);
            resultList.add(mailWorkBookDTO);
        }
        return resultList;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult inOutStore(Integer type, String data) {

        if (!Objects.equals(StoreRecordType.SUPPLY_AGAIN_TASK.getId(), type)) {
            return AjaxResult.getErrorWithMsg("出库类型错误!");
        }
        List<StockTaskSaleOutReq> stockTaskSaleOutReqs = JSONObject.parseArray(data, StockTaskSaleOutReq.class);

        if (CollectionUtils.isEmpty(stockTaskSaleOutReqs)) {
            throw new DefaultServiceException("参数有误!");
        }

        StockTask task = stockTaskMapper.selectByPrimaryKey(stockTaskSaleOutReqs.get(0).getStockTaskId());
        if (task == null) {
            throw new DefaultServiceException("参数有误!");
        }
        if (task.getState().equals(StockTaskState.FINISH.getId())) {
            throw new DefaultServiceException("已完成的任务不可再次操作!");
        }

        boolean systemAuto = null != stockTaskSaleOutReqs.get(0).getSystemAuto() && Boolean.TRUE.equals(stockTaskSaleOutReqs.get(0).getSystemAuto());
        if (!systemAuto) {
            log.info("非系统自动处理，即将查询车辆信息");
            AjaxResult<Boolean> result = tmsTrunkService.haveTrunkMsg(task.getId());
            if (!result.getData()) {
                throw new DefaultServiceException("未选择车辆");
            }
        }

        String adminName = baseService.getAdminName();
        Long tenantId = SaasThreadLocalUtil.getTenantId();
        if (Objects.nonNull(tenantId) && tenantId > WmsConstant.XIANMU_TENANT_ID) {
            adminName = baseService.getTenantAccountName();
        }

        // 重构出库流程切流
        if (stockOutBranchConfig.isSwitch(task.getAreaNo())) {
            StockOutCommonCommandInput stockOutCommonCommandInput = stockOutCommonCommandInputFactory.buildSaleInput(stockTaskSaleOutReqs, task, tenantId, systemAuto);
            // 默认按20个sku分组切割明细
            List<StockOutCommonCommandInput> partitionStockOutCommonCommandInputList = stockOutCommonCommandInputFactory.partitionStockOutCommonCommandInput(stockOutCommonCommandInput);
            for (int i = 0; i < partitionStockOutCommonCommandInputList.size(); i++) {
                StockOutCommonCommandInput currentInput = partitionStockOutCommonCommandInputList.get(i);
                log.info("重构出库流程切流，出库任务：{}，调用批次：{}", currentInput.getStockTaskId(), i);
                stockOutCommonServiceFactory.getService(task.getType()).outStore(currentInput);
            }
            // 记录操作日志
            StockTaskOperationLogCommand stockTaskOperationLogCommand = StockTaskOperationLogCommand.builder()
                    .bizId(String.valueOf(task.getId()))
                    .bizType(task.getType())
                    .operator(null == baseService.getAdminId() ? null : baseService.getAdminId().toString())
                    .operatorName(baseService.getAdminName())
                    .operateType("确认出库")
                    .build();
            storeTaskLogCommandService.createStoreTaskLog(stockTaskOperationLogCommand);
            return AjaxResult.getOK();
        }

        if (OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            StockTaskWaveConfig waveConfig = stockTaskWaveRepositoryV1.queryWaveConfig(task.getAreaNo(), task.getOutStoreNo());
            WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(task.getOutStoreNo());
            if (waveConfig != null &&
                    net.summerfarm.common.util.StringUtils.isNotBlank(waveConfig.getWaveTime()) &&
                    warehouseLogisticsCenter != null &&
                    net.summerfarm.common.util.StringUtils.isNotBlank(warehouseLogisticsCenter.getCloseTime())) {
                LocalTime nowTime = LocalTime.now();
                LocalTime closeTime = LocalTime.parse(warehouseLogisticsCenter.getCloseTime());
                LocalTime closePlus10Time = closeTime.plusMinutes(1);
                LocalTime waveTime = LocalTime.parse(waveConfig.getWaveTime());
                if (waveTime.isAfter(closeTime)) {
                    log.error("出库波次配置异常，波次时间在截单时间后 {} {} \n1", waveTime, closeTime);
                    return AjaxResult.getErrorWithMsg("出库波次配置异常，波次时间在截单时间后");
                }
                // 当前时间在波次前
                if (LocalDateTime.now().isBefore(task.getExpectTime()) && nowTime.isAfter(waveTime) && nowTime.isBefore(closePlus10Time)) {
                    log.error("请在截单时间{}过后操作波次出库任务!", closeTime);
                    return AjaxResult.getErrorWithMsg("请在截单时间" + closeTime + "过后操作波次出库任务!");
                }

                // 初始化单据占用
                stockTaskOrderCancelService.handleStockTaskWaveOccupyInit(task.getId());
            }
        }


        //校验出库数据
        stockTaskSaleOutReqs.stream().forEach(x -> {
            List<StockTaskItemDetailVO> stockTaskItemDetailVOS = x.getStockTaskItemDetailVOS();
            if (!CollectionUtils.isEmpty(stockTaskItemDetailVOS)) {
                stockTaskItemDetailVOS.stream().forEach(y -> {
                    Integer quantity = y.getQuantity();
                    if (quantity < 0) {
                        throw new DefaultServiceException("出库数量不能小于0");
                    }
                });
            }
        });

        //新增出库单
        StockTaskProcess process = new StockTaskProcess();
        process.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        process.setAddtime(LocalDateTime.now());
        process.setRecorder(adminName);
        process.setTenantId(tenantId);
        stockTaskProcessMapper.insert(process);
        // 新增物流信息
        if (baseService.isSaasRequest()) {
            StockTaskProcessLogisticsOrder logisticsOrder = stockTaskSaleOutReqs.get(0).getLogisticsOrder();
            StockTaskProcessLogisticsOrderDO logisticsOrderDO = StockTaskProcessLogisticsOrderDO.builder()
                    .stockTaskProcessId(process.getId().longValue())
                    .deliveryType(logisticsOrder.getDeliveryType())
                    .deliveryOrderNo(logisticsOrder.getDeliveryOrderNo())
                    .company(logisticsOrder.getCompany())
                    .remark(logisticsOrder.getRemark())
                    .creator(process.getRecorder())
                    .operator(process.getRecorder())
                    .gmtCreated(new Date())
                    .isDeleted(0)
                    .lastVer(1)
                    .build();
            logisticsOrderMapper.insert(logisticsOrderDO);
        }
        // 完成出库拣货任务号
        Set<String> missionNoSet = new HashSet<>();
        // 波次任务完成处理sku
        Map<String, Integer> skuHandledMap = new HashMap<>();
        // 更新mapping状态
        List<Long> updateStatusMappingIdList = new ArrayList<>();
        //排序
        stockTaskSaleOutReqs.sort(Comparator.comparing(StockTaskSaleOutReq::getSku));
        for (StockTaskSaleOutReq req : stockTaskSaleOutReqs) {
            selfService.handleStockTaskSaleOutReq(req, adminName, task, process.getId(), missionNoSet, skuHandledMap, updateStatusMappingIdList);
        }
        List<String> skuList = stockTaskSaleOutReqs.stream().map(StockTaskSaleOutReq::getSku).distinct().collect(Collectors.toList());
        stockTaskProcessOrderSkuService.saveStockTaskProcessOrderSku(process.getId(), skuList);

        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(task.getAreaNo());
        if (openCabinetManagement) {
            // 波次任务
            if (OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
                // 库位精细化出库取当前已完成拣货任务应拣数量作为释放数量
                if (!CollectionUtils.isEmpty(updateStatusMappingIdList)) {
                    log.info("波次出库任务本次更新mapping集合id：{}", updateStatusMappingIdList);
                    MissionTaskItemMappingResp completeMissionTaskItemMappingResp = missionTaskItemMappingQueryService.queryMissionTaskItemMappingByIds(updateStatusMappingIdList);
                    if (null != completeMissionTaskItemMappingResp && !CollectionUtils.isEmpty(completeMissionTaskItemMappingResp.getMissionTaskItemMappings())) {
                        log.info("波次出库任务本次更新mapping集合数据：{}", JSON.toJSONStringWithDateFormat(completeMissionTaskItemMappingResp.getMissionTaskItemMappings(), DateUtil.YYYY_MM_DD));
                        Map<String, List<MissionTaskItemMappingDTO>> groupSkuMappingMap = completeMissionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(groupingBy(MissionTaskItemMappingDTO::getSku));
                        groupSkuMappingMap.forEach((sku, mappingList) -> {
                            Integer shouldPickQuantity = mappingList.stream().filter(item -> item.getInitPickQuantity() != null && item.getInitPickQuantity() > 0).map(MissionTaskItemMappingDTO::getInitPickQuantity).reduce(Integer::sum).orElse(0);
                            log.info("波次出库数量，任务id：" + task.getId() + "，sku：" + sku + "，应出数量：" + shouldPickQuantity);
                            // 出库波次SKU占用
                            List<StockTaskWaveSkuOccupyDO> waveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository.selectListByStockTaskId(task.getId(), Arrays.asList(sku));
                            log.info("波次占用记录，出库任务：{}，{}", task.getId(), JSON.toJSONStringWithDateFormat(waveSkuOccupyDOList, DateUtil.YYYY_MM_DD));
                            if (!CollectionUtils.isEmpty(waveSkuOccupyDOList)) {
                                log.info("任务id：{}，当前sku：{}未处理，执行库存处理", task.getId(), sku);
//                                StockTaskWaveSkuOccupyDO stockTaskWaveSkuOccupyDO = waveSkuOccupyDOList.get(0);
                                stockTaskOrderCancelService.handleStockTaskWaveOccupyReduce(task.getId(), sku, shouldPickQuantity, null);
//                                skuHandledMap.put(sku, stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
                            }
                        });
                    }
                }
            }
            // 灰度配置
            Boolean greyWarehouse = stockOutWarehouseQueryService.isGreyWarehouse(task.getAreaNo());
            if (Boolean.TRUE.equals(greyWarehouse)) {
                // 存在缺拣或缺出情况释放差异冻结
                releaseInventoryLockWithDiffOutForGrey(task, updateStatusMappingIdList);
            } else {
                // 存在缺拣或缺出情况释放差异冻结
                if (!CollectionUtils.isEmpty(updateStatusMappingIdList)) {
                    MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryMissionTaskItemMappingByIds(updateStatusMappingIdList);
                    log.info("查询缺出数据，出库任务：{}，{}", task.getId(), JSON.toJSONStringWithDateFormat(missionTaskItemMappingResp, DateUtil.YYYY_MM_DD));
                    if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                        Map<String, List<MissionTaskItemMappingDTO>> groupSkuMappingDTOList = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(groupingBy(MissionTaskItemMappingDTO::getSku));
                        groupSkuMappingDTOList.forEach((sku, mappingList) -> {
                            Integer initPickQuantity = mappingList.stream().filter(item -> item.getInitPickQuantity() != null && item.getInitPickQuantity() > 0).map(MissionTaskItemMappingDTO::getInitPickQuantity).reduce(Integer::sum).orElse(0);
                            Integer actualQuantity = mappingList.stream().filter(item -> item.getActualQuantity() != null && item.getActualQuantity() > 0).map(MissionTaskItemMappingDTO::getActualQuantity).reduce(Integer::sum).orElse(0);
                            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                            if (!initPickQuantity.equals(actualQuantity)) {
                                log.info("存在缺出库存处理，出库任务：{}，{}", task.getId(), JSON.toJSONStringWithDateFormat(mappingList, DateUtil.YYYY_MM_DD));
                                // 如果缺出数量小于订单取消数量会存在虚拟库存变多情况，风险已提出，产品侧按原方案执行，暂时注释，统一处理逻辑
//                            Integer notRemainQuantity = skuHandledMap.get(sku);
//                            log.info("存在缺出库存处理订单取消数据，出库任务：{}，{}", task.getId(), sku + ":" + notRemainQuantity);
//                            int diffQuantity;
//                            if (notRemainQuantity != null && notRemainQuantity > 0 && actualQuantity <= notRemainQuantity) {
//                                diffQuantity = initPickQuantity - actualQuantity - notRemainQuantity;
//                                log.info("存在缺出库存处理，实出小于等于取消数量，出库任务：{}，需调整数量：{}", task.getId(), diffQuantity);
//                            } else {
//                            }
                                int diffQuantity = initPickQuantity - actualQuantity;
                                log.info("存在缺出库存处理，实出大于取消数量，出库任务：{}，需调整数量：{}", task.getId(), diffQuantity);
                                // 非波次任务
                                if (!OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
                                    // 释放缺拣或缺出产生差异冻结库存
                                    areaStoreServiceV1.updateLockStockByWarehouseNo(-diffQuantity, sku, task.getOutStoreNo(), task.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, task.getId() + "", recordMap);
                                }
                                // 增加虚拟
                                areaStoreServiceV1.updateOnlineStockByStoreNo(true, diffQuantity, sku, task.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, task.getId() + "", recordMap, NumberUtils.INTEGER_ONE);
                            }
                            quantityChangeRecordServiceV1.insertRecord(recordMap);
                            List<Long> idList = mappingList.stream().map(MissionTaskItemMappingDTO::getId).collect(Collectors.toList());
                            missionTaskItemMappingCommandService.updateDiffConfirmedOutStatus(idList);
                        });
                    }
                }
            }
            // 记录操作日志
            StockTaskOperationLogCommand stockTaskOperationLogCommand = StockTaskOperationLogCommand.builder()
                    .bizId(String.valueOf(task.getId()))
                    .bizType(task.getType())
                    .operator(null == baseService.getAdminId() ? null : baseService.getAdminId().toString())
                    .operatorName(baseService.getAdminName())
                    .operateType("确认出库")
                    .build();
            storeTaskLogCommandService.createStoreTaskLog(stockTaskOperationLogCommand);
            // 释放容器
            if (!missionNoSet.isEmpty()) {
                // 发送拣货完成释放容器消息
                ContainerReleaseForPickEvent containerReleaseForPickEvent = ContainerReleaseForPickEvent.builder()
                        .missionNoList(new ArrayList<>(missionNoSet))
                        .build();
                SendResultDTO sendResultDTO = mqProducer.send(WmsConstant.MQ_TOPIC, WmsConstant.CONTAINER_RELEASE_PICK_TAG, containerReleaseForPickEvent);
                log.info("发送拣货完成释放容器消息，消息体：{}，结果：{}", JSON.toJSONString(containerReleaseForPickEvent), JSON.toJSONString(sendResultDTO));
            }
        }

        boolean close = closeStockTask(stockTaskSaleOutReqs.get(0).getStockTaskId(), openCabinetManagement);
        StockTask update = new StockTask();
        update.setId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        List<StockTaskItem> items = stockTaskItemMapper.select(select);
        update.setState(StockTaskState.PART_IN_OUT.getId());
        if (close) {
            update.setState(StockTaskState.FINISH.getId());
            tmsTrunkService.sendFinishStock(stockTaskSaleOutReqs.get(0).getStockTaskId());
            // 检验多出入库数量
            stockTaskOrderCancelService.checkOutMoreInQuantity(task.getId());
            log.info("任务编号:" + stockTaskSaleOutReqs.get(0).getStockTaskId() + "关闭");
        }
        update.setUpdatetime(LocalDateTime.now());
        stockTaskMapper.update(update);
        items.sort(Comparator.comparing(StockTaskItem::getSku));
        for (StockTaskItem item : items) { //修改sku状态、采购预警
            areaStoreServiceV1.updateAreaStoreStatus(stockTaskSaleOutReqs.get(0).getAreaNo(), item.getSku());
            purchasesConfigService.msgArrival(stockTaskSaleOutReqs.get(0).getAreaNo(), item.getSku());
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                stockTaskMsgService.handleStockTaskSkuOutMsg(process.getId());
                handlerOutMoreInTask(task.getId());
            }
        });
        log.info("{}添加库存记录:{}", adminName, type);
        return AjaxResult.getOK();
    }

    /**
     * 处理多出入库任务
     * @param id 入库任务id
     */
    private void handlerOutMoreInTask(Integer id) {
        // 查询任务信息
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(id);
        if (Objects.isNull(stockTask)) {
            log.warn("未查询到出库任务信息 stockTaskId:{}", id);
            return;
        }
        if (!Objects.equals(StockTaskStateEnum.FINISH.getId(), stockTask.getState())) {
            log.info("当前出库未完结，不触发变更多出入库消息 stockTaskId:{}", id);
            return;
        }
        // 销售出库 补发出库 出样出库 越库出库
        List<Integer> stockTaskTypeList = Lists.newArrayList(StockTaskType.SALE_OUT.getId(),
                StockTaskType.SUPPLY_AGAIN_TASK.getId(),
                StockTaskType.CROSS_OUT.getId(),
                StockTaskType.DEMO_OUT.getId());
        if (!stockTaskTypeList.contains(stockTask.getType())) {
            log.info("当前出库任务不是 销售出库/补发出库/出样出库/越库出库, 不执行多出入库变更");
            return;
        }
        // 释放数量 = 缺出数量 通过出库任务查询对应的多出入库
        if (stockOutWarehouseQueryService.isGreyWarehouse(stockTask.getAreaNo())) {
            log.info("出库灰度仓库:{} 更新多出入库信息内容", stockTask.getAreaNo());
            OutMoreInStockStorageChangeEvent storageChangeEvent = OutMoreInStockStorageChangeEvent.builder()
                    .stockTaskId(id)
                    .warehouseNo(stockTask.getAreaNo())
                    .build();
            mqProducer.send(Global.STOCK_TASK, "tag_wms_out_more_in_change", storageChangeEvent);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void handleStockTaskSaleOutReq(StockTaskSaleOutReq req, String adminName, StockTask stockTask, Integer processId, Set<String> missionNoSet, Map<String, Integer> skuHandledMap, List<Long> updateStatusMappingIdList) {

        List<StockTaskItemDetailVO> stockTaskItemDetails = req.getStockTaskItemDetailVOS();

        if (CollectionUtils.isEmpty(stockTaskItemDetails)) {
            return;
        }

        String sku = req.getSku();
        Integer type = stockTask.getType();
        Integer outQuantity = stockTaskItemDetails.stream().mapToInt(StockTaskItemDetail::getQuantity).sum(); //即将出(入)库数量
        StockTaskItem selectKey = new StockTaskItem();
        selectKey.setSku(req.getSku());
        selectKey.setStockTaskId(req.getStockTaskId());
        List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(selectKey);
        if (CollectionUtils.isEmpty(stockTaskItems)) {
            throw new DefaultServiceException("参数有误!");
        }
        StockTaskItem item = stockTaskItems.get(0);
        Integer areaNo = req.getAreaNo();
        if (outQuantity + item.getActualQuantity() > item.getQuantity()) {
            throw new DefaultServiceException("sku:" + req.getSku() + "出库数量大于任务数量");
        }
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        //出入库单详情
        List<StockTaskProcessDetail> processDetails = new ArrayList<>();

        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = new HashMap<>();
        Map<String, List<StockTaskItemCabinetOccupyDO>> originSkuQualityDateOrderMap = new HashMap<>();
        Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateJJMap = new HashMap<>();
        List<Long> updateMappingIdList = new ArrayList<>();
        if (openCabinetManagement) {
            List<String> skuCodeList = stockTaskItemDetails.stream()
                    .map(StockTaskItemDetail::getSku)
                    .distinct()
                    .collect(Collectors.toList());
            // 校验是否已完成拣货
            MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
            missionTaskItemMappingSkuQuery.setStockTaskId(stockTask.getId().toString());
            missionTaskItemMappingSkuQuery.setSkus(Collections.singletonList(sku));
            MissionTaskItemMappingResp noPickedMissionTaskItemMapping = missionTaskItemMappingQueryService.queryNoPickedMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
            if (null == noPickedMissionTaskItemMapping || !CollectionUtils.isEmpty(noPickedMissionTaskItemMapping.getMissionTaskItemMappings())) {
                log.info("出库任务：{}，sku：{}未完成拣货", stockTask.getId(), sku);
                throw new BizException("当前出库任务，sku：" + sku + "未完成拣货，无法操作");
            }
            MissionTaskItemMappingResp completePickedMissionTaskItemMapping = missionTaskItemMappingQueryService.queryCompletePickedMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
            if (null == completePickedMissionTaskItemMapping || CollectionUtils.isEmpty(completePickedMissionTaskItemMapping.getMissionTaskItemMappings())) {
                log.info("出库任务：{}，sku：{}无完成拣货数据", stockTask.getId(), sku);
                throw new BizException("当前出库任务，sku：" + sku + "无完成拣货数据，无法操作");
            }
            List<String> missionList = completePickedMissionTaskItemMapping.getMissionTaskItemMappings().stream().map(MissionTaskItemMappingDTO::getMissionNo).distinct().collect(Collectors.toList());
            missionNoSet.addAll(missionList);

            // 查询交接库位【JJ01】占用
            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepositoryV1
                    .selectListByStockTaskIdOccupyedForJJ(stockTask.getId(), skuCodeList);
            skuQualityDateOrderMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> s.getSku() + "_" + DateUtil.formatYmdDate(s.getQualityDate())));
            skuQualityDateJJMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> getGroupKey(s.getSku(), s.getCabinetCode(), s.getProductionDate(), s.getQualityDate())));

            // 原库位占用，存在多余冻结兜底
            List<StockTaskItemCabinetOccupyDO> originStockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepositoryV1
                    .selectListByStockTaskIdOccupyedExcludedJJV1(stockTask.getId(), skuCodeList);
            originSkuQualityDateOrderMap = originStockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> getGroupKey(s.getSku(), s.getCabinetCode(), s.getProductionDate(), s.getQualityDate())));
        }

        if (openCabinetManagement) {
            for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {
                // 原库位
                String originCabinetCode = stockTaskItemDetail.getCabinetCode();
                List<CabinetInventoryOccupyReduceBatchRespDTO> occupyReduceBatchRespDTOList = new ArrayList<>();
                // 出库数量大于0
                if (stockTaskItemDetail.getQuantity() > 0) {
                    // 库存已在JJ01库位上
                    stockTaskItemDetail.setCabinetCode(DefaultCabinetCodeEnum.JJ.getCode());
                    List<StockTaskItemCabinetOccupyDO> skuOccupyList = skuQualityDateOrderMap.get(
                            req.getSku() + "_" + DateUtil.formatYmdDate(stockTaskItemDetail.getQualityDate()));
                    if (CollectionUtils.isEmpty(skuOccupyList)) {
                        throw new DefaultServiceException("sku:" + req.getSku() + "没有锁定库位库存，请稍后进行重试");
                    }
                    if (stockTaskItemDetail.getQuantity() <= 0) {
                        throw new DefaultServiceException("sku:" + req.getSku() + "请求出库数量小于等于0");
                    }

                    // 精细化仓批次、库位库存处理
                    CabinetInventoryOccupyReduceReqDTO occupyReduceReqDTO = StockTaskItemDetailUpdateFactory.newInstance(stockTask, stockTaskItemDetail, baseService.getAdminName());
                    occupyReduceBatchRespDTOList = cabinetInventoryFacade.occupyReduceCabinetInventory(occupyReduceReqDTO);

                    // 根据库位信息查询批次库存信息
                    List<StockTaskItemDetail> batchItemDetailList = StockTaskItemDetailUpdateFactory.matchStoreRecordQuantity(
                            stockTaskItemDetail, occupyReduceBatchRespDTOList);
                    batchItemDetailList.forEach(batchItemDetail ->
                            handleStoreRecord(req, adminName, type, areaNo, stockTaskItemDetail,
                                    batchItemDetail.getQuantity(), batchItemDetail.getListNo(), openCabinetManagement, stockTask)
                    );

                    // 处理库位占用更新
                    stockTaskItemCabinetOccupyRepositoryV1.handlerCabinetOccupyItem(skuOccupyList, occupyReduceBatchRespDTOList);
                }

                // 更新出库拣货映射数据
                MissionTaskItemMappingActualCommand missionTaskItemMappingActualCommand = new MissionTaskItemMappingActualCommand();
                missionTaskItemMappingActualCommand.setStockTaskId(Long.valueOf(stockTask.getId()));
                missionTaskItemMappingActualCommand.setSku(stockTaskItemDetail.getSku());
                missionTaskItemMappingActualCommand.setCabinetCode(originCabinetCode);
                missionTaskItemMappingActualCommand.setProductionDate(DateUtil.toDate(stockTaskItemDetail.getProductionDate()));
                missionTaskItemMappingActualCommand.setQualityDate(DateUtil.toDate(stockTaskItemDetail.getQualityDate()));
                missionTaskItemMappingActualCommand.setActualQuantity(stockTaskItemDetail.getQuantity());
                missionTaskItemMappingCommandService.updateActualQuantityAndStatus(missionTaskItemMappingActualCommand, updateMappingIdList);
                updateStatusMappingIdList.addAll(updateMappingIdList);

                for (CabinetInventoryOccupyReduceBatchRespDTO cabinetInventoryOccupyReduceBatchRespDTO : occupyReduceBatchRespDTOList) {
                    //出库单详情
                    StockTaskProcessDetail processDetail = new StockTaskProcessDetail();
                    processDetail.setSku(req.getSku());
                    processDetail.setListNo(cabinetInventoryOccupyReduceBatchRespDTO.getBatchNo());
                    processDetail.setQuantity(cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity());
                    processDetail.setQualityDate(DateUtil.toLocalDate(cabinetInventoryOccupyReduceBatchRespDTO.getQualityDate()));
                    processDetail.setProductionDate(DateUtil.toLocalDate(cabinetInventoryOccupyReduceBatchRespDTO.getProduceDate()));
                    processDetail.setRemark(stockTaskItemDetail.getRemark());
                    processDetail.setStockTaskProcessId(processId);
                    processDetails.add(processDetail);
                }
                // 回写原库位
                stockTaskItemDetail.setCabinetCode(originCabinetCode);
            }
        } else {
            for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {
                Integer quantity = stockTaskItemDetail.getQuantity(); // 出库数量
                handleStoreRecord(req, adminName, type, areaNo, stockTaskItemDetail, quantity,
                        stockTaskItemDetail.getListNo(), openCabinetManagement, stockTask);


                //出库单详情
                StockTaskProcessDetail processDetail = new StockTaskProcessDetail();
                processDetail.setSku(req.getSku());
                processDetail.setListNo(stockTaskItemDetail.getListNo());
                processDetail.setQuantity(quantity);
                processDetail.setQualityDate(stockTaskItemDetail.getQualityDate());
                processDetail.setProductionDate(stockTaskItemDetail.getProductionDate());
                processDetail.setRemark(stockTaskItemDetail.getRemark());
                processDetail.setStockTaskProcessId(processId);
                processDetails.add(processDetail);
            }
        }

        for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {

            Integer quantity = stockTaskItemDetail.getQuantity(); // 出库数量

            //更新出库任务条目详情
            StockTaskItemDetail selectKeys = new StockTaskItemDetail();
            selectKeys.setCabinetCode(stockTaskItemDetail.getCabinetCode());
            selectKeys.setListNo(stockTaskItemDetail.getListNo());
            selectKeys.setStockTaskItemId(req.getId());
            selectKeys.setSku(req.getSku());
            selectKeys.setQualityDate(stockTaskItemDetail.getQualityDate());
            selectKeys.setProductionDate(stockTaskItemDetail.getProductionDate());

            StockTaskItemDetail record = stockTaskItemDetailMapper.selectOne(selectKeys);
            //新增详情
            if (record == null) {
                StockTaskItemDetail insert = createItemDetail(stockTaskItemDetail, req.getId(), req.getSku(), quantity);
                stockTaskItemDetailMapper.insert(insert);
                //修改详情
            } else {
                StockTaskItemDetail update = new StockTaskItemDetail();
                update.setId(record.getId());
                update.setQuantity(record.getQuantity() + quantity);
                update.setOutStoreQuantity(quantity + record.getOutStoreQuantity());
                stockTaskItemDetailMapper.update(update);
            }
        }
        //更新已出库数量
        StockTaskItem update = new StockTaskItem();
        update.setId(item.getId());
        update.setActualQuantity(outQuantity);
        int updateCount = stockTaskItemMapper.updateActualQuantity(update);
        if (updateCount <= 0) {
            throw new BizException("出库任务【" + stockTask.getId() + "】条目更新并发，请稍后再试");
        }
        List<DiffPickingMoveDTO> diffPickingMoveDTOList = new ArrayList<>();
        // 波次出库任务不操作冻结库存，可能存在销售未到货退款释放冻结情况
        if (!OptionFlagUtil.hasValue(stockTask.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            // 释放冻结库存
            areaStoreServiceV1.updateLockStockByWarehouseNo(-outQuantity, req.getSku(), stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap);
            if (Boolean.TRUE.equals(openCabinetManagement)) {
                // 存在缺拣或缺出情况释放差异冻结
                MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
                missionTaskItemMappingSkuQuery.setStockTaskId(stockTask.getId().toString());
                missionTaskItemMappingSkuQuery.setSkus(Collections.singletonList(sku));
                MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryDiffConfirmedMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
                Map<Long, Integer> occupyReleaseMap = new HashMap<>();
                if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                    log.info("存在缺拣或缺出情况释放差异冻结，出库任务id：{}，diff：{}", req.getStockTaskId(), JSON.toJSONStringWithDateFormat(missionTaskItemMappingResp, DateUtil.YYYY_MM_DD));
                    AtomicReference<Integer> diffQuantity = new AtomicReference<>(0);
                    Map<String, List<StockTaskItemCabinetOccupyDO>> finalSkuQualityDateJJMap = skuQualityDateJJMap;
                    missionTaskItemMappingResp.getMissionTaskItemMappings().forEach(diffItem -> {
                        DiffPickingMoveDTO diffPickingMoveDTO = DiffPickingMoveDTO.builder()
                                .warehouseNo(Long.valueOf(stockTask.getAreaNo()))
                                .sku(sku)
                                .produceTime(DateUtil.toLocalDate(diffItem.getProductionDate()))
                                .shelfLife(DateUtil.toLocalDate(diffItem.getQualityDate()))
                                .moveCabinetNo(DefaultCabinetCodeEnum.JJ.getCode())
                                .targetCabinetNo(DefaultCabinetCodeEnum.CY.getCode())
                                .quantity(diffItem.getAbnormalQuantity())
                                .abnormalOutQuantity(diffItem.getPickQuantity() - diffItem.getActualQuantity())
                                .isAbnormalOut(diffItem.getPickQuantity() - diffItem.getActualQuantity() != 0)
                                .operatorName(baseService.getAdminName())
                                .operatorId(null == baseService.getAdminId() ? null : baseService.getAdminId().toString())
                                .build();
                        diffPickingMoveDTOList.add(diffPickingMoveDTO);
                        diffQuantity.updateAndGet(v -> v + (diffItem.getPickQuantity() - diffItem.getActualQuantity()));
                        if (Boolean.TRUE.equals(diffPickingMoveDTO.getIsAbnormalOut())) {
                            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = finalSkuQualityDateJJMap.get(getGroupKey(sku, DefaultCabinetCodeEnum.JJ.getCode(), diffItem.getProductionDate(), diffItem.getQualityDate()));
                            if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                                Long id = stockTaskItemCabinetOccupyDOList.get(0).getId();
                                occupyReleaseMap.put(id, diffItem.getPickQuantity() - diffItem.getActualQuantity());
                            }
                        }
                    });
//                    if (diffQuantity.get() > 0) {
//                        // 释放缺拣或缺出产生差异冻结库存
//                        areaStoreServiceV1.updateLockStockByWarehouseNo(-diffQuantity.get(), sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap);
//                        // 增加虚拟
//                        areaStoreServiceV1.updateOnlineStockByStoreNo(true, diffQuantity.get(), sku, stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap, NumberUtils.INTEGER_ONE);
//                    }
                    // 更新确认出库异常状态
                    List<Long> idList = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().map(MissionTaskItemMappingDTO::getId).collect(Collectors.toList());
                    missionTaskItemMappingCommandService.updateDiffConfirmedStatus(idList);
                    // 存在缺出情况释放多余占用
                    List<DiffPickingMoveDTO> filterAbnormalOutList = diffPickingMoveDTOList.stream().filter(moveDTO -> Boolean.TRUE.equals(moveDTO.getIsAbnormalOut())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterAbnormalOutList)) {
                        // 释放库位库存请求实体
                        CabinetInventoryReleaseReqDTO cabinetInventoryReleaseReqDTO = new CabinetInventoryReleaseReqDTO();
                        cabinetInventoryReleaseReqDTO.setWarehouseNo(stockTask.getAreaNo());
                        cabinetInventoryReleaseReqDTO.setOperatorType(CabinetInventoryOperationType.TRANSFER_OUT.getId());
                        cabinetInventoryReleaseReqDTO.setOrderTypeName(StoreRecordType.SUPPLY_AGAIN_TASK.getName());
                        cabinetInventoryReleaseReqDTO.setTenantId(stockTask.getTenantId());
                        cabinetInventoryReleaseReqDTO.setOrderNo(stockTask.getId().toString());
                        cabinetInventoryReleaseReqDTO.setReleaseDetailReqList(new ArrayList<>());
                        cabinetInventoryReleaseReqDTO.setOperatorName(baseService.getAdminName());
                        filterAbnormalOutList.forEach(outItem -> {
                            // 库位库存释放明细
                            CabinetInventoryReleaseDetailReqDTO cabinetInventoryReleaseDetailReqDTO = new CabinetInventoryReleaseDetailReqDTO();
                            cabinetInventoryReleaseDetailReqDTO.setCabinetCode(outItem.getMoveCabinetNo());
                            cabinetInventoryReleaseDetailReqDTO.setSkuCode(outItem.getSku());
                            cabinetInventoryReleaseDetailReqDTO.setReleaseQuantity(outItem.getAbnormalOutQuantity());
                            cabinetInventoryReleaseDetailReqDTO.setProduceDate(DateUtil.toDate(outItem.getProduceTime()));
                            cabinetInventoryReleaseDetailReqDTO.setQualityDate(DateUtil.toDate(outItem.getShelfLife()));
                            cabinetInventoryReleaseReqDTO.getReleaseDetailReqList().add(cabinetInventoryReleaseDetailReqDTO);
                        });
                        log.info("出库存在缺出释放库位库存入参：{}", JSON.toJSONStringWithDateFormat(cabinetInventoryReleaseReqDTO, "yyyy-MM-dd"));
                        List<CabinetInventoryReleaseDetail> cabinetInventoryReleaseDetails = cabinetInventoryFacade.releaseCabinetInventory(cabinetInventoryReleaseReqDTO);
                        log.info("出库存在缺出释放库位库存返回：{}", JSON.toJSONStringWithDateFormat(cabinetInventoryReleaseDetails, "yyyy-MM-dd"));
                        // 更新occupy数量
                        occupyReleaseMap.forEach((id, release) -> {
                            stockTaskItemCabinetOccupyRepositoryV1.updateReleaseChange(id, release);
                            log.info("差异数量释放，id：{}，释放数量：{}", id, release);
                        });
                    }
                }
            }
        } else {
            // 外层处理波次任务
//            // 库位精细化出库取当前已完成拣货任务应拣数量作为释放数量
//            AtomicReference<Integer> shouldPickQuantity = new AtomicReference<>(0);
//            if (!CollectionUtils.isEmpty(updateMappingIdList)) {
//                log.info("波次出库任务本次更新mapping集合id：{}", updateMappingIdList);
//                MissionTaskItemMappingResp completeMissionTaskItemMappingResp = missionTaskItemMappingQueryService.queryMissionTaskItemMappingByIds(updateMappingIdList);
//                if (null != completeMissionTaskItemMappingResp && !CollectionUtils.isEmpty(completeMissionTaskItemMappingResp.getMissionTaskItemMappings())) {
//                    log.info("波次出库任务本次更新mapping集合数据：{}", JSON.toJSONStringWithDateFormat(completeMissionTaskItemMappingResp.getMissionTaskItemMappings(), DateUtil.YYYY_MM_DD));
//                    List<MissionTaskItemMappingDTO> confirmMappingList = completeMissionTaskItemMappingResp.getMissionTaskItemMappings();
//                    Map<String, List<MissionTaskItemMappingDTO>> groupMissionNoMap = confirmMappingList.stream().collect(groupingBy(MissionTaskItemMappingDTO::getMissionNo));
//                    groupMissionNoMap.forEach((missionNo, mappingList) -> {
//                        mappingList.sort(Comparator.comparing(MissionTaskItemMappingDTO::getCreateTime).thenComparing(MissionTaskItemMappingDTO::getId));
//                        MissionTaskItemMappingDTO missionTaskItemMappingDTO = mappingList.get(0);
//                        int quantity = missionTaskItemMappingDTO.getInitPickQuantity() != null && missionTaskItemMappingDTO.getInitPickQuantity() > 0 ? missionTaskItemMappingDTO.getInitPickQuantity() : missionTaskItemMappingDTO.getShouldPickQuantity();
//                        shouldPickQuantity.updateAndGet(v -> v + quantity);
//                    });
//                }
//            }
//            log.info("补货波次出库数量，任务id：" + stockTask.getId() + "，sku：" + sku + "，出库数量：" + outQuantity + "，应出数量：" + shouldPickQuantity.get());
//            // 扣减单据占用
//            if (!skuHandledMap.containsKey(sku)) {
//                List<StockTaskWaveSkuOccupyDO> waveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository.selectListByStockTaskId(stockTask.getId(), Arrays.asList(sku));
//                if (!CollectionUtils.isEmpty(waveSkuOccupyDOList)) {
//                    log.info("任务id：{}，当前sku：{}未处理，执行库存处理", stockTask.getId(), sku);
//                    StockTaskWaveSkuOccupyDO stockTaskWaveSkuOccupyDO = waveSkuOccupyDOList.get(0);
//                    if (shouldPickQuantity.get() > 0 && !outQuantity.equals(shouldPickQuantity.get())) {
//                        stockTaskOrderCancelService.handleStockTaskWaveOccupyReduce(stockTask.getId(), sku, shouldPickQuantity.get());
//                        skuHandledMap.put(sku, stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
//                    } else {
//                        stockTaskOrderCancelService.handleStockTaskWaveOccupyReduce(stockTask.getId(), sku, outQuantity);
//                        skuHandledMap.put(sku, stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
//                    }
//                }
//            } else {
//                log.info("任务id：{}，当前sku：{}已处理，过滤", stockTask.getId(), sku);
//            }
            if (Boolean.TRUE.equals(openCabinetManagement)) {
                // 存在缺拣或缺出情况释放差异冻结
                MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
                missionTaskItemMappingSkuQuery.setStockTaskId(stockTask.getId().toString());
                missionTaskItemMappingSkuQuery.setSkus(Collections.singletonList(sku));
                MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryDiffConfirmedMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
                Map<Long, Integer> occupyReleaseMap = new HashMap<>();
                if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                    log.info("补货存在缺拣或缺出情况释放差异冻结，出库任务id：{}，diff：{}", req.getStockTaskId(), JSON.toJSONStringWithDateFormat(missionTaskItemMappingResp, DateUtil.YYYY_MM_DD));
                    AtomicReference<Integer> diffQuantity = new AtomicReference<>(0);
                    Map<String, List<StockTaskItemCabinetOccupyDO>> finalSkuQualityDateJJMap = skuQualityDateJJMap;
                    missionTaskItemMappingResp.getMissionTaskItemMappings().forEach(diffItem -> {
                        DiffPickingMoveDTO diffPickingMoveDTO = DiffPickingMoveDTO.builder()
                                .warehouseNo(Long.valueOf(stockTask.getAreaNo()))
                                .sku(sku)
                                .produceTime(DateUtil.toLocalDate(diffItem.getProductionDate()))
                                .shelfLife(DateUtil.toLocalDate(diffItem.getQualityDate()))
                                .moveCabinetNo(DefaultCabinetCodeEnum.JJ.getCode())
                                .targetCabinetNo(DefaultCabinetCodeEnum.CY.getCode())
                                .quantity(diffItem.getAbnormalQuantity())
                                .abnormalOutQuantity(diffItem.getPickQuantity() - diffItem.getActualQuantity())
                                .isAbnormalOut(diffItem.getPickQuantity() - diffItem.getActualQuantity() != 0)
                                .operatorName(baseService.getAdminName())
                                .operatorId(null == baseService.getAdminId() ? null : baseService.getAdminId().toString())
                                .build();
                        diffPickingMoveDTOList.add(diffPickingMoveDTO);
                        diffQuantity.updateAndGet(v -> v + (diffItem.getPickQuantity() - diffItem.getActualQuantity()));
                        if (Boolean.TRUE.equals(diffPickingMoveDTO.getIsAbnormalOut())) {
                            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = finalSkuQualityDateJJMap.get(getGroupKey(sku, DefaultCabinetCodeEnum.JJ.getCode(), diffItem.getProductionDate(), diffItem.getQualityDate()));
                            if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                                Long id = stockTaskItemCabinetOccupyDOList.get(0).getId();
                                occupyReleaseMap.put(id, diffItem.getPickQuantity() - diffItem.getActualQuantity());
                            }
                        }
                    });
//                    if (diffQuantity.get() > 0) {
//                        // 释放缺拣或缺出产生差异冻结库存
//                        areaStoreServiceV1.updateLockStockByWarehouseNo(-diffQuantity.get(), sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap);
//                        // 增加虚拟
//                        areaStoreServiceV1.updateOnlineStockByStoreNo(true, diffQuantity.get(), sku, stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap, NumberUtils.INTEGER_ONE);
//                    }
                    // 更新确认出库异常状态
                    List<Long> idList = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().map(MissionTaskItemMappingDTO::getId).collect(Collectors.toList());
                    missionTaskItemMappingCommandService.updateDiffConfirmedStatus(idList);
                    // 存在缺出情况释放多余占用
                    List<DiffPickingMoveDTO> filterAbnormalOutList = diffPickingMoveDTOList.stream().filter(moveDTO -> Boolean.TRUE.equals(moveDTO.getIsAbnormalOut())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterAbnormalOutList)) {
                        // 释放库位库存请求实体
                        CabinetInventoryReleaseReqDTO cabinetInventoryReleaseReqDTO = new CabinetInventoryReleaseReqDTO();
                        cabinetInventoryReleaseReqDTO.setWarehouseNo(stockTask.getAreaNo());
                        cabinetInventoryReleaseReqDTO.setOperatorType(CabinetInventoryOperationType.TRANSFER_OUT.getId());
                        cabinetInventoryReleaseReqDTO.setOrderTypeName(StoreRecordType.SUPPLY_AGAIN_TASK.getName());
                        cabinetInventoryReleaseReqDTO.setTenantId(stockTask.getTenantId());
                        cabinetInventoryReleaseReqDTO.setOrderNo(stockTask.getId().toString());
                        cabinetInventoryReleaseReqDTO.setReleaseDetailReqList(new ArrayList<>());
                        cabinetInventoryReleaseReqDTO.setOperatorName(baseService.getAdminName());
                        filterAbnormalOutList.forEach(outItem -> {
                            // 库位库存释放明细
                            CabinetInventoryReleaseDetailReqDTO cabinetInventoryReleaseDetailReqDTO = new CabinetInventoryReleaseDetailReqDTO();
                            cabinetInventoryReleaseDetailReqDTO.setCabinetCode(outItem.getMoveCabinetNo());
                            cabinetInventoryReleaseDetailReqDTO.setSkuCode(outItem.getSku());
                            cabinetInventoryReleaseDetailReqDTO.setReleaseQuantity(outItem.getAbnormalOutQuantity());
                            cabinetInventoryReleaseDetailReqDTO.setProduceDate(DateUtil.toDate(outItem.getProduceTime()));
                            cabinetInventoryReleaseDetailReqDTO.setQualityDate(DateUtil.toDate(outItem.getShelfLife()));
                            cabinetInventoryReleaseReqDTO.getReleaseDetailReqList().add(cabinetInventoryReleaseDetailReqDTO);
                        });
                        log.info("出库存在缺出释放库位库存入参：{}", JSON.toJSONStringWithDateFormat(cabinetInventoryReleaseReqDTO, "yyyy-MM-dd"));
                        List<CabinetInventoryReleaseDetail> cabinetInventoryReleaseDetails = cabinetInventoryFacade.releaseCabinetInventory(cabinetInventoryReleaseReqDTO);
                        log.info("出库存在缺出释放库位库存返回：{}", JSON.toJSONStringWithDateFormat(cabinetInventoryReleaseDetails, "yyyy-MM-dd"));
                        // 更新occupy数量
                        occupyReleaseMap.forEach((id, release) -> {
                            stockTaskItemCabinetOccupyRepositoryV1.updateReleaseChange(id, release);
                            log.info("差异数量释放，id：{}，释放数量：{}", id, release);
                        });
                    }
                }
            } else if (!openCabinetManagement) {
                // 扣减单据占用
                stockTaskOrderCancelService.handleStockTaskWaveOccupyReduce(stockTask.getId(), sku, outQuantity, null);
            }
        }
        //同步仓库库存
        areaStoreServiceV1.updateStoreStockByWarehouseNo(-outQuantity, req.getSku(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, SaleStockChangeTypeEnum.RECOVER_OUT.ordinal(), req.getStockTaskId() + "", recordMap);
        //插入数据
        quantityChangeRecordServiceV1.insertRecord(recordMap);

        // 批次生成出库单
        if (!CollectionUtils.isEmpty(processDetails)) {
            stockTaskProcessDetailMapper.insertBatch(processDetails);
        }
        // 出库单订单明细处理&回写任务订单明细数量
//        ProcessOrderSkuSaveCmd saveCmd = ProcessOrderSkuSaveCmd.builder()
//                .stockTaskProcessId(processId)
//                .sku(sku)
//                .quantity(outQuantity).build();
//        stockTaskProcessOrderSkuService.saveStockTaskProcessOrderSku(saveCmd);
        // 发送移库&生成盘点消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (!CollectionUtils.isEmpty(diffPickingMoveDTOList)) {
                    DiffPickingMoveEvent diffPickingMoveEvent = DiffPickingMoveEvent.builder()
                            .stockTaskId(Long.valueOf(stockTask.getId()))
                            .uid(UUID.randomUUID().toString())
                            .diffPickingMoveDTOList(diffPickingMoveDTOList)
                            .build();
                    SendResultDTO sendResultDTO = mqProducer.send(WmsConstant.MQ_TOPIC, WmsConstant.DIFF_PICKING_MOVE_TAG, diffPickingMoveEvent);
                    log.info("发送移库&生成盘点消息，消息体：{}，结果：{}", JSON.toJSONString(diffPickingMoveEvent), JSON.toJSONString(sendResultDTO));
                }
            }
        });

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void closeTask(StockTask stockTask) {
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTask.getId());
        StockTask queryStockTask = stockTaskMapper.selectByPrimaryKey(stockTask.getId());
        Integer areaNo = queryStockTask.getAreaNo();
        // 原逻辑先注释留存
//        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
//        List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(select);
//        if (!CollectionUtils.isEmpty(stockTaskItems)) {
//            //排序
//            stockTaskItems.sort(Comparator.comparing(StockTaskItem::getSku));
//            for (StockTaskItem item : stockTaskItems) {
//                //校验所有补货任务是否出库,否将未出的冻结解除并更新虚拟库存
//                String sku = item.getSku();
//                if (item.getQuantity() > item.getActualQuantity()) {
//                    Integer quantity = item.getQuantity() - item.getActualQuantity();
//                    updateStock(sku, quantity, areaNo, stockTask.getId() + "", recordMap);
//                }
//                areaStoreServiceV1.updateAreaStoreStatus(stockTask.getAreaNo(), item.getSku());
//            }
//        }
//        quantityChangeRecordServiceV1.insertRecord(recordMap);
        // 释放订单冻结
        stockTaskOrderCancelService.releaseInventoryLockForSale(stockTask.getId(), SaleStockChangeTypeEnum.RECOVER_OUT);

        // 释放订单冻结剩余库位库存
        stockTaskOrderCancelService.handleCabinetReleaseByStockTaskIdFinish(stockTask.getId(), baseService.getAdminName());
        // 出库数据分配
        stockTaskProcessOrderSkuService.finishStockTask(stockTask);
        // 完成出库明细消息处理
        if (stockOutWarehouseQueryService.isGreyWarehouse(stockTask.getAreaNo())) {
            stockTaskMsgService.checkAndSendStockTaskFinishMsg(stockTask.getId());
        }
    }

    /**
     * 减冻结 加虚拟
     */
    public void updateStock(String sku, Integer quantity, Integer areaNo, String recordNo, Map<String, QuantityChangeRecord> recordMap) {

        //加虚拟库存
        areaStoreServiceV1.updateOnlineStockByStoreNo(true, quantity, sku, areaNo, SaleStockChangeTypeEnum.RECOVER_OUT, recordNo, recordMap, NumberUtils.INTEGER_ZERO);
        //减冻结库存
        areaStoreServiceV1.updateLockStockByStoreNo(-quantity, sku, areaNo, SaleStockChangeTypeEnum.RECOVER_OUT, recordNo, recordMap);

    }

    private void handleStoreRecord(StockTaskSaleOutReq req, String adminName, Integer type, Integer areaNo,
                                   StockTaskItemDetail stockTaskItemDetail, Integer quantity, String listNo, boolean openCabinetManagement, StockTask stockTask) {
        StoreRecord select = new StoreRecord();
        select.setBatch(listNo);
        select.setSku(req.getSku());
        select.setAreaNo(areaNo);
        select.setQualityDate(stockTaskItemDetail.getQualityDate());
        select.setProductionDate(stockTaskItemDetail.getProductionDate());
        String remark = stockTaskItemDetail.getRemark();
        StoreRecord lasted = storeRecordMapper.selectOne(select);
        if (lasted != null) {
            if (quantity > lasted.getStoreQuantity()) {
                throw new DefaultServiceException("sku:" + req.getSku() + "此批次库存仅剩：" + stockTaskItemDetail.getListNo() + "-"
                        + stockTaskItemDetail.getQualityDate() + "-" + lasted.getStoreQuantity());
            }
        } else {
            throw new DefaultServiceException("当前仓不存在采购批次" + stockTaskItemDetail.getListNo() + "保质期为" + stockTaskItemDetail.getQualityDate() + "的" + req.getSku());
        }

        // 切换生产批次库存处理
        Boolean switched = produceBatchSwitchConfig.isSwitched(areaNo);
        // 暂不影响精细化仓
        if (Boolean.TRUE.equals(switched) && Boolean.FALSE.equals(openCabinetManagement)) {
            ProduceBatchResDTO produceBatchResDTO = produceBatchInnerService.queryProduceBatch(ProduceBatchQueryReqDTO.builder()
                    .warehouseNo(areaNo)
                    .sku(req.getSku())
                    .produceAt(DateUtil.toMill(stockTaskItemDetail.getProductionDate()))
                    .shelfLife(DateUtil.toMill(stockTaskItemDetail.getQualityDate()))
                    .build());
            if (null == produceBatchResDTO) {
                throw new BizException(req.getSku() + "未获取到生产批次库存数据");
            }
            ProduceBatchUpdateReqDTO produceBatchUpdateReqDTO = ProduceBatchUpdateReqDTO.builder()
                    .operationType(type)
                    .operationName(baseService.getAdminName())
                    .purchaseNo(listNo)
                    .id(produceBatchResDTO.getId())
                    .quantity(-quantity)
                    .tenantId(stockTask.getTenantId())
                    .warehouseNo(areaNo.longValue())
                    .sourceId(stockTask.getId().longValue())
                    .build();
            try {
                produceBatchInnerService.updateProduceBatch(produceBatchUpdateReqDTO);
            } catch (DataIntegrityViolationException e) {
                log.warn("生产批次数量更新异常", e);
                // 并发抢资源重试退避
                SystemLocalUtil.sleepForMQConsumer();
                throw new BizException("生产批次数量更新并发，会重试。。");
            }
            log.info("切换生产批次处理库存完成，任务：{}，明细：{}", stockTask.getId(), JSON.toJSONString(stockTaskItemDetail));
            return;
        }
        //新增库存变化记录
        StoreRecord storeRecord = createNewStoreRecord(lasted, type, quantity, remark, adminName);
        storeRecordMapper.insert(storeRecord);
    }

    /**
     * 生成库存记录
     */
    private StoreRecord createNewStoreRecord(StoreRecord lasted, Integer type, Integer quantity, String remark, String adminName) {
        StoreRecord storeRecord = new StoreRecord();
        storeRecord.setBatch(lasted.getBatch());
        storeRecord.setSku(lasted.getSku());
        storeRecord.setType(type);
        storeRecord.setQuantity(quantity);
        storeRecord.setUnit(lasted.getUnit());
        storeRecord.setRecorder(adminName);
        storeRecord.setRemark(remark);
        storeRecord.setUpdateTime(new Date());
        storeRecord.setQualityDate(lasted.getQualityDate());
        storeRecord.setAreaNo(lasted.getAreaNo());
        storeRecord.setProductionDate(lasted.getProductionDate());
        storeRecord.setStoreQuantity(lasted.getStoreQuantity() - quantity);
        storeRecord.setCost(lasted.getCost());
        storeRecord.setTenantId(lasted.getTenantId());
        return storeRecord;
    }

    /**
     * 出库详情
     */
    private StockTaskItemDetail createItemDetail(StockTaskItemDetail old, Integer id, String sku, Integer quantity) {
        StockTaskItemDetail insert = new StockTaskItemDetail();
        insert.setCabinetCode(old.getCabinetCode());
        insert.setListNo(old.getListNo());
        insert.setStockTaskItemId(id);
        insert.setSku(sku);
        insert.setQualityDate(old.getQualityDate());
        insert.setQuantity(quantity);
        insert.setProductionDate(old.getProductionDate());
        insert.setRemark(old.getRemark());
        insert.setOutStoreQuantity(quantity);
        insert.setShouldQuantity(quantity);
        return insert;
    }

    /**
     * 是否完成出库
     */
    public boolean closeStockTask(Integer stockTaskId, Boolean openCabinetManagement) {
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTaskId);
        List<StockTaskItem> items = stockTaskItemMapper.select(select);
        List<String> skuCodeList = items.stream().map(StockTaskItem::getSku).collect(Collectors.toList());
        List<StockTaskItemCabinetOccupyDO> originStockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepositoryV1
                .selectListByStockTaskIdOccupyedExcludedJJV1(stockTaskId, skuCodeList);
        Map<String, List<StockTaskItemCabinetOccupyDO>> groupSkuOccupyMap = originStockTaskItemCabinetOccupyDOS.stream().collect(groupingBy(StockTaskItemCabinetOccupyDO::getSku));
        return items.stream().allMatch(item -> {
            // 开启库位管理优先取应拣数做比对
            if (Boolean.TRUE.equals(openCabinetManagement)) {
                List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = groupSkuOccupyMap.get(item.getSku());
                if (CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)) {
                    return Objects.equals(item.getQuantity(), item.getActualQuantity());
                } else {
                    Integer shouldPickQuantity = stockTaskItemCabinetOccupyDOList.stream().map(StockTaskItemCabinetOccupyDO::getShouldPickQuantity).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
                    if (shouldPickQuantity > 0) {
                        return item.getQuantity() <= shouldPickQuantity;
                    } else {
                        return Objects.equals(item.getQuantity(), item.getActualQuantity());
                    }
                }
            } else {
                return Objects.equals(item.getQuantity(), item.getActualQuantity());
            }
        });
    }

    /**
     * 录入出库任务SKU信息
     *
     * @param loadingSheet  装货单sheet
     * @param type          出库任务类型
     * @param stockTaskList 出库任务list
     * @param itemVOS       出库任务条目list
     */
    private void loadingGoods(Sheet loadingSheet, Integer type, List<StockTask> stockTaskList, List<StockTaskItemVO> itemVOS) {
        int rowIndex = 0;
        Row first = loadingSheet.createRow(rowIndex++);
        first.createCell(0).setCellValue(StockTaskType.getNameById(type) + ":");
        StringBuilder ids = new StringBuilder();
        stockTaskList.forEach(st -> {
            ids.append(st.getId()).append(Global.SEPARATING_SYMBOL);
        });
        ids.deleteCharAt(ids.length() - 1);
        first.createCell(1).setCellValue(ids.toString());

        Row second = loadingSheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("出库仓：");
        Integer areaNo = stockTaskList.get(0).getAreaNo();
        if (Objects.nonNull(areaNo)) {
            second.createCell(1).setCellValue(Global.warehouseMap.get(Long.valueOf(stockTaskList.get(0).getAreaNo())));
        }
        second.createCell(2).setCellValue("城配仓：");
        second.createCell(3).setCellValue(Global.storeMap.get(stockTaskList.get(0).getOutStoreNo()));

        Row three = loadingSheet.createRow(rowIndex++);
        three.createCell(0).setCellValue("预计出库时间：");
        three.createCell(1).setCellValue(Objects.isNull(stockTaskList.get(0).getExpectTime()) ? " " : stockTaskList.get(0).getExpectTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS)));

        Row four = loadingSheet.createRow(rowIndex++);
        four.createCell(0).setCellValue("实际出库时间：");
        four.createCell(1).setCellValue(stockTaskList.get(0).getUpdatetime() != null ? stockTaskList.get(0).getUpdatetime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS)) : "");

        //中间空一行
        rowIndex++;
        Row title = loadingSheet.createRow(rowIndex++);
        String[] titleNames = new String[]{"一级类目", "二级类目", "sku", "商品名称", "规格", "商品归属", "储存区域", "包装", "进口/国产", "应出数量", "实发数量"};
        for (int i = 0; i < titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }
        int index = rowIndex;
        for (StockTaskItemVO itemVO : itemVOS) {
            Row row = loadingSheet.createRow(index);
            row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
            row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
            row.createCell(2).setCellValue(itemVO.getSku());
            row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
            row.createCell(4).setCellValue(itemVO.getWeight());
            String productsType = net.summerfarm.common.util.StringUtils.productType(itemVO.getSkuType(), itemVO.getNameRemakes());
            row.createCell(5).setCellValue(productsType);
            row.createCell(6).setCellValue(itemVO.getStorageArea());
            row.createCell(7).setCellValue(itemVO.getUnit());
            String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, itemVO.getIsDomestic()) ? "进口" : "国产";
            row.createCell(8).setCellValue(originStr);
            row.createCell(9).setCellValue(itemVO.getQuantity());
            row.createCell(10).setCellValue(itemVO.getDetailQuantity());
            index++;
        }
    }

    private String getGroupKey(String sku, String cabinetCode, Date produceTime, Date shelfLife) {
        return sku + WmsConstant.SPLIT_U + cabinetCode + WmsConstant.SPLIT_U + produceTime + WmsConstant.SPLIT_U + shelfLife;
    }

    private void releaseInventoryLockWithDiffOutForGrey(StockTask task, List<Long> updateStatusMappingIdList) {
        // 灰度配置
        Boolean greyWarehouse = stockOutWarehouseQueryService.isGreyWarehouse(task.getAreaNo());
        if (Boolean.FALSE.equals(greyWarehouse)) {
            return;
        }
        // 异常通知单明细
        if (!CollectionUtils.isEmpty(updateStatusMappingIdList)) {
            List<StockTaskNoticeOrderAbnormalDetail> stockTaskNoticeOrderAbnormalDetailList = outNoticeAbnormalDetailQueryRepository.findByStockTaskId(task.getId().longValue());
            Map<String, Integer> noticeOrderAbnormalSkuMap = stockTaskNoticeOrderAbnormalDetailList.stream().collect(Collectors.toMap(StockTaskNoticeOrderAbnormalDetail::getSku, StockTaskNoticeOrderAbnormalDetail::getQuantity, Integer::sum));
            MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryMissionTaskItemMappingByIds(updateStatusMappingIdList);
            log.info("查询缺出数据（灰度），出库任务：{}，{}", task.getId(), JSON.toJSONStringWithDateFormat(missionTaskItemMappingResp, DateUtil.YYYY_MM_DD));
            if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                Map<String, List<MissionTaskItemMappingDTO>> groupSkuMappingDTOList = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(groupingBy(MissionTaskItemMappingDTO::getSku));
                groupSkuMappingDTOList.forEach((sku, mappingList) -> {
                    Integer initPickQuantity = mappingList.stream().filter(item -> item.getInitPickQuantity() != null && item.getInitPickQuantity() > 0).map(MissionTaskItemMappingDTO::getInitPickQuantity).reduce(Integer::sum).orElse(0);
                    Integer actualQuantity = mappingList.stream().filter(item -> item.getActualQuantity() != null && item.getActualQuantity() > 0).map(MissionTaskItemMappingDTO::getActualQuantity).reduce(Integer::sum).orElse(0);
                    Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                    if (!initPickQuantity.equals(actualQuantity)) {
                        log.info("存在缺出库存处理，出库任务：{}，{}", task.getId(), JSON.toJSONStringWithDateFormat(mappingList, DateUtil.YYYY_MM_DD));
                        // 如果缺出数量小于订单取消数量会存在虚拟库存变多情况，风险已提出，产品侧按原方案执行，暂时注释，统一处理逻辑（切履约灰度链路解决）
                        int diffQuantity = initPickQuantity - actualQuantity;
                        // 存在异常数量处理
                        Integer noticeOrderAbnormalQuantity = null == noticeOrderAbnormalSkuMap.get(sku) ? 0 : noticeOrderAbnormalSkuMap.get(sku);
                        log.info("存在缺出库存处理（灰度），实出大于取消数量，出库任务：{}，需调整数量：{}", task.getId(), diffQuantity - noticeOrderAbnormalQuantity);
                        // 缺出数量>异常数量【缺出>退单】（只操作缺出-异常部分的虚拟和冻结，剩余部分由多出入库驱动处理）
                        if (diffQuantity > noticeOrderAbnormalQuantity) {
                            Integer doQuantity = diffQuantity - noticeOrderAbnormalQuantity;
                            // 非波次任务
                            if (!OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
                                // 释放缺拣或缺出产生差异冻结库存
                                areaStoreServiceV1.updateLockStockByWarehouseNo(-doQuantity, sku, task.getOutStoreNo(), task.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, task.getId() + "", recordMap);
                            }
                            // 增加虚拟
                            areaStoreServiceV1.updateOnlineStockByStoreNo(true, doQuantity, sku, task.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, task.getId() + "", recordMap, NumberUtils.INTEGER_ONE);
                        }
                        quantityChangeRecordServiceV1.insertRecord(recordMap);
                        List<Long> idList = mappingList.stream().map(MissionTaskItemMappingDTO::getId).collect(Collectors.toList());
                        missionTaskItemMappingCommandService.updateDiffConfirmedOutStatus(idList);
                    }
                });
            }
        }
    }
}
