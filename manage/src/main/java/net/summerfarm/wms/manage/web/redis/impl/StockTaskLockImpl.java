package net.summerfarm.wms.manage.web.redis.impl;

import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.manage.web.redis.StockTaskLock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@Repository
public class StockTaskLockImpl implements StockTaskLock {

    public static final String KEY_ID = "lock:LockId:stockTask";

    private final Integer expireTime = 10 * 60;

    private final Long sleepTime = 1000L;

    private static final MessageFormat KEY_FORMAT = new MessageFormat("lock:LockId:stockTask:{0}");

    @Resource
    private RedisTemplate<String,String> redisTemplate;

    @Resource(name = "delLockScript")
    private RedisScript<Boolean> delLockScript;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Long nextId() {
        return redisTemplate.opsForValue().increment(KEY_ID,1L);
    }

    @Override
    public Boolean tryLock(String lockKey, Long lockId, Long timeout, TimeUnit unit) {
        final String key = KEY_FORMAT.format(new String[]{lockKey});
        final String value = lockId.toString();

        // todo 修改了使用的redis工具类型
        return redisUtil.tryLock(key, timeout, unit, timeout);
    }

    @Override
    public Boolean lock(String lockKey, Long lockId) {
        final String key = KEY_FORMAT.format(new String[]{lockKey});
        final String value = lockId.toString();
        // todo 修改了使用的redis工具类型
        return redisUtil.tryLock(key, sleepTime, TimeUnit.MILLISECONDS, sleepTime);
    }

    @Override
    public Boolean unLock(String lockKey, Long lockId) {
        final String key = KEY_FORMAT.format(new String[]{lockKey});
        redisUtil.unlock(key);
        return true;
    }
}
