package net.summerfarm.wms.api.h5.mission.stocktaking.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StocktakingPagePdaQuery implements Serializable {
    /**
     * 仓库编号
     */
    @NotNull(message = "仓库号不可为空")
    Long warehouseNo;

    /**
     * 任务号
     */
    String missionNo;

    /**
     * 库位编号
     */
    String cabinetNo;

    /**
     * 商品条码
     */
    String barcode;

    /**
     * 明细行状态
     * 为了查询领取时，同库位还有多少待领取的明细
     */
    Integer detailState;

    @NotNull(message = "分页页数不可为空")
    Integer pageIndex;

    @NotNull(message = "分页数量不可为空")
    Integer pageSize;
}
