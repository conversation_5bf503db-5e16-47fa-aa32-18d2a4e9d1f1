package net.summerfarm.wms.domain.goods.enums;

/**
 * @author: dongcheng
 * @date: 2023/9/4
 */
public enum QualityTimeUnitEnum {

    DAY("day", "日"),
    MONTH("month", "月"),
    ;

    private final String code;
    private final String desc;

    QualityTimeUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (QualityTimeUnitEnum qualityTimeUnitEnum : QualityTimeUnitEnum.values()) {
            if (qualityTimeUnitEnum.getCode().equals(code)) {
                return qualityTimeUnitEnum.getDesc();
            }
        }
        return "";
    }
}
