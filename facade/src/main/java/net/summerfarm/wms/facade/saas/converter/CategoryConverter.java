package net.summerfarm.wms.facade.saas.converter;

import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
import net.summerfarm.wms.facade.saas.dto.CategoryVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/1/30 17:10
 */
public class CategoryConverter {


    public static List<CategoryVO> cateResultRespList2CateGoryVO(List<CategoryDetailResultResp> resultRespList) {

        if (resultRespList == null) {
            return Collections.emptyList();
        }
        List<CategoryVO> categoryVOList = new ArrayList<>();
        for (CategoryDetailResultResp cateGoryDetailResultResp : resultRespList) {
            categoryVOList.add(toCategoryVO(cateGoryDetailResultResp));
        }
        return categoryVOList;
    }

    public static CategoryVO toCategoryVO(CategoryDetailResultResp cateGoryDetailResultResp) {
        if (cateGoryDetailResultResp == null) {
            return null;
        }
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setId(cateGoryDetailResultResp.getId());
        categoryVO.setName(cateGoryDetailResultResp.getName());
        categoryVO.setParentId(cateGoryDetailResultResp.getParentId());
        categoryVO.setCheck(cateGoryDetailResultResp.getCheck());
// Not mapped TO fields:
// categoryVOS
// Not mapped FROM fields:
// cateGoryDetailResultRespList
        if (!CollectionUtils.isEmpty(cateGoryDetailResultResp.getCategoryDetailResultRespList())) {
            List<CategoryVO> categoryVOList = cateResultRespList2CateGoryVO(cateGoryDetailResultResp.getCategoryDetailResultRespList());
            categoryVO.setCategoryVOS(categoryVOList);
        }
        return categoryVO;
    }

}
