package net.summerfarm.wms.api.h5.mission.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionChangeUnitCommand {
    /**
     * 仓库号
     */
    @NotNull(message = "仓库号不可为空")
    Long warehouseNo;

    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 容器号
     */
    @NotNull(message = "容器号不可为空")
    @NotBlank(message = "容器号不可为空")
    String containerNo;

    /**
     * 租户id
     */
    Long tenantId;
}
