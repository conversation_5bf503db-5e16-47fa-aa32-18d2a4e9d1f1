package net.summerfarm.wms.domain.config.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversionSkuQuantity implements Serializable {

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 昨天订单最小值
     */
    private Double minSaleCnt;

    /**
     * 前15天平均销量
     */
    private Double saleCntFifteen;

    /**
     * 前7天平均销量
     */
    private Double saleCntSeven;

    /**
     * 前七天每日总销量峰值
     */
    private Double maxSaleSeven;

    /**
     * 计算时间
     */
    private Date date;

}
