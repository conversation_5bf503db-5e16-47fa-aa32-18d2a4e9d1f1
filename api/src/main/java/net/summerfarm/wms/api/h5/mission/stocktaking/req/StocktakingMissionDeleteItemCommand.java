package net.summerfarm.wms.api.h5.mission.stocktaking.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StocktakingMissionDeleteItemCommand implements Serializable {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 实例id
     */
    Long missionDetailId;

    /**
     * sku
     */
    String sku;

    /**
     * 生产日期
     */
    LocalDate produceDate;

    /**
     * 保质期
     */
    LocalDate qualityDate;

    /**
     * 库位
     */
    String cabinetNo;

    /**
     * 操作人
     */
    String operator;
}
