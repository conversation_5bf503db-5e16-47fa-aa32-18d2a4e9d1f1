package net.summerfarm.wms.scheduler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryWarnCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/16
 */
@Component
@Slf4j
public class SafeInventoryWarnJob extends XianMuJavaProcessorV2 {

    @Resource
    private SafeInventoryWarnCommandService safeInventoryWarnCommandService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("执行安全库存预警通知开始");
        Boolean result = safeInventoryWarnCommandService.exeWarn();
        log.info("执行安全库存预警通知完成");
        return new ProcessResult(result);
    }
}
