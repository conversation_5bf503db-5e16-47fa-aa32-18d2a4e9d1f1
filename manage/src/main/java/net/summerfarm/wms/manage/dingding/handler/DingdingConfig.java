package net.summerfarm.wms.manage.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.manage.model.domain.Config;
import net.summerfarm.wms.manage.model.domain.plan.ProcessConfig;
import net.summerfarm.wms.manage.web.mapper.manage.ConfigMapper;
import net.summerfarm.wms.manage.web.mapper.plan.ProcessConfigMapper;
import net.summerfarm.wms.manage.web.utils.TempThreadLocalUtil;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Slf4j
@Component
public class DingdingConfig {

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private ProcessConfigMapper processConfigMapper;

    public String getProcessCode(String key) {
        if (StringUtils.isBlank(key)) {
            log.info("传入钉钉key值为空");
            return null;
        }
        Boolean process = TempThreadLocalUtil.getProcess();
        if(BooleanUtils.isTrue(process)){
            ProcessConfig processConfig = processConfigMapper.selectOneByProcessCode(key);
            if(Objects.isNull(processConfig)){
                log.info("KEY查出对应ProcessCode为null，KEY值为：" + key);
                return null;
            }
            return processConfig.getFeishuCode();
        }else{
            Config one = configMapper.selectOne(key);
            if (one == null) {
                log.info("KEY查出对应ProcessCode为null，KEY值为：" + key);
                return null;
            }
            return one.getValue();
        }
    }
}
