<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskProcessOrderSkuBatchDAO">
  <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuBatchDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="stock_task_process_id" jdbcType="BIGINT" property="stockTaskProcessId" />
    <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="goods_supply_no" jdbcType="VARCHAR" property="goodsSupplyNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="produce_date" jdbcType="DATE" property="produceDate" />
    <result column="quality_date" jdbcType="DATE" property="qualityDate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="delivery_date" jdbcType="DATE" property="deliveryDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, stock_task_process_id, stock_task_id, out_order_no, goods_supply_no, sku, quantity, 
    batch, produce_date, quality_date, creator, `operator`, create_time, update_time, 
    is_deleted, last_ver, delivery_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wms_stock_task_process_order_sku_batch
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wms_stock_task_process_order_sku_batch
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuBatchDO" useGeneratedKeys="true">
    insert into wms_stock_task_process_order_sku_batch (stock_task_process_id, stock_task_id, out_order_no, 
      goods_supply_no, sku, quantity, 
      batch, produce_date, quality_date, 
      creator, `operator`, create_time, 
      update_time, is_deleted, last_ver, 
      delivery_date)
    values (#{stockTaskProcessId,jdbcType=BIGINT}, #{stockTaskId,jdbcType=BIGINT}, #{outOrderNo,jdbcType=VARCHAR}, 
      #{goodsSupplyNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, 
      #{batch,jdbcType=VARCHAR}, #{produceDate,jdbcType=DATE}, #{qualityDate,jdbcType=DATE}, 
      #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{deliveryDate,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuBatchDO" useGeneratedKeys="true">
    insert into wms_stock_task_process_order_sku_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stockTaskProcessId != null">
        stock_task_process_id,
      </if>
      <if test="stockTaskId != null">
        stock_task_id,
      </if>
      <if test="outOrderNo != null">
        out_order_no,
      </if>
      <if test="goodsSupplyNo != null">
        goods_supply_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="qualityDate != null">
        quality_date,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deliveryDate != null">
        delivery_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stockTaskProcessId != null">
        #{stockTaskProcessId,jdbcType=BIGINT},
      </if>
      <if test="stockTaskId != null">
        #{stockTaskId,jdbcType=BIGINT},
      </if>
      <if test="outOrderNo != null">
        #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsSupplyNo != null">
        #{goodsSupplyNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=DATE},
      </if>
      <if test="qualityDate != null">
        #{qualityDate,jdbcType=DATE},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deliveryDate != null">
        #{deliveryDate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuBatchDO">
    update wms_stock_task_process_order_sku_batch
    <set>
      <if test="stockTaskProcessId != null">
        stock_task_process_id = #{stockTaskProcessId,jdbcType=BIGINT},
      </if>
      <if test="stockTaskId != null">
        stock_task_id = #{stockTaskId,jdbcType=BIGINT},
      </if>
      <if test="outOrderNo != null">
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsSupplyNo != null">
        goods_supply_no = #{goodsSupplyNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=DATE},
      </if>
      <if test="qualityDate != null">
        quality_date = #{qualityDate,jdbcType=DATE},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuBatchDO">
    update wms_stock_task_process_order_sku_batch
    set stock_task_process_id = #{stockTaskProcessId,jdbcType=BIGINT},
      stock_task_id = #{stockTaskId,jdbcType=BIGINT},
      out_order_no = #{outOrderNo,jdbcType=VARCHAR},
      goods_supply_no = #{goodsSupplyNo,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      batch = #{batch,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=DATE},
      quality_date = #{qualityDate,jdbcType=DATE},
      creator = #{creator,jdbcType=VARCHAR},
      `operator` = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      delivery_date = #{deliveryDate,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>