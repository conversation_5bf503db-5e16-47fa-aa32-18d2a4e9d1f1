package net.summerfarm.wms.common.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * qlexpress runner使用bean func
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FunctionOfServiceMethodDTO {

    /**
     * 需要替换为方法执行的表达式别名
     */
    String alias;

    /**
     * 方法名称
     */
    String funcName;

    /**
     * 服务名称beanName
     */
    String serviceName;

    /**
     * 入参类型
     */
    Class<?>[] parameterClassTypes;

    /**
     * 错误信息
     */
    String errorInfo;
}
