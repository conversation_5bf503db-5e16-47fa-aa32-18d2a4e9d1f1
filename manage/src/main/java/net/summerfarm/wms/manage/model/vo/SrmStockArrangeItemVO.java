package net.summerfarm.wms.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: dongcheng
 * @date: 2023/12/1
 */
@Data
public class SrmStockArrangeItemVO {
    private Integer id;

    @ApiModelProperty(value = "预约单id")
    private Integer stockArrangeId;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "重量单位")
    private String weightNum;

    @ApiModelProperty(value = "类型 0 自营 1 代仓")
    private Integer type;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "保质期时长")
    private Integer qualityTime;

    @ApiModelProperty(value = "保质期时长单位")
    private String qualityTimeUnit;

    @ApiModelProperty(value = "预约数量")
    private Integer arrivalQuantity;

    @ApiModelProperty(value = "实到数量")
    private Integer actualQuantity;

    @ApiModelProperty(value = "实到数量")
    private Integer arrangeQuantity;

    @ApiModelProperty(value = "图片")
    private String pic;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;
}
