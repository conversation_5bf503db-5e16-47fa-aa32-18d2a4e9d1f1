package net.summerfarm.wms.domain.external.param;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-05-21 17:13:30
 * @version 1.0
 *
 */
@Data
public class WmsExternalOrderMappingQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 外部单号
	 */
	private String externalNo;

	/**
	 * 业务单号
	 */
	private String bizNo;

	/**
	 * 内部单号
	 */
	private String internalNo;

	/**
	 * 业务类型
	 */
	private Integer type;

	/**
	 * 业务类型
	 */
	private List<Integer> types;

	/**
	 * appkey
	 */
	private String appKey;

	/**
	 * 库存仓
	 */
	private Integer warehouseNo;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 仓库租户id
	 */
	private Long warehouseTenantId;

	

	
}