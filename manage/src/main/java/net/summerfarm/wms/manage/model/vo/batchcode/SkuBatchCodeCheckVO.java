package net.summerfarm.wms.manage.model.vo.batchcode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 校验批次码是否可以打印
 * @author: dongcheng
 * @date: 2024/1/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SkuBatchCodeCheckVO implements Serializable {
    private static final long serialVersionUID = -6688634683649006477L;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 二维码 批次码 true 可以打印
     */
    private boolean canPrintBatchCode;

}
