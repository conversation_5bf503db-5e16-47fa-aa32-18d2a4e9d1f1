package net.summerfarm.wms.application.acl.mq;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.wms.FulfillmentStockNoticeItemMessage;
import net.summerfarm.ofc.client.common.message.wms.FulfillmentStockNoticeMessage;
import net.summerfarm.wms.api.h5.stocktask.StockTaskNoticeOrderCommandService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskNoticeOrderCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskNoticeOrderCommand.NoticeOrderDetailCommand;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.stocktask.enums.NoticeOrderStatusEnum;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.summerfarm.wms.facade.wnc.dto.WarehousLogisticsCenterRespDTO;
import net.summerfarm.wms.facade.wnc.dto.WarehouseLogisticsQueryReqDTO;
import net.summerfarm.wms.outstore.dto.StockTaskNoticeOrderCreateMsgDTO;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.wms.common.constant.Global.STOCK_TASK;

/**
 * @Description 出库通知单消息监听
 * @Date 2023/4/10 11:18
 * @<AUTHOR>
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_stock_notice", tag = "tag_stock_notice_create", consumerGroup = "GID_wms_notice_order")
public class StockTaskNoticeOrderListener extends AbstractMqListener<FulfillmentStockNoticeMessage> {

    @Resource
    private StockTaskNoticeOrderCommandService noticeOrderCommandService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private WarehouseLogisticsFacade warehouseLogisticsFacade;
    @Resource
    private ProductRepository productRepository;


    @Override
    public void process(FulfillmentStockNoticeMessage stockNoticeMessage) {
        log.info("receive notice order msg, body:{}", JSONUtil.toJsonStr(stockNoticeMessage));
        StockTaskNoticeOrderCommand command = this.buildNoticeOrderCommand(stockNoticeMessage);
        Long noticeOrderId = null;
        try {
            noticeOrderId = noticeOrderCommandService.createStockTaskNoticeOrder(command);
            log.info("出库通知单创建成功 noticeOrderId:{}", noticeOrderId);
        } catch (BizException bizException) {
            // 通知单创建失败通知
            StockTaskNoticeOrderCreateMsgDTO createMsgDTO = StockTaskNoticeOrderCreateMsgDTO.builder()
                    .goodsSupplyNo(command.getGoodsSupplyNo())
                    .createResult(Boolean.FALSE)
                    .createMsg(bizException.getMessage())
                    .noticeOrderId(null)
                    .build();
            mqProducer.send(STOCK_TASK, "tag_create_notice_order", createMsgDTO);
        } catch (Exception e) {
            // 异常告警
            log.error("出库通知单创建失败, msg body:{}", JSONUtil.toJsonStr(stockNoticeMessage), e);
        }

        try {
            // 处理合并出库任务
            if (stockNoticeMessage.isAutoCreateStockTask() && noticeOrderId != null) {
                noticeOrderCommandService.handleAutoMergeStockTask(stockNoticeMessage.getGoodsSupplyNo());
                return;
            }
        } catch (Throwable throwable) {
            log.error("出库通知单合并失败, msg body:{}", JSONUtil.toJsonStr(stockNoticeMessage), throwable);
        }

        // 判断城配截单时间，超过则飞书告警，人工介入后续处理
        feiShuWarn(stockNoticeMessage);
    }

    private void feiShuWarn(FulfillmentStockNoticeMessage stockNoticeMessage) {
        if (Objects.isNull(stockNoticeMessage.getExpectArriveTime()) || Objects.isNull(stockNoticeMessage.getStoreNo())) {
            return;
        }
        List<WarehousLogisticsCenterRespDTO> resp = warehouseLogisticsFacade.queryWarehouseLogisticsList(WarehouseLogisticsQueryReqDTO.builder()
                .storeNo(stockNoticeMessage.getStoreNo().intValue())
                .build());
        if (CollectionUtils.isEmpty(resp)) {
            return;
        }
        String closeTime = resp.get(NumberUtils.INTEGER_ZERO).getCloseTime();
        if (StringUtils.isEmpty(closeTime)) {
            return;
        }
        LocalTime closeTimeParse = DateUtil.toLocalTime(closeTime);
        Boolean allow = warehouseLogisticsFacade.queryIsSupportAddOrder(stockNoticeMessage.getStoreNo().intValue());
        closeTimeParse = Boolean.TRUE.equals(allow) ? closeTimeParse.plusMinutes(30L) : closeTimeParse; //NOSONAR
        // 未截单时间且预计出库时间明天的不告警
        if (Objects.nonNull(closeTimeParse) &&
                LocalTime.now().isBefore(closeTimeParse) &&
                stockNoticeMessage.getExpectArriveTime().toLocalDate().equals(LocalDate.now().plusDays(1L)) ||
                stockNoticeMessage.getExpectArriveTime().toLocalDate().isAfter(LocalDate.now().plusDays(1L))) {
            return;
        }
        String url = configRepository.queryValueByKey("goods_supply_notice_warn_url");
        if (StringUtils.isEmpty(url)) {
            log.error("出库通知单生成时间超过截单时间，飞书告警url查询失败: goods_supply_notice_warn_url \n");
            return;
        }
        Integer storeNo = Objects.isNull(stockNoticeMessage.getStoreNo()) ? null : stockNoticeMessage.getStoreNo().intValue();
        // pop城配仓不需要告警
        List<WarehousLogisticsCenterRespDTO> popWarehouseLogisticsList = warehouseLogisticsFacade.queryPopWarehouseLogisticsList();
        List<Integer> popStoreNosList = popWarehouseLogisticsList.stream().map(WarehousLogisticsCenterRespDTO::getStoreNo).collect(Collectors.toList());
        if(popStoreNosList.contains(storeNo)){
            return;
        }
        StringBuilder stringBuilder = new StringBuilder("请注意出库通知单生成时间超过截单时间\n");
        stringBuilder.append("货品供应单:").append(stockNoticeMessage.getGoodsSupplyNo()).append("\n");
        stringBuilder.append("仓库:").append(Global.warehouseMap.get(stockNoticeMessage.getWarehouseNo())).append("\n");
        stringBuilder.append("城配仓:").append(Global.storeMap.get(storeNo)).append("\n");
        stringBuilder.append("生成时间:").append(DateUtil.formatDate(new Date()));
        FeishuBotUtil.sendMarkdownMsgAndAtAll(url, stringBuilder.toString());
    }

    private StockTaskNoticeOrderCommand buildNoticeOrderCommand(FulfillmentStockNoticeMessage stockNoticeMessage) {


        List<NoticeOrderDetailCommand> detailCommandList = Lists.newArrayList();
        List<FulfillmentStockNoticeItemMessage> itemMessageList = stockNoticeMessage.getItemList();
        Map<String, List<FulfillmentStockNoticeItemMessage>> skuMap = itemMessageList.stream().collect(Collectors.groupingBy(FulfillmentStockNoticeItemMessage::getSku));
        Set<String> skus = skuMap.keySet();
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockNoticeMessage.getWarehouseNo(),
                Lists.newArrayList(skus));

        skuMap.forEach((sku, itemList) -> {
            Integer quantity = itemList.stream().mapToInt(FulfillmentStockNoticeItemMessage::getQuantity).sum();
            String customSkuCode = itemList.get(0).getCustomSkuCode();
            detailCommandList.add(NoticeOrderDetailCommand.builder()
                    .sku(sku)
                    .goodsName(productMap.getOrDefault(sku, Product.builder().build()).getPdName())
                    .quantity(quantity)
                    .customerSkuCode(customSkuCode)
                    .build());

        });

        LocalDateTime expectTime = stockNoticeMessage.getExpectArriveTime();
        return StockTaskNoticeOrderCommand.builder()
                .tenantId(stockNoticeMessage.getTenantId())
                .shopId(stockNoticeMessage.getShopId())
                .shopName(stockNoticeMessage.getClientName())
                .goodsSupplyNo(stockNoticeMessage.getGoodsSupplyNo())
                .outOrderNo(stockNoticeMessage.getOrderNo())
                .outOrderType(stockNoticeMessage.getType())
                .warehouseNo(stockNoticeMessage.getWarehouseNo().intValue())
                .warehouseTenantId(stockNoticeMessage.getWarehouseTenantId())
                .storeNo(Objects.isNull(stockNoticeMessage.getStoreNo()) ? null : stockNoticeMessage.getStoreNo().intValue())
                .supplyMode(stockNoticeMessage.getSupplyMode())
                .exceptTime(expectTime)
                .status(NoticeOrderStatusEnum.INIT.getStatus())
                .receiver(stockNoticeMessage.getReceiver())
                .phone(stockNoticeMessage.getPhone())
                .province(stockNoticeMessage.getProvince())
                .city(stockNoticeMessage.getCity())
                .area(stockNoticeMessage.getArea())
                .detailAddress(stockNoticeMessage.getDetailAddress())
                .creator("系统生成")
                .operator("系统生成")
                .detailCommandList(detailCommandList)
                .autoCreateStockTask(stockNoticeMessage.isAutoCreateStockTask())
                .pushMode(stockNoticeMessage.getPushMode())
                .closeTime(stockNoticeMessage.getCloseTime())
                .build();
    }
}
