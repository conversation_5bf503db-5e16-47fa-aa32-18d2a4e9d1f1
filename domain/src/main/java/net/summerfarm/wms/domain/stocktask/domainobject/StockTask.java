package net.summerfarm.wms.domain.stocktask.domainobject;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/11/28  13:29
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class StockTask {

    private Long id;

    private String taskNo;

    @NotNull(groups = {Add.class}, message = "areaNo.not.null")
    private Integer areaNo;


    private Integer type;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDateTime expectTime;

    @ApiModelProperty(value = "出入库进展: 0待入(出)库 1部分入(出)库 2已入(出)库")
    private Integer state;

    private Integer adminId;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDateTime addtime;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDateTime updatetime;

    @ApiModelProperty(value = "出库仓")
    private Integer outStoreNo;

    @ApiModelProperty(value = "出库性质,0普通 1越库")
    private Integer outType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点维度：0、SKU 1、类目 2、批次 3、货位,\n" +
            "                  转换维度：0、库存转换 1、降级转换")
    private Integer dimension;

    @ApiModelProperty(value = "入库进度:0.全未入库1.部分入库2.完全入库")
    private Integer processState;

    private String mismatchReason;

    //任务类型 1、退货 2、拒收 3、拦截
    private Integer taskType;

    //类目名称 鲜果/非鲜果
    private String category;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty(value = "最后修改人admin_id")
    private String updater;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 库存冻结状态 0-未冻结 1-已冻结
     */
    private Integer inventoryLocked;

    /**
     * 系统来源
     */
    private Integer systemSource;

    /**
     * 外部单号
     */
    private String outOrderNo;

    /**
     * 扩展标志位
     */
    private Long optionFlag;

    /**
     * 出库分类 0-默认，1-POP出库 ，2POP T+2出库，3顺路达
     */
    private Integer outboundCategory;

    /**
     * 外部仓
     */
    private String externalWarehouseNo;
}
