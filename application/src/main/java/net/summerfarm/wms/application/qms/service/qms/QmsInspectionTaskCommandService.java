package net.summerfarm.wms.application.qms.service.qms;

import net.summerfarm.wms.application.qms.inbound.controller.qms.input.command.QmsInspectionTaskCommitCommandInput;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.command.QmsInspectionTaskCreateCommandInput;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.command.QmsInspectionTaskRejectChangeCommandInput;
import net.summerfarm.wms.application.qms.inbound.controller.qms.vo.QmsInspectionTaskCommitVO;
import net.summerfarm.wms.application.qms.inbound.controller.qms.vo.QmsInspectionTaskVO;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.xianmu.common.result.CommonResult;

import java.time.LocalDate;

/**
 * @date 2024-04-02 09:34:21
 * @version 1.0
 */
public interface QmsInspectionTaskCommandService {

    /**
     * @description: 新增
     * @return QmsInspectionTaskEntity
     **/
    CommonResult<QmsInspectionTaskVO> insert(QmsInspectionTaskCreateCommandInput input);

    void insertBySomeDayStockInTask(LocalDate now);

    void insertBySomeDayStockInTask(StockTaskStorage stockTaskStorage);

    CommonResult<Integer> close(Long id);

    CommonResult<Integer> reInsp(Long id);

    CommonResult<Integer> reOpen(Long id);

    CommonResult<Integer> rejectClose(Long id, String remark);

    CommonResult<Integer> rejectChange(Long id, QmsInspectionTaskRejectChangeCommandInput commandInput);

    CommonResult<QmsInspectionTaskCommitVO> commitPda(QmsInspectionTaskCommitCommandInput input);

    CommonResult<QmsInspectionTaskCommitVO> commitPc(QmsInspectionTaskCommitCommandInput input);

    void noticeAllInspectTaskWarehouse();

    void yesterdayAllInspectionNoticeFeishu(Integer warehouseNo, LocalDate localDate);
}