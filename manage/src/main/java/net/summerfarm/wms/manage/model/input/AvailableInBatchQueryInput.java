package net.summerfarm.wms.manage.model.input;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/24
 */
@Data
public class AvailableInBatchQueryInput implements Serializable {

    private static final long serialVersionUID = 7000020570382898473L;

    /**
     * 入库任务id
     */
    private Long taskId;

    /**
     * 入库任务类型
     */
    private Integer type;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 止保日期
     */
    private LocalDate qualityDate;

}
