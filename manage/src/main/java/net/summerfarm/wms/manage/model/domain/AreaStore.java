package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@ApiModel(description = "城市库存实体类")
@Data
@Builder
@AllArgsConstructor
public class AreaStore implements Serializable {
    private static final long serialVersionUID = 358086028164837599L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "仓库编号")
    private Integer areaNo;

    @ApiModelProperty(value = "sku编号")
    private String sku;

    @ApiModelProperty(value = "仓库库存数量")
    private Integer quantity;

    @ApiModelProperty(value = "修改时间",hidden = true)
    private Date updateTime;

    @ApiModelProperty(value = "采购负责人ID")
    private Integer adminId;

    @ApiModelProperty(value = "采购提前期")
    private Integer leadTime;

    private int count;

    @ApiModelProperty(value = "采购价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    private Integer priceStatus;

    @ApiModelProperty(value = "预售库存")
    private Integer advanceQuantity;

    @ApiModelProperty(value = "虚拟库存")
    private Integer onlineQuantity;

    @ApiModelProperty(value = "冻结库存")
    private Integer lockQuantity;

    @ApiModelProperty(value = "在途库存")
    private Integer roadQuantity;

    @ApiModelProperty(value = "安全库存")
    private Integer safeQuantity;

    @ApiModelProperty(value = "销售冻结库存")
    private Integer saleLockQuantity;

    @ApiModelProperty(value = "跟可用库存对比变化值")
    private Integer change;

    @ApiModelProperty(value = "是否同步")
    private Integer sync=0 ;

    @ApiModelProperty(value = "是否自动生成转换任务")
    private Boolean autoTransfer;

    @ApiModelProperty(value = "是否支持预留库存 0 不支持 1支持")
    private Integer supportReserved ;

    @ApiModelProperty(value = "预留库存最大值")
    private Integer reserveMaxQuantity;

    @ApiModelProperty(value = "预留库存最小值")
    private Integer reserveMinQuantity;

    /**
     * 预留库存最大值大于0（支持预留）大客户下单就会累加
     * 在线库存展示逻辑： 实际使用 > 最大值减最小值 展示 最大值减最小值
     * 反之 展示预留库存实际使用数量
     */
    @ApiModelProperty(value = "预留库存实际使用数量")
    private Integer reserveUseQuantity;

    @ApiModelProperty(value = "预警库存")
    private Integer warningQuantity;

    /**
     * status状态
     */
    private Integer status;

    /**
     * 是否预警消息提醒过 0 未提醒 1 已提醒
     */
    private Integer sendWarningFlag;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    private Long warehouseTenantId;

    private Long warehouseNo;

    /**
     * 获取销售可用库存
     * @return
     */
    public Integer getSaleAbleQuantity(){
        Integer onlineQuantity = this.getOnlineQuantity();
        Integer reserveMaxQuantity = this.getReserveMaxQuantity();
        Integer reserveMinQuantity = this.getReserveMinQuantity();
        Integer reserveUseQuantity = this.getReserveUseQuantity();
        if (reserveMaxQuantity != null && reserveUseQuantity != null
                && reserveMinQuantity != null && onlineQuantity != null) {
            Integer reserveQuantity = reserveMaxQuantity - reserveMinQuantity;
            onlineQuantity = reserveUseQuantity > reserveQuantity ?
                    onlineQuantity - reserveMinQuantity :
                    onlineQuantity - (reserveMaxQuantity - reserveUseQuantity);
        }
        return onlineQuantity;
    }

    public AreaStore(Integer areaNo, String sku) {
        this.areaNo = areaNo;
        this.sku = sku;
    }

    public AreaStore(Integer areaNo){
        this.areaNo = areaNo;
    }

    public AreaStore() {

    }

    public Long getWarehouseNo() {
        if (this.warehouseNo == null && this.areaNo != null) {
            this.warehouseNo = (long)this.areaNo;
            return this.warehouseNo;
        } else {
            return this.warehouseNo;
        }
    }
}
