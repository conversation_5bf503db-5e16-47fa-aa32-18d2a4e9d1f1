package net.summerfarm.wms.infrastructure.dao.goods;

import net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description sku条码DAO
 * @date 2023/4/28
 */
public interface SkuBarcodeDAO {

    /**
     * create
     *
     * @param skuBarcode
     * @return
     */
    Long create(SkuBarcodeDO skuBarcode);

    /**
     * batch create
     *
     * @param skuBarcodes
     * @return
     */
    Integer creates(List<SkuBarcodeDO> skuBarcodes);

    /**
     * findById
     *
     * @param id
     * @return
     */
    SkuBarcodeDO findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<SkuBarcodeDO> findByIds(List<Long> ids);

    /**
     * update
     *
     * @param skuBarcode
     * @return
     */
    Integer update(SkuBarcodeDO skuBarcode);

    /**
     * updateStatusBySku
     *
     * @param skuBarcode
     * @return
     */
    Integer updateStatusBySku(SkuBarcodeDO skuBarcode);

    /**
     * count
     *
     * @param skuBarcode
     * @return
     */
    Long count(SkuBarcodeDO skuBarcode);

    /**
     * findOne
     *
     * @param skuBarcode
     * @return
     */
    SkuBarcodeDO findOne(SkuBarcodeDO skuBarcode);

    /**
     * list
     *
     * @param skuBarcode
     * @return
     */
    List<SkuBarcodeDO> list(SkuBarcodeDO skuBarcode);

    List<SkuBarcodeDO> listBySkus(List<String> skus);

    /**
     * 批量更新sku的状态内容
     *
     * @param skus   sku编码列表
     * @param status 状态值
     * @return 返回更新的行数
     */
    int updateStatusBySkuList(@Param("skus") List<String> skus, @Param("status") Integer status, @Param("tenantId") Long tenantId, @Param("updateOperator") String updateOperator);
}
