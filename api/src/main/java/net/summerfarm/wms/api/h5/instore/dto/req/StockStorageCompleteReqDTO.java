package net.summerfarm.wms.api.h5.instore.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import net.summerfarm.wms.api.base.BaseReq;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/21  11:09
 * 入库任务完成，关闭
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class StockStorageCompleteReqDTO extends BaseReq {

    /**
     * 任务操作id
     */
    private Long stockStorageId;

    /**
     * 任务操作原因
     */
    private String reason;

    /**
     * 操作人
     */
    private Long adminId;

    /**
     * 上传图片地址
     */
    private List<String> fileAddress;
}
