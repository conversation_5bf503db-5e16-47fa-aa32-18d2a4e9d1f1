package net.summerfarm.wms.domain.instore.enums;

/**
 * <AUTHOR> ct
 * create at:  2022/11/21  11:18
 */
public enum  StockTaskStorageStateEnum {

    NORMAL(0,"正常"),
    FINISH(1,"已完成"),
    CANCEL(2,"已取消"),
    CLOSE(3,"已关闭");

    private int id;

    private String state;

    StockTaskStorageStateEnum(int id, String state){
        this.id = id;
        this.state = state;
    }

    public int getId(){
        return id;
    }

    public String getState(){
        return state;
    }
}
