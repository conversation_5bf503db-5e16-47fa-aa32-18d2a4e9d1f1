package net.summerfarm.wms.application.batch.convert;

import net.summerfarm.wms.api.inner.batch.req.CostBatchDetailReqDTO;
import net.summerfarm.wms.api.inner.batch.req.CostBatchReqDTO;
import net.summerfarm.wms.application.batch.bo.CostBatchBO;
import net.summerfarm.wms.domain.batch.domainobject.CostBatch;
import net.summerfarm.wms.domain.batch.domainobject.CostBatchDetail;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/10/18  11:44
 */
public class CostBatchBoConverter {

    public static CostBatchReqDTO ProduceBatchBOConvert(CostBatch costBatch){
        CostBatchReqDTO costBatchReqDTO = new CostBatchReqDTO();
        costBatchReqDTO.setWarehouseNo(costBatch.getWarehouseNo());
        costBatchReqDTO.setSku(costBatch.getSku());
        costBatchReqDTO.setQuantity(costBatch.getQuantity());
        costBatchReqDTO.setProduceBatchId(costBatch.getProduceBatchId());
        costBatchReqDTO.setPurchaseNo(costBatch.getPurchaseNo());
        costBatchReqDTO.setId(costBatch.getId());
        costBatchReqDTO.setLotType(costBatch.getLotType());
        costBatchReqDTO.setTenantId(costBatch.getTenantId());
        return costBatchReqDTO;
    }


    public static CostBatch CostBatchBOConvert(CostBatchBO costBatchBO){
        CostBatch costBatch = new CostBatch();
        costBatch.setWarehouseNo(costBatchBO.getWarehouseNo());
        costBatch.setSku(costBatchBO.getSku());
        costBatch.setQuantity(costBatchBO.getQuantity());
        costBatch.setProduceBatchId(costBatchBO.getProduceBatchId());
        costBatch.setPurchaseNo(costBatchBO.getPurchaseNo());
        costBatch.setId(costBatchBO.getId());
        costBatch.setLotType(costBatchBO.getLotType());
        costBatch.setTenantId(costBatchBO.getTenantId());
        costBatch.setCost(costBatchBO.getCost());
        return costBatch;
    }

    public static CostBatchBO CostBatchConvert(CostBatch costBatch){
        CostBatchBO costBatchBO = new CostBatchBO();
        costBatchBO.setWarehouseNo(costBatch.getWarehouseNo());
        costBatchBO.setSku(costBatch.getSku());
        costBatchBO.setQuantity(costBatch.getQuantity());
        costBatchBO.setProduceBatchId(costBatch.getProduceBatchId());
        costBatchBO.setPurchaseNo(costBatch.getPurchaseNo());
        costBatchBO.setId(costBatch.getId());
        costBatchBO.setLotType(costBatch.getLotType());
        costBatchBO.setTenantId(costBatch.getTenantId());
        return costBatchBO;
    }

    public static CostBatchBO CostBatchConvert(CostBatchReqDTO costBatchReqDTO){

        if(Objects.isNull(costBatchReqDTO)){
            return null;
        }
        CostBatchBO costBatchBO = CostBatchBO.builder()
                .warehouseNo(costBatchReqDTO.getWarehouseNo())
                .sku(costBatchReqDTO.getSku())
                .produceBatchId(costBatchReqDTO.getProduceBatchId())
                .purchaseNo(costBatchReqDTO.getPurchaseNo())
                .id(costBatchReqDTO.getId())
                .lotType(costBatchReqDTO.getLotType())
                .quantity(costBatchReqDTO.getQuantity())
                .tenantId(costBatchReqDTO.getTenantId())
                .build();
        List<CostBatchDetailReqDTO> costBatchDetailResDTOList = costBatchReqDTO.getCostBatchDetailResDTOList();
        List<CostBatchDetail> costBatchDetails = costBatchDetailResDTOList.stream().map(costBatchDetailReqDTO -> {
            CostBatchDetail costBatchDetail = CostBatchDetailResConverter.CostBatchDetailConvert(costBatchDetailReqDTO);
            return costBatchDetail;
        }).collect(Collectors.toList());
        costBatchBO.setCostBatchDetails(costBatchDetails);
        return costBatchBO;
    }

}


