package net.summerfarm.wms.api.h5.outstore.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 拣货提交明细
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderPickingCommitDetailDTO implements Serializable {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 明细行id
     */
    Long detailId;

    /**
     * 任务编号
     */
    String missionNo;

    /**
     * sku
     */
    String sku;

    /**
     * 库位
     */
    String cabinetNo;

    /**
     * 生产日期
     */
    LocalDate produceDate;

    /**
     * 保质期
     */
    LocalDate qualityDate;

    /**
     * 拣货容器
     */
    String containerNo;

    /**
     * 拣货数量
     */
    Integer quantity;

}
