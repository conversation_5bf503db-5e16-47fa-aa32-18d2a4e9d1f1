package net.summerfarm.wms.application.common.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.summerfarm.wms.common.model.EasyExcelModel;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/9
 */
@Data
public class CabinetExcelReadExModel extends EasyExcelModel {

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "所属仓库")
    private Object warehouseName;

    /**
     * 库位编码
     */
    @ExcelProperty(value = "库位编码")
    private Object cabinetCode;

    /**
     * 库位类型描述（1：高位货架，2：轻型货架，3：地堆）
     */
    private Object cabinetTypeDesc;

    /**
     * 库位属性描述（1：拣货区，2：存储区，3：暂存区）
     */
    @ExcelProperty(value = "库位属性")
    private Object purposeDesc;

    /**
     * 长（单位：m）
     */
    @ExcelProperty(value = "库位长（单位：m)")
    private Object length;

    /**
     * 宽（单位：m）
     */
    @ExcelProperty(value = "库位宽（单位：m)")
    private Object width;

    /**
     * 高（单位：m）
     */
    @ExcelProperty(value = "库位高（单位：m)")
    private Object high;

    /**
     * 允许sku混放数量
     */
    @ExcelProperty(value = "商品混放数")
    private Object allowMixedSkuQuantity;

    /**
     * 允许效期混放数量
     */
    @ExcelProperty(value = "效期混放数")
    private Object allowMixedPeriodQuantity;

    /**
     * 允许批次混放数量
     */
    @ExcelProperty(value = "批次混放数")
    private Object allowMixedBatchQuantity;

    /**
     * 库位顺序
     */
    @ExcelProperty(value = "拣货顺序")
    private Object sequence;

}
