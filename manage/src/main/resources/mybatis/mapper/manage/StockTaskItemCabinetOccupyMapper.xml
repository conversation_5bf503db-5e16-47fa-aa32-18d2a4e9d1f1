<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskItemCabinetOccupyMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.manage.model.domain.StockTaskItemCabinetOccupyDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="stock_task_item_id" jdbcType="BIGINT" property="stockTaskItemId" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="cabinet_code" jdbcType="VARCHAR" property="cabinetCode" />
        <result column="production_date" jdbcType="DATE" property="productionDate" />
        <result column="quality_date" jdbcType="DATE" property="qualityDate" />
        <result column="occupy_quantity" jdbcType="INTEGER" property="occupyQuantity" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
        <result column="pick_quantity" jdbcType="INTEGER" property="pickQuantity" />
        <result column="release_quantity" jdbcType="INTEGER" property="releaseQuantity" />
        <result column="should_quantity" jdbcType="INTEGER" property="shouldQuantity" />
        <result column="actual_pick_quantity" jdbcType="INTEGER" property="actualPickQuantity" />
        <result column="should_pick_quantity" jdbcType="INTEGER" property="shouldPickQuantity"/>
        <result column="abnormal_quantity" jdbcType="INTEGER" property="abnormalQuantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, stock_task_id, stock_task_item_id, warehouse_no, sku, cabinet_code, production_date,
    quality_date, occupy_quantity, create_time, update_time, is_deleted, last_ver, pick_quantity, release_quantity,
            actual_pick_quantity, should_pick_quantity, abnormal_quantity
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where id = #{id}
    </select>

    <select id="selectListByStockTaskIdOccupyed" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0)
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectListByStockTaskIdOccupyedExcludedJJ" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0 or should_pick_quantity > 0)
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code != 'JJ01'
    </select>

    <select id="selectListByStockTaskIdOccupyedExcludedJJV1" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0 or should_pick_quantity > 0)
        and stock_task_id in
        <foreach collection="stockTaskIdList" open="(" close=")" item="stockTaskId" separator=",">
            #{stockTaskId, jdbcType=INTEGER}
        </foreach>
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code != 'JJ01'
    </select>

    <select id="selectListByStockTaskIdOccupyedForJJ" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0)
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code = 'JJ01'
    </select>

    <select id="selectOneByStockTaskIdOccupyed" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0)
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        and sku = #{sku}
        and cabinet_code = #{cabinetCode}
        and production_date = #{productionDate}
        and quality_date = #{qualityDate}
    </select>

    <update id="updatePickChange">
        update wms_stock_task_item_cabinet_occupy
        set occupy_quantity = occupy_quantity - #{pickChangeQuantity},
            pick_quantity = ifnull(pick_quantity, 0) + #{pickChangeQuantity}
        where id = #{id}
          and occupy_quantity >= #{pickChangeQuantity}
    </update>

    <update id="updatePickChangeOnly">
        update wms_stock_task_item_cabinet_occupy
        set pick_quantity = ifnull(pick_quantity, 0) + #{pickChangeQuantity}
        where id = #{id}
    </update>

    <update id="updateReleaseChange">
        update wms_stock_task_item_cabinet_occupy
        set occupy_quantity = occupy_quantity - #{releaseChangeQuantity},
            release_quantity = ifnull(release_quantity, 0) + #{releaseChangeQuantity}
        where id = #{id}
          and occupy_quantity >= #{releaseChangeQuantity}
    </update>

    <select id="selectListByTaskIdListAndSkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0)
        and stock_task_id in
        <foreach collection="stockTaskIdList" open="(" close=")" item="stockTaskId" separator=",">
            #{stockTaskId, jdbcType=INTEGER}
          </foreach>
        and sku in
        <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>
</mapper>