package net.summerfarm.wms.domain.StoreRecord.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/27
 */
@Data
public class ProduceCostDifferentBatchDTO implements Serializable {

    private Long id;

    private Integer warehouseNo;

    private String sku;

    private Integer productQuantity;

    private Integer costQuantity;

    private LocalDate produceAt;

    private LocalDate shelfLife;

}
