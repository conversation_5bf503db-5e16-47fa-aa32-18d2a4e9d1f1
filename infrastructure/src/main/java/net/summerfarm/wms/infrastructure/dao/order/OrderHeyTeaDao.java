package net.summerfarm.wms.infrastructure.dao.order;

import net.summerfarm.wms.infrastructure.dao.order.dataobject.OrderDeliveryDO;
import net.summerfarm.wms.infrastructure.dao.order.dataobject.OrderHeyTeaDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * (OrderHeyTea)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-23 12:59:44
 */
public interface OrderHeyTeaDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    OrderHeyTeaDO queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param orderHeyTeaDO 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<OrderHeyTeaDO> queryAllByLimit(OrderHeyTeaDO orderHeyTeaDO);

    /**
     * 统计总行数
     *
     * @param orderHeyTeaDO 查询条件
     * @return 总行数
     */
    long count(OrderHeyTeaDO orderHeyTeaDO);

    /**
     * 新增数据
     *
     * @param orderHeyTeaDO 实例对象
     * @return 影响行数
     */
    int insert(OrderHeyTeaDO orderHeyTeaDO);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<OrderHeyTea> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<OrderHeyTeaDO> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<OrderHeyTea> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<OrderHeyTeaDO> entities);

    /**
     * 修改数据
     *
     * @param orderHeyTeaDO 实例对象
     * @return 影响行数
     */
    int update(OrderHeyTeaDO orderHeyTeaDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<OrderHeyTeaDO> listByHtOrderCode(@Param("htOrderCodeList") List<String> htOrderCodeList);

    List<OrderHeyTeaDO> listByOrderCode(@Param("orderCodeList") List<String> orderCodeList);


    List<OrderDeliveryDO> listDeliveryPlanByOrderNoAndDeliveryTime(
            @Param("deliveryTime") String deliveryTime,
            @Param("orderNoList") List<String> orderNoList);
}

