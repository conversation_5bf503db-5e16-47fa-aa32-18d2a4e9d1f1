package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.wms.manage.model.vo.StockSubscribeVO;
import net.summerfarm.wms.manage.model.vo.StockTaskVO;
import net.summerfarm.wms.manage.model.vo.StoreRecordVO;
import net.summerfarm.wms.manage.web.req.StockTaskReq;
import net.summerfarm.wms.manage.model.domain.PurchasesPlan;
import net.summerfarm.wms.manage.model.domain.StockTask;
import net.summerfarm.wms.manage.model.domain.StockTaskWaveConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import net.summerfarm.wms.manage.model.domain.StockTaskItem;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Repository
public interface StockTaskMapper {

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectAllotIn(StockTaskReq stockTaskReq);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectAllotOut(StockTaskReq stockTaskReq);

    @RequiresDataPermission(originalField = "t.area_no")
    List<StockTaskVO> selectWithItem(@Param("id") Integer id, @Param("querySkus") List<String> querySkus, @Param("sku") String sku
            , @Param("list") List<Integer> types, @Param("type") Integer type, @Param("typeList") List<Integer> typeList, @Param("areaNo") Integer areaNo, @Param("state") Integer state
            , @Param("stateList") List<Integer> states, @Param("taskNo") String taskNo, @Param("storeNo") Integer storeNo, @Param("supplierId") Integer supplierId
            , @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("processState") Integer processState, @Param("mailPushState") Integer mailPushState,
                                     @Param("startAddTime") LocalDateTime startAddTime, @Param("endAddTime") LocalDateTime endAddTime, @Param("tenantId") Long tenantId,
                                     @Param("warehouseNoList") List<Integer> warehouseNoList,@Param("outboundCategory") Integer outboundCategory);

    StockTask selectByPrimaryKey(Integer id);

    /**
     * 根据出库任务编号查询出库任务的推送状态位
     * @param idList 出库任务id列表
     * @return 出库任务列表
     */
    List<StockTask> selectOptionFlagByIdList(@Param("idList") List<Integer> idList);

    /**
     * 根据出库任务id更新邮件推送状态
     * @param stockTask 出库任务
     * @return 更新结果
     */
    int updateOptionFlagById(StockTask stockTask);

    /**
     * 根据id list查询出库任务列表
     * @param list id list
     * @return 出库任务列表
     */
    List<StockTask> selectListByPrimaryKey(@Param("list") List<Integer> list);

    /**
     * 根据出库任务id查询任务扩展状态位
     * @param id 出库任务id
     * @return 查询结果
     */
    StockTask selectOptionFlagById(@Param("id") Integer id);

    StockTask selectOne(@Param("taskNo") String taskNo, @Param("type") Integer type);

    int update(StockTask stockTask);

    int updateByTaskNo(StockTask stockTask);

    List<StockTaskItem> selectStoreingSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    //查询采购入库中的sku
    List<PurchasesPlan> selectPurchasingSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    int insert(StockTask stockTask);

    List<StockTaskWaveConfig> queryWaveConfig();

    int updateByPrimaryKey(StockTask stockTask);

    StockTask selectBySku(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("list") List<Integer> types);

    List<StockTask> selectList(@Param("taskNoList") List<String> taskNoList, @Param("type") Integer type);

    List<StockTask> selectListByOutStoreNo(@Param("areaNo") Integer areaNo, @Param("outStoreNo") Integer outStoreNo, @Param("expectTime") LocalDate expectTime, @Param("type") Integer type);

    List<Integer> selectWaveNotOccupyTaskIds(@Param("outStoreNos") List<Integer> outStoreNos, @Param("expectTime") LocalDate expectTime, @Param("type") Integer type);

    List<StoreRecordVO> selectQuantitySumExcludeProducts(StockTaskReq stockTaskReq);

    List<StoreRecordVO> selectSaleTaskQuantitySumEx(StockTaskReq stockTaskReq);

    int selectSaleTaskQuantitySumExCount(StockTaskReq stockTaskReq);

    // 待出库调拨任务
    List<StockSubscribeVO> waitOutAllocationTask(@Param("areaNo") Integer areaNo, @Param("expectTime") LocalDate expectTime);

    List<StoreRecordVO> selectQuantitySumEx(StockTaskReq stockTaskReq);

    int selectQuantitySumExCount(StockTaskReq stockTaskReq);


}
