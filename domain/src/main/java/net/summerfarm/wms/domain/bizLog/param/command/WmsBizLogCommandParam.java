package net.summerfarm.wms.domain.bizLog.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-04-10 11:06:13
 * @version 1.0
 *
 */
@Data
public class WmsBizLogCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 业务类型
	 */
	private String bizType;

	/**
	 * 业务编号
	 */
	private String bizNo;

	/**
	 * 业务id
	 */
	private Long bizId;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 内容
	 */
	private String content;

	/**
	 * 操作人名称
	 */
	private String operatorName;

	

	
}