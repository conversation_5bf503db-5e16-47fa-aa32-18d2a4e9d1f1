package net.summerfarm.wms.facade.admin.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminQueryFacade implements Serializable {

    private static final long serialVersionUID = -2398876429332515914L;

    private List<Long> adminIds;
}
