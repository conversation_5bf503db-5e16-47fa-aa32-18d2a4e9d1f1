package net.summerfarm.wms.application.stocktask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.stocktask.MissionTaskItemMappingQueryService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.MissionTaskItemMappingSkuQuery;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.*;
import net.summerfarm.wms.application.stocktask.convert.MissionTaskItemMappingForOutCompletedDTOConverter;
import net.summerfarm.wms.application.stocktask.convert.MissionTaskItemMappingRespConverter;
import net.summerfarm.wms.domain.mission.domainobject.MissionTaskItemMapping;
import net.summerfarm.wms.domain.mission.domainobject.query.MissionTaskItemMappingQuery;
import net.summerfarm.wms.domain.mission.enums.MissionTaskItemMappingStatusEnum;
import net.summerfarm.wms.domain.mission.repository.MissionTaskItemMappingRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/5
 */
@Slf4j
@Service
public class MissionTaskItemMappingQueryServiceImpl implements MissionTaskItemMappingQueryService {

    @Resource
    private MissionTaskItemMappingRepository missionTaskItemMappingRepository;

    @Override
    public MissionTaskItemMappingResp queryCompletePickedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId()) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setStatus(MissionTaskItemMappingStatusEnum.PICK_COMPLETE.getCode());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.list(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingForPCResp queryCompletePickedMissionTaskItemMappingForPC(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId()) {
            return new MissionTaskItemMappingForPCResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
//        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setStatus(MissionTaskItemMappingStatusEnum.PICK_COMPLETE.getCode());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.list(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingForOutCompletedDTO> missionTaskItemMappingForOutCompletedDTOList = missionTaskItemMappingList.stream().map(MissionTaskItemMappingForOutCompletedDTOConverter.INSTANCE::convert).collect(Collectors.toList());
        MissionTaskItemMappingForPCResp missionTaskItemMappingResp = new MissionTaskItemMappingForPCResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingForOutCompletedDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingResp queryNoPickedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId()) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setStatus(MissionTaskItemMappingStatusEnum.PICK_CREATE.getCode());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.list(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingResp queryDiffConfirmedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId() || CollectionUtils.isEmpty(missionTaskItemMappingSkuQuery.getSkus())) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setStatus(MissionTaskItemMappingStatusEnum.OUT_CONFIRM.getCode());
        missionTaskItemMappingQuery.setCabinetCode(missionTaskItemMappingSkuQuery.getCabinetCode());
        missionTaskItemMappingQuery.setQualityDate(missionTaskItemMappingQuery.getQualityDate());
        missionTaskItemMappingQuery.setProductionDate(missionTaskItemMappingQuery.getProductionDate());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        List<MissionTaskItemMapping> filterMissionTaskItemMappingList = missionTaskItemMappingList.stream().filter(item -> (null != item.getPickQuantity()
                && null != item.getActualQuantity()
                && !item.getPickQuantity().equals(item.getActualQuantity()))
                || (null != item.getAbnormalQuantity() && item.getAbnormalQuantity() > 0))
                .collect(Collectors.toList());
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(filterMissionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingResp queryDiffOutMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId() || CollectionUtils.isEmpty(missionTaskItemMappingSkuQuery.getSkus())) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setStatus(MissionTaskItemMappingStatusEnum.DIFF_CONFIRM.getCode());
        missionTaskItemMappingQuery.setCabinetCode(missionTaskItemMappingSkuQuery.getCabinetCode());
        missionTaskItemMappingQuery.setQualityDate(missionTaskItemMappingQuery.getQualityDate());
        missionTaskItemMappingQuery.setProductionDate(missionTaskItemMappingQuery.getProductionDate());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingResp queryInitMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId() || CollectionUtils.isEmpty(missionTaskItemMappingSkuQuery.getSkus())) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        missionTaskItemMappingQuery.setCabinetCode(missionTaskItemMappingSkuQuery.getCabinetCode());
        missionTaskItemMappingQuery.setQualityDate(missionTaskItemMappingQuery.getQualityDate());
        missionTaskItemMappingQuery.setProductionDate(missionTaskItemMappingQuery.getProductionDate());
        missionTaskItemMappingQuery.setInitQuantityGtZero(true);
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    @Override
    public MissionTaskItemMappingResp queryAllMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId() || CollectionUtils.isEmpty(missionTaskItemMappingSkuQuery.getSkus())) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        missionTaskItemMappingQuery.setSkus(missionTaskItemMappingSkuQuery.getSkus());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

    /**
     * 统计任务关系映射列表
     * @param missionTaskItemMappingSkuQuery 查询条件
     * @return 返回统计任务关系映射列表
     */
    @Override
    public MissionTaskItemMappingCountResp countMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery) {
        if (null == missionTaskItemMappingSkuQuery || null == missionTaskItemMappingSkuQuery.getStockTaskId()) {
            return new MissionTaskItemMappingCountResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setStockTaskId(missionTaskItemMappingSkuQuery.getStockTaskId());
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        // 拣货任务生成数量
        long pickCreateNum = missionTaskItemMappingList.stream()
                .filter(it -> MissionTaskItemMappingStatusEnum.PICK_CREATE.getCode().equals(it.getStatus()))
                .count();
        // 拣货任务完成
        long pickCompleteNum = missionTaskItemMappingList.stream()
                .filter(it -> MissionTaskItemMappingStatusEnum.PICK_COMPLETE.getCode().equals(it.getStatus()))
                .count();
        // 拣货任务生成数量
        long outConfirmNum = missionTaskItemMappingList.stream()
                .filter(it -> MissionTaskItemMappingStatusEnum.OUT_CONFIRM.getCode().equals(it.getStatus()))
                .count();
        // 拣货任务生成数量
        long diffConfirmNum = missionTaskItemMappingList.stream()
                .filter(it -> MissionTaskItemMappingStatusEnum.DIFF_CONFIRM.getCode().equals(it.getStatus()))
                .count();

        return MissionTaskItemMappingCountResp.builder()
                .pickCreateNum(pickCreateNum)
                .pickCompleteNum(pickCompleteNum)
                .outConfirmNum(outConfirmNum)
                .diffConfirmNum(diffConfirmNum)
                .build();
    }

    @Override
    public MissionTaskItemMappingResp queryMissionTaskItemMappingByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new MissionTaskItemMappingResp();
        }
        MissionTaskItemMappingQuery missionTaskItemMappingQuery = new MissionTaskItemMappingQuery();
        missionTaskItemMappingQuery.setIds(idList);
        List<MissionTaskItemMapping> missionTaskItemMappingList = missionTaskItemMappingRepository.listByMaster(missionTaskItemMappingQuery);
        List<MissionTaskItemMappingDTO> missionTaskItemMappingDTOList = MissionTaskItemMappingRespConverter.INSTANCE.convertList(missionTaskItemMappingList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = new MissionTaskItemMappingResp();
        missionTaskItemMappingResp.setMissionTaskItemMappings(missionTaskItemMappingDTOList);
        return missionTaskItemMappingResp;
    }

}
