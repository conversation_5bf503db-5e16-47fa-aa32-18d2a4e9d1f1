package net.summerfarm.wms.manage.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
@Data
public class WmsPesticideResidueReport implements Serializable {
    private static final long serialVersionUID = -4814645351437965787L;

    /**
     * 状态 生效
     */
    public static final Integer REPORT_STATUS_HAVE = 0;

    /**
     * 失效
     */
    public static final Integer REPORT_STATUS_VLI = 1;


    private Integer id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
    /**
     * '批次'
     */
    private  String batch;
    /**
     * sku
     */
    private String sku;

    /**
     * 状态处理
     */
    private  Integer status;

    /**
     * '检测结果 0 合格 1 不合格'
     */
    private Integer detectionResult;

    /**
     * '抽样基数'
     */
    private BigDecimal samplingBase ;
    /**
     * '抽样数'
     */
    private BigDecimal numberSamples;
    /**
     * '抑制率'
     */
    private BigDecimal inhibitionRate;

    /**
     * 图片
     */
    private String pictureUrl;


    /**
     * 保质期
     */
    private LocalDate productionDate;


    public WmsPesticideResidueReport(){}

    public WmsPesticideResidueReport(String sku,String  batch,LocalDate productionDate){
        this.sku = sku;
        this.batch = batch;
        this.productionDate = productionDate;
    }

}
