package net.summerfarm.wms.infrastructure.dao.skucode;

import net.summerfarm.wms.domain.skucode.domainobject.SkuBatchCode;
import net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> ct
 * create at:  2021/12/13  13:54
 */
public interface SkuBatchCodeDAO {

    /**
     * 插入批次唯一码信息
     *
     * @param batchCode
     * @return
     */
    int saveSkuBatchCode(SkuBatchCodeDO batchCode);

    int saveSkuBatchCodeForSync(SkuBatchCodeDO batchCode);

    /**
     * 修改唯一码信息
     *
     * @param batchCode
     * @return
     */
    int updateSkuBatchCode(SkuBatchCodeDO batchCode);

    int updateSkuBatchCodeForSync(SkuBatchCodeDO batchCode);

    /**
     * 根据批次 sku 生产日期获取批次信息
     *
     * @param batchCode
     * @return
     */
    SkuBatchCodeDO querySkuBatchCode(SkuBatchCodeDO batchCode);

    SkuBatchCodeDO querySkuBatchCodeForSync(SkuBatchCodeDO batchCode);


    List<SkuBatchCodeDO> querySkuBatchCodeByCd(@Param("sku") String sku, @Param("purchaseNo") String purchaseNo,
                                         @Param("productionDate") LocalDate productionDate, @Param("skuBatchOnlyCode") String skuBatchOnlyCode,
                                         @Param("bizId") Long bizId, @Param("bizType") Integer bizType);

    /**
     * 根据转入batch+sku查询
     */
    List<SkuBatchCodeDO> listBySkuAndBatches(@Param("skus") List<String> skus, @Param("batches") List<String> batches);

    List<SkuBatchCodeDO> listBySkus(@Param("skus") Set<String> skus);

    /**
     * 批量查询
     *
     * @param batchCode
     * @return
     */
    List<SkuBatchCodeDO> queryBatchSkuBatchCode(@Param("batchCode") List<String> batchCode);

    SkuBatchCodeDO queryProduceBatchByWnoAndSkuCodeAndDate(@Param("sku") String sku, @Param("purchaseNo") String purchaseNo,
                                                         @Param("productionDate") Date productionDate,
                                                         @Param("qualityDate") Date qualityDate);

    /**
     * 根据批次唯一码获取数据 - 批量
     *
     * @param batchCodeList 批次号集合
     * @return {@link List}<{@link SkuBatchCode}>
     */
    List<SkuBatchCodeDO> batchQuerySkuBatchCodeForSync(@Param("batchCodeList") List<String> batchCodeList);

    /**
     * 查询批次码 通过业务id类型和sku列表
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @param skuList sku列表
     * @return 返回批次码
     */
    List<SkuBatchCodeDO> queryByBizIdAndSkuList(@Param("bizId") Long bizId,
                                                @Param("bizType") Integer bizType,
                                                @Param("skuList") List<String> skuList);


    /**
     * 修改批次类型
     *
     * @param batchType 转换批次类型
     * @param batchs    批次信息列表
     * @return 返回转换任务批次数量
     */
    int updateSkuBatchType(@Param("batchType") Integer batchType, @Param("batchs") List<String> batchs);

    /**
     * 追加打印次数
     * @param id
     * @param canPrintNumber
     * @return
     */
    int addPrintNumber(@Param("id") Integer id, @Param("canPrintNumber") Integer canPrintNumber);
}
