<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StoreGoodsTaskMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.manage.model.domain.po.StoreGoodsTaskPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_source" jdbcType="INTEGER" property="taskSource"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="pd_id" jdbcType="BIGINT" property="pdId"/>
        <result column="sku" jdbcType="INTEGER" property="sku"/>
        <result column="sku_name" jdbcType="INTEGER" property="skuName"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="storage_location" jdbcType="TINYINT" property="storageLocation"/>
        <result column="volume" jdbcType="VARCHAR" property="volume"/>
        <result column="weight_num" jdbcType="DECIMAL" property="weightNum"/>
        <result column="push_status" jdbcType="TINYINT" property="pushStatus"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated"/>
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="is_domestic" jdbcType="TINYINT" property="isDomestic"/>
        <result column="quality_time" jdbcType="INTEGER" property="qualityTime"/>
        <result column="quality_time_unit" jdbcType="VARCHAR" property="qualityTimeUnit"/>
        <result column="quality_time_type" jdbcType="TINYINT" property="qualityTimeType"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , task_source, task_status, warehouse_no, pd_id, sku, sku_name, weight, storage_location,
    volume, weight_num, push_status, creator, operator, gmt_created, gmt_modified, is_deleted, last_ver,
    unit, is_domestic, quality_time, quality_time_unit, quality_time_type, pic_url
    </sql>

    <select id="selectBySkuListAndStatus" resultType="java.lang.String">
        select distinct sku
        from goods_check_task
        where sku in
        <foreach collection="skuList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and task_status = #{taskStatus}
    </select>

    <select id="selectBySkuAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where sku = #{sku}
        and task_status = #{taskStatus}
    </select>

    <insert id="insert" parameterType="net.summerfarm.wms.manage.model.domain.po.StoreGoodsTaskPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into goods_check_task (task_source, task_status,
        warehouse_no, pd_id, sku, sku_name,
        weight, storage_location, volume,
        weight_num, push_status, creator, operator,
        gmt_created, gmt_modified, is_deleted,
        last_ver, unit, is_domestic, quality_time,
        quality_time_unit, quality_time_type, pic_url)
        values ( #{taskSource,jdbcType=INTEGER}, #{taskStatus,jdbcType=INTEGER},
        #{warehouseNo,jdbcType=INTEGER}, #{pdId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR}, #{storageLocation,jdbcType=TINYINT}, #{volume,jdbcType=VARCHAR},
        #{weightNum,jdbcType=DECIMAL}, #{pushStatus,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
        #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT}, 0, 1,
        #{unit,jdbcType=VARCHAR}, #{isDomestic,jdbcType=TINYINT}, #{qualityTime,jdbcType=INTEGER}, #{qualityTimeUnit,jdbcType=VARCHAR},
        #{qualityTimeType,jdbcType=TINYINT}, #{picUrl,jdbcType=VARCHAR})
    </insert>

    <select id="selectBySkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where sku in
        <foreach collection="skuList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectBySkuAndWarehouseNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where task_status = #{taskStatus}
        and sku in
        <foreach collection="skuCodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>