package net.summerfarm.wms.manage.web.utils;

import net.summerfarm.wms.manage.common.function.BranchFunction;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
public class FunctionalUtil {

    public static BranchFunction isTureOrFalse(boolean flag){
        return (trueHandle, falseHandle) -> {
            if (flag){
                trueHandle.run();
            } else {
                falseHandle.run();
            }
        };
    }

}
