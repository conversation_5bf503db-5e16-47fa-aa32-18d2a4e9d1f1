package net.summerfarm.wms.scheduler.external;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.inventory.biz.impl.ExternalInventoryCompareService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 三方仓库存对账 定时任务
 */
@Slf4j
@Component
public class ExternalInventoryCompareJOB extends XianMuJavaProcessorV2 {

    @Autowired
    private ExternalInventoryCompareService externalInventoryCompareService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        // 比对时间默认为当前日期
        LocalDate compareTime = LocalDate.now();
        if (!StringUtils.isEmpty(context.getInstanceParameters())) {
            try {
                compareTime = LocalDate.parse(context.getInstanceParameters(), DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e) {
                log.error("日期参数错误: {}", e.getMessage());
                return new ProcessResult(false, "日期参数错误");
            }
        }

        log.info("三方仓库存对账 定时任务-开始执行，比对日期 >>> {}", compareTime);
        externalInventoryCompareService.compare(compareTime);
        log.info("三方仓库存对账 定时任务-结束执行，比对日期 >>> {}", compareTime);
        return new ProcessResult(true);
    }
}