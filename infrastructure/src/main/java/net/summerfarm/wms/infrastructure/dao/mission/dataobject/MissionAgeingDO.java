package net.summerfarm.wms.infrastructure.dao.mission.dataobject;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;
import lombok.Data;

/**
 * wms_mission_ageing
 * <AUTHOR>
@Data
public class MissionAgeingDO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 仓库号
     */
    private Long warehouseNo;

    /**
     * 任务编号
     */
    private String missionNo;

    /**
     * 任务类型
     */
    private Integer missionType;

    /**
     * sku
     */
    private String sku;

    /**
     * 库位
     */
    private String cabinetNo;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 时效
     */
    private Date ageing;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private String opeartorId;

    private static final long serialVersionUID = 1L;
}