package net.summerfarm.wms.domain.roadsale.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.enums.OrderRoadSaleRecordTypeEnum;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2023/9/14 14:16
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderRoadSaleUpdateValue {

    private Integer operateRoadSaleQuantity;

    private Integer operateRemainingRoadSaleQuantity;

    private Integer operateInStoreRoadSaleQuantity;

    private Integer operateRejectRoadSaleQuantity;

    private BigDecimal operateRoadSaleRate;

    private Integer originalRoadSaleQuantity;

    private Integer originalRemainingRoadSaleQuantity;

    private Integer originalInStoreRoadSaleQuantity;

    private Integer originalRejectRoadSaleQuantity;

    private OrderRoadSaleRecordTypeEnum recordTypeEnum;

}
