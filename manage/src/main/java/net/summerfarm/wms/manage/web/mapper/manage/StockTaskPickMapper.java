package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StockTaskPick;
import net.summerfarm.wms.manage.model.vo.StockTaskPickVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Repository
public interface StockTaskPickMapper {

    int insertBatch(List<StockTaskPick> list);


    List<StockTaskPickVO> selectTaskPickByType(@Param("type") Integer type, @Param("storeNo") Integer storeNo,
                                               @Param("deliveryTime") LocalDate deliveryTime, @Param("categoryType") Integer categoryType,
                                               @Param("closeOrderTime") String closeOrderTime);


    /**
     * 根据id查询捡货单
     * @param ids
     * @return
     */
    List<StockTaskPickVO> selectByBatchId(@Param("ids") List<Integer> ids);

}
