package net.summerfarm.wms.application.download.adapter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.filedown.handler.FileDownLoadHandler;
import net.summerfarm.wms.api.h5.filedown.req.FileDownloadAsyncQuery;
import net.summerfarm.wms.application.download.converter.FileDownloadConverter;
import net.summerfarm.wms.domain.download.domainobject.FileDownloadAsyncDTO;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Objects;

/**
 * 文件导出适配器
 * @author: xdc
 * @date: 2024/2/22
 **/
@Component
@Slf4j
public class WmsDownLoadExportAdapter implements ApplicationRunner {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 文件导出服务列表
     */
    private Collection<FileDownLoadHandler> fileDownLoadHandlerList;

    /**
     * springboot启动后执行初始化
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("加载文件导出适配器列表-开始");
        fileDownLoadHandlerList = applicationContext
                .getBeansOfType(FileDownLoadHandler.class)
                .values();
        if (CollectionUtils.isNotEmpty(fileDownLoadHandlerList)) {
            fileDownLoadHandlerList.forEach(fileDownLoadHandler -> {
                log.info("文件导出服务:" + fileDownLoadHandler.getClass().getName());
            });
        }
        log.info("加载文件导出适配器列表-完成");
    }

    public void executeExport(FileDownloadAsyncDTO fileDownloadAsyncDTO) {
        log.info("执行文件下载参数:{}", JSON.toJSONString(fileDownloadAsyncDTO));
        if (Objects.isNull(fileDownloadAsyncDTO)) {
            return;
        }
        // 初始化判断
        if (CollectionUtils.isEmpty(fileDownLoadHandlerList)) {
            throw new BizException("还未完成异步下载初始化, 请稍后重试");
        }
        // 获取文件下载处理服务
        FileDownLoadHandler downLoadService = fileDownLoadHandlerList.stream()
                .filter(fileDownLoadHandler -> fileDownLoadHandler.support(fileDownloadAsyncDTO.getFileDownloadMessageType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(downLoadService)) {
            // 文件下载类型请参考 @see net.summerfarm.wms.domain.download.enums.WmsFileDownloadMessageType
            log.error("未知文件下载类型 {} \n ", fileDownloadAsyncDTO.getFileDownloadMessageType());
            return;
        }

        FileDownloadAsyncQuery downloadAsyncQuery = FileDownloadConverter.INSTANCE.convert(fileDownloadAsyncDTO);
        // 执行文件下载
        downLoadService.asyncExport(downloadAsyncQuery);
    }


}
