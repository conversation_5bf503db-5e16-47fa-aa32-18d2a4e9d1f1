package net.summerfarm.wms.manage.model.domain.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreGoodsTaskPO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务来源 1-定时任务 2-手动新增 3-新品入仓
     */
    private Integer taskSource;

    /**
     * 任务状态 1-待完成 2-已完成
     */
    private Integer taskStatus;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 商品ID
     */
    private Long pdId;

    /**
     * 商品sku编码
     */
    private String sku;

    /**
     * 商品sku名称
     */
    private String skuName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 存储区域
     */
    private Integer storageLocation;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 推送状态
     */
    private Integer pushStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;

    /**
     * 是否软删
     */
    private Byte isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 规格，包装
     */
    private String unit;

    /**
     * 是否国产（0：不是，1：是）
     */
    private Integer isDomestic;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期时长单位（day，month）
     */
    private String qualityTimeUnit;

    /**
     * 保质期时长类型, 0 固定时长, 1 到期时间
     */
    private Integer qualityTimeType;

    /**
     * 照片url
     */
    private String picUrl;

}
