package net.summerfarm.wms.data;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.stocktask.StockTaskCommandService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCreateCommand;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.data.dto.GoodsSupplyAppendTaskDTO;
import net.summerfarm.wms.data.dto.GoodsSupplyCreateTaskDTO;
import net.summerfarm.wms.domain.stocktask.StockTaskNoticeOrderDetailRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskNoticeOrderRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderDetail;
import net.summerfarm.wms.inventory.WmsInventoryQueryProvider;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AvailableInventory4ACBatchQueryReq;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AvailableInventory4ACQueryReq;
import net.summerfarm.wms.inventory.resp.AvailableInventory4ACResp;
import net.summerfarm.wms.manage.model.domain.StockTaskItem;
import net.summerfarm.wms.manage.model.domain.StockTaskOrderSku;
import net.summerfarm.wms.manage.web.enums.StockTaskType;
import net.summerfarm.wms.manage.web.mapper.manage.OrdersMapper;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskItemMapper;
import net.summerfarm.wms.manage.web.service.StockTaskOrderSkuService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping(value = "/notice/tool")
public class OutNoticeToolController {

    @Resource
    private StockTaskNoticeOrderRepository stockTaskNoticeOrderRepository;

    @Resource
    private StockTaskNoticeOrderDetailRepository stockTaskNoticeOrderDetailRepository;

    @Resource
    private StockTaskRepository stockTaskRepository;

    @Resource
    private StockTaskItemMapper stockTaskItemMapper;

    @Resource
    private StockTaskOrderSkuService stockTaskOrderSkuService;

    @Resource
    private StockTaskCommandService stockTaskCommandService;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private WmsInventoryQueryProvider wmsInventoryQueryProvider;

    @PostMapping(value = "/test_query_av")
    public CommonResult<AvailableInventory4ACResp> queryAvailableInventory4AC(@RequestBody AvailableInventory4ACQueryReq req){
        return CommonResult.ok(wmsInventoryQueryProvider.queryAvailableInventory4AC(req).getData());
    }

    /**
     * 测试 查询仓库公式可用库存（批量） - 设置预售订单校验库存专用
     * @param req req
     * @return CommonResult
     */
    @PostMapping(value = "/test_query_av4PreSaleOrderValidate")
    public CommonResult<List<AvailableInventory4ACResp>> listAvailableInventory4PreSaleOrderValidate(@RequestBody AvailableInventory4ACBatchQueryReq req){
        return CommonResult.ok(wmsInventoryQueryProvider.listAvailableInventory4PreSaleOrderValidate(req).getData());
    }

    @PostMapping(value = "/append_task_detail")
    @Transactional(rollbackFor = Exception.class)
    public void appendStockTask(@RequestBody GoodsSupplyAppendTaskDTO dto) {
        // 后门使用前请评估可行性
        StockTaskNoticeOrder noticeOrder = stockTaskNoticeOrderRepository.findByGoodsSupplyNo(dto.getGoodsSupplyNo());
        Integer warehouseNo = noticeOrder.getWarehouseNo();
        Integer storeNo = noticeOrder.getStoreNo();
        StockTask stockTask = stockTaskRepository.selectByWarehouseNoAndStoreNo(warehouseNo.longValue(), storeNo.longValue());
        // 任务出库时间在今天之前的，走新增
        if (stockTask.getExpectTime().toLocalDate().isBefore(LocalDate.now())) {
            stockTask = null;
        }
        if (Objects.isNull(stockTask)) {
            throw new BizException("无可追加的出库任务");
        }
        List<StockTaskNoticeOrderDetail> stockTaskNoticeOrderDetails = stockTaskNoticeOrderDetailRepository.listByNoticeOrderId(noticeOrder.getId());
        // 追加
        StockTask finalStockTask = stockTask;
        // 明细-orderSku
        stockTaskNoticeOrderDetails.stream().collect(Collectors.groupingBy(StockTaskNoticeOrderDetail::getSku))
                .forEach((sku, items) -> {
                    StockTaskItem item1 = new StockTaskItem();
                    item1.setSku(sku);
                    item1.setQuantity(items.stream().mapToInt(StockTaskNoticeOrderDetail::getQuantity).sum());
                    item1.setStockTaskId(finalStockTask.getId().intValue());
                    stockTaskItemMapper.insert(item1);

                    StockTaskOrderSku orderSku = StockTaskOrderSku.builder()
                            .stockTaskId(finalStockTask.getId())
                            .outOrderNo(noticeOrder.getOutOrderNo())
                            .sku(sku)
                            .quantity(items.stream().mapToInt(StockTaskNoticeOrderDetail::getQuantity).sum())
                            .actualQuantity(NumberUtils.INTEGER_ZERO)
                            .abnormalQuantity(NumberUtils.INTEGER_ZERO)
                            .creator(StockTaskType.getNameById(finalStockTask.getType()))
                            .operator(StockTaskType.getNameById(finalStockTask.getType())).build();

                    stockTaskOrderSkuService.batchInsert(Lists.newArrayList(orderSku));
                });

        // 订单状态改为出库
        ordersMapper.batchUpdateByOrderNoList(Lists.newArrayList(noticeOrder.getOutOrderNo()), 1);
    }

    @PostMapping(value = "/create_task")
    @Transactional(rollbackFor = Exception.class)
    public void createStockTask(@RequestBody GoodsSupplyCreateTaskDTO dto) {
        List<StockTaskNoticeOrder> orders = stockTaskNoticeOrderRepository.findByExceptTime(dto.getWarehouseNo().intValue(),
                dto.getStoreNo().intValue(), DateUtil.parseLocalDateTime(dto.getExpectTime()));
        if (CollectionUtils.isEmpty(orders)) {
            throw new BizException("无对应的出库通知单");
        }
        StockTaskCreateCommand stockTaskCommand = StockTaskCreateCommand.builder()
                .noticeOrderIdList(orders.stream().map(StockTaskNoticeOrder::getId).collect(Collectors.toList()))
                .build();
        stockTaskCommandService.batchCreateStockTask(stockTaskCommand);

        try {
            List<String> collect = orders.stream().map(StockTaskNoticeOrder::getOutOrderNo).collect(Collectors.toList());
            //更新订单信息
            if (CollectionUtils.isNotEmpty(collect)) {
                List<List<String>> orderNosList = Lists.partition(collect, 1000);
                for (List<String> tmpOrderNos : orderNosList) {
                    ordersMapper.batchUpdateByOrderNoList(tmpOrderNos, 1);
                }
            }
        } catch (Exception e) {
            log.warn("货品供应单生成出库任务后门改订单状态失败", e);
        }
    }

    @PostMapping(value = "/create_task_one_supply")
    @Transactional(rollbackFor = Exception.class)
    public void createStockTaskByGoodsSupplyNo(@RequestBody GoodsSupplyCreateTaskDTO dto) {
        List<StockTaskNoticeOrder> orders = stockTaskNoticeOrderRepository.findByGoodsSupplyNoList(dto.getGoodsSupplyNos());
        if (CollectionUtils.isEmpty(orders)) {
            throw new BizException("无对应的出库通知单");
        }
        StockTaskCreateCommand stockTaskCommand = StockTaskCreateCommand.builder()
                .noticeOrderIdList(orders.stream().map(StockTaskNoticeOrder::getId).collect(Collectors.toList()))
                .build();
        // todo dhm 改订单状态
        stockTaskCommandService.batchCreateStockTask(stockTaskCommand);

        try {
            List<String> collect = orders.stream().map(StockTaskNoticeOrder::getOutOrderNo).collect(Collectors.toList());
            //更新订单信息
            if (CollectionUtils.isNotEmpty(collect)) {
                List<List<String>> orderNosList = Lists.partition(collect, 1000);
                for (List<String> tmpOrderNos : orderNosList) {
                    ordersMapper.batchUpdateByOrderNoList(tmpOrderNos, 1);
                }
            }
        } catch (Exception e) {
            log.warn("货品供应单生成出库任务后门改订单状态失败", e);
        }
    }
}
