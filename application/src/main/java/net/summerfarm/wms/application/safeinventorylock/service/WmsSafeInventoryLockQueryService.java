package net.summerfarm.wms.application.safeinventorylock.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.safeinventorylock.input.query.WmsSafeInventoryLockQueryInput;
import net.summerfarm.wms.application.safeinventorylock.vo.WmsSafeInventoryLockVO;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 安全库存锁定表查询服务接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface WmsSafeInventoryLockQueryService {

    /**
     * 分页查询
     * @param input 查询输入
     * @return 分页结果
     */
    CommonResult<PageInfo<WmsSafeInventoryLockVO>> getPage(WmsSafeInventoryLockQueryInput input);

    /**
     * getById
     * @param id
     * @return
     */
    WmsSafeInventoryLockEntity getById(Long id);

}
