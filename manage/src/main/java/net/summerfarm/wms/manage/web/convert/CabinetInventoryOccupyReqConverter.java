package net.summerfarm.wms.manage.web.convert;

import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryOccupyReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryOccupyResp;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryOccupyReqDTO;
import net.summerfarm.wms.manage.model.domain.CabinetInventoryOccupyDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Mapper
public interface CabinetInventoryOccupyReqConverter {

    CabinetInventoryOccupyReqConverter INSTANCE = Mappers.getMapper(CabinetInventoryOccupyReqConverter.class);


    CabinetInventoryOccupyReq convert(CabinetInventoryOccupyReqDTO req);

    CabinetInventoryOccupyDetail convertResp(CabinetInventoryOccupyResp req);

    List<CabinetInventoryOccupyDetail> convertRespList(List<CabinetInventoryOccupyResp> req);

}
