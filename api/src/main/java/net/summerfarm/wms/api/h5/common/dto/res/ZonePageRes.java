package net.summerfarm.wms.api.h5.common.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/3
 */
@Data
public class ZonePageRes implements Serializable {

    private static final long serialVersionUID = -3826556872696794031L;

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 库区编码
     */
    private String zoneCode;

    /**
     * 库区名称
     */
    private String zoneName;

    /**
     * 库区类型（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    private Integer zoneType;

    /**
     * 库区状态（0：禁用，1：启用）
     */
    private Integer zoneStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 初始化库区标签（0：非系统初始化库区，1：系统初始化库区）
     */
    private Integer initOption;

    /**
     * 是否支持拣货(0-不支持  1-支持)
     */
    private Integer pickOption;

}
