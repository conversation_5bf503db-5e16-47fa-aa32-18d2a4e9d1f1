package net.summerfarm.wms.domain.mission.param.command;

import java.time.LocalDateTime;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2024-04-19 11:32:26
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class WmsSubmitDetailExtendCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 仓库编码
	 */
	private Long warehouseNo;

	/**
	 * 任务编号
	 */
	private String missionNo;

	/**
	 * 待提交明细id
	 */
	private Long missionDetailId;

	/**
	 * 提交明细id
	 */
	private Long missionSubmitId;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 扩展信息
	 */
	private String extend;

	/**
	 * 扩展信息类型，json/ossaddress, 1-json,2-ossaddress
	 */
	private Integer extendType;

	

	
}