package net.summerfarm.wms.application.stocktask.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.outstore.event.PickingFinishEvent;
import net.summerfarm.wms.application.stocktask.listener.handler.PickingFinishHandler;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/2
 */
@Slf4j
@Component
@MqListener(topic = Global.MQ_TOPIC,
        tag = WmsConstant.MISSION_FINISH_TAG,
        consumerGroup = WmsConstant.PICK_FINISH_GID)
public class PickingFinishListener extends AbstractMqListener<PickingFinishEvent> {

    @Resource
    private PickingFinishHandler pickingFinishHandler;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(PickingFinishEvent pickingFinishEvent) {
        log.info("拣货任务完成回告：{}", JSON.toJSONStringWithDateFormat(pickingFinishEvent, DateUtil.YYYY_MM_DD));
        if (null == pickingFinishEvent || StringUtils.isBlank(pickingFinishEvent.getMissionNo())) {
            return;
        }
        redisUtil.execute(RedisKeys.buildPickFinishConsumeExKey(pickingFinishEvent.getMissionNo()),
                5L,
                TimeUnit.MINUTES,
                0L,
                () -> pickingFinishHandler.execute(pickingFinishEvent));
        log.info("任务完成回告消费完成：{}", pickingFinishEvent.getMissionNo());
    }


}
