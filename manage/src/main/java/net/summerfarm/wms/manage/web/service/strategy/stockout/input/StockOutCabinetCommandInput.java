package net.summerfarm.wms.manage.web.service.strategy.stockout.input;

import lombok.Data;
import net.summerfarm.wms.manage.web.service.strategy.stockout.dto.StockOutCabinetDetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/28
 */
@Data
public class StockOutCabinetCommandInput implements Serializable {

    private static final long serialVersionUID = 6715706758875377057L;

    /**
     * 出库任务id
     */
    private Long stockTaskId;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 出库入参
     */
    private String jsonData;

    /**
     * 出库库位明细
     */
    private List<StockOutCabinetDetail> stockOutCabinetDetails;

}
