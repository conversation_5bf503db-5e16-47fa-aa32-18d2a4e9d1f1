package net.summerfarm.wms.domain.inventory.domainobject.converter;

import net.summerfarm.wms.domain.inventory.domainobject.aggregate.PreDistributionOrderOccupyAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.valueObject.OrderOccupyDetail;
import net.summerfarm.wms.facade.wnc.dto.WarehouseStorageFenceQueryReqDTO;

import java.util.List;
import java.util.stream.Collectors;

public class WarehouseStorageFenceQueryReqDTOConverter {

    public static WarehouseStorageFenceQueryReqDTO convert(PreDistributionOrderOccupyAggregate preDistributionOrderOccupyReqDTO) {
        WarehouseStorageFenceQueryReqDTO queryReqDTO = new WarehouseStorageFenceQueryReqDTO();

        queryReqDTO.setTenantId(preDistributionOrderOccupyReqDTO.getTenantId());
        if (preDistributionOrderOccupyReqDTO.getWarehouseTenantId() != null){
            queryReqDTO.setTenantId(preDistributionOrderOccupyReqDTO.getWarehouseTenantId());
        }
        queryReqDTO.setArea(preDistributionOrderOccupyReqDTO.getArea());
        queryReqDTO.setCity(preDistributionOrderOccupyReqDTO.getCity());
        queryReqDTO.setPoi(preDistributionOrderOccupyReqDTO.getPoi());
        queryReqDTO.setStoreNo(preDistributionOrderOccupyReqDTO.getStoreNo());
        queryReqDTO.setContactId(preDistributionOrderOccupyReqDTO.getContactId());
        queryReqDTO.setMerchantId(preDistributionOrderOccupyReqDTO.getMerchantId());
        queryReqDTO.setSource(preDistributionOrderOccupyReqDTO.getSource());
        queryReqDTO.setAddOrderFlag(preDistributionOrderOccupyReqDTO.getAddOrderFlag());

        queryReqDTO.setSkuList(preDistributionOrderOccupyReqDTO.getOrderOccupyDetailList().stream()
                .map(OrderOccupyDetail::getSkuCode)
                .distinct()
                .collect(Collectors.toList()));

        return queryReqDTO;
    }

    public static WarehouseStorageFenceQueryReqDTO convertQuery(
            Long tenantId, String area, String city, String poi, List<String> skuCodeList,
            Integer storeNo, Long contactId,
            Long merchantId, Integer source, Boolean addOrderFlag) {
        WarehouseStorageFenceQueryReqDTO queryReqDTO = new WarehouseStorageFenceQueryReqDTO();

        queryReqDTO.setTenantId(tenantId);
        queryReqDTO.setArea(area);
        queryReqDTO.setCity(city);
        queryReqDTO.setPoi(poi);

        queryReqDTO.setStoreNo(storeNo);
        queryReqDTO.setContactId(contactId);

        queryReqDTO.setSkuList(skuCodeList);
        queryReqDTO.setMerchantId(merchantId);
        queryReqDTO.setSource(source);
        queryReqDTO.setAddOrderFlag(addOrderFlag);

        return queryReqDTO;
    }
}
