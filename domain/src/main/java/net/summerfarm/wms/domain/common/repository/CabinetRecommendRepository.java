package net.summerfarm.wms.domain.common.repository;

import net.summerfarm.wms.domain.common.domainobject.Cabinet;
import net.summerfarm.wms.domain.common.domainobject.CabinetRecommendQuery;

import java.util.List;

public interface CabinetRecommendRepository {
    
    /**
     * 同品同效期，推荐库位
     * @param query
     * @return
     */
    List<Cabinet> recommendListForSameSkuSameQualityDate(CabinetRecommendQuery query);
    /**
     * 相同SKU不同保质期
     * @param query
     * @return
     */
    List<Cabinet> recommendListForSameSkuDiffQualityDate(CabinetRecommendQuery query);
    /**
     * 同品存放过的空库位
     * @param query
     * @return
     */
    List<Cabinet> recommendListForSameSkuEmpty(CabinetRecommendQuery query);
    /**
     * 空库位
     * @param query
     * @return
     */
    List<Cabinet> recommendListForEmpty(CabinetRecommendQuery query);
    /**
     * 不同品允许混的库位
     * @param query
     * @return
     */
    List<Cabinet> recommendListForDiffSku(CabinetRecommendQuery query);
}
