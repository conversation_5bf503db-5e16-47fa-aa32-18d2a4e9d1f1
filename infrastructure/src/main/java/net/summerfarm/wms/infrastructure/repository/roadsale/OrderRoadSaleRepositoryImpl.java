package net.summerfarm.wms.infrastructure.repository.roadsale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.summerfarm.wms.common.enums.OrderRoadSaleStatusEnum;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.common.util.NumberUtils;
import net.summerfarm.wms.domain.roadsale.domainobject.OrderRoadSale;
import net.summerfarm.wms.domain.roadsale.param.query.OrderRoadSaleQueryParam;
import net.summerfarm.wms.domain.roadsale.repository.OrderRoadSaleRepository;
import net.summerfarm.wms.domain.roadsale.valueobject.OrderRoadSaleUpdateValue;
import net.summerfarm.wms.infrastructure.dao.roadsale.entity.InventoryOrderRoadSaleDO;
import net.summerfarm.wms.infrastructure.dao.roadsale.mapper.InventoryOrderRoadSaleDAO;
import net.summerfarm.wms.infrastructure.repository.roadsale.convert.OrderRoadSaleConvert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2023/9/8 14:26
 * @<AUTHOR>
 */
@Repository
public class OrderRoadSaleRepositoryImpl implements OrderRoadSaleRepository {

    @Resource
    private InventoryOrderRoadSaleDAO inventoryOrderRoadSaleDAO;

    @Override
    public Long saveOrderRoadSale(OrderRoadSale orderRoadSale) {
        InventoryOrderRoadSaleDO inventoryOrderRoadSaleDO = OrderRoadSaleConvert.INSTANCE.convert(orderRoadSale);
        inventoryOrderRoadSaleDAO.insert(inventoryOrderRoadSaleDO);
        return inventoryOrderRoadSaleDO.getId();
    }

    @Override
    public List<OrderRoadSale> selectList(String outOrderNo, Integer outOrderType, Integer status) {
        return selectList(outOrderNo, outOrderType, status, null, null);
    }

    @Override
    public List<OrderRoadSale> selectList(String outOrderNo, Integer outOrderType, Integer status, List<String> skus, Integer warehouseNo) {
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(outOrderNo), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(outOrderType != null, "外部订单类型不能为空");

        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda().select();
        queryWrapper.eq(InventoryOrderRoadSaleDO::getOutOrderNo, outOrderNo);
        queryWrapper.eq(InventoryOrderRoadSaleDO::getOutOrderType, outOrderType);
        if (Objects.nonNull(warehouseNo)) {
            queryWrapper.eq(InventoryOrderRoadSaleDO::getWarehouseNo, warehouseNo);
        }
        if (CollectionUtils.isNotEmpty(skus)) {
            queryWrapper.in(InventoryOrderRoadSaleDO::getSku, skus);
        }
        if (Objects.nonNull(status)) {
            queryWrapper.eq(InventoryOrderRoadSaleDO::getStatus, status);
        }
        List<InventoryOrderRoadSaleDO> inventoryOrderRoadSaleDOList = inventoryOrderRoadSaleDAO.selectList(queryWrapper);
        return inventoryOrderRoadSaleDOList.stream().map(OrderRoadSaleConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<OrderRoadSale> selectList(Integer warehouseNo, String sku) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(sku), "sku不能为空");

        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda().select()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, warehouseNo)
                .eq(InventoryOrderRoadSaleDO::getSku, sku)
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode());
        List<InventoryOrderRoadSaleDO> inventoryOrderRoadSaleDOList = inventoryOrderRoadSaleDAO.selectList(queryWrapper);
        return inventoryOrderRoadSaleDOList.stream().map(OrderRoadSaleConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public boolean cancel(OrderRoadSale orderRoadSale) {
        ExceptionUtil.checkAndThrow(orderRoadSale.getWarehouseNo() != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getSku()), "sku不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getOutOrderNo()), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(orderRoadSale.getOutOrderType() != null, "外部订单类型不能为空");

        InventoryOrderRoadSaleDO orderRoadSaleDO = InventoryOrderRoadSaleDO.builder()
                .status(OrderRoadSaleStatusEnum.CANCEL.getCode())
                .roadSaleQuantity(orderRoadSale.getRoadSaleQuantity())
                .remainingRoadSaleQuantity(0)
                .operator(orderRoadSale.getOperator())
                .updateTime(new Date())
                .build();

        boolean hasOriginalRoadSaleQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRoadSaleQuantity());
        boolean hasOriginalInStoreQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalInStoreRoadSaleQuantity());
        boolean hasOriginalRemainingRoadSaleQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRemainingRoadSaleQuantity());
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda().select()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, orderRoadSale.getWarehouseNo())
                .eq(InventoryOrderRoadSaleDO::getSku, orderRoadSale.getSku())
                .eq(InventoryOrderRoadSaleDO::getOutOrderNo, orderRoadSale.getOutOrderNo())
                .eq(InventoryOrderRoadSaleDO::getOutOrderType, orderRoadSale.getOutOrderType())
                .eq(hasOriginalRoadSaleQuantity, InventoryOrderRoadSaleDO::getRoadSaleQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRoadSaleQuantity())
                .eq(hasOriginalInStoreQuantity, InventoryOrderRoadSaleDO::getInStoreQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalInStoreRoadSaleQuantity())
                .eq(hasOriginalRemainingRoadSaleQuantity, InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRemainingRoadSaleQuantity())
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode());
        return inventoryOrderRoadSaleDAO.update(orderRoadSaleDO, queryWrapper) == 1;
    }

    @Override
    public OrderRoadSale selectOne(Integer warehouseNo, String sku, Integer outOrderType, String outOrderNo) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(sku), "sku不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(outOrderNo), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(outOrderType != null, "外部订单类型不能为空");

        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda().select()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, warehouseNo)
                .eq(InventoryOrderRoadSaleDO::getSku, sku)
                .eq(InventoryOrderRoadSaleDO::getOutOrderNo, outOrderNo)
                .eq(InventoryOrderRoadSaleDO::getOutOrderType, outOrderType)
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode());
        InventoryOrderRoadSaleDO orderRoadSaleDO = inventoryOrderRoadSaleDAO.selectOne(queryWrapper);
        return OrderRoadSaleConvert.INSTANCE.convert(orderRoadSaleDO);
    }

    @Override
    public boolean updateInStoreQuantity(OrderRoadSale orderRoadSale) {
        ExceptionUtil.checkAndThrow(orderRoadSale.getWarehouseNo() != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getSku()), "sku不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getOutOrderNo()), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(orderRoadSale.getOutOrderType() != null, "外部订单类型不能为空");
        ExceptionUtil.checkAndThrow(orderRoadSale.getInStoreQuantity() != null, "入库数量不能为空");

        OrderRoadSaleUpdateValue updateValue = orderRoadSale.getOrderRoadSaleUpdateValue();
        InventoryOrderRoadSaleDO roadSaleDO = InventoryOrderRoadSaleDO.builder()
                .inStoreQuantity(orderRoadSale.getInStoreQuantity())
                .remainingRoadSaleQuantity(orderRoadSale.getRemainingRoadSaleQuantity())
                .operator(orderRoadSale.getOperator())
                .updateTime(new Date())
                .build();
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, orderRoadSale.getWarehouseNo())
                .eq(InventoryOrderRoadSaleDO::getSku, orderRoadSale.getSku())
                .eq(InventoryOrderRoadSaleDO::getOutOrderNo, orderRoadSale.getOutOrderNo())
                .eq(InventoryOrderRoadSaleDO::getOutOrderType, orderRoadSale.getOutOrderType())
                .eq(Objects.nonNull(updateValue.getOriginalRoadSaleQuantity()), InventoryOrderRoadSaleDO::getRoadSaleQuantity, updateValue.getOriginalRoadSaleQuantity())
                .eq(Objects.nonNull(updateValue.getOriginalInStoreRoadSaleQuantity()), InventoryOrderRoadSaleDO::getInStoreQuantity, updateValue.getOriginalInStoreRoadSaleQuantity())
                .eq(Objects.nonNull(updateValue.getOriginalRemainingRoadSaleQuantity()), InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, updateValue.getOriginalRemainingRoadSaleQuantity());
        return inventoryOrderRoadSaleDAO.update(roadSaleDO, queryWrapper) == 1;
    }

    @Override
    public List<OrderRoadSale> selectListRemainQuantityGtZero(Integer warehouseNo) {
        return selectListRemainQuantityGtZero(warehouseNo, null);
    }

    @Override
    public List<OrderRoadSale> selectListRemainQuantityGtZero(Integer warehouseNo, String sku) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "查询剩余在途可售库存大于0的仓库号不能为空");
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, warehouseNo)
                .eq(StringUtils.isNotEmpty(sku), InventoryOrderRoadSaleDO::getSku, sku)
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode())
                .gt(InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, NumberUtils.INTEGER_ZERO);
        List<InventoryOrderRoadSaleDO> inventoryOrderRoadSaleDOS = inventoryOrderRoadSaleDAO.selectList(queryWrapper);
        return OrderRoadSaleConvert.INSTANCE.convert(inventoryOrderRoadSaleDOS);
    }

    @Override
    public List<OrderRoadSale> selectListCreatedAfterTime(Integer warehouseNo, String sku, LocalDateTime time) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "查询晚于指定时间创建的在途可售单据时仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(sku), "查询晚于指定时间创建的在途可售单据时sku不能为空");
        ExceptionUtil.checkAndThrow(time != null, "查询晚于指定时间创建的在途可售单据时指定时间不能为空");
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, warehouseNo)
                .eq(InventoryOrderRoadSaleDO::getSku, sku)
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode())
                .gt(InventoryOrderRoadSaleDO::getCreateTime, time);
        List<InventoryOrderRoadSaleDO> inventoryOrderRoadSaleDOS = inventoryOrderRoadSaleDAO.selectList(queryWrapper);
        return OrderRoadSaleConvert.INSTANCE.convert(inventoryOrderRoadSaleDOS);
    }

    @Override
    public boolean update(OrderRoadSale orderRoadSale) {
        ExceptionUtil.checkAndThrow(orderRoadSale.getWarehouseNo() != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getSku()), "sku不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getOutOrderNo()), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(orderRoadSale.getOutOrderType() != null, "外部订单类型不能为空");

        OrderRoadSaleUpdateValue updateValue = orderRoadSale.getOrderRoadSaleUpdateValue();
        InventoryOrderRoadSaleDO roadSaleDO = InventoryOrderRoadSaleDO.builder()
                .roadSaleRate(orderRoadSale.getRoadSaleRate())
                .roadSaleQuantity(orderRoadSale.getRoadSaleQuantity())
                .inStoreQuantity(orderRoadSale.getInStoreQuantity())
                .rejectQuantity(orderRoadSale.getRejectQuantity())
                .remainingRoadSaleQuantity(orderRoadSale.getRemainingRoadSaleQuantity())
                .operator(orderRoadSale.getOperator())
                .updateTime(new Date())
                .build();

        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, orderRoadSale.getWarehouseNo())
                .eq(InventoryOrderRoadSaleDO::getSku, orderRoadSale.getSku())
                .eq(InventoryOrderRoadSaleDO::getOutOrderNo, orderRoadSale.getOutOrderNo())
                .eq(InventoryOrderRoadSaleDO::getOutOrderType, orderRoadSale.getOutOrderType())
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode())
                .eq(Objects.nonNull(updateValue.getOriginalRoadSaleQuantity()), InventoryOrderRoadSaleDO::getRoadSaleQuantity, updateValue.getOriginalRoadSaleQuantity())
                .eq(Objects.nonNull(updateValue.getOriginalInStoreRoadSaleQuantity()), InventoryOrderRoadSaleDO::getInStoreQuantity, updateValue.getOriginalInStoreRoadSaleQuantity())
                .eq(Objects.nonNull(updateValue.getOriginalRejectRoadSaleQuantity()), InventoryOrderRoadSaleDO::getRejectQuantity, updateValue.getOriginalRejectRoadSaleQuantity())
                .eq(Objects.nonNull(updateValue.getOriginalRemainingRoadSaleQuantity()), InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, updateValue.getOriginalRemainingRoadSaleQuantity());
        return inventoryOrderRoadSaleDAO.update(roadSaleDO, queryWrapper) == 1;
    }

    @Override
    public boolean clear(OrderRoadSale orderRoadSale) {
        ExceptionUtil.checkAndThrow(orderRoadSale.getWarehouseNo() != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getSku()), "sku不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(orderRoadSale.getOutOrderNo()), "外部订单号不能为空");
        ExceptionUtil.checkAndThrow(orderRoadSale.getOutOrderType() != null, "外部订单类型不能为空");

        InventoryOrderRoadSaleDO orderRoadSaleDO = InventoryOrderRoadSaleDO.builder()
                .status(OrderRoadSaleStatusEnum.CLEAR.getCode())
                .roadSaleQuantity(orderRoadSale.getRoadSaleQuantity())
                .remainingRoadSaleQuantity(0)
                .operator(orderRoadSale.getOperator())
                .updateTime(new Date())
                .build();

        boolean hasOriginalRoadSaleQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRoadSaleQuantity());
        boolean hasOriginalInStoreQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalInStoreRoadSaleQuantity());
        boolean hasOriginalRemainingRoadSaleQuantity = Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue()) && Objects.nonNull(orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRemainingRoadSaleQuantity());
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda().select()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, orderRoadSale.getWarehouseNo())
                .eq(InventoryOrderRoadSaleDO::getSku, orderRoadSale.getSku())
                .eq(InventoryOrderRoadSaleDO::getOutOrderNo, orderRoadSale.getOutOrderNo())
                .eq(InventoryOrderRoadSaleDO::getOutOrderType, orderRoadSale.getOutOrderType())
                .eq(hasOriginalRoadSaleQuantity, InventoryOrderRoadSaleDO::getRoadSaleQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRoadSaleQuantity())
                .eq(hasOriginalInStoreQuantity, InventoryOrderRoadSaleDO::getInStoreQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalInStoreRoadSaleQuantity())
                .eq(hasOriginalRemainingRoadSaleQuantity, InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, orderRoadSale.getOrderRoadSaleUpdateValue().getOriginalRemainingRoadSaleQuantity())
                .eq(InventoryOrderRoadSaleDO::getStatus, OrderRoadSaleStatusEnum.NORMAL.getCode());
        return inventoryOrderRoadSaleDAO.update(orderRoadSaleDO, queryWrapper) == 1;
    }

    @Override
    public List<OrderRoadSale> queryOrderRoadSale(OrderRoadSaleQueryParam param) {
        ExceptionUtil.checkAndThrow(param.getWarehouseNo() != null, "仓库号不能为空");
        LambdaQueryWrapper<InventoryOrderRoadSaleDO> queryWrapper = new QueryWrapper<InventoryOrderRoadSaleDO>().lambda()
                .eq(InventoryOrderRoadSaleDO::getWarehouseNo, param.getWarehouseNo())
                .eq(param.getOutOrderType() != null, InventoryOrderRoadSaleDO::getOutOrderType, param.getOutOrderType())
                .eq(StringUtils.isNotEmpty(param.getOutOrderNo()), InventoryOrderRoadSaleDO::getOutOrderNo, param.getOutOrderNo())
                .eq(StringUtils.isNotEmpty(param.getSku()), InventoryOrderRoadSaleDO::getSku, param.getSku())
                .eq(param.getStatus() != null, InventoryOrderRoadSaleDO::getStatus, param.getStatus())
                .ge(param.getCreateStartTime() != null, InventoryOrderRoadSaleDO::getCreateTime, param.getCreateStartTime())
                .gt(Boolean.TRUE.equals(param.getQuantityGtZero()), InventoryOrderRoadSaleDO::getRoadSaleQuantity, 0)
                .gt(Boolean.TRUE.equals(param.getRemainingQuantityGtZero()), InventoryOrderRoadSaleDO::getRemainingRoadSaleQuantity, 0);
        List<InventoryOrderRoadSaleDO> inventoryOrderRoadSaleDOS = inventoryOrderRoadSaleDAO.selectList(queryWrapper);
        return OrderRoadSaleConvert.INSTANCE.convert(inventoryOrderRoadSaleDOS);
    }
}
