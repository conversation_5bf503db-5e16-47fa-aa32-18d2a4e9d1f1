package net.summerfarm.wms.manage.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * @author: xdc
 * @date: 2024/1/26
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskManualGenInput implements Serializable {
    private static final long serialVersionUID = 7757581592128665462L;

    /**
     * 仓库编号
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;

    /**
     * 城配仓
     */
    @NotNull(message = "城配仓不能为空")
    private Integer storeNo;

    /**
     * 期望出库时间
     */
    @NotNull(message = "期望出库时间不能为空")
    private LocalDate deliveryDate;

    /**
     * 出库类型
     *  0, "销售出库",
     *  1, "补发出库",
     *  2, "自提出库",
     *  3, "出样出库",
     *  4, "样品自提出库",
     */
    @NotNull(message = "出库类型类型不能为空")
    private Integer type;

    /**
     * 是否越库出库
     */
    @NotNull(message = "是否越库出库不能为空")
    private boolean crossFlag;

    /**
     * 是否波次出库
     */
    @NotNull(message = "是否波次出库不能为空")
    private boolean waveFlag;
}
