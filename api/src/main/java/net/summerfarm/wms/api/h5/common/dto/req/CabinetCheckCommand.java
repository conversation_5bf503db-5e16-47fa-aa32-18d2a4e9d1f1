package net.summerfarm.wms.api.h5.common.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/3
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetCheckCommand implements Serializable {

    /**
     * 仓库编号
     */
    @NotNull
    private Integer warehouseNo;

    /**
     * 容器号
     */
    private String containerNo;

    /**
     * 业务任务类型
     */
    private Integer missionType;

    /**
     * 任务编号
     */
    private String parentMissionNo;

}
