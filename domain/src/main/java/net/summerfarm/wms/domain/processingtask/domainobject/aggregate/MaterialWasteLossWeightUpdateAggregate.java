package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MaterialWasteLossWeightUpdateAggregate implements Serializable {

    /**
     * 物料领用ID
     */
    private Long materialReceiveId;

    /**
     * 加工任务原料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;


    /**
     * 废料损耗更新
     */
    private BigDecimal wasteLossWeight;
}
