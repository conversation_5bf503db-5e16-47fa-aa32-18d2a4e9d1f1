package net.summerfarm.wms.infrastructure.dao.processingtask.dataobject;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * SKU包裹规格(WmsSkuSpec)实体类
 *
 * <AUTHOR>
 * @since 2023-02-12 11:04:35
 */
public class WmsSkuSpecDO implements Serializable {
    private static final long serialVersionUID = 300894859023187570L;
    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;
    /**
     * 加工规则ID
     */
    private Long processingConfigId;
    /**
     * 原料sku
     */
    private String materialSkuCode;
    /**
     * 原料sku名称
     */
    private String materialSkuName;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku重量
     */
    private BigDecimal productSkuWeight;
    /**
     * 成品sku单位
     */
    private String productSkuUnit;
    /**
     * 成品sku规格重量
     */
    private BigDecimal productSkuSpecWeight;
    /**
     * 成品sku规格单位
     */
    private String productSkuSpecUnit;
    /**
     * 是否作废，0：否，1：是
     */
    private Integer invalid;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;
    /**
     * 成品sku规格单位描述
     */
    private String productSkuSpecUnitDesc;

    /**
     * 成品sku规格单位描述
     */
    private String productSkuUnitDesc;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseNo() {
        return warehouseNo;
    }

    public void setWarehouseNo(Integer warehouseNo) {
        this.warehouseNo = warehouseNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getProcessingConfigId() {
        return processingConfigId;
    }

    public void setProcessingConfigId(Long processingConfigId) {
        this.processingConfigId = processingConfigId;
    }

    public String getMaterialSkuCode() {
        return materialSkuCode;
    }

    public void setMaterialSkuCode(String materialSkuCode) {
        this.materialSkuCode = materialSkuCode;
    }

    public String getMaterialSkuName() {
        return materialSkuName;
    }

    public void setMaterialSkuName(String materialSkuName) {
        this.materialSkuName = materialSkuName;
    }

    public String getProductSkuCode() {
        return productSkuCode;
    }

    public void setProductSkuCode(String productSkuCode) {
        this.productSkuCode = productSkuCode;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public BigDecimal getProductSkuWeight() {
        return productSkuWeight;
    }

    public void setProductSkuWeight(BigDecimal productSkuWeight) {
        this.productSkuWeight = productSkuWeight;
    }

    public String getProductSkuUnit() {
        return productSkuUnit;
    }

    public void setProductSkuUnit(String productSkuUnit) {
        this.productSkuUnit = productSkuUnit;
    }

    public BigDecimal getProductSkuSpecWeight() {
        return productSkuSpecWeight;
    }

    public void setProductSkuSpecWeight(BigDecimal productSkuSpecWeight) {
        this.productSkuSpecWeight = productSkuSpecWeight;
    }

    public String getProductSkuSpecUnit() {
        return productSkuSpecUnit;
    }

    public void setProductSkuSpecUnit(String productSkuSpecUnit) {
        this.productSkuSpecUnit = productSkuSpecUnit;
    }

    public Integer getInvalid() {
        return invalid;
    }

    public void setInvalid(Integer invalid) {
        this.invalid = invalid;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getProductSkuSpecUnitDesc() {
        return productSkuSpecUnitDesc;
    }

    public void setProductSkuSpecUnitDesc(String productSkuSpecUnitDesc) {
        this.productSkuSpecUnitDesc = productSkuSpecUnitDesc;
    }

    public String getProductSkuUnitDesc() {
        return productSkuUnitDesc;
    }

    public void setProductSkuUnitDesc(String productSkuUnitDesc) {
        this.productSkuUnitDesc = productSkuUnitDesc;
    }
}

