package net.summerfarm.wms.infrastructure.repository.allocation.impl;

import net.summerfarm.wms.domain.allocation.AllocationRoadCostBatchRecordCommandRepository;
import net.summerfarm.wms.domain.allocation.domainobject.InventoryAllocationRoadCostBatchRecord;
import net.summerfarm.wms.infrastructure.dao.allocation.InventoryAllocationRoadCostBatchRecordDAO;
import net.summerfarm.wms.infrastructure.dao.allocation.dataobject.InventoryAllocationRoadCostBatchRecordDO;
import net.summerfarm.wms.infrastructure.repository.allocation.converter.AllocationRoadCostBatchRecordConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AllocationRoadCostBatchRecordCommandRepositoryImpl implements AllocationRoadCostBatchRecordCommandRepository {
    @Autowired
    private InventoryAllocationRoadCostBatchRecordDAO allocationRoadCostBatchRecordDAO;

    @Override
    public void batchInsert(List<InventoryAllocationRoadCostBatchRecord> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }
        List<InventoryAllocationRoadCostBatchRecordDO> allocationRoadCostBatchRecordDOList = AllocationRoadCostBatchRecordConverter.toInventoryAllocationRoadCostBatchRecordDOList(recordList);
        allocationRoadCostBatchRecordDAO.batchInsert(allocationRoadCostBatchRecordDOList);
    }
}
