package net.summerfarm.wms.application.materialManage.controller.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialTaskCommandInput implements Serializable{

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	@NotNull(message = "仓库号不可为空")
	private Integer warehouseNo;

	/**
	 * 类型，96-领用，97-归还
	 */
	@NotNull(message = "领用类型不可为空")
	private Integer type;

	/**
	 * 领用用途
	 */
	private String destination;

	/**
	 * 来源类型
	 */
	private Integer sourceType;

	/**
	 * 来源id
	 */
	private Long sourceId;

	/**
	 * 来源单号
	 */
	private String sourceCode;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 明细
	 */
	private List<WmsMaterialTaskDetailCommandInput> detailList;

}