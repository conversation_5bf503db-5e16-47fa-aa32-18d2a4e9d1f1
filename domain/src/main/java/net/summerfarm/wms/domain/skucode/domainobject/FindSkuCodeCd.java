package net.summerfarm.wms.domain.skucode.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FindSkuCodeCd {
    String sku;
    String purchaseNo;
    LocalDate productionDate;
    String skuBatchOnlyCode;
    Long bizId;
    Integer bizType;
}
