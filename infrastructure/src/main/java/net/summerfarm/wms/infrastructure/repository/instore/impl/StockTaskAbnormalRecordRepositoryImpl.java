package net.summerfarm.wms.infrastructure.repository.instore.impl;

import net.summerfarm.wms.domain.instore.domainobject.StockTaskAbnormalRecord;
import net.summerfarm.wms.domain.instore.repository.StockTaskAbnormalRecordRepository;
import net.summerfarm.wms.infrastructure.dao.instore.StockTaskAbnormalRecordDAO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO;
import net.summerfarm.wms.infrastructure.repository.instore.converter.StockTaskAbnormalRecordConvert;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/18  16:44
 */
@Repository
public class StockTaskAbnormalRecordRepositoryImpl implements StockTaskAbnormalRecordRepository {


    @Resource
    StockTaskAbnormalRecordDAO stockTaskAbnormalRecordDAO;

    @Override
    public List<StockTaskAbnormalRecord> select(Long stockTaskId, String sku) {
        List<StockTaskAbnormalRecordDO> queryRecordDO = stockTaskAbnormalRecordDAO.select(stockTaskId, sku);
        List<StockTaskAbnormalRecord> recordList = queryRecordDO.stream().map(StockTaskAbnormalRecordConvert.INSTANCE::convert).collect(Collectors.toList());
        return recordList;
    }

    @Override
    public List<StockTaskAbnormalRecord> list(Long stockTaskId, List<String> skus) {
        List<StockTaskAbnormalRecordDO> queryRecordDO = stockTaskAbnormalRecordDAO.list(stockTaskId, skus);
        return queryRecordDO.stream().map(StockTaskAbnormalRecordConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<StockTaskAbnormalRecord> listByStockTaskIds(List<Long> stockTaskIds) {
        List<StockTaskAbnormalRecordDO> stockTaskAbnormalRecordDOS = stockTaskAbnormalRecordDAO.listByTaskIds(stockTaskIds);
        return stockTaskAbnormalRecordDOS.stream().map(StockTaskAbnormalRecordConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public void insert(StockTaskAbnormalRecord record) {
        StockTaskAbnormalRecordDO recordDO = StockTaskAbnormalRecordConvert.INSTANCE.convert(record);
        stockTaskAbnormalRecordDAO.insert(recordDO);
        return;
    }
}
