package net.summerfarm.wms.infrastructure.dao.inventory;

import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryUpdateDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 库位批次库存DAO
 * @date 2023/4/28
 */
public interface CabinetBatchInventoryDAO {

    /**
     * create
     *
     * @param cabinetBatchInventory
     * @return
     */
    Long create(CabinetBatchInventoryDO cabinetBatchInventory);

    /**
     * batch create
     *
     * @param cabinetBatchInventorys
     * @return
     */
    Integer creates(List<CabinetBatchInventoryDO> cabinetBatchInventorys);

    /**
     * findById
     *
     * @param id
     * @return
     */
    CabinetBatchInventoryDO findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<CabinetBatchInventoryDO> findByIds(List<Long> ids);

    /**
     * update
     *
     * @param cabinetBatchInventory
     * @return
     */
    Integer update(CabinetBatchInventoryUpdateDO cabinetBatchInventory);

    /**
     * count
     *
     * @param cabinetBatchInventory
     * @return
     */
    Long count(CabinetBatchInventoryQueryDO cabinetBatchInventory);

    /**
     * findOne
     *
     * @param cabinetBatchInventory
     * @return
     */
    CabinetBatchInventoryDO findOne(CabinetBatchInventoryQueryDO cabinetBatchInventory);

    /**
     * list
     *
     * @param cabinetBatchInventoryQueryDO
     * @return
     */
    List<CabinetBatchInventoryDO> list(CabinetBatchInventoryQueryDO cabinetBatchInventoryQueryDO);

    Long updateCabinetBatchInventoryCabinetInfo100(@Param("warehouseNo") Long warehouseNo, @Param("cabinetCode") String cabinetCode,
                                                   @Param("cabinetType") Integer cabinetType, @Param("cabinetPurpose") Integer cabinetPurpose);

    Long updateCabinetBatchInventoryZoneInfo100(@Param("warehouseNo") Long warehouseNo, @Param("zoneCode") String zoneCode,
                                                @Param("zoneType") Integer zoneType);

    /**
     * findAvailableQuantitySum
     *
     * @param cabinetBatchInventoryQueryDO
     * @return
     */
    Integer findAvailableQuantitySum(CabinetBatchInventoryQueryDO cabinetBatchInventoryQueryDO);
}
