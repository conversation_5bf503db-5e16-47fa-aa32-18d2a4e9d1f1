<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskPickMapper">

    <resultMap id="stockTaskPickVO" type="net.summerfarm.wms.manage.model.vo.StockTaskPickVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="add_time" property="addTime"/>
        <result column="sku" property="sku"/>
        <result column="pd_name" property="pdName"/>
        <result column="amount" property="amount"/>
        <result column="admin_id" property="adminId"/>
        <result column="admin_name" property="adminName"/>
        <result column="type" property="type"/>
        <result column="weight" property="weight"/>
        <result column="out_store_no" property="outStoreNo"/>
        <result column="store_no" property="storeNo"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="unit" property="unit"/>
        <result column="category" property="categoryName"/>
        <result column="categoryType" property="categoryType"/>
        <result column="storage_method" property="storageMethod"/>
        <result column="close_order_time" jdbcType="VARCHAR" property="closeOrderTime"/>
        <result column="name_remakes" property="nameRemakes"/>
        <result column="skuType" property="skuType"/>
        <collection property="stockTaskPickDetails" ofType ="net.summerfarm.wms.manage.model.domain.StockTaskPickDetail">
            <id column="stpdId" property="id"  jdbcType="INTEGER"/>
            <result column="stock_task_pick_id" property="stockTaskPickId"/>
            <result column="list_no" property="listNo"/>
            <result column="gl_no" property="glNo"/>
            <result column="quality_date" property="qualityDate"/>
            <result column="production_date" property="productionDate"/>
            <result column="should_quantity" property="shouldQuantity"/>
        </collection>
    </resultMap>
    <insert id="insertBatch" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskPick" useGeneratedKeys="true" keyProperty="id">
        insert into stock_task_pick (`add_time`, `sku` , `pd_name`,`weight`,`amount` ,`admin_id`,`admin_name`,`type`,store_no,delivery_time,close_order_time,out_store_no)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime},#{item.sku},#{item.pdName},#{item.weight},#{item.amount},#{item.adminId},#{item.adminName}
            ,#{item.type},#{item.storeNo},#{item.deliveryTime},#{item.closeOrderTime},#{item.outStoreNo})
        </foreach>

    </insert>

    <select id="selectTaskPickByType" resultMap="stockTaskPickVO">

        select stp.id, stp.add_time, stp.sku, stp.pd_name, stp.amount, stp.admin_id, stp.weight ,stp.admin_name, stp.`type`,stpd.id stpdId,stp.delivery_time,
        stpd.stock_task_pick_id, stpd.list_no,stpd.gl_no,stpd.quality_date,stpd.production_date,stpd.should_quantity,stp.store_no,i.unit,
        stp.close_order_time
        from stock_task_pick stp
        left join stock_task_pick_detail  stpd on stp.id = stpd.stock_task_pick_id
        where stp.store_no = #{storeNo}  and stp.delivery_time = #{deliveryTime}
        <if test="type != null">
            and  stp.`type` = #{type}
        </if>
        <if test="closeOrderTime != null ">
            and stp.close_order_time = #{closeOrderTime}
        </if>
        order by id desc
    </select>
    <select id="selectByBatchId" resultMap="stockTaskPickVO">
        select stp.id,stp.add_time,stp.sku, stp.pd_name, stp.weight, stp.amount, stp.admin_id, stp.admin_name, stp.type, stp.store_no, stp.delivery_time, stp.close_order_time,
        stpd.id stpdId,stpd.stock_task_pick_id, stpd.list_no,stpd.gl_no,stpd.quality_date,stpd.production_date,stpd.should_quantity,i.type skuType,i.ext_type extType,c.category,
        stp.out_store_no,c.type categoryType,p.storage_method
        from stock_task_pick stp
        left join stock_task_pick_detail stpd on stp.id = stpd.stock_task_pick_id
        left join inventory i on stp.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        where stp.id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>