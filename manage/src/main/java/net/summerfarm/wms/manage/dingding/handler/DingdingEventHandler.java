package net.summerfarm.wms.manage.dingding.handler;

import net.summerfarm.wms.manage.dingding.bo.DingdingResultBO;

/**
 * @author: dongcheng
 * @date: 2023/8/14
 */
public interface DingdingEventHandler {

    /**
     * 获取模版当前事件的模版code
     * 用于区分唯一的业务事件处理器
     * @return 审批模版code
     */
    String getProcessCode();

    /**
     * 审批实例开始时触发
     */
    void start(DingdingResultBO result);

    /**
     * 审批终止时触发-发起人撤销审批单
     */
    void terminate(DingdingResultBO result);

    /**
     * 审批实例通过后触发
     */
    void agree(DingdingResultBO result);

    /**
     * 审批实例拒绝时触发
     */
    void refuse(DingdingResultBO result);

}

