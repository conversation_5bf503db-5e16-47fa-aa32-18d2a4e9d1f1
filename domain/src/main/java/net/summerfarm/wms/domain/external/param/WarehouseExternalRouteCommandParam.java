package net.summerfarm.wms.domain.external.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 仓库外部路由command
 * @date 2024/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarehouseExternalRouteCommandParam implements Serializable {

    private static final long serialVersionUID = 8644916247955352375L;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 仓库租户id
     */
    private Long warehouseTenantId;
    /**
     * 库存仓
     */
    private Integer warehouseNo;
    /**
     * 单据类型（销售等）
     */
    private Integer orderType;
    /**
     * 能力点id
     */
    private Long abilityId;
    /**
     * 能力点
     */
    private String abilityCode;


}
