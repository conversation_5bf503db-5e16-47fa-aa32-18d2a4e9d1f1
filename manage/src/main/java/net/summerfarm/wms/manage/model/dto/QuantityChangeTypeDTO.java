package net.summerfarm.wms.manage.model.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import net.summerfarm.wms.manage.web.enums.impl.OtherStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.enums.impl.StoreRecordType;

import java.util.Set;

/**
 * @Description
 * @Date 2023/9/13 14:35
 * @<AUTHOR>
 */
public class QuantityChangeTypeDTO {

    public static Set<String> changeTypeName = Sets.newHashSet(Lists.newArrayList(
            StoreRecordType.PURCHASE_IN.getTypeName(),
            StoreRecordType.RETURN_IN.getTypeName(),
            StoreRecordType.SKIP_STORE_IN.getTypeName(),
            StoreRecordType.AFTER_SALE_IN.getTypeName(),
            StoreRecordType.LACK_GOODS_APPROVED.getTypeName(),
            StoreRecordType.STORE_ALLOCATION_IN.getTypeName(),
            OtherStockChangeTypeEnum.STORE_ALLOCATION_NOT_IN.getTypeName()
    ));
}
