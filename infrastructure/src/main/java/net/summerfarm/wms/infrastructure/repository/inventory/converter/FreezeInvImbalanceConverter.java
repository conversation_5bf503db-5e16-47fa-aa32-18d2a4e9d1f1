package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.FreezeInvImbalance;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.FreezeInvImbalanceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: dongcheng
 * @date: 2023/11/27
 */
@Mapper
public interface FreezeInvImbalanceConverter {

    FreezeInvImbalanceConverter INSTANCE = Mappers.getMapper(FreezeInvImbalanceConverter.class);

    FreezeInvImbalanceDO convert(FreezeInvImbalance freezeInvImbalance);

    FreezeInvImbalance convert(FreezeInvImbalanceDO freezeInvImbalanceDO);
}
