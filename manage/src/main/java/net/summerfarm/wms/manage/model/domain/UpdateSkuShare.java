package net.summerfarm.wms.manage.model.domain;

import lombok.Builder;
import lombok.Data;

/**
 * @author: dongcheng
 * @date: 2023/12/7
 */
@Data
@Builder
public class UpdateSkuShare {

    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 外部订单号
     */
    private String outOrderNo;
    /**
     * 待转出数量的变化值
     */
    private Integer remainingTransferOutQuantityChange;
    /**
     * 待转入数量的变化值
     */
    private Integer remainingTransferInQuantityChange;
    /**
     * 老的待转出数量
     */
    private Integer oldRemainingTransferOutQuantity;
    /**
     * 老的待转入数量
     */
    private Integer oldRemainingTransferInQuantity;
    /**
     * 库存变动类型
     */
    private Integer recordType;
    /**
     * 库存变动类型描述
     */
    private String recordTypeDesc;
    /**
     * 操作人
     */
    private String operator;

}
