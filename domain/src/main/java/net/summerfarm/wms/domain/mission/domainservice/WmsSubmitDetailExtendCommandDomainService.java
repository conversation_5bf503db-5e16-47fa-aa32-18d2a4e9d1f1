package net.summerfarm.wms.domain.mission.domainservice;


import net.summerfarm.wms.domain.mission.repository.WmsSubmitDetailExtendQueryRepository;
import net.summerfarm.wms.domain.mission.repository.WmsSubmitDetailExtendCommandRepository;
import net.summerfarm.wms.domain.mission.entity.WmsSubmitDetailExtendEntity;
import net.summerfarm.wms.domain.mission.param.command.WmsSubmitDetailExtendCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 任务提交明细领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-19 11:32:26
 * @version 1.0
 *
 */
@Service
public class WmsSubmitDetailExtendCommandDomainService {


    @Autowired
    private WmsSubmitDetailExtendCommandRepository wmsSubmitDetailExtendCommandRepository;
    @Autowired
    private WmsSubmitDetailExtendQueryRepository wmsSubmitDetailExtendQueryRepository;



    public WmsSubmitDetailExtendEntity insert(WmsSubmitDetailExtendCommandParam param) {
        return wmsSubmitDetailExtendCommandRepository.insertSelective(param);
    }


    public int update(WmsSubmitDetailExtendCommandParam param) {
        return wmsSubmitDetailExtendCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wmsSubmitDetailExtendCommandRepository.remove(id);
    }
}
