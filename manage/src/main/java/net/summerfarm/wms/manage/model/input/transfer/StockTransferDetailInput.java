package net.summerfarm.wms.manage.model.input.transfer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 转换任务列表查询
 *
 * @author: xdc
 * @date: 2024/2/19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockTransferDetailInput implements Serializable {
    private static final long serialVersionUID = -8650464486886364918L;

    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    private Long stockTransferId;
}
