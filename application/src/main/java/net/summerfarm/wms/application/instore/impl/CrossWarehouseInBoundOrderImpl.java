package net.summerfarm.wms.application.instore.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.common.ContainerCommandService;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderDetailReqDTO;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.application.common.enums.ContainerOccupyStatusEnum;
import net.summerfarm.wms.application.common.enums.ContainerStatusEnum;
import net.summerfarm.wms.application.instore.InBoundOrderTemplate;
import net.summerfarm.wms.application.instore.dto.CheckDTO;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.QualityTimeUnitEnum;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.mqUtil.MqUtil;
import net.summerfarm.wms.common.mqUtil.base.enums.MType;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.common.domainobject.Container;
import net.summerfarm.wms.domain.common.repository.ContainerRepository;
import net.summerfarm.wms.domain.instore.domainobject.*;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskAbnormalRecordRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.mission.domainobject.MissionDetail;
import net.summerfarm.wms.domain.mission.domainobject.query.ContainerRuleCheck;
import net.summerfarm.wms.domain.mission.enums.MissionTypeEnum;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.stock.domainobject.Stock;
import net.summerfarm.wms.domain.transaction.domainobject.AreaStoreInventoryTransaction;
import net.summerfarm.wms.domain.transaction.enums.TransactionStateEnum;
import net.summerfarm.wms.facade.product.GoodsCheckTaskFacade;
import net.summerfarm.wms.facade.product.dto.CreateGoodsCheckTaskDTO;
import net.summerfarm.wms.facade.product.dto.ProductQualityTimeTypeDTO;
import net.summerfarm.wms.facade.purchase.StockArrangeFacade;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import net.summerfarm.wms.inventory.enums.AreaStoreInventoryTypeEnum;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AreaStoreInventoryReqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @author: dongcheng
 * @date: 2023/10/17
 */
@Slf4j
@Service
public class CrossWarehouseInBoundOrderImpl extends InBoundOrderTemplate {
    @Resource
    StockArrangeFacade stockArrangeFacade;
    @Resource
    StockTaskAbnormalRecordRepository stockTaskAbnormalRecordRepository;
    @Resource
    StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    InBoundOrderQueryRepository inBoundOrderQueryRepository;
    @Resource
    StockStorageItemRepository stockStorageItemRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private GoodsCheckTaskFacade goodsCheckTaskFacade;
    @Resource
    private MqUtil mqUtil;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ContainerRepository containerRepository;
    @Resource
    private MissionRepository missionRepository;
    @Resource
    private ContainerCommandService containerCommandService;
    @Resource
    private InBoundOrderMsgService inBoundOrderMsgService;

    @Override
    public List<Integer> getTypes() {
        return Lists.newArrayList(InBoundOrderEnum.CROSS_WAREHOUSE_IN.getCode());
    }

    @Override
    public void stockChange(Stock stock) {
        stock.updateRoadStock();
        stock.updateStoreStock();

//        if (stock.getSync() != null && NumberUtils.INTEGER_ONE.equals(stock.getSync())) {
//            stock.updateOnlineStock();
//        }
    }

    @Override
    public void sendStockChangeMq(Stock stock) {
        AreaStoreInventoryReqDTO inventory = AreaStoreInventoryReqDTO.builder()
                .sku(stock.getSku())
                .bizId(stock.getBizId())
                .diffChange(stock.getDiffChange())
                .warehouseNo(stock.getWarehouseNo())
                .diffStock(stock.getDiffStock())
                .operator(stock.getOperator())
                .type(Objects.nonNull(stock.getType()) ? stock.getType().getTypeName() : null)
                .build();

        mqProducer.sendTransaction(WmsConstant.AREA_STORE_INVENTORY_PG, WmsConstant.AREA_STORE_INVENTORY_TOPIC,
                WmsConstant.AREA_STORE_INVENTORY_STORE_TAG, inventory);

        inventory.setDiffStock(-stock.getDiffStock());
        mqProducer.sendTransaction(WmsConstant.AREA_STORE_INVENTORY_PG, WmsConstant.AREA_STORE_INVENTORY_TOPIC,
                WmsConstant.AREA_STORE_INVENTORY_ROAD_TAG, inventory);
    }

    /**
     * 越库出库任务不加虚拟库存
     * @param stock        库存对象
     * @param transactions 事务日志
     */
    @Override
    public void buildStockTransaction(Stock stock, List<AreaStoreInventoryTransaction> transactions) {
        transactions.add(buildTs(stock, AreaStoreInventoryTypeEnum.STORE.getDesc()));
        transactions.add(buildTs(stock, AreaStoreInventoryTypeEnum.ROAD.getDesc()));

    }

    private AreaStoreInventoryTransaction buildTs(Stock stock, String inventoryType) {
        return AreaStoreInventoryTransaction.builder()
                .bizId(stock.getBizId())
                .bizType(stock.getType().getTypeName())
                .inventoryType(inventoryType)
                .warehouseNo(stock.getWarehouseNo())
                .transactionState(TransactionStateEnum.DOING.getCode())
                .sku(stock.getSku())
                .param(JSON.toJSONString(stock))
                .build();
    }

    @Override
    public void checkExternal(InBoundOrderCommand command) {
        Long stockStorageTaskId = command.getStockStorageTaskId();

        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageTaskId);
        Boolean StockArrangeState = stockArrangeFacade.queryStockArrangeState(stockTaskStorage.getStockTaskId());
        if (Boolean.FALSE.equals(StockArrangeState)) {
            // todo ct
            throw new BizException("预约单状态以取消不可入库");
        }

    }

    @Override
    public void checkBaseInfo(InBoundOrderCommand command) {
        //越仓入库，调拨回库不校验
        List<Integer> skipCheckType = Arrays.asList(InBoundOrderEnum.SKIP_STORE_IN.getCode(),
                InBoundOrderEnum.ALLOCATION_ABNORMAL_IN.getCode());
        if (skipCheckType.contains(command.getType())) {
            return;
        }

        boolean check = command.getOrderDetails().stream().allMatch(item -> Objects.nonNull(item.getSku()) &&
                Objects.nonNull(item.getProduceAt()) &&
                Objects.nonNull(item.getShelfLife()) &&
                Objects.nonNull(item.getPurchaseNo()));
        if (!check && !CheckDTO.notNeedCheckElement.contains(command.getType())) {
            throw new BizException("sku,批次号以及效期不可为空");
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());

        if (Objects.isNull(stockTaskStorage)) {
            throw new BizException("未查询到入库任务信息");
        }

        if (!Objects.equals(stockTaskStorage.getState(), StockTaskStorageStateEnum.NORMAL.getId())) {
            throw new BizException("该任务状态下不允许操作入库单");
        }
        List<InBoundOrderDetailReqDTO> orderDetails = command.getOrderDetails();
        Long stockStorageTaskId = command.getStockStorageTaskId();
        Map<String, List<InBoundOrderDetailReqDTO>> skuMap = orderDetails.stream().collect(groupingBy(InBoundOrderDetailReqDTO::getSku));

        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockStorageTaskId);
        if (CollectionUtils.isEmpty(stockStorageItems)) {
            throw new BizException("无入库任务信息");
        }
        //异常操作 旧任务id 异常操作需要重构 todo
        List<StockTaskAbnormalRecord> abnormalRecords = stockTaskAbnormalRecordRepository.select(stockTaskStorage.getStockTaskId(), null);
        Map<String, List<StockTaskAbnormalRecord>> abnormalRecordMap = abnormalRecords.stream().collect(Collectors.groupingBy(StockTaskAbnormalRecord::getSku));
        Map<String, List<StockStorageItem>> skuStockStorageItemMap = stockStorageItems.stream().collect(groupingBy(StockStorageItem::getSku));
        List<InBoundOrderDetail> inBoundOrderDetail =
                inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(stockStorageTaskId).build());

        Map<String, List<InBoundOrderDetail>> skuDetail = inBoundOrderDetail.stream().collect(groupingBy(InBoundOrderDetail::getSku));
        skuMap.forEach((sku, reqDTOList) -> {
            //操作入库数量
            int stockNumSum = reqDTOList.stream().mapToInt(InBoundOrderDetailReqDTO::getStockNum).sum();
            List<InBoundOrderDetail> inBoundOrderDetails = skuDetail.get(sku);
            //已入数量
            int inStockSum = CollectionUtils.isEmpty(inBoundOrderDetails) ? 0 : inBoundOrderDetails.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
            //异常操作
            List<StockTaskAbnormalRecord> recordList = abnormalRecordMap.get(sku);
            int abnormalRecordSum = CollectionUtils.isEmpty(recordList) ? 0 : recordList.stream().mapToInt(StockTaskAbnormalRecord::getQuantity).sum();
            List<StockStorageItem> stockStorageItemList = skuStockStorageItemMap.get(sku);
            //总数量
            StockStorageItem storageItem = stockStorageItemList.stream().findFirst().orElseThrow(() -> new BizException("入库任务异常"));
            Integer quantity = storageItem.getQuantity();
            if (stockNumSum + inStockSum + abnormalRecordSum > quantity) {
                // err
                throw new BizException("入库数量异常,请刷新页面确认数量");
            }
        });
    }

    @Override
    public void noticeExternal(InBoundOrderCommand command) {
        command.getInBoundOrderIdList().forEach(orderId -> inBoundOrderMsgService.sendInBoundOrderMsg(orderId));
        if (null != command.getTenantId() && command.getTenantId() > WmsConstant.XIANMU_TENANT_ID) {
            log.info("非鲜沐仓过滤返回，入库任务id：{}", command.getStockStorageTaskId());
            return;
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());
        // 获取商品保质期限
        List<String> skuList = command.getOrderDetails().stream().map(InBoundOrderDetailReqDTO::getSku).distinct().collect(Collectors.toList());
        List<Product> productList = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                .warehouseNo(command.getWarehouseNo())
                .skus(skuList)
                .tenantId(stockTaskStorage.getTenantId()).build());
        if (CollectionUtils.isEmpty(productList)) {
            log.info("根据sku查询spu为空，sku：{}", JSON.toJSONString(skuList));
            return;
        }
        // 保质期类型不同则通知商品变更保质期类型
        List<ProductQualityTimeTypeDTO> productQualityTimeTypeDTOList = new ArrayList<>();
        Map<String, Integer> skuQualityTypeMap = command.getOrderDetails().stream()
                .filter(item -> null != item.getQualityTimeType())
                .collect(Collectors.toMap(InBoundOrderDetailReqDTO::getSku, InBoundOrderDetailReqDTO::getQualityTimeType, (a, b) -> a));
        productList.forEach(item -> {
            if (null == item.getQualityTimeType() || !item.getQualityTimeType().equals(skuQualityTypeMap.get(item.getSku()))) {
                ProductQualityTimeTypeDTO productQualityTimeTypeDTO = new ProductQualityTimeTypeDTO();
                productQualityTimeTypeDTO.setSku(item.getSku());
                productQualityTimeTypeDTO.setQualityTimeType(skuQualityTypeMap.get(item.getSku()));
                if (null != productQualityTimeTypeDTO.getQualityTimeType()) {
                    productQualityTimeTypeDTOList.add(productQualityTimeTypeDTO);
                }
            }
        });
        // 发送mq到wms-list
        if (!CollectionUtils.isEmpty(productQualityTimeTypeDTOList)) {
            List<List<ProductQualityTimeTypeDTO>> partitionList = Lists.partition(productQualityTimeTypeDTOList, WmsConstant.BATCH_SIZE_LIMIT);
            partitionList.forEach(list -> {
                try {
                    mqUtil.sendMqData(Global.MQ_TOPIC, list, MType.CHANGE_QUALITY_TIME_TYPE.name());
                } catch (Exception e) {
                    log.error("变更商品保质期类型发送消息失败，{}", JSON.toJSONString(list), e);
                }
            });
        }
        Map<String, Product> skuProductMap = productList.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (a, b) -> a));
        // 入库保质期时长和商品侧对比，存在差异创建商品校验任务
        List<CreateGoodsCheckTaskDTO> createGoodsCheckTaskDTOList = new ArrayList<>();
        command.getOrderDetails().forEach(item -> {
            Product product = skuProductMap.get(item.getSku());
            if (null == product) {
                return;
            }
            if (Boolean.FALSE.equals(equalsQualityTime(item.getProduceAt(), item.getShelfLife(), product.getEffectiveTime(), product.getTimeUnit()))) {
                log.info("入库计算保质期时长存在差异，warehouseNo{}，sku{}，produceAt{}，shelfLife{}，effectiveTime{}，timeUnit{}，qualityTimeType{}", command.getWarehouseNo(), item.getSku(), item.getProduceAt(), item.getShelfLife(), product.getEffectiveTime(), product.getTimeUnit(), item.getQualityTimeType());
                CreateGoodsCheckTaskDTO createGoodsCheckTaskDTO = new CreateGoodsCheckTaskDTO();
                createGoodsCheckTaskDTO.setWarehouseNo(command.getWarehouseNo().intValue());
                createGoodsCheckTaskDTO.setSku(item.getSku());
                createGoodsCheckTaskDTO.setOperator("系统—效期校验");
                createGoodsCheckTaskDTOList.add(createGoodsCheckTaskDTO);
            }
        });
        if (!CollectionUtils.isEmpty(createGoodsCheckTaskDTOList)) {
            try {
                goodsCheckTaskFacade.createGoodsCheckTask(createGoodsCheckTaskDTOList);
            } catch (Exception e) {
                log.error("创建商品校验任务异常", e);
            }
        }

    }

    /**
     * 预处理资源信息
     *
     * @param command c
     */
    @Override
    public void preHandlerSource(InBoundOrderCommand command) {
        // 校验占用容器信息
        checkContainerAndOccpuy(command);
    }

    /**
     * 判断保质期时长是否相同
     *
     * @param startTimestamp
     * @param endTimestamp
     * @param effectiveTime
     * @param timeUnit
     * @return
     */
    private Boolean equalsQualityTime(Long startTimestamp, Long endTimestamp, Integer effectiveTime, String timeUnit) {
        if (null == startTimestamp || null == endTimestamp || null == effectiveTime || StringUtils.isBlank(timeUnit)) {
            return false;
        }
        // 单位为月
        if (QualityTimeUnitEnum.MONTH.getCode().equals(timeUnit)) {
            // 判断期间有多少个月，开始日期的天大于结束日期的天则减去一个月
            Long betweenMonth = DateUtil.getBetweenMonth(startTimestamp, endTimestamp, false);
            if (!effectiveTime.equals(betweenMonth.intValue())) {
                return false;
            }
            // 判断结束日期大于开始日期时，天数是否一致
            Integer startDay = DateUtil.getDayOfMonth(startTimestamp);
            Integer endDay = DateUtil.getDayOfMonth(endTimestamp);
            if (!startDay.equals(endDay)) {
                return false;
            }
            return true;
            // 单位为天
        } else if (QualityTimeUnitEnum.DAY.getCode().equals(timeUnit)) {
            // 判断期间有多少天
            Long betweenDay = DateUtil.getBetweenDay(startTimestamp, endTimestamp, false);
            if (!effectiveTime.equals(betweenDay.intValue())) {
                return false;
            }
            return true;
        }
        // 其他单位返回
        return false;
    }

    /**
     * 校验容器信息
     *
     * @param command 入库单创建请求
     */
    private void checkContainerAndOccpuy(InBoundOrderCommand command) {
        // 获取容器号列表
        List<String> containerNoList = command.getOrderDetails().stream()
                .map(InBoundOrderDetailReqDTO::getReceivingContainer)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        // 没有容器号直接返回
        if (CollectionUtils.isEmpty(containerNoList)) {
            return;
        }
        // 首先判断容器是否被占用
        List<Container> containerList = containerRepository.listByContainerNos(command.getWarehouseNo(), containerNoList);
        if (CollectionUtils.isEmpty(containerList)) {
            return;
        }

        // 容器校验
        List<ContainerRuleCheck> checks = command.getOrderDetails().stream().map(item -> ContainerRuleCheck.builder()
                .qualityDate(DateUtil.toLocalDate(item.getShelfLife()))
                .produceTime(DateUtil.toLocalDate(item.getProduceAt()))
                .containerNo(item.getReceivingContainer())
                .sku(item.getSku())
                .build()).collect(Collectors.toList());

        // 获取当前任务占用的所有容器列表
        InBoundTaskId inBoundTaskId = InBoundTaskId.builder().taskId(command.getStockStorageTaskId()).build();
        List<InBoundOrder> inBoundOrderList = inBoundOrderQueryRepository.findInBoundOrderByTaskId(inBoundTaskId);
        Map<String, List<InBoundOrder>> inBoundOrderListMap = inBoundOrderList.stream().collect(groupingBy(InBoundOrder::getContainerNo));
        for (Container container : containerList) {
            Integer occupyStatus = container.getOccupyStatus();
            // 容器不可用 被禁用
            if (ContainerStatusEnum.CLOSE.getCode().equals(container.getContainerStatus())) {
                throw new BizException("当前容器【" + container.getContainerCode() + "】已被禁止使用");
            }
            // 查询历史创建的入库单
            List<InBoundOrder> boundOrders = inBoundOrderListMap.get(container.getContainerCode());
            // 容器被别的任务占用了
            if (CollectionUtils.isEmpty(boundOrders) && ContainerOccupyStatusEnum.DO.getCode().equals(occupyStatus)) {
                throw new BizException("当前容器【" + container.getContainerCode() + "】已被别的任务占用");
            }
            // 容器没有被占用的话 占用容器
            if (ContainerOccupyStatusEnum.UNDO.getCode().equals(occupyStatus)) {
                // 容器占用
                Boolean occupy = containerCommandService.occupyContainer(command.getWarehouseNo().intValue(), container.getContainerCode());
                if (Boolean.FALSE.equals(occupy)) {
                    throw new BizException("当前容器【" + container.getContainerCode() + "】已被别的任务占用");
                }
            }

            // 当前容器第一执行收货
            if (CollectionUtils.isEmpty(boundOrders)) {
                continue;
            }
            // 当前容器不是第一次执行收货
            // 查询入库单明细
            List<Long> inBoundOrderIdList = boundOrders.stream().map(InBoundOrder::getId).collect(Collectors.toList());
            List<InBoundOrderDetail> inBoundOrderDetails = inBoundOrderQueryRepository.findInBoundOrderDetails(inBoundOrderIdList);
            Map<String, List<InBoundOrderDetail>> inboundOrderDetailListMap
                    = inBoundOrderDetails.stream().collect(groupingBy(InBoundOrderDetail::dpKey));
            List<String> dpKeyList = inBoundOrderDetails.stream().map(InBoundOrderDetail::dpKey)
                    .distinct().collect(Collectors.toList());
            // 查询已经生成的任务明细
            List<MissionDetail> missionDetailList = missionRepository.findDetailByPMissionNoAndContainerNo(command.getStockStorageTaskId().toString(),
                    StockStorageTypeEnums.CROSS_WAREHOUSE_IN.getId(),
                    MissionTypeEnum.CROSS_WAREHOUSE_SORT.getCode(),
                    container.getContainerCode());
            Map<String, List<MissionDetail>> missionDetailListMap = missionDetailList.stream().collect(groupingBy(MissionDetail::duplicateKeyForCross));
            // 扣减剩下来的就是已经收货了的但是没有生成任务的明细内容
            for (String dpKey : dpKeyList) {
                List<InBoundOrderDetail> detailList = inboundOrderDetailListMap.get(dpKey);
                int sum = detailList.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                List<MissionDetail> details = missionDetailListMap.get(dpKey);
                if (!CollectionUtils.isEmpty(details)) {
                    int shouldInQuantity = details.stream().mapToInt(MissionDetail::getShouldInQuantity).sum();
                    if (shouldInQuantity >= sum) {
                        continue;
                    }
                }
                InBoundOrderDetail orderDetail = detailList.get(0);
                ContainerRuleCheck check = ContainerRuleCheck.builder()
                        .containerNo(container.getContainerCode())
                        .sku(orderDetail.getSku())
                        .produceTime(DateUtil.toLocalDate(orderDetail.getProduceAt()))
                        .qualityDate(DateUtil.toLocalDate(orderDetail.getShelfLife()))
                        .build();
                checks.add(check);
            }
        }

        // check
        checks.stream().collect(Collectors.groupingBy(ContainerRuleCheck::dupKey))
                .forEach((dupKey, list) -> {
                    Set<LocalDate> date = list.stream().map(ContainerRuleCheck::getQualityDate).filter(Objects::nonNull).collect(Collectors.toSet());
                    if (date.size() > 1) {
                        ContainerRuleCheck check = list.stream().findFirst().get();
                        throw new BizException(check.getContainerNo() + "容器不允许混放同商品不同效期"
                                , ErrorCodeNew.CONTAINER_NOT_ALLOW_FIX);
                    }
                });
    }
}
