package net.summerfarm.wms.data;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.wms.dto.dto.StockTaskSkuMsgDTO;
import net.summerfarm.wms.application.instore.impl.InBoundOrderMsgService;
import net.summerfarm.wms.application.instore.impl.StockTaskStorageMsgService;
import net.summerfarm.wms.application.stocktask.ExternalSystemMessageService;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.data.dto.LockStockRepairDTO;
import net.summerfarm.wms.data.dto.StoreRecordCostReqDTO;
import net.summerfarm.wms.domain.external.repository.WmsExternalBusinessTransferRecordCommandRepository;
import net.summerfarm.wms.domain.instore.domainobject.InBoundOrder;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.summerfarm.wms.facade.purchase.PurchaseFacade;
import net.summerfarm.wms.facade.purchase.dto.PurchaseFacadeResDTO;
import net.summerfarm.wms.infrastructure.dao.instore.StockTaskStorageDAO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskStorageDO;
import net.summerfarm.wms.infrastructure.dao.inventory.AreaStoreDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.StoreRecordDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO;
import net.summerfarm.wms.manage.model.domain.Admin;
import net.summerfarm.wms.manage.model.vo.StockArrangeVO;
import net.summerfarm.wms.manage.mq.StockTaskProcessDetailOrderSkuListener;
import net.summerfarm.wms.manage.web.enums.StockTaskType;
import net.summerfarm.wms.manage.web.mapper.manage.*;
import net.summerfarm.wms.manage.web.service.StockTaskMsgService;
import net.summerfarm.wms.manage.web.service.StockTaskProcessOrderSkuService;
import net.summerfarm.wms.outstore.StockTaskProvider;
import net.summerfarm.wms.outstore.dto.OrderSelfStockTaskReq;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2023/3/15 16:34
 * @<AUTHOR>
 */
@RestController
@RequestMapping("/data-repair")
@Slf4j
public class DataRepairController {

    @Resource
    private AreaStoreDAO areaStoreDAO;

    @Resource
    private StoreRecordDAO storeRecordDAO;

    @Resource
    private PurchaseFacade purchaseFacade;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private StockTaskProvider stockTaskProvider;
    @Resource
    private StockTaskProcessDetailOrderSkuListener stockTaskProcessDetailOrderSkuListener;
    @Resource
    private StockArrangeMapper stockArrangeMapper;
    @Resource
    private StockTaskStorageDAO stockTaskStorageDAO;
    @Resource
    private InBoundOrderMsgService inBoundOrderMsgService;
    @Resource
    private StockTaskStorageMsgService stockTaskStorageMsgService;

    @Resource
    private ExternalSystemMessageService externalSystemMessageService;

    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private StockTaskMsgService stockTaskMsgService;
    @Resource
    private WmsExternalBusinessTransferRecordCommandRepository wmsExternalBusinessTransferRecordCommandRepository;
    @Resource
    private InBoundOrderQueryRepository inBoundOrderQueryRepository;

    @PostMapping("/refresh_redis_admin")
    public void refreshAdminRedis() {
        try {
            log.info("开始初始化admin信息");
            Integer pageNum = 1;
            while (true) {
                //log.info("rpc查询admin开始");
                PageInfo<Admin> objectPageInfo = PageHelper.startPage(pageNum, 200).doSelectPageInfo(() -> adminMapper.selectAll());
                List<Admin> admins = objectPageInfo.getList();
                if (CollectionUtils.isEmpty(admins)) {
                    break;
                }
                log.info("rpc查询admin结束");
                admins.forEach(item -> {
                    AdminFacadeDTO adminFacadeDTO = AdminFacadeDTO.builder()
                            .adminId(item.getAdminId().longValue())
                            .realName(item.getRealname())
                            .nameRemakes(item.getNameRemakes())
                            .username(item.getUsername())
                            .phone(item.getPhone())
                            .build();
                    redisUtil.setNx(RedisKeys.buildAdminKey(adminFacadeDTO.getAdminId()), JSON.toJSONString(adminFacadeDTO),
                            7L, TimeUnit.DAYS);
                });
                log.info("redis插入admin结束");
                pageNum++;
            }
            log.info("初始化admin信息结束");
        } catch (Exception e) {
            log.warn("admin数据初始化异常");
        }
    }

    @PostMapping("/store_record_cost_repair")
    public void repairStoreRecordCostNull(@RequestBody StoreRecordCostReqDTO storeRecordCostReqDTO) {
        log.info("开始更新storeRecord表cost为null或0的数据,开始时间:{},结束时间:{}", storeRecordCostReqDTO.getStartTime(),
                storeRecordCostReqDTO.getEndTime());
        Integer pageNum = 1;
        while (true) {
            PageInfo<StoreRecordDO> records = PageHelper.startPage(pageNum, 200).doSelectPageInfo(() ->
                    storeRecordDAO.repairStoreRecordCostNull(DateUtil.toDate(storeRecordCostReqDTO.getStartTime()),
                            DateUtil.toDate(storeRecordCostReqDTO.getEndTime())));

            List<StoreRecordDO> list = records.getList();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            List<String> skus = list.stream().map(StoreRecordDO::getSku).collect(Collectors.toList());
            List<String> batchs = list.stream().map(StoreRecordDO::getBatch).collect(Collectors.toList());
            log.info("开始更新storeRecord表cost为null或0的数据,sku:{}", skus);
            List<PurchaseFacadeResDTO> purchaseFacadeResDTOS = purchaseFacade.queryPurchase(batchs, skus);
            Map<String, BigDecimal> costMap = purchaseFacadeResDTOS.stream().collect(Collectors.toMap(item -> item.getPurchaseNo() + item.getSku(),
                    PurchaseFacadeResDTO::getCost, (o1, o2) -> o1));

            list.forEach(item -> {
                if (costMap.containsKey(item.getBatch() + item.getSku())) {
                    storeRecordDAO.updateCost(item.getId().longValue(), costMap.get(item.getBatch() + item.getSku()));
                }
            });

            pageNum++;

            try {
                Thread.sleep(500L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        log.info("结束更新storeRecord表cost为null或0的数据");

    }

    @RequestMapping("/repairLockStock")
    public String repairLockStock(@RequestBody List<LockStockRepairDTO> repairDTOList) {
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(repairDTOList), "参数不能为空");
        List<String> failList = Lists.newArrayList();
        repairDTOList.forEach(repairDTO -> {
            try {
                this.updateLockStock(repairDTO);
            } catch (Exception e) {
                failList.add(JSONUtil.toJsonStr(repairDTO) + e.getMessage());
            }
        });
        return JSONUtil.toJsonStr(failList);
    }

    private void updateLockStock(LockStockRepairDTO repairDTO) {
        String sku = repairDTO.getSku();
        Integer warehouseNo = repairDTO.getWarehouseNo();
        Integer quantity = repairDTO.getOperateQuantity();
        ExceptionUtil.checkAndThrow(Objects.nonNull(sku), "sku不能为空");
        ExceptionUtil.checkAndThrow(Objects.nonNull(warehouseNo) && warehouseNo > 0, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(Objects.nonNull(quantity) && quantity != 0, "调整冻结数量不能为空");
        AreaStoreDO areaStoreDO = areaStoreDAO.selectOne(sku, warehouseNo);
//        ExceptionUtil.checkAndThrow(Objects.nonNull(areaStoreDO), "未查询到库存信息");
        if (null == areaStoreDO) {
            throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), "未查询到库存信息");
        }
        Integer id = areaStoreDO.getId();
        areaStoreDAO.updateLockQuantity(id, quantity);
    }

    @RequestMapping("/initAreaStore")
    public void initAreaStore(@RequestParam("warehouseNo") Integer warehouseNo, @RequestBody List<String> skuList) {
        List<AreaStoreDO> areaStoreDOList = Lists.newArrayList();
        skuList.forEach(sku -> {
            AreaStoreDO areaStoreDO = AreaStoreDO.builder()
                    .areaNo(warehouseNo)
                    .sku(sku)
                    .sync(1).build();
            areaStoreDOList.add(areaStoreDO);
        });
        areaStoreDAO.insertBatch(areaStoreDOList);
    }

    @RequestMapping("/orderSelfStockTask")
    public void orderSelfStockTask(@RequestBody OrderSelfStockTaskReq taskReq) {
        log.info("后门工具补发自提出库任务开始,taskReq:{}", JSONUtil.toJsonStr(taskReq));
        stockTaskProvider.orderSelfStockTask(taskReq);
        log.info("后门工具补发自提出库任务完成");
    }

    @RequestMapping("/initStockTaskProcessDetailOrderSku")
    public String initStockTaskProcessDetailOrderSku(@RequestBody List<Long> initProcessIdList) {
        if (CollectionUtils.isEmpty(initProcessIdList)) {
            return "初始化失败，出库单ID列表为空";
        }
        List<Long> failList = Lists.newArrayList();
        initProcessIdList.forEach(processId -> {
            try {
                if (Objects.isNull(processId)) {
                    return;
                }
                StockTaskSkuMsgDTO stockTaskSkuMsgDTO = StockTaskSkuMsgDTO.builder()
                        .stockTaskProcessId(processId.intValue()).build();
                stockTaskProcessDetailOrderSkuListener.process(stockTaskSkuMsgDTO);
            } catch (Exception e) {
                failList.add(processId);
                log.warn("初始化订单出库单明细关联关系异常，出库单ID:{}", processId, e);
            }

        });
        return CollectionUtils.isEmpty(failList) ? "初始化成功" : "存在出库单详情信息初始化失败" + JSONUtil.toJsonStr(failList);
    }

    @RequestMapping("/initStockTaskStorageArrangeId")
    public String initStockTaskStorageArrangeId(@RequestBody List<Integer> initStockTaskIdList) {
        if (CollectionUtils.isEmpty(initStockTaskIdList)) {
            return "初始化失败，任务ID列表为空";
        }
        initStockTaskIdList.forEach(stockTaskId -> {
            StockArrangeVO stockArrangeVO = stockArrangeMapper.selectByTaskId(stockTaskId);
            if (Objects.isNull(stockArrangeVO)) {
                log.warn("未查询到预约单信息 stockTaskId:{}", stockTaskId);
                return;
            }
            StockTaskStorageDO stockTaskStorageDO = stockTaskStorageDAO.queryByTaskId(stockTaskId.longValue());
            if (Objects.isNull(stockTaskStorageDO)) {
                log.warn("未查询到入库任务信息 stockTaskId:{}", stockTaskId);
                return;
            }
            List<Integer> supportTypeList = Arrays.asList(StockTaskType.PURCHASE_IN.getId(), StockTaskType.CROSS_WAREHOUSE_IN.getId());
            if (!supportTypeList.contains(stockTaskStorageDO.getType())) {
                log.warn("非采购类型任务，无需初始化 stockTaskId:{}", stockTaskId);
                return;
            }
            stockTaskStorageDAO.updateThirdOrderNoById(stockTaskStorageDO.getId(), stockArrangeVO.getId().toString());
            log.info("采购入库任务预约单id更新成功 stockTaskId:{},stockArrangeId:{}", stockTaskId, stockArrangeVO.getId());

        });


        return "初始化成功";
    }

    @RequestMapping("/resendInBoundOrderMsg")
    public String resendInBoundOrderMsg(@RequestParam("inBoundOrderId") Long inBoundOrderId) {
        if (Objects.isNull(inBoundOrderId) || inBoundOrderId <= 0) {
            return "参数异常";
        }
        inBoundOrderMsgService.sendInBoundOrderMsg(inBoundOrderId);
        return "success";
    }

    @RequestMapping("/resendStockStorageCreatedMsg")
    public String resendStockStorageCreatedMsg(@RequestParam("stockTaskStorageId") Long stockTaskStorageId) {
        if (Objects.isNull(stockTaskStorageId) || stockTaskStorageId <= 0) {
            return "参数异常";
        }
        stockTaskStorageMsgService.sendStockStorageCreatedMsg(stockTaskStorageId);
        return "success";
    }

    @RequestMapping("/resendStockStorageFinishNotice")
    public String resendStockStorageFinishNotice(@RequestParam("stockTaskStorageId") Long stockTaskStorageId) {
        if (Objects.isNull(stockTaskStorageId) || stockTaskStorageId <= 0) {
            return "参数异常";
        }
        inBoundOrderMsgService.sendStockStorageFinishNotice(stockTaskStorageId);
        return "success";
    }

    @RequestMapping("/batchSendInBoundOrderMsg")
    public String batchSendInBoundOrderMsg(@RequestBody List<Long> inBoundOrderIdList) {
        if (CollectionUtils.isEmpty(inBoundOrderIdList)) {
            return "参数异常";
        }
        List<Long> failResult = Lists.newArrayList();
        inBoundOrderIdList.forEach(inBoundOrderId -> {
            try {
                inBoundOrderMsgService.sendInBoundOrderMsg(inBoundOrderId);
            } catch (Exception e) {
                failResult.add(inBoundOrderId);
                log.warn("入库单消息发送失败,inBoundOrderId:{}", inBoundOrderId, e);
            }

        });
        return CollectionUtils.isNotEmpty(failResult) ? JSONUtil.toJsonStr(failResult) : "all success";
    }

    /**
     * 重发出库任务创建消息
     *
     * @param stockTaskIdList 出库任务id数组
     * @return 结果
     */
    @PostMapping("/resendStockTaskCreatedMsg")
    public String resendStockTaskCreatedMsg(@RequestBody List<Long> stockTaskIdList) {
        if (CollectionUtils.isEmpty(stockTaskIdList)) {
            return "参数为空";
        }
        List<StockTask> outStockTaskList = stockTaskRepository.findOutStockTaskList(stockTaskIdList);
        if (CollectionUtils.isEmpty(outStockTaskList)){
            return "未查询到任务";
        }
        for (StockTask stockTask : outStockTaskList) {
            externalSystemMessageService.sendMessageToExternalSystem(stockTask.getAreaNo(), stockTask.getType(), stockTask.getId());
        }
        return "success";
    }

    /**
     * 补发采退出库单据推送
     * @param stockTaskIdList
     * @return
     */
    @PostMapping("/resendPurchaseOutBillNotice")
    public String resendPurchaseOutBillNotice(@RequestBody List<Long> stockTaskIdList) {
        if (CollectionUtils.isEmpty(stockTaskIdList)) {
            return "参数为空";
        }
        List<StockTask> outStockTaskList = stockTaskRepository.findOutStockTaskList(stockTaskIdList);
        if (CollectionUtils.isEmpty(outStockTaskList)){
            return "未查询到任务";
        }
        for (StockTask stockTask : outStockTaskList) {
            externalSystemMessageService.sendPurchaseOutBillToExternalSystem(stockTask.getAreaNo(), stockTask.getId());
        }
        return "success";
    }

    /**
     * 补发采购入库单据推送
     * @param inboundIdList
     * @return
     */
    @PostMapping("/resendPurchaseInBillNotice")
    public String resendPurchaseInBillNotice(@RequestBody List<Long> inboundIdList) {
        if (CollectionUtils.isEmpty(inboundIdList)) {
            return "参数为空";
        }
        List<InBoundOrder> inBoundOrderList = inBoundOrderQueryRepository.findInBoundOrders(inboundIdList);
        if (CollectionUtils.isEmpty(inBoundOrderList)){
            return "未查询到任务";
        }
        for (InBoundOrder inBoundOrder : inBoundOrderList) {
            externalSystemMessageService.sendPurchaseInBillToExternalSystem(inBoundOrder.getWarehouseNo().intValue(), inBoundOrder.getStockStorageTaskId(), inBoundOrder.getId());
        }
        return "success";
    }

    @RequestMapping("/resendStockOutFinishNotice")
    public String resendStockOutFinishNotice(@RequestParam("stockTaskId") Long stockTaskId) {
        if (null == stockTaskId || stockTaskId <= 0) {
            return "参数异常";
        }
        StockTask stockTask = stockTaskRepository.findOutStockTask(stockTaskId);
        if (null == stockTask) {
            return "未查询到任务";
        }
        if (StockTaskTypeEnum.STORE_ALLOCATION_OUT.getId() == stockTask.getType() || StockTaskTypeEnum.PURCHASES_BACK.getId() == stockTask.getType()) {
            stockTaskMsgService.handleStockTaskFinishMsg(stockTaskId.intValue(), null);
        } else {
            stockTaskMsgService.checkAndSendStockTaskFinishMsg(stockTaskId.intValue());
        }
        return "success";
    }

    /**
     * 删除外部推送记录
     * @param id
     * @return
     */
    @RequestMapping("/removeExternalBusinessTransferRecord")
    public String removeExternalBusinessTransferRecord(@RequestParam("id") Long id) {
        if (null == id || id <= 0) {
            return "参数异常";
        }
        wmsExternalBusinessTransferRecordCommandRepository.deleteById(id);
        return "success";
    }


}
