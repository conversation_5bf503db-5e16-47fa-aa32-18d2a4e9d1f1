<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskDAO">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        INSERT INTO stock_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                task_no,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="outStoreNo != null">
                out_store_no,
            </if>
            <if test="outType != null">
                out_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="dimension != null">
                dimension,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="systemSource != null">
                system_source,
            </if>
            <if test="outOrderNo != null">
                out_order_no,
            </if>
            <if test="optionFlag != null">
                option_flag,
            </if>
            <if test="outboundCategory != null">
                outbound_category,
            </if>
            <if test="externalWarehouseNo != null">
                external_warehouse_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                #{taskNo},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="expectTime != null">
                #{expectTime},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="outStoreNo != null">
                #{outStoreNo},
            </if>
            <if test="outType != null">
                #{outType},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="dimension != null">
                #{dimension},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="category != null">
                #{category},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="systemSource != null">
                #{systemSource},
            </if>
            <if test="outOrderNo != null">
                #{outOrderNo},
            </if>
            <if test="optionFlag != null">
                #{optionFlag},
            </if>
            <if test="outboundCategory != null">
                #{outboundCategory},
            </if>
            <if test="externalWarehouseNo != null">
                #{externalWarehouseNo},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        UPDATE stock_task
        SET state = #{state},
        <if test="processState != null">
            process_state = #{processState},
        </if>
        <if test="mismatchReason != null">
            mismatch_reason = #{mismatchReason},
        </if>
        <if test="adminId != null">
            admin_id = #{adminId},
        </if>
        <if test="taskNo != null">
            task_no = #{taskNo},
        </if>
        updatetime = #{updatetime}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT st.id,
               st.task_no             taskNo,
               st.out_store_no        outStoreNo,
               st.type,
               st.expect_time         expectTime,
               st.state,
               st.addtime,
               st.updatetime,
               st.area_no             areaNo,
               st.out_type            outType,
               st.remark,
               st.dimension,
               st.process_state       processState,
               st.remark,
               st.mismatch_reason  as mismatchReason,
               st.tenant_id        as tenantId,
               st.task_type        as taskType,
               st.category,
               st.close_reason        closeReason,
               st.inventory_locked as inventoryLocked,
               st.system_source    as systemSource,
               st.option_flag      as optionFlag,
               st.external_warehouse_no as externalWarehouseNo
        FROM stock_task st
        WHERE st.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectOne" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        /*FORCE_MASTER*/
        SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime,dimension
        dimension,tenant_id tenantId,external_warehouse_no externalWarehouseNo
        , inventory_locked as inventoryLocked
        , system_source as systemSource
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectById" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        /*FORCE_MASTER*/ SELECT id
                              , task_no             taskNo
                              , area_no             areaNo
                              , type
                              , expect_time         expectTime
                              , state
                              , addtime
                              , updatetime
                              , dimension           dimension
                              , tenant_id           tenantId
                              , inventory_locked as inventoryLocked
                              , out_store_no     as outStoreNo
                              , external_warehouse_no as externalWarehouseNo
                         FROM stock_task
                         WHERE id = #{id}
    </select>

    <select id="selectByIds" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT id
        , task_no taskNo
        , area_no areaNo
        , type
        , expect_time expectTime
        , state
        , addtime
        , updatetime
        , dimension dimension
        , tenant_id tenantId
        , inventory_locked as inventoryLocked
        , out_store_no as outStoreNo
        , external_warehouse_no as externalWarehouseNo
        , outbound_category as outboundCategory
        FROM stock_task
        WHERE id IN
        <foreach collection="ids" item="id1" open="(" close=")" separator=",">
            #{id1}
        </foreach>
    </select>

    <select id="listByTypes" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime,dimension
        dimension,
        admin_id adminId,category,close_reason closeReason,mismatch_reason mismatchReason,out_store_no
        outStoreNo,tenant_id tenantId,external_warehouse_no externalWarehouseNo
        FROM stock_task
        WHERE type in
        <foreach collection="types" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        <if test="maxId != null">
            and id <![CDATA[<=]]> #{maxId}
        </if>
        <if test="minId != null">
            and id <![CDATA[>=]]> #{minId}
        </if>
        <if test="id != null">
            and id = #{id}
        </if>
        and addtime <![CDATA[<=]]> '2022-12-17 00:00:00'
        order by id desc
    </select>

    <select id="queryName" resultType="java.lang.String">
        select m.mname
        from orders o
                 left join merchant m on o.m_id = m.m_id
        where o.order_no = #{taskNo}
    </select>

    <select id="listByWareNoAndInventoryLocked500"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT id,task_no taskNo,area_no areaNo,type
        FROM stock_task
        WHERE state = 0
        AND area_no = #{warehouseNo,jdbcType=INTEGER}
        and type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        AND (inventory_locked is null or inventory_locked = 0)
        <if test="deliveryDateStart != null">
            AND expect_time >= #{deliveryDateStart}
        </if>
        <if test="deliveryDateEnd != null">
            AND expect_time &lt;= #{deliveryDateEnd}
        </if>
        <if test="addTimeStart != null">
            AND addtime >= #{addTimeStart}
        </if>
        limit 500
    </select>

    <select id="countPartFinishedTask" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM stock_task
        WHERE area_no = #{warehouseNo}
          AND state = 1
          AND type in (50, 51, 52, 53, 56, 57, 58, 59, 60)
    </select>

    <update id="updateInventoryLocked">
        UPDATE stock_task
        SET inventory_locked = 1
        WHERE id = #{stockTaskId,jdbcType=INTEGER}
          and (inventory_locked = 0 or inventory_locked is null)
    </update>

    <update id="updateExpectTime"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        UPDATE stock_task
        <set>
            <if test="expectTime != null">
                expect_time = #{expectTime}
            </if>
            <if test="updater != null">
                ,updater = #{updater}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateExternalWarehouseNo"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        UPDATE stock_task
        SET external_warehouse_no = #{externalWarehouseNo}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="countUnFinishTask" resultType="java.lang.Long">
        SELECT count(distinct st.id)
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.type in (51, 52, 57, 58)
        AND st.state IN (0, 1, 5)
        AND st.area_no = #{warehouseNo}
        AND sti.sku in
        <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
            #{sku1}
        </foreach>
    </select>

    <select id="selectListByTaskNo" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT
        id,
        task_no taskNo,
        area_no areaNo,
        type,
        expect_time expectTime,
        state,
        addtime,
        updatetime,
        dimension
        dimension,
        tenant_id tenantId,
        inventory_locked as inventoryLocked,
        system_source systemSource
        FROM stock_task
        WHERE task_no in
        <foreach collection="taskNoList" item="taskNo" open="(" close=")" separator=",">
            #{taskNo}
        </foreach>
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectOneByTaskNo" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT
        id,
        task_no taskNo,
        area_no areaNo,
        type,
        expect_time expectTime,
        state,
        addtime,
        updatetime,
        dimension dimension,
        tenant_id tenantId,
        inventory_locked as inventoryLocked,
        system_source as systemSource
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
        order by id desc limit 1
    </select>

    <select id="selectByWarehouseNoAndStoreNo"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT id,
        task_no taskNo,
        area_no areaNo,
        type,
        expect_time expectTime,
        state,
        addtime,
        updatetime,
        dimension dimension,
        tenant_id tenantId,
        inventory_locked as inventoryLocked,
        system_source as systemSource
        FROM stock_task
        WHERE state = 0
        <if test="warehouseNo != null">
            and area_no = #{warehouseNo}
        </if>
        <if test="storeNo != null">
            and out_store_no = #{storeNo}
        </if>
        order by id desc limit 1
    </select>

    <select id="selectExpectTask" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT id, task_no taskNo, area_no areaNo, type
        from stock_task
        where expect_time
                  between #{expectStartTime} and #{expectEndTime}
            and area_no = #{areaNo}
            and state in (0, 1)
            and type in (10, 11)
           or (expect_time between #{expectStartTime} and #{expectEndTime}
            and updateTime between #{expectStartTime} and #{expectEndTime}
            and area_no = #{areaNo}
            and state = 2
            and type in (10, 11))
        union
        SELECT st.id, st.task_no taskNo, st.area_no areaNo, st.type
        from stock_task st
                 left join stock_allocation_list sal on sal.list_no = st.task_no
        where sal.expect_out_time
                  between #{expectStartTime} and #{expectEndTime}
            and st.area_no = #{areaNo}
            and st.state in (0, 1)
            and st.type = 50
           or (sal.expect_out_time between #{expectStartTime} and #{expectEndTime}
            and st.updateTime between #{expectStartTime} and #{expectEndTime}
            and st.area_no = #{areaNo}
            and st.state = 2
            and st.type = 50)
    </select>

    <select id="countCrossTaskByType" resultType="java.lang.Long">
        select count(1)
        from stock_task
        where type = #{type} and area_no = #{warehouseNo} and expect_time = #{expectTime}
        <if test="storeNo != null">
            and out_store_no = #{storeNo}
        </if>
    </select>

    <select id="queryByTypeAndExpectTimeAndState"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO">
        SELECT st.id,
               st.task_no              taskNo,
               st.out_store_no         outStoreNo,
               st.type,
               st.expect_time          expectTime,
               st.state,
               st.addtime,
               st.updatetime,
               st.area_no              areaNo,
               st.out_type             outType,
               st.remark,
               st.dimension,
               st.process_state        processState,
               st.remark,
               st.mismatch_reason   as mismatchReason,
               st.tenant_id         as tenantId,
               st.task_type         as taskType,
               st.category,
               st.close_reason         closeReason,
               st.inventory_locked  as inventoryLocked,
               st.system_source     as systemSource,
               st.option_flag       as optionFlag,
               st.outbound_category as outboundCategory
        from stock_task st
        where st.type = #{type}
          and st.expect_time = #{expectTime}
          and st.state = #{state}
          and st.outbound_category = #{outboundCategory}
    </select>

    <select id="queryTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
        SELECT COUNT(DISTINCT st.id) AS notFinishTaskNum,
        COUNT(DISTINCT CASE
            WHEN NOW() >= DATE (st.expect_time) + INTERVAL 1 DAY THEN st.id
            ELSE NULL
            END) AS notFinishInTimeTaskNum
        FROM stock_task st
        WHERE st.addtime >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.addtime &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND st.state in
        <foreach collection="stateList" item="state" open="(" close=")" separator=",">
            #{state}
        </foreach>
        AND st.type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
    </select>

    <select id="queryTaskPanelNotFinishTask" resultType="java.lang.Long">
        SELECT DISTINCT st.id
        FROM stock_task st
        WHERE st.addtime >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.addtime &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND st.state in
        <foreach collection="stateList" item="state" open="(" close=")" separator=",">
            #{state}
        </foreach>
        AND st.type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
    </select>
    <select id="queryTaskPanelNotFinishInTimeTask" resultType="java.lang.Long">
        SELECT distinct st.id AS taskId
        FROM stock_task st
        WHERE st.addtime >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.addtime &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND st.state in
        <foreach collection="stateList" item="state" open="(" close=")" separator=",">
            #{state}
        </foreach>
        AND st.type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
        AND NOW() >= DATE(st.expect_time) + INTERVAL 1 DAY
    </select>

    <select id="queryOutTaskNeedNoticeKingdee" resultType="java.lang.Long">
        SELECT distinct st.id AS id
        FROM stock_task st
        LEFT JOIN wms_external_business_transfer_record webtr ON CAST(st.id AS CHAR) = webtr.biz_code AND st.type = webtr.order_type AND webtr.`external_app_key` = 'kingdee_app'
        WHERE st.state in (1,2)
        AND st.type in (51,58)
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
        AND webtr.biz_code IS NULL
        <if test="createTimeStart != null">
            AND st.addtime >= #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
            AND st.addtime &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="queryPurchaseOutTaskNeedNoticeKingdee" resultType="java.lang.Long">
        SELECT distinct st.id AS id
        FROM stock_task st
        LEFT JOIN wms_external_business_transfer_record webtr ON CAST(st.id AS CHAR) = webtr.biz_code AND st.type = webtr.order_type AND webtr.`external_app_key` = 'kingdee_app'
        WHERE st.state in (1,2)
        AND st.type = 56
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
        AND webtr.biz_code IS NULL
        <if test="createTimeStart != null">
            AND st.addtime >= #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
            AND st.addtime &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

</mapper>
