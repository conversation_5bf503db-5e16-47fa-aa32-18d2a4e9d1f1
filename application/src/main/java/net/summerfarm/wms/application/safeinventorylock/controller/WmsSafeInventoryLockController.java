package net.summerfarm.wms.application.safeinventorylock.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryUnlockInput;
import net.summerfarm.wms.application.safeinventorylock.input.query.WmsSafeInventoryLockQueryInput;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockCommandService;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockQueryService;
import net.summerfarm.wms.application.safeinventorylock.vo.WmsSafeInventoryLockVO;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 安全库存精细化锁定锁定
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping(value = "/wmsSafeInventoryLock")
public class WmsSafeInventoryLockController {

    @Autowired
    private WmsSafeInventoryLockCommandService wmsSafeInventoryLockCommandService;

    @Autowired
    private WmsSafeInventoryLockQueryService wmsSafeInventoryLockQueryService;

    /**
     * 安全库存锁定表列表
     * @param input 查询输入
     * @return 分页结果
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<WmsSafeInventoryLockVO>> getPage(@RequestBody WmsSafeInventoryLockQueryInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求参数为空");
        }
        input.setTenantId(LoginInfoThreadLocal.getTenantId());
        return wmsSafeInventoryLockQueryService.getPage(input);
    }

    /**
     * 锁定库存
     * @param input 锁定输入
     * @return 锁定结果
     */
    @PostMapping(value = "/command/lock")
    public CommonResult<Boolean> lock(@Valid @RequestBody WmsSafeInventoryLockInput input) {
        input.setTenantId(LoginInfoThreadLocal.getTenantId());
        return wmsSafeInventoryLockCommandService.lock(input);
    }

    /**
     * 释放锁定
     * @param input 释放锁定输入
     * @return 释放结果
     */
    @PostMapping(value = "/command/unlock")
    public CommonResult<Boolean> unlock(@Valid @RequestBody WmsSafeInventoryUnlockInput input) {
        return wmsSafeInventoryLockCommandService.unlock(input);
    }
}
