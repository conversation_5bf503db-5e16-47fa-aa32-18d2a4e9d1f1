package net.summerfarm.wms.domain.outstore.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.outstore.domainobject.QueryRecheckOutStoreTask;
import net.summerfarm.wms.domain.outstore.domainobject.RecheckOutStoreTask;

/**
 * 复核数据服务
 *
 * <AUTHOR>
 */
public interface RecheckOutStoreTaskRepository {
    /**
     * 列表查询复核数据
     * @param query q
     * @return page
     */
    PageInfo<RecheckOutStoreTask> pageQueryRecheck(QueryRecheckOutStoreTask query);
}
