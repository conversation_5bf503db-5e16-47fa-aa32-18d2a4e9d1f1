package net.summerfarm.wms.web.controller.goods;

import net.summerfarm.wms.api.h5.goods.GoodsDefectConfigCommandService;
import net.summerfarm.wms.api.h5.goods.GoodsDefectConfigQueryService;
import net.summerfarm.wms.api.h5.goods.dto.req.GoodsDefectConfigCreateCommand;
import net.summerfarm.wms.api.h5.goods.dto.req.GoodsDefectConfigQueryCommand;
import net.summerfarm.wms.api.h5.goods.dto.req.GoodsDefectConfigUpdateCommand;
import net.summerfarm.wms.api.h5.goods.dto.res.GoodsDefectConfigDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货检评价
 * @author: dongcheng
 * @date: 2023/9/1
 * 货检评价
 */
@RestController
@RequestMapping("/goods-defect-config")
public class GoodsDefectConfigController {

    @Resource
    private GoodsDefectConfigQueryService goodsDefectConfigQueryService;

    @Resource
    private GoodsDefectConfigCommandService goodsDefectConfigCommandService;

    /**
     * 货检评价-创建
     */
    @PostMapping("/create")
    public CommonResult<Boolean> saveConfig(@Valid @RequestBody GoodsDefectConfigCreateCommand defectConfig) {
        if (defectConfig.getDefectDesc() == null ||
                defectConfig.getDefectType() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"参数不能为空");
        }

        goodsDefectConfigCommandService.createDefectConfig(defectConfig);
        return CommonResult.ok(true);
    }

    /**
     * 货检评价-修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody GoodsDefectConfigUpdateCommand defectConfig) {
        if (defectConfig.getId() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"id不能为空");
        }
        goodsDefectConfigCommandService.updateDefectConfig(defectConfig);
        return CommonResult.ok(true);
    }

    /**
     * 货检评价-删除
     */
    @PostMapping("/delete")
    public CommonResult<Boolean> deleteConfig(@Valid @RequestBody GoodsDefectConfigUpdateCommand defectConfig) {
        goodsDefectConfigCommandService.deleteDefectConfig(defectConfig);
        return CommonResult.ok(true);
    }

    /**
     * 货检评价-列表
     */
    @GetMapping("/list")
    public CommonResult<List<GoodsDefectConfigDTO>> listConfig() {
        List<GoodsDefectConfigDTO> defectConfigs = goodsDefectConfigQueryService.selectConfigs();
        return CommonResult.ok(defectConfigs);
    }

    /**
     * 货检评价-列表筛选-问题归属
     */
    @PostMapping("/list/belong")
    public CommonResult<List<GoodsDefectConfigDTO>> belongConfig(GoodsDefectConfigQueryCommand queryCommand) {
        List<GoodsDefectConfigDTO> defectConfigs = goodsDefectConfigQueryService.selectConfigs();
        if (StringUtils.isEmpty(queryCommand.getDefectBelong())){
            return CommonResult.ok(defectConfigs);
        }

        defectConfigs = defectConfigs.stream()
                .filter(s -> CollectionUtils.isEmpty(s.getDefectBelongList()) ||
                        s.getDefectBelongList().contains("全部") ||
                        s.getDefectBelongList().contains(queryCommand.getDefectBelong()))
                .collect(Collectors.toList());

        return CommonResult.ok(defectConfigs);
    }
}
