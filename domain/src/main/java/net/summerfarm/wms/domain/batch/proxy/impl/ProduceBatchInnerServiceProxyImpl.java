package net.summerfarm.wms.domain.batch.proxy.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.proxy.CglibDynamicProxy;
import net.summerfarm.wms.common.proxy.model.CglibRetryModel;
import net.summerfarm.wms.common.util.WmsSpringContextUtil;
import net.summerfarm.wms.domain.batch.proxy.ProduceBatchInnerServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Slf4j
public class ProduceBatchInnerServiceProxyImpl implements ProduceBatchInnerServiceProxy {

    @Autowired
    private CglibDynamicProxy cglibDynamicProxy;

    @Override
    public Long saveProduceBatch(String sku, Integer warehouseNo, Long shelfLife,
                                 Long produceAt, String operationName, Integer operationType,
                                 String purchaseNo, Integer lotType, BigDecimal customCost,
                                 Long tenantId) {
        try {
            Object result = cglibDynamicProxy.invoke(CglibRetryModel.Builder.aCglibRetryModel()
                    .className(WmsSpringContextUtil.getBean("produceBatchInnerService").getClass())
                    .methodName("saveProduceBatchForProxy")
                    .parameterTypes(String.class, Integer.class, Long.class,
                            Long.class, String.class, Integer.class,
                            String.class, Integer.class, BigDecimal.class,
                            Long.class)
                    .parameterValue(sku, warehouseNo, shelfLife,
                            produceAt, operationName, operationType,
                            purchaseNo, lotType, customCost,
                            tenantId)
                    .build());
            return (Long) result;
        } catch (Throwable throwable) {
            log.error("ProduceBatchInnerServiceProxy saveProduceBatch Throwable", throwable);
            throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), throwable.getCause().getMessage() != null ?
                    throwable.getCause().getMessage() : "发生异常，请稍后进行重试");
        }
    }

    @Override
    public void updateProduceBatch(Integer operationType, String operationName,
                                   String purchaseNo, Long produceBatchId, Integer quantity, BigDecimal customCost,
                                   Long recordNo, Long tenantId) {
        try {
            cglibDynamicProxy.invoke(CglibRetryModel.Builder.aCglibRetryModel()
                    .className(WmsSpringContextUtil.getBean("produceBatchInnerService").getClass())
                    .methodName("updateProduceBatchForProxy")
                    .parameterTypes(Integer.class, String.class, String.class, Long.class,
                            Integer.class, BigDecimal.class, Long.class,
                            Long.class)
                    .parameterValue(operationType, operationName, purchaseNo, produceBatchId,
                            quantity, customCost, recordNo,
                            tenantId)
                    .build());
        } catch (Throwable throwable) {
            log.error("ProduceBatchInnerServiceProxy updateProduceBatch Throwable 库存发生变更", throwable);
            throw new BizException("库存发生变更，请刷新页面进行重试");
        }
    }
}
