package net.summerfarm.wms.manage.web.enums;

/**
 * @author: dongcheng
 * @date: 2023/8/4
 */
public enum SkuTypeEnum {

    SELF_SUPPORT(0,"自营"),
    SUBSTITUTE_WAREHOUSE(1,"代仓"),
    POP(2,"POP");

    private Integer id;
    private String status;

    SkuTypeEnum(Integer id, String status) {
        this.id = id;
        this.status = status;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
