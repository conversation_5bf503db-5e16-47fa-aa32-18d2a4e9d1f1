package net.summerfarm.wms.common.enums;

/**
 * <AUTHOR>
 * @description 唯一码打印类型
 * @date 2024/8/5
 */
public enum OnlyCodePrintTypeEnum {

    BARCODE(1, "69条码"),
    BATCH_CODE(2, "批次码"),
    ;

    private Integer code;
    private String desc;

    OnlyCodePrintTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
