package net.summerfarm.wms.domain.mission.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.ValueObject;

/**
 * 明细额外信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionDetailExtend implements ValueObject {
    /**
     * 任务编号
     */
    String missionNo;
    /**
     * 仓库号
     */
    Long warehouseNo;
    /**
     * sku
     */
    String sku;
    /**
     * sku额外信息json串
     */
    String extend;

    /**
     * 供应商
     */
    private Long supplierId;
    /**
     * 货品pdId
     */
    private Long pdId;
    /**
     * 货品类目id
     */
    private Integer categoryId;
    /**
     * 货品类目名称
     */
    private String categoryName;
}
