package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.param.StockTransferCreateParam;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferUpdateParam;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferCommandRepository {

    /**
     * 保存转换任务
     *
     * @param stockTransferCreateParam 转换任务保存参数
     */
    void insert(StockTransferCreateParam stockTransferCreateParam);

    /**
     * 保存转换任务
     *
     * @param stockTransferUpdateParam 转换任务修改参数
     */
    int updateStateById(StockTransferUpdateParam stockTransferUpdateParam);

}
