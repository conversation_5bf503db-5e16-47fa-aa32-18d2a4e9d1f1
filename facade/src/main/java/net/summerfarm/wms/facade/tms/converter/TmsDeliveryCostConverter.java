package net.summerfarm.wms.facade.tms.converter;

import net.summerfarm.tms.client.cost.resp.DeliveryCostResp;
import net.summerfarm.wms.facade.tms.dto.DeliveryCostFacadeResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TmsDeliveryCostConverter {
    TmsDeliveryCostConverter INSTANCE = Mappers.getMapper(TmsDeliveryCostConverter.class);

    @Mappings({
            @Mapping(source = "outBatchId", target = "costBatchId"),
            @Mapping(source = "outItemId", target = "sku"),
    })
    DeliveryCostFacadeResp convert(DeliveryCostResp deliveryCostResp);
}
