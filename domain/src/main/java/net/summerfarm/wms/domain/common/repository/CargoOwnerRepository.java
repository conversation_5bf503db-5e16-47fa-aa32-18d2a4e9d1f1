package net.summerfarm.wms.domain.common.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.common.domainobject.CargoOwner;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/17
 */
public interface CargoOwnerRepository {

    /**
     * create
     *
     * @param cargoOwner * @return
     */
    Long create(CargoOwner cargoOwner);

    /**
     * batch create
     *
     * @param cargoOwners
     * @return
     */
    Integer creates(List<CargoOwner> cargoOwners);

    /**
     * update
     *
     * @param cargoOwner
     * @return
     */
    Boolean update(CargoOwner cargoOwner);

    /**
     * findById
     *
     * @param id
     * @return
     */
    CargoOwner findById(Long id);

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    List<CargoOwner> findByIds(List<Long> ids);

    /**
     * count
     *
     * @param cargoOwner
     * @return
     */
    Long count(CargoOwner cargoOwner);

    /**
     * findOne
     *
     * @param cargoOwner
     * @return
     */
    CargoOwner findOne(CargoOwner cargoOwner);

    /**
     * list
     *
     * @param cargoOwner
     * @return
     */
    List<CargoOwner> list(CargoOwner cargoOwner);

    /**
     * page
     * @param cargoOwner
     * @param pageIndex
     * @param pageSize
     * @return
     */
    PageInfo<CargoOwner> page(CargoOwner cargoOwner, Integer pageIndex, Integer pageSize);
}
