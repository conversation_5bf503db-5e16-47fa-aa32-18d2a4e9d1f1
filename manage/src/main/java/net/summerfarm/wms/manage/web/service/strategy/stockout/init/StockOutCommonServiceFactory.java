package net.summerfarm.wms.manage.web.service.strategy.stockout.init;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.manage.web.service.strategy.stockout.StockOutCommonServiceTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/24
 */
@Slf4j
@Component
public class StockOutCommonServiceFactory {

    @Resource
    private ApplicationContext applicationContext;
    public Map<Integer, StockOutCommonServiceTemplate> stockOutCommonServiceTemplateMap = Maps.newConcurrentMap();

    public StockOutCommonServiceTemplate getService(Integer type) {
        StockOutCommonServiceTemplate stockOutCommonServiceTemplate = stockOutCommonServiceTemplateMap.get(type);
        if (Objects.isNull(stockOutCommonServiceTemplate)) {
            throw new BizException("找不到出库类型对应的处理器【" + type + "】");
        }
        return stockOutCommonServiceTemplate;
    }

    @PostConstruct
    public void init() {
        Map<String, StockOutCommonServiceTemplate> beansOfType = applicationContext.getBeansOfType(StockOutCommonServiceTemplate.class);
        log.info("正在加载StockOutCommonServiceTemplate实现类，数量：{}", beansOfType.size());
        beansOfType.forEach((key, value) -> {
            List<Integer> types = value.getTypes();
            if (types == null || types.isEmpty()) {
                log.error("StockOutCommonServiceTemplate实现类 {} 未定义types属性", value.getClass().getSimpleName());
                return;
            }
            types.forEach(type -> {
                if (stockOutCommonServiceTemplateMap.containsKey(type)) {
                    log.warn("出库类型 {} 存在重复注册，当前实现类：{}，已存在实现类：{}",
                            type,
                            value.getClass().getSimpleName(),
                            stockOutCommonServiceTemplateMap.get(type).getClass().getSimpleName());
                }
                stockOutCommonServiceTemplateMap.put(type, value);
            });
        });
    }
}
