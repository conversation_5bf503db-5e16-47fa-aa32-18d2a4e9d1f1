package net.summerfarm.wms.manage.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.api.h5.inventory.dto.enums.StorageLocationEnum;
import net.summerfarm.wms.api.inner.stocktransfer.TransferTaskHandler;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.batch.domainobject.CostBatch;
import net.summerfarm.wms.domain.batch.entity.BatchRelationOriginEntity;
import net.summerfarm.wms.domain.batch.repository.BatchRelationOriginQueryRepository;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import net.summerfarm.wms.domain.stockTransfer.*;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpDetailEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpEntity;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferCreateParam;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferItemCreateParam;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferItemOpDetailParam;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferUpdateParam;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsPropertyDTO;
import net.summerfarm.wms.inventory.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.inventory.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceDetailReqDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceReqDTO;
import net.summerfarm.wms.manage.common.exceptions.ErrorCode;
import net.summerfarm.wms.manage.dao.stockTransfer.StockTransferItemOpDAOV1;
import net.summerfarm.wms.manage.model.domain.*;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferCreateBO;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferFinishBO;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferItemOpBO;
import net.summerfarm.wms.manage.model.dto.SkuBaseInfoDTO;
import net.summerfarm.wms.manage.model.param.BatchProveParam;
import net.summerfarm.wms.manage.model.req.CabinetBatchInventoryAddDetailFacadeReq;
import net.summerfarm.wms.manage.model.req.CabinetBatchInventoryAddFacadeReq;
import net.summerfarm.wms.manage.model.resp.ExportDetailRes;
import net.summerfarm.wms.manage.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.wms.manage.web.constants.RedisKeys;
import net.summerfarm.wms.manage.web.constants.TransferExportConstant;
import net.summerfarm.wms.manage.web.convert.StockTransferConverterV1;
import net.summerfarm.wms.manage.web.enums.*;
import net.summerfarm.wms.manage.web.enums.impl.OtherStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.enums.impl.StoreRecordType;
import net.summerfarm.wms.manage.web.enums.stocktransfer.SkuBatchType;
import net.summerfarm.wms.manage.web.facade.CabinetBatchInventoryFacade;
import net.summerfarm.wms.manage.web.mapper.executor.ExecutorFactory;
import net.summerfarm.wms.manage.web.mapper.manage.*;
import net.summerfarm.wms.manage.web.service.*;
import net.summerfarm.wms.manage.web.utils.CheckHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
@Service
@Slf4j
public class StockTransferBizServiceImpl implements StockTransferBizService {

    @Resource
    private StockTransferQueryRepository stockTransferQueryRepository;
    @Resource
    private StockTransferCommandRepository stockTransferCommandRepository;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private StockTransferItemQueryRepository stockTransferItemQueryRepository;
    @Resource
    private StockTransferItemCommandRepository stockTransferItemCommandRepository;
    @Resource
    private StockTransferItemOpDAOV1 stockTransferItemOpDAOV1;
    @Resource
    private StockTransferItemOpQueryRepository stockTransferItemOpQueryRepository;
    @Resource
    private StockTransferItemOpDetailQueryRepository stockTransferItemOpDetailQueryRepository;
    @Resource
    private StockTransferItemOpDetailCommandRepository stockTransferItemOpDetailCommandRepository;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private SkuBatchCodeMapper skuBatchCodeMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SkuBatchCodeService skuBatchCodeService;
    @Resource
    public StringRedisTemplate stringRedisTemplate;
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private PriceAdjustmentTriggerMapper priceAdjustmentTriggerMapper;
    @Resource
    private InterestRateRecordService interestRateRecordService;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private StockTakingService stockTakingService;
    @Resource
    private SkuShareBizService skuShareBizService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private AreaStoreServiceV1 areaStoreServiceV1;
    @Resource
    private BaseService baseService;
    @Resource
    private CabinetBatchInventoryFacade cabinetBatchInventoryFacade;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private QuantityChangeRecordServiceV1 quantityChangeRecordServiceV1;
    @Resource
    private WarehouseBatchProveRecordMapper warehouseBatchProveRecordMapper;

    @Autowired
    private TransferTaskHandler transferTaskHandler;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private CostBatchRepository costBatchRepository;
    @Resource
    private BatchRelationOriginQueryRepository batchRelationOriginQueryRepository;

    @Override
    public Workbook excelTransferPool(Long warehouseNo, List<Long> stockTransferIds) {
        if (CollectionUtils.isEmpty(stockTransferIds)) {
            return new HSSFWorkbook();
        }
        List<StockTransferEntity> stockTransferDos = stockTransferQueryRepository.selectByIdAndWarehouseNo(stockTransferIds, warehouseNo, null);
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        StockTransferEntity transferDo = StockTransferEntity.builder().warehouseNo(warehouseNo).build();
        Integer index = excelTitle(transferDo, sheet);
        for (StockTransferEntity transfer : stockTransferDos) {
            index = excelContact(sheet, index, transfer.getId());
        }
        return workbook;
    }

    /**
     * 拼装表头信息
     */
    private Integer excelTitle(StockTransferEntity stockTransferDO, Sheet sheet) {

        int rowIndex = 0;
        Row firstRow = sheet.createRow(rowIndex++);
        firstRow.createCell(0).setCellValue(TransferExportConstant.INVENTORY_TRANSFER);
        String id = Objects.isNull(stockTransferDO.getId()) ? "" : stockTransferDO.getId().toString();
        firstRow.createCell(1).setCellValue(id);

        Row secondRow = sheet.createRow(rowIndex++);
        secondRow.createCell(0).setCellValue(TransferExportConstant.WAREHOUSE);

        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(stockTransferDO.getWarehouseNo().intValue());
        String warehouseName = center.getWarehouseName();
        secondRow.createCell(1).setCellValue(warehouseName);

        Row thirdRow = sheet.createRow(rowIndex++);
        thirdRow.createCell(0).setCellValue(TransferExportConstant.CREATED_AT);
        String time = DateUtil.formatDate(stockTransferDO.getCreatedAt());
        thirdRow.createCell(1).setCellValue(StringUtils.isEmpty(time) ? "" : time);

        Row fourthRow = sheet.createRow(rowIndex++);
        fourthRow.createCell(0).setCellValue(TransferExportConstant.CREATOR);
        fourthRow.createCell(1).setCellValue(StringUtils.isEmpty(stockTransferDO.getOperator()) ?
                TransferExportConstant.SYSTEM : getRealNameById(Integer.valueOf(stockTransferDO.getOperator())));

        Row fifthRow = sheet.createRow(rowIndex++);
        fifthRow.createCell(0).setCellValue(TransferExportConstant.STATE);
        String stateDesc = StockTransferStateEnum.convert(stockTransferDO.getState());
        fifthRow.createCell(1).setCellValue(stateDesc);

        rowIndex++; //空置一行

        Row title = sheet.createRow(rowIndex++);
        String[] titleNames = {TransferExportConstant.CATEGORY_TYPE,
                TransferExportConstant.CATEGORY_NAME,
                TransferExportConstant.IN_SKU,
                TransferExportConstant.GOODS_NAME,
                TransferExportConstant.SPEC,
                TransferExportConstant.GOODS_BELONG,
                TransferExportConstant.STORE_AREA,
                TransferExportConstant.PACKAGING,
                TransferExportConstant.ORIGIN,
                TransferExportConstant.PRE_IN_NUM,
                TransferExportConstant.AC_IN_NUM,
                TransferExportConstant.OUT_SKU,
                TransferExportConstant.OUT_GOODS_NAME,
                TransferExportConstant.SPEC,
                TransferExportConstant.BATCH,
                TransferExportConstant.OUT_CABINET_NO,
                TransferExportConstant.IN_CABINET_NO,
                TransferExportConstant.PRODUCE_AT,
                TransferExportConstant.SHELF_LIFE,
                TransferExportConstant.OUT_NUM,
                TransferExportConstant.OUT_TOTAL,
                TransferExportConstant.TRANSFER_RATIO,
                TransferExportConstant.IN_TOTAL,
                TransferExportConstant.EXTERNAL_TRANSFER_IN_NUM,
                TransferExportConstant.OPERATOR,
                TransferExportConstant.OPERATE_AT};
        for (int i = 0; i < titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }
        return rowIndex;
    }

    private int excelContact(Sheet sheet, Integer rowIndex, Long stockTransferId) {
        ExportDetailRes stockTransferDetail = getExportDetail(stockTransferId);
        List<StockTransferItemRes> items = stockTransferDetail.getStockTransferItems();
        int index = rowIndex;
        for (StockTransferItemRes item : items) {
            int startMerge = index;
            int endMerge = index;
            List<StockTransferItemOpRes> stockTransferItemOps = item.getStockTransferItemOps();
            for (StockTransferItemOpRes opRes : stockTransferItemOps) {
                int endMerge1 = index;
                int startMerge1 = index;
                List<TransferOutInfo> transferOutInfos = opRes.getTransferOutInfos();
                for (TransferOutInfo outInfo : transferOutInfos) {
                    Row row = sheet.createRow(index++);
                    row.createCell(0).setCellValue(CheckHelper.stringIfNull(item.getGoodsCategory()));
                    row.createCell(1).setCellValue(CheckHelper.stringIfNull(item.getCategoryName()));
                    row.createCell(2).setCellValue(CheckHelper.stringIfNull(item.getTransferInGoodsSku()));
                    row.createCell(3).setCellValue(CheckHelper.stringIfNull(item.getTransferInGoodsName()));
                    row.createCell(4).setCellValue(CheckHelper.stringIfNull(item.getTransferInGoodsSpec()));
                    row.createCell(5).setCellValue(CheckHelper.stringIfNull(item.getPdAttribute()));
                    row.createCell(6).setCellValue(CheckHelper.stringIfNull(item.getStorageArea()));
                    row.createCell(7).setCellValue(CheckHelper.stringIfNull(item.getPackaging()));
                    row.createCell(8).setCellValue(CheckHelper.stringIfNull(Objects.equals(NumberUtils.INTEGER_ZERO, item.getIsDomestic()) ? "进口" : "国产"));
                    row.createCell(9).setCellValue(CheckHelper.stringIfNull(item.getPreTransferInNum()));
                    row.createCell(10).setCellValue(CheckHelper.stringIfNull(item.getActualTransferInNum()));
                    row.createCell(11).setCellValue(CheckHelper.stringIfNull(outInfo.getTransferOutSku()));
                    row.createCell(12).setCellValue(CheckHelper.stringIfNull(outInfo.getTransferOutGoodsName()));
                    row.createCell(13).setCellValue(CheckHelper.stringIfNull(outInfo.getSpecification()));
                    row.createCell(14).setCellValue(CheckHelper.stringIfNull(outInfo.getTransferOutBatch()));
                    row.createCell(15).setCellValue(CheckHelper.stringIfNull(outInfo.getTransferOutCabinetNo()));
                    row.createCell(16).setCellValue(CheckHelper.stringIfNull(opRes.getTransferInCabinet()));
                    row.createCell(17).setCellValue(CheckHelper.stringIfNull(DateUtil.formatYmd(outInfo.getProduceTime())));
                    row.createCell(18).setCellValue(CheckHelper.stringIfNull(DateUtil.formatYmd(outInfo.getShelfLife())));
                    row.createCell(19).setCellValue(CheckHelper.stringIfNull(outInfo.getTransferOutNum()));
                    row.createCell(20).setCellValue(CheckHelper.stringIfNull(opRes.getTransferOutTotal()));
                    row.createCell(21).setCellValue(CheckHelper.stringIfNull(opRes.getTransferRatio()));
                    row.createCell(22).setCellValue(CheckHelper.stringIfNull(opRes.getTransferInTotal()));
                    row.createCell(23).setCellValue(CheckHelper.stringIfNull(outInfo.getExternalTransferInNum()));
                    row.createCell(24).setCellValue(CheckHelper.stringIfNull(opRes.getOperator()));
                    row.createCell(25).setCellValue(CheckHelper.stringIfNull(DateUtil.formatDate(opRes.getOperateTime())));

                    endMerge1 = index - 1;
                }
                // 合并单元格
                if (endMerge1 - startMerge1 > 0) {
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 22, 22));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 23, 23));
//                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 10, 10));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 11, 11));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 12, 12));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 15, 15));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 19, 19));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 20, 20));
                    sheet.addMergedRegion(new CellRangeAddress(startMerge1, endMerge1, 21, 21));
                }
                endMerge = endMerge1;
            }
            // 合并单元格
            if (endMerge - startMerge > 0) {
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 1, 1));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 2, 2));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 4, 4));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 5, 5));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 6, 6));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 7, 7));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 8, 8));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 9, 9));
                sheet.addMergedRegion(new CellRangeAddress(startMerge, endMerge, 10, 10));
            }
        }
        index++;
        return index;
    }

    public ExportDetailRes getExportDetail(Long stockTransferId) {
        log.info("开始查询详情信息:{}", stockTransferId);
        // 查询主任务信息
        StockTransferEntity stockTransferDO = stockTransferQueryRepository.selectById(stockTransferId);
        CheckHelper.isNull(stockTransferDO, ErrorCode.STOCK_TRANSFER_NOT_EXIST);
        Long warehouseNo = stockTransferDO.getWarehouseNo();
        ExportDetailRes stockTransferDetailRes = StockTransferConverterV1.INSTANCE.stockTransferDoToExDetail(stockTransferDO);

        // 查询实例
        List<StockTransferItemEntity> stockTransferItemDos = stockTransferItemQueryRepository.listByStockTransferId(stockTransferId);
        if (CollectionUtils.isEmpty(stockTransferItemDos)) {
            return stockTransferDetailRes;
        }
        List<Long> itemIds = CheckHelper.toListIfNotNull(stockTransferItemDos, StockTransferItemEntity::getId);

        // 查询操作
        List<StockTransferItemOpDO> opDos = stockTransferItemOpDAOV1.listByItemId(itemIds);
        Map<Long/* itemId*/, List<StockTransferItemOpDO>> itemIdMap = CheckHelper.groupByIfNotNull(opDos, StockTransferItemOpDO::getStockTransferItemId);
        List<Long> opIds = CheckHelper.toListIfNotNull(opDos, StockTransferItemOpDO::getId);

        // 查询操作详情
        List<StockTransferItemOpDetailEntity> opDetailDos = CollectionUtils.isEmpty(opIds) ? Lists.newArrayList() :
                stockTransferItemOpDetailQueryRepository.listByOpIdList(opIds);
        Map<Long/*opId*/, List<StockTransferItemOpDetailEntity>> opIdMap = CheckHelper.groupByIfNotNull(opDetailDos, StockTransferItemOpDetailEntity::getStockTransferItemOpId);

        // sku去inventory找pdId
        // 通过pdId查询products商品信息
        Set<String> skus = CheckHelper.toSetIfNotNull(opDos, StockTransferItemOpDO::getTransferOutSku);
        List<String> inSkus = CheckHelper.toListIfNotNull(stockTransferItemDos, StockTransferItemEntity::getTransferInSku);
        skus.addAll(inSkus);
        List<SkuBaseInfoDTO> skuBaseInfos = inventoryMapper.selectSkuBaseInfosBySku(Lists.newArrayList(skus));
        Map<String/*sku*/, SkuBaseInfoDTO> skuMap = CheckHelper.toMapIfNotNull(skuBaseInfos, SkuBaseInfoDTO::getSku, Function.identity());

        // category 查询类目
        Map<Integer, Category> categoryIdMap = getCategoryMap(skuBaseInfos);

        // 查询打印次数
        List<String> inBatchs = CheckHelper.toListIfNotNull(opDetailDos, StockTransferItemOpDetailEntity::getTransferInBatch);
        List<SkuBatchCode> skuBatchCodes = CollectionUtils.isEmpty(inBatchs) ? Lists.newArrayList() :
                skuBatchCodeMapper.listBySkuAndBatchs(inSkus, inBatchs);
        Map<String, Integer> batchPrintMap = CheckHelper.toMapIfNotNull(skuBatchCodes, SkuBatchCode::getPurchaseNo, SkuBatchCode::getPrintNumber);

        // 组合参数
        List<StockTransferItemRes> itemRes = stockTransferItemDos.stream().map(stockTransferItemDO -> {
            List<StockTransferItemOpDO> opDOList = itemIdMap.getOrDefault(stockTransferItemDO.getId(), Lists.newArrayList());
            List<StockTransferItemOpRes> opRes = buildOpRes(opIdMap, skuMap, opDOList, batchPrintMap, warehouseNo);
            SkuBaseInfoDTO skuInfo = skuMap.getOrDefault(stockTransferItemDO.getTransferInSku(), new SkuBaseInfoDTO());
            Category category = categoryIdMap.getOrDefault(skuInfo.getCategoryId(), new Category());
            return StockTransferItemRes.builder()
                    .actualTransferInNum(opRes.stream().mapToLong(StockTransferItemOpRes::getTransferInTotal).sum())
                    .stockTransferItemId(stockTransferItemDO.getId())
                    .stockTransferItemOps(opRes)
                    .pdAttribute(Objects.equals(skuInfo.getPdAttribute(), 0) ? "自营" : "代仓")
                    .preTransferInNum(stockTransferItemDO.getPreTransferInNum())
                    .transferInGoodsId(skuInfo.getPdId())
                    .packaging(skuInfo.getPackaging())
                    .storageArea(StorageLocationEnum.convert(skuInfo.getStorageArea()))
                    .transferInGoodsName(skuInfo.getPdName())
                    .transferInGoodsSku(stockTransferItemDO.getTransferInSku())
                    .transferInGoodsSpec(skuInfo.getWeight())
                    .goodsCategory(Objects.equals(category.getType(), 4) ? "鲜果" : "非鲜果")
                    .categoryName(category.getCategory())
                    .isDomestic(skuInfo.getIsDomestic())
                    .build();
        }).collect(Collectors.toList());
        stockTransferDetailRes.setStockTransferItems(itemRes);
        log.info("详情为:{}", stockTransferDetailRes);
        return stockTransferDetailRes;
    }

    public String getRealNameById(Integer adminId) {
        return adminMapper.selectByPrimaryKey(adminId).getRealname();
    }

    private Map<Integer, Category> getCategoryMap(List<SkuBaseInfoDTO> skuBaseInfos) {
        List<Integer> categoryIds = CheckHelper.toListIfNotNull(skuBaseInfos, SkuBaseInfoDTO::getCategoryId);
        List<Category> categories = CollectionUtils.isEmpty(categoryIds) ? Lists.newArrayList() :
                categoryMapper.selectTypeByIds(categoryIds);
        List<Integer> parentIds = categories.stream().map(Category::getParentId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Category> parentCategories = CollectionUtils.isEmpty(categoryIds) ? Lists.newArrayList() :
                categoryMapper.selectTypeByIds(parentIds);
        Map<Integer, Category> parentCategoryIdMap = CheckHelper.toMapIfNotNull(parentCategories, Category::getId, Function.identity());

        List<Integer> grandParentIds = parentCategories.stream().map(Category::getParentId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Category> grandParentCategories = CollectionUtils.isEmpty(grandParentIds) ? Lists.newArrayList() :
                categoryMapper.selectTypeByIds(grandParentIds);
        Map<Integer, Category> grandParentCategoryIdMap = CheckHelper.toMapIfNotNull(grandParentCategories, Category::getId, Function.identity());
        categories.forEach(category -> {
            StringBuilder stringBuilder = new StringBuilder();
            Category parentCategory = parentCategoryIdMap.get(category.getParentId());
            if (Objects.nonNull(parentCategory)) {
                Category grandParentCategory = grandParentCategoryIdMap.get(parentCategory.getParentId());
                if (Objects.nonNull(grandParentCategory)) {
                    stringBuilder.append(grandParentCategory.getCategory());
                    stringBuilder.append("-");
                }
                stringBuilder.append(parentCategory.getCategory());
                stringBuilder.append("-");
            }
            stringBuilder.append(category.getCategory());
            category.setCategory(stringBuilder.toString());
        });
        return CheckHelper.toMapIfNotNull(categories, Category::getId, Function.identity());
    }

    private List<StockTransferItemOpRes> buildOpRes(Map<Long, List<StockTransferItemOpDetailEntity>> opIdMap,
                                                    Map<String, SkuBaseInfoDTO> skuMap,
                                                    List<StockTransferItemOpDO> opDOList,
                                                    Map<String, Integer> batchPrintMap,
                                                    Long warehouseNo) {
        List<Integer> adminIds = opDOList.stream()
                .map(StockTransferItemOpDO::getOperator)
                .filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
                .filter(item -> !org.apache.commons.lang3.StringUtils.equals(item, TransferExportConstant.NULL))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        Map<Integer, String> adminMap = getAdminMap(adminIds);
        return opDOList.stream().map(opDo -> {
            List<StockTransferItemOpDetailEntity> allOutInfos = opIdMap.getOrDefault(opDo.getId(), Lists.newArrayList());
            String inBatch = allOutInfos.stream().map(StockTransferItemOpDetailEntity::getTransferInBatch).distinct().findFirst().orElse("");
            long outTotal = allOutInfos.stream().mapToLong(StockTransferItemOpDetailEntity::getTransferOutNum).sum();
            List<TransferOutInfo> transferOutInfos = buildOutInfos(skuMap, opDo, allOutInfos, warehouseNo);
            return StockTransferItemOpRes.builder()
                    .transferInTotal(calculate(outTotal, opDo.getTransferRatio()))
                    .transferOutInfos(transferOutInfos)
                    .transferOutTotal(outTotal)
                    .produceTime(opDo.getProduceDate())
                    .shelfLife(opDo.getShelfLife())
                    .transferInBatch(inBatch)
                    .transferInCabinet(CollectionUtils.isEmpty(allOutInfos) ? null : allOutInfos.get(NumberUtils.INTEGER_ZERO).getTransferInCabinet())
                    .transferRatio(opDo.getTransferRatio())
                    .operateTime(opDo.getCreatedAt())
                    .operator(adminMap.getOrDefault(CheckHelper.stringIfEmpty(opDo.getOperator()), TransferExportConstant.SYSTEM))
                    .printNum(batchPrintMap.getOrDefault(inBatch, 0))
                    .build();
        }).collect(Collectors.toList());
    }

    private List<TransferOutInfo> buildOutInfos(Map<String/*sku*/, SkuBaseInfoDTO> skuMap,
                                                StockTransferItemOpDO opDo,
                                                List<StockTransferItemOpDetailEntity> allOutInfos,
                                                Long warehouseNo) {
        SkuBaseInfoDTO skuInfo = skuMap.getOrDefault(opDo.getTransferOutSku(), new SkuBaseInfoDTO());
        return allOutInfos.stream().map(opDetail -> TransferOutInfo.builder()
                .transferOutBatch(opDetail.getTransferOutBatch())
                .transferOutCabinetNo(opDetail.getTransferOutCabinet())
                .shelfLife(opDetail.getShelfLife())
                .produceTime(opDetail.getProduceAt())
                .transferOutNum(opDetail.getTransferOutNum())
                .transferOutGoodsName(skuInfo.getPdName())
                .packaging(skuInfo.getPackaging())
                .specification(skuInfo.getWeight())
                .transferOutSku(opDo.getTransferOutSku())
                .storageArea(StorageLocationEnum.convert(skuInfo.getStorageArea()))
                .externalTransferInNum(opDetail.getExternalTransferInNum())
                .build()).collect(Collectors.toList());
    }

    private String getGroupKey(String sku, String cabinetCode, Long produceTime, Long shelfLife) {
        return sku + "_" + cabinetCode + "_" + produceTime + "_" + shelfLife;
    }

    private Long calculate(Long transferOutNum, String transferRatio) {
        String[] split = transferRatio.split(":");
        Double num = transferOutNum * Double.parseDouble(split[1]) / Double.parseDouble(split[0]);
        return num.longValue();
    }

    public Map<Integer/*adminId*/, String/*userName*/> getAdminMap(List<Integer> adminIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(adminIds)) {
            return Maps.newHashMap();
        }
        List<Admin> admins = adminMapper.listNameByAdminId(adminIds);
        return admins.stream().collect(Collectors.toMap(Admin::getAdminId, Admin::getRealname));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransferTask(StockTransferCreateBO stockTransferCreateBO) {
        if (CollectionUtils.isEmpty(stockTransferCreateBO.getTransferInInfos())) {
            return 0L;
        }
        // 存储转换任务
        StockTransferCreateParam stockTransferDO = StockTransferConverterV1.INSTANCE.stockTransferCreateBoToParam(stockTransferCreateBO);
        stockTransferDO.setState(StockTransferStateEnum.INIT.getCode());
        stockTransferCommandRepository.insert(stockTransferDO);

        // 存储转换任务实例信息
        List<StockTransferItemCreateParam> items = stockTransferCreateBO.getTransferInInfos().stream()
                .map(StockTransferConverterV1.INSTANCE::convertStockTransferItemParam)
                .peek(item -> item.setStockTransferId(stockTransferDO.getId()))
                .collect(Collectors.toList());
        stockTransferItemCommandRepository.batchInsert(items);
        return stockTransferDO.getId();
    }

    @Override
    public Boolean finishStockTransferTask(StockTransferFinishBO stockTransferFinishBO) {
        Long stockTransferId = stockTransferFinishBO.getStockTransferId();
        StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
        if (stockTransferEntity == null){
            throw new net.xianmu.common.exception.BizException("未查询到转换任务，完成转换任务失败");
        }
        StockTransferUpdateParam stockTransferDO = StockTransferUpdateParam.builder()
                .id(stockTransferId)
                .state(StockTransferStateEnum.FINISH.getCode())
                .updatedAt(new Date())
                .build();
        int update = stockTransferCommandRepository.updateStateById(stockTransferDO);
        // 更新成功 且 手动创建的转换任务才下发三方 （自动创建的转换任务由定时任务下发）
        if (1 == update && StockTransferTaskSourceEnum.HAND.getCode().equals(stockTransferEntity.getTaskSource())){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit(){
                    transferTaskHandler.finishPostProcessor(stockTransferId);
                }
            });
        }
        return update > 0;
    }

    // fixme 后续任务需要状态化，便于拆分
    @Override
    @SuppressWarnings("unchecked")
    public Boolean operateStockTransferItem(StockTransferItemOpBO stockTransferItemOpBO) {
        log.info("开始操作实例进行转换:{}", stockTransferItemOpBO);
        // service层加任务id锁
        Long warehouseNo = check(stockTransferItemOpBO);
        TransferOpEnum type = TransferOpEnum.convert(stockTransferItemOpBO.getType());

        // 事务处理，返回操作id
        List<Long> opIds = (List<Long>) transactionTemplate.execute((TransactionCallback<Object>) status -> {
            List<Long> itemOpIds;
            try {
                // 保存转换任务详情
                itemOpIds = saveTransferItemOp(stockTransferItemOpBO);

                // 转换库存
                transferInventory(stockTransferItemOpBO, warehouseNo, type, itemOpIds);

                // 更新任务状态
                updateTransferState(stockTransferItemOpBO.getStockTransferItemId());
            } catch (Exception ex) {
                log.error("创建异常:{}", JSON.toJSONString(stockTransferItemOpBO), ex);
                status.setRollbackOnly();
                if (ex instanceof BizException || ex instanceof DefaultServiceException || ex instanceof net.xianmu.common.exception.BizException) {
                    ValueOperations<String, String> stringStringValueOperations = stringRedisTemplate.opsForValue();
                    stringStringValueOperations.set(RedisKeys.buildTransferExKey(), ex.getMessage(), 2000L, TimeUnit.MILLISECONDS);
                }
                itemOpIds = null;
            }
            return itemOpIds;
        });

        // 异常返回
        if (CollectionUtils.isEmpty(opIds)) {
            String exMessage = stringRedisTemplate.opsForValue().get(RedisKeys.buildTransferExKey());
            throw new BizException(ErrorCode.STOCK_TRANSFER_CREATE_FAIL.getCode(),
                    StringUtils.isEmpty(exMessage) ? ErrorCode.STOCK_TRANSFER_CREATE_FAIL.getMessage() : exMessage);
        }

        // 异步掉调价、告警、货位；货位后续拿掉
        ExecutorFactory.stockTransferExecutor.execute(() -> {
            // 预警通知
            warningNotice(warehouseNo, stockTransferItemOpBO.getStockTransferItemId(), stockTransferItemOpBO.getTransferOutSku());
            // 自动调价
            autoRevisePrice(stockTransferItemOpBO, warehouseNo, opIds, type);
        });

        return true;
    }

    /**
     * 预警通知
     * 老代码迁移不动
     *
     * @param warehouseNo         库存仓
     * @param stockTransferItemId 转换实例id
     * @param outSku              转出sku
     */
    private void warningNotice(Long warehouseNo, Long stockTransferItemId, String outSku) {
        log.info("warningNotice warehouseNo:{},outSku:{},stockTransferItemId:{}", warehouseNo, outSku, stockTransferItemId);
        StockTransferItemEntity stockTransferItemDO = stockTransferItemQueryRepository.selectById(stockTransferItemId);
        purchasesConfigService.msgArrival(warehouseNo.intValue(), stockTransferItemDO.getTransferInSku());
        purchasesConfigService.msgArrival(warehouseNo.intValue(), outSku);

        List<AreaStore> areaStores = new ArrayList<>();
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(stockTransferItemDO.getTransferInSku());
        areaStore.setAreaNo(warehouseNo.intValue());
        areaStores.add(areaStore);
        msgAdminService.sendOnSaleMsg(areaStores);
    }

    private void autoRevisePrice(StockTransferItemOpBO opBO, Long warehouseNo, List<Long> itemOpIds, TransferOpEnum type) {
        log.info("生成自动调价:{}", itemOpIds);
        StockTransferItemEntity itemDO = stockTransferItemQueryRepository.selectById(opBO.getStockTransferItemId());
        TransferOutInfo maxBatch = opBO.getTransferOutInfos().stream().max(Comparator.comparing(TransferOutInfo::getTransferOutBatch)).orElse(null);
        if (Objects.isNull(maxBatch)) {
            return;
        }
        long sum = opBO.getTransferOutInfos().stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
        maxBatch.setTransferOutNum(sum);
        switch (type) {
            case ONE_WAY:
                List<StockTransferItemOpDetailEntity> opDetailDos = stockTransferItemOpDetailQueryRepository.listByOpIdList(itemOpIds);
                Map<String, Long> opIdMap = opDetailDos.stream().collect(Collectors.toMap(
                        StockTransferItemOpDetailEntity::getTransferOutBatch, StockTransferItemOpDetailEntity::getStockTransferItemOpId, (o1, o2) -> o1));
                opBO.getTransferOutInfos().forEach(outInfo -> revisePriceByOneWay(opBO, warehouseNo,
                        opIdMap.getOrDefault(outInfo.getTransferOutBatch(), 0L), itemDO, outInfo));
                break;
            case MIX:
                revisePriceByMix(opBO, warehouseNo, itemOpIds.get(NumberUtils.INTEGER_ZERO), itemDO);
                break;
            default:
                break;
        }
    }

    private void revisePriceByOneWay(StockTransferItemOpBO opBO, Long warehouseNo, Long itemOpId, StockTransferItemEntity itemDO, TransferOutInfo outInfo) {
        // 查询新批次记录
        StockTransferItemOpDetailEntity opDetailDO = stockTransferItemOpDetailQueryRepository.selectByBatch(itemOpId, outInfo.getTransferOutBatch());
        StoreRecord inRecord = storeRecordMapper.selectByBatchAndSku(itemDO.getTransferInSku(),
                warehouseNo.intValue(), opDetailDO.getTransferInBatch());

        // 查询老批次记录
        StoreRecord storeRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, outInfo);

        //计算市场价
        PriceAdjustmentTrigger trigger = new PriceAdjustmentTrigger();
        trigger.setCostPrice(inRecord.getCost());
        trigger.setQuantity(inRecord.getQuantity());
        trigger.setValid(1);
        trigger.setBusinessId(SnowflakeUtil.nextId());
        PurchasesPlan pp = purchasesPlanMapper.selectByNoAndSku(storeRecord.getBatch(), opBO.getTransferOutSku(), storeRecord.getQualityDate());
        if (pp != null && pp.getMarketPrice() != null) {
            //转入市场价 = 转出市场价*转出数量 / 转入数量
            Long transferInNum = calculate(outInfo.getTransferOutNum(), opBO.getTransferRatio());
            BigDecimal marketPrice = pp.getMarketPrice().multiply(BigDecimal.valueOf(outInfo.getTransferOutNum()))
                    .divide(BigDecimal.valueOf(transferInNum), 2, BigDecimal.ROUND_HALF_EVEN);
            trigger.setMarketPrice(marketPrice);
        }
        priceAdjustmentTriggerMapper.insertSelective(trigger);

        //触发自动调价：采购价自动更新，售价进入审核
        log.info("调价的sku:{}", opBO.getTransferInSku());
        interestRateRecordService.autoChangePrice(warehouseNo.intValue(), opBO.getTransferInSku(), trigger.getCostPrice(),
                trigger.getMarketPrice(), trigger.getBusinessId());
    }

    private StoreRecord queryRecord(String transferOutSku, Long warehouseNo, TransferOutInfo transferOutInfo) {
        return storeRecordMapper.selectOne(StoreRecord.builder()
                .batch(transferOutInfo.getTransferOutBatch())
                .sku(transferOutSku)
                .productionDate(DateUtil.toLocalDate(transferOutInfo.getProduceTime()))
                .qualityDate(DateUtil.toLocalDate(transferOutInfo.getShelfLife()))
                .areaNo(warehouseNo.intValue())
                .build());
    }

    private void revisePriceByMix(StockTransferItemOpBO opBO, Long warehouseNo, Long itemOpId, StockTransferItemEntity itemDO) {
        List<TransferOutInfo> transferOutInfos = opBO.getTransferOutInfos();
        if (CollectionUtils.isEmpty(transferOutInfos)) {
            return;
        }
        // 查询新批次记录
        TransferOutInfo oneInfo = transferOutInfos.stream().findFirst().orElse(TransferOutInfo.builder().build());
        StockTransferItemOpDetailEntity opDetailDO = stockTransferItemOpDetailQueryRepository.selectByBatch(itemOpId, oneInfo.getTransferOutBatch());
        StoreRecord inRecord = storeRecordMapper.selectByBatchAndSku(itemDO.getTransferInSku(),
                warehouseNo.intValue(), opDetailDO.getTransferInBatch());

        // 查询老批次记录的市场价总和,计算新批次市场价
        double outMarketPrice = transferOutInfos.stream().mapToDouble(outInfo -> {
            StoreRecord storeRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, outInfo);
            PurchasesPlan pp = purchasesPlanMapper.selectByNoAndSku(storeRecord.getBatch(), opBO.getTransferOutSku(), storeRecord.getQualityDate());
            if (pp != null && pp.getMarketPrice() != null) {
                return pp.getMarketPrice().doubleValue() * outInfo.getTransferOutNum();
            }
            return 0d;
        }).sum();
        long outSum = transferOutInfos.stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
        Long inSum = calculate(outSum, opBO.getTransferRatio());
        BigDecimal inMarketPrice = BigDecimal.valueOf(outMarketPrice).divide(BigDecimal.valueOf(inSum), 2, BigDecimal.ROUND_HALF_EVEN);

        //计算市场价
        PriceAdjustmentTrigger trigger = new PriceAdjustmentTrigger();
        trigger.setCostPrice(inRecord.getCost());
        trigger.setQuantity(inRecord.getQuantity());
        trigger.setValid(1);
        trigger.setBusinessId(SnowflakeUtil.nextId());
        trigger.setMarketPrice(inMarketPrice);
        priceAdjustmentTriggerMapper.insertSelective(trigger);

        //触发自动调价：采购价自动更新，售价进入审核
        log.info("调价的sku:{}", opBO.getTransferInSku());
        interestRateRecordService.autoChangePrice(warehouseNo.intValue(), opBO.getTransferInSku(), trigger.getCostPrice(),
                trigger.getMarketPrice(), trigger.getBusinessId());
    }

    private void updateTransferState(Long itemId) {
        StockTransferItemEntity itemDO = stockTransferItemQueryRepository.selectById(itemId);
        stockTransferCommandRepository.updateStateById(StockTransferUpdateParam.builder()
                .id(itemDO.getStockTransferId())
                .state(StockTransferStateEnum.PART_TRANSFER.getCode())
                .build());
    }

    private Long check(StockTransferItemOpBO stockTransferItemOpBO) {
        CheckHelper.isNull(stockTransferItemOpBO, ErrorCode.PARAM_ERROR);
        Long stockTransferItemId = stockTransferItemOpBO.getStockTransferItemId();
        StockTransferItemEntity stockTransferItemDO = stockTransferItemQueryRepository.selectById(stockTransferItemId);
        CheckHelper.isNull(stockTransferItemDO, ErrorCode.STOCK_TRANSFER_NOT_EXIST);

        StockTransferEntity stockTransferDO = stockTransferQueryRepository.selectById(stockTransferItemDO.getStockTransferId());
        CheckHelper.isNull(stockTransferDO, ErrorCode.STOCK_TRANSFER_NOT_EXIST);
        if (Objects.equals(stockTransferDO.getState(), StockTransferStateEnum.FINISH.getCode())) {
            throw new BizException(ErrorCode.STOCK_TRANSFER_NOT_OP);
        }
        //判断是否在盘点中
        /*List<StockTakingListDetail> outStockTakings = stockTakingMapper.selectTakingSku(stockTransferDO.getWarehouseNo().intValue(),
                stockTransferItemOpBO.getTransferOutSku());
        List<StockTakingListDetail> inStockTakings = stockTakingMapper.selectTakingSku(stockTransferDO.getWarehouseNo().intValue(),
                stockTransferItemOpBO.getTransferInSku());
        if (CollectionUtils.isNotEmpty(outStockTakings) || CollectionUtils.isNotEmpty(inStockTakings)) {
            throw new BizException(ErrorCode.SKU_STOCKTAKING);
        }*/
        stockTransferItemOpBO.setWarehouseNo(stockTransferDO.getWarehouseNo());
        return stockTransferDO.getWarehouseNo();
    }

    public List<Long> saveTransferItemOp(StockTransferItemOpBO stockTransferItemOpBO) {
        // 后续需要改为接口调用，dao不应该直接对外暴露
        Inventory inventory = inventoryMapper.queryBySku(stockTransferItemOpBO.getTransferOutSku());
        CheckHelper.isNull(inventory, ErrorCode.SKU_OUT_OF_INVENTORY);
        Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());
        CheckHelper.isNull(products, ErrorCode.GOODS_NOT_EXIST);
        // 货品属性校验，代销入仓品只能和代销入仓品转换
        List<GoodsPropertyDTO> goodsPropertyDTOList = goodsReadFacade.listGoodsPropertyBySkuList(Arrays.asList(stockTransferItemOpBO.getTransferInSku(), stockTransferItemOpBO.getTransferOutSku()));
        if (!CollectionUtils.isEmpty(goodsPropertyDTOList)) {
            List<Integer> subAgentTypeList = goodsPropertyDTOList.stream().map(GoodsPropertyDTO::getSubAgentType).distinct().collect(Collectors.toList());
            Map<String, Integer> skuSubAgentTypeMap = goodsPropertyDTOList.stream().collect(Collectors.toMap(GoodsPropertyDTO::getSku, GoodsPropertyDTO::getSubAgentType, (a, b) -> a));
            if (subAgentTypeList.contains(SubAgentTypeEnum.CONSIGNMENT_IN_WAREHOUSING.getType()) && subAgentTypeList.size() > 1) {
                String errorMsg = "转出sku：" + stockTransferItemOpBO.getTransferOutSku() + "【" + SubAgentTypeEnum.getDescByType(skuSubAgentTypeMap.get(stockTransferItemOpBO.getTransferOutSku())) + "】" +
                        "转入sku：" + stockTransferItemOpBO.getTransferInSku() + "【" + SubAgentTypeEnum.getDescByType(skuSubAgentTypeMap.get(stockTransferItemOpBO.getTransferInSku())) + "】" +
                        "代销入仓品不可与其他商品性质互转";
                throw new net.xianmu.common.exception.BizException(errorMsg);
            }
        }

        TransferOpEnum type = TransferOpEnum.convert(stockTransferItemOpBO.getType());
        List<Long> opIds = Lists.newArrayList();
        switch (type) {
            case ONE_WAY:
                opIds = saveByOneWay(stockTransferItemOpBO);
                break;
            case MIX:
                opIds = saveByMix(stockTransferItemOpBO, products);
                break;
            default:
                break;
        }
        return opIds;
    }

    private List<Long> saveByOneWay(StockTransferItemOpBO stockTransferItemOpBO) {
        ArrayList<StockTransferItemOpDO> opDos = Lists.newArrayList();
        stockTransferItemOpBO.getTransferOutInfos().forEach(outInfo -> {
            StockTransferItemOpDO stockTransferItemOpDO = StockTransferItemOpDO.builder()
                    .operator(stockTransferItemOpBO.getOperator())
                    .transferOutSku(stockTransferItemOpBO.getTransferOutSku())
                    .stockTransferItemId(stockTransferItemOpBO.getStockTransferItemId())
                    .transferRatio(stockTransferItemOpBO.getTransferRatio())
                    .produceDate(outInfo.getProduceTime())
                    .shelfLife(outInfo.getShelfLife())
                    .type(stockTransferItemOpBO.getType())
                    .build();
            stockTransferItemOpDAOV1.insert(stockTransferItemOpDO);
            opDos.add(stockTransferItemOpDO);

            StockTransferItemOpDetailParam itemOpDetailParam = StockTransferItemOpDetailParam.builder()
                    .stockTransferItemOpId(stockTransferItemOpDO.getId())
                    .transferOutBatch(outInfo.getTransferOutBatch())
                    .transferOutNum(outInfo.getTransferOutNum())
                    .produceAt(outInfo.getProduceTime())
                    .shelfLife(outInfo.getShelfLife())
                    .transferOutCabinet(outInfo.getTransferOutCabinetNo())
                    .transferInCabinet(outInfo.getTransferOutCabinetNo())
                    .transferInBatch(stockTakingService.createNewPurchasesNo())
                    .build();
            stockTransferItemOpDetailCommandRepository.insert(itemOpDetailParam);
        });
        return opDos.stream().map(StockTransferItemOpDO::getId).collect(Collectors.toList());
    }

    private List<Long> saveByMix(StockTransferItemOpBO stockTransferItemOpBO, Products products) {
        StockTransferItemOpDO stockTransferItemOpDO = StockTransferItemOpDO.builder()
                .operator(stockTransferItemOpBO.getOperator())
                .transferOutSku(stockTransferItemOpBO.getTransferOutSku())
                .stockTransferItemId(stockTransferItemOpBO.getStockTransferItemId())
                .transferRatio(stockTransferItemOpBO.getTransferRatio())
                .produceDate(stockTransferItemOpBO.getProduceDate())
                .shelfLife(DateUtil.datePlusDays(stockTransferItemOpBO.getProduceDate(), products.getQualityTime()))
                .type(stockTransferItemOpBO.getType())
                .build();
        stockTransferItemOpDAOV1.insert(stockTransferItemOpDO);

        // 生成新批次号
        String transferInCabinetNo = stockTransferItemOpBO.getTransferOutInfos().stream()
                .findFirst()
                .orElse(TransferOutInfo.builder().build())
                .getTransferOutCabinetNo();
        String newPurchasesNo = stockTakingService.createNewPurchasesNo();
        List<StockTransferItemOpDetailParam> stockTransferItemOpDetailParams = stockTransferItemOpBO.getTransferOutInfos().stream()
                .map(transferOutInfo -> StockTransferItemOpDetailParam.builder()
                        .stockTransferItemOpId(stockTransferItemOpDO.getId())
                        .transferOutBatch(transferOutInfo.getTransferOutBatch())
                        .transferInCabinet(transferInCabinetNo)
                        .transferOutNum(transferOutInfo.getTransferOutNum())
                        .produceAt(transferOutInfo.getProduceTime())
                        .shelfLife(transferOutInfo.getShelfLife())
                        .transferInBatch(newPurchasesNo)
                        .transferOutCabinet(transferOutInfo.getTransferOutCabinetNo())
                        .build()).collect(Collectors.toList());
        stockTransferItemOpDetailCommandRepository.batchInsert(stockTransferItemOpDetailParams);
        return Lists.newArrayList(stockTransferItemOpDO.getId());
    }

    private void transferInventory(StockTransferItemOpBO opBO, Long warehouseNo, TransferOpEnum type, List<Long> itemOpId) {
        // 更新库存
        updateInventory(opBO, warehouseNo, opBO.getTransferOutInfos(), type, itemOpId);
        // 生成新的转入批次
        createNewBatch(opBO, warehouseNo, itemOpId, type);
    }

    private void updateInventory(StockTransferItemOpBO stockTransferItemOpBO, Long warehouseNo, List<TransferOutInfo> transferOutInfos,
                                 TransferOpEnum type, List<Long> itemOpIds) {
        AtomicReference<Long> tenantId = new AtomicReference<>(1L);
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        List<StockTransferItemOpDetailEntity> opDetails = stockTransferItemOpDetailQueryRepository.listByOpIdList(itemOpIds);
        Map<String/*转出批次*/, Long/*操作id*/> opIdMap = opDetails.stream().collect(Collectors.toMap(StockTransferItemOpDetailEntity::getTransferOutBatch,
                StockTransferItemOpDetailEntity::getStockTransferItemOpId, (o1, o2) -> o1));

        // 库存转出先填补订单规格共享的虚拟转换数据
        String recordNo = opIdMap.values().stream().filter(Objects::nonNull).findFirst().map(String::valueOf).orElse("0");
        int transferInSkuOnlineQuantityChange = skuShareBizService.handleSkuShareTransfer(stockTransferItemOpBO, recordMap, recordNo, warehouseNo.intValue(), baseService.getAdminName());

        // 库存的转出在目前逻辑看来不分单次还是混合，所有批次都需要进行单独的操作
        transferOutInfos.forEach(transferOutInfo -> {
            StoreRecord storeRecord = inventoryCheck(stockTransferItemOpBO, warehouseNo, transferOutInfo);
            tenantId.set(storeRecord.getTenantId());
            AreaStore areaStoreOut = areaStoreMapper.selectOne(AreaStore.builder().areaNo(warehouseNo.intValue())
                    .sku(stockTransferItemOpBO.getTransferOutSku()).build());
            Long opId = opIdMap.getOrDefault(transferOutInfo.getTransferOutBatch(), 0L);
            if (areaStoreOut.getSync() == 1) {
                // 1.扣除虚拟库存、库存仓库库存、增加转换出库记录
                // fixme recordMap放在这里记录的意义不大，库存这块后续需要全部重构+重写; 涉及块太多，这次先按原逻辑走
                areaStoreServiceV1.updateOnlineStockByStoreNo(true, -transferOutInfo.getTransferOutNum().intValue(),
                        stockTransferItemOpBO.getTransferOutSku(), warehouseNo.intValue(),
                        OtherStockChangeTypeEnum.TRANSFORM_OUT, opId.toString(), recordMap, NumberUtils.INTEGER_ONE);
            }
            // 更新库存
            areaStoreServiceV1.updateStoreStock4AutoTransfer(-transferOutInfo.getTransferOutNum().intValue(),
                    stockTransferItemOpBO.getTransferOutSku(), warehouseNo.intValue(),
                    OtherStockChangeTypeEnum.TRANSFORM_OUT, StoreRecordType.TRANSFER_OUT.getId(), opId.toString(), recordMap);

            // 库位库存变更
            if (StringUtils.isNotEmpty(transferOutInfo.getTransferOutCabinetNo())) {
                cabinetBatchInventoryFacade.reduceCabinetBatchInventory(CabinetBatchInventoryReduceReqDTO.builder()
                        .reduceDetailReqList(Lists.newArrayList(CabinetBatchInventoryReduceDetailReqDTO.builder()
                                .reduceQuantity(transferOutInfo.getTransferOutNum().intValue())
                                .cabinetCode(transferOutInfo.getTransferOutCabinetNo())
                                .produceDate(DateUtil.toDate(transferOutInfo.getProduceTime()))
                                .qualityDate(DateUtil.toDate(transferOutInfo.getShelfLife()))
                                .skuBatchCode(transferOutInfo.getTransferOutBatch())
                                .skuCode(transferOutInfo.getTransferOutSku())
                                .build()))
                        .orderNo(stockTransferItemOpBO.getStockTransferId().toString())
                        .containerCode(null)
                        .orderTypeName(CabinetInventoryChangeTypeEnum.TRANSFORM.getTypeName())
                        .internalNo(stockTransferItemOpBO.getStockTransferId().toString())
                        .operatorType(CabinetInventoryOperationType.TRANSFER_OUT.getId())
                        .tenantId(storeRecord.getTenantId())
                        .warehouseNo(stockTransferItemOpBO.getWarehouseNo().intValue())
                        .build());
            }

            StoreRecord outRecord = StoreRecord.builder()
                    .areaNo(warehouseNo.intValue())
                    .batch(storeRecord.getBatch())
                    .cost(storeRecord.getCost())
                    .productionDate(storeRecord.getProductionDate())
                    .quantity(transferOutInfo.getTransferOutNum().intValue())
                    .sku(stockTransferItemOpBO.getTransferOutSku())
                    .qualityDate(storeRecord.getQualityDate())
                    .type(StoreRecordType.TRANSFER_OUT.getId())
                    .recorder(baseService.getAdminName())
                    .updateTime(new Date())
                    .storeQuantity(storeRecord.getStoreQuantity() - transferOutInfo.getTransferOutNum().intValue())
                    .bizId(opId.toString())
                    .tenantId(storeRecord.getTenantId())
                    .build();

            storeRecordMapper.insert(outRecord);
        });

        // 精细化仓去批次处理
        if (Boolean.TRUE.equals(warehouseConfigService.openCabinetManagement(warehouseNo.intValue()))) {
            Map<String, Long> transferOutMap = transferOutInfos.stream().collect(Collectors.toMap(item -> getGroupKey(item.getTransferOutSku(), item.getTransferOutCabinetNo(), item.getProduceTime(), item.getShelfLife()), TransferOutInfo::getTransferOutNum, Long::sum));
            for (Map.Entry<String, Long> entry : transferOutMap.entrySet()) {
                StockTransferItemEntity stockTransferItemDO = stockTransferItemQueryRepository.selectById(stockTransferItemOpBO.getStockTransferItemId());
                AreaStore areaStoreIn = areaStoreMapper.selectOne(new AreaStore(warehouseNo.intValue(), stockTransferItemDO.getTransferInSku()));
                String transferRatio = stockTransferItemOpBO.getTransferRatio();
                // 转出数量
                long transferOutNum = entry.getValue();
                if (TransferOpEnum.MIX.equals(type)) {
                    transferOutNum = transferOutInfos.stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
                }
                Long inNum = calculate(transferOutNum, transferRatio);
                if (areaStoreIn.getSync() == 1) {
                    //2.增加虚拟库存、增加仓库库存、增加入库记录
                    areaStoreServiceV1.updateOnlineStockByStoreNo(true, inNum.intValue(), stockTransferItemDO.getTransferInSku(),
                            warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, recordNo, recordMap, NumberUtils.INTEGER_ONE);
                }
                areaStoreServiceV1.updateStoreStock4AutoTransfer(inNum.intValue(), stockTransferItemDO.getTransferInSku(),
                        warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, StoreRecordType.TRANSFER_IN.getId(), recordNo, recordMap);
                // 混合批次最终只会转换一笔，所以直接跳出循环
                if (TransferOpEnum.MIX.equals(type)) {
                    break;
                }
            }

        } else {
            // 转入的逻辑需要区分单次还是混合
            for (int index = 0; index < transferOutInfos.size(); index++) {
                TransferOutInfo transferOutInfo = transferOutInfos.get(index);
                Long opId = opIdMap.getOrDefault(transferOutInfo.getTransferOutBatch(), 0L);
                StockTransferItemEntity stockTransferItemDO = stockTransferItemQueryRepository.selectById(stockTransferItemOpBO.getStockTransferItemId());
                AreaStore areaStoreIn = areaStoreMapper.selectOne(new AreaStore(warehouseNo.intValue(), stockTransferItemDO.getTransferInSku()));
                String transferRatio = stockTransferItemOpBO.getTransferRatio();
                // 转出数量
                long transferOutNum = transferOutInfo.getTransferOutNum();
                if (TransferOpEnum.MIX.equals(type)) {
                    transferOutNum = transferOutInfos.stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
                }
                Long inNum = calculate(transferOutNum, transferRatio);
                if (areaStoreIn.getSync() == 1) {
                    //2.增加虚拟库存、增加仓库库存、增加入库记录
                    areaStoreServiceV1.updateOnlineStockByStoreNo(true, inNum.intValue(), stockTransferItemDO.getTransferInSku(),
                            warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, opId.toString(), recordMap, NumberUtils.INTEGER_ONE);
                }
                areaStoreServiceV1.updateStoreStock4AutoTransfer(inNum.intValue(), stockTransferItemDO.getTransferInSku(),
                        warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, StoreRecordType.TRANSFER_IN.getId(), opId.toString(), recordMap);
                // 混合批次最终只会转换一笔，所以直接跳出循环
                if (TransferOpEnum.MIX.equals(type)) {
                    break;
                }
            }
        }


        // 如果填补订单规格共享后转入sku的虚拟库存有变化，则进行相应的更新
        if (transferInSkuOnlineQuantityChange != 0) {
            AreaStore areaStoreIn = areaStoreMapper.selectOne(new AreaStore(warehouseNo.intValue(), stockTransferItemOpBO.getTransferInSku()));
            if (areaStoreIn.getSync() == 1) {
                areaStoreServiceV1.updateOnlineStockByStoreNo(true, transferInSkuOnlineQuantityChange, stockTransferItemOpBO.getTransferInSku(),
                        warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, recordNo, recordMap, NumberUtils.INTEGER_ONE);
            }
        }

        // 保存库存转换流水
        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    private StoreRecord inventoryCheck(StockTransferItemOpBO stockTransferItemOpBO, Long warehouseNo, TransferOutInfo transferOutInfo) {
        // 库存记录查询逻辑后续涉及库存优化的时候在改
        StoreRecord storeRecord = queryRecord(stockTransferItemOpBO.getTransferOutSku(), warehouseNo, transferOutInfo);
        CheckHelper.isNull(storeRecord, ErrorCode.PARAM_ERROR);
        // 库存量校验
        if (storeRecord.getStoreQuantity() < transferOutInfo.getTransferOutNum()) {
            throw new BizException(ErrorCode.INVENTORY_EX);
        }
        // 原成本校验
        CheckHelper.isNull(storeRecord, ErrorCode.SKU_COST_ERROR, storeRecord.getBatch() + "批次" +
                storeRecord.getQualityDate() + "转出sku单价成本异常");
        return storeRecord;
    }

    private void createNewBatch(StockTransferItemOpBO opBO, Long warehouseNo, List<Long> itemOpIds, TransferOpEnum type) {
        // 查询原批次库存记录
        StockTransferItemEntity stockTransferItemDO = stockTransferItemQueryRepository.selectById(opBO.getStockTransferItemId());
        switch (type) {
            case ONE_WAY:
                // 单一批次校验批次供应商
                checkBatchSupplierByOneWay(opBO, warehouseNo);
                createBatchByOneWay(opBO, warehouseNo, itemOpIds, stockTransferItemDO);
                break;
            case MIX:
                // 混合批次校验批次供应商
                checkBatchSupplierByByMix(opBO, warehouseNo);
                createBatchByMix(opBO, warehouseNo, itemOpIds, stockTransferItemDO);
                break;
            default:
                break;
        }

    }

    /**
     * 单一批次校验批次供应商
     * @param stockTransferItemOpBO
     * @param warehouseNo
     */
    private void checkBatchSupplierByOneWay(StockTransferItemOpBO stockTransferItemOpBO, Long warehouseNo) {
        if (null == stockTransferItemOpBO || CollectionUtils.isEmpty(stockTransferItemOpBO.getTransferOutInfos()) || null == warehouseNo) {
            throw new net.xianmu.common.exception.BizException("单一批次校验批次供应商入参不通过");
        }
        log.info("单一批次校验批次供应商，任务id：{}", stockTransferItemOpBO.getStockTransferId());
        // 获取商品属性
        List<GoodsPropertyDTO> goodsPropertyDTOList = goodsReadFacade.listGoodsPropertyBySkuList(Arrays.asList(stockTransferItemOpBO.getTransferInSku(), stockTransferItemOpBO.getTransferOutSku()));
        List<Integer> subAgentTypeList = goodsPropertyDTOList.stream().map(GoodsPropertyDTO::getSubAgentType).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subAgentTypeList)) {
            return;
        }
        // 存在代销入仓品时进行校验
        if (!subAgentTypeList.contains(SubAgentTypeEnum.CONSIGNMENT_IN_WAREHOUSING.getType())) {
            log.info("单一批次获取商品属性不存在代销入仓品：{}", JSON.toJSONString(goodsPropertyDTOList));
            return;
        }
        List<String> transferOutCabinetList = stockTransferItemOpBO.getTransferOutInfos().stream().map(TransferOutInfo::getTransferOutCabinetNo).filter(Objects::nonNull).collect(Collectors.toList());
        // 非自营仓无需校验
        if (CollectionUtils.isEmpty(transferOutCabinetList)) {
            log.info("单一批次非自营仓无需执行供应商校验");
            return;
        }
        // 自营仓根据库位分组校验
        Map<String, List<TransferOutInfo>> groupTransferOutCabinetMap = stockTransferItemOpBO.getTransferOutInfos().stream().collect(Collectors.groupingBy(item -> item.getTransferOutCabinetNo() + "_" + item.getProduceTime() + "_" + item.getShelfLife()));
        groupTransferOutCabinetMap.forEach((transferOutCabinet, transferOutInfoList) -> {
            log.info("单一批次自营仓执行供应商校验，分组key：{}", transferOutCabinet);
            // 执行供应商校验
            checkBatchSupplier(transferOutInfoList, warehouseNo, stockTransferItemOpBO.getTransferOutSku());
        });
    }

    /**
     * 混合批次校验批次供应商
     * @param stockTransferItemOpBO
     * @param warehouseNo
     */
    private void checkBatchSupplierByByMix(StockTransferItemOpBO stockTransferItemOpBO, Long warehouseNo) {
        if (null == stockTransferItemOpBO || CollectionUtils.isEmpty(stockTransferItemOpBO.getTransferOutInfos()) || null == warehouseNo) {
            throw new net.xianmu.common.exception.BizException("混合批次校验批次供应商入参不通过");
        }
        log.info("混合批次校验批次供应商，任务id：{}", stockTransferItemOpBO.getStockTransferId());
        // 获取商品属性
        List<GoodsPropertyDTO> goodsPropertyDTOList = goodsReadFacade.listGoodsPropertyBySkuList(Arrays.asList(stockTransferItemOpBO.getTransferInSku(), stockTransferItemOpBO.getTransferOutSku()));
        List<Integer> subAgentTypeList = goodsPropertyDTOList.stream().map(GoodsPropertyDTO::getSubAgentType).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subAgentTypeList)) {
            return;
        }
        // 存在代销入仓品时进行校验
        if (!subAgentTypeList.contains(SubAgentTypeEnum.CONSIGNMENT_IN_WAREHOUSING.getType())) {
            log.info("混合批次获取商品属性不存在代销入仓品：{}", JSON.toJSONString(goodsPropertyDTOList));
            return;
        }
        List<String> transferOutCabinetList = stockTransferItemOpBO.getTransferOutInfos().stream().map(TransferOutInfo::getTransferOutCabinetNo).filter(Objects::nonNull).collect(Collectors.toList());
        // 非自营仓校验
        if (CollectionUtils.isEmpty(transferOutCabinetList)) {
            log.info("混合批次非自营仓执行供应商校验");
            // 执行供应商校验
            checkBatchSupplier(stockTransferItemOpBO.getTransferOutInfos(), warehouseNo, stockTransferItemOpBO.getTransferOutSku());
        } else {
            // 自营仓校验
            log.info("混合批次自营仓执行供应商校验");
            // 执行供应商校验
            checkBatchSupplier(stockTransferItemOpBO.getTransferOutInfos(), warehouseNo, stockTransferItemOpBO.getTransferOutSku());
        }
    }

    /**
     * 校验批次供应商
     * @param transferOutInfoList
     * @param warehouseNo
     */
    private void checkBatchSupplier(List<TransferOutInfo> transferOutInfoList, Long warehouseNo, String transferOutSku) {
        if (CollectionUtils.isEmpty(transferOutInfoList) || null == warehouseNo || StringUtils.isBlank(transferOutSku)) {
            throw new net.xianmu.common.exception.BizException("校验批次供应商入参不通过");
        }
        List<String> transferOutBatchList = transferOutInfoList.stream().map(TransferOutInfo::getTransferOutBatch).distinct().collect(Collectors.toList());
        List<CostBatch> costBatchList = costBatchRepository.queryByWarehouseAndSkuAndBatchAndGtZero(warehouseNo, transferOutSku, transferOutBatchList);
        log.info("获取存在库存成本批次记录：{}", JSON.toJSONString(costBatchList));
        if (CollectionUtils.isEmpty(costBatchList)) {
            return;
        }
        // 批次供应商列表
        List<Long> supplierIdList = costBatchList.stream().map(CostBatch::getSupplierId).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("获取批次供应商id列表：{}", supplierIdList);
        // 不存在供应商成本批次
        List<Long> emptySupplierCostBatchIdList = costBatchList.stream().filter(item -> null == item.getSupplierId()).map(CostBatch::getId).collect(Collectors.toList());
        log.info("不存在供应商成本批次id：{}", emptySupplierCostBatchIdList);
        // 批次溯源供应商
        List<BatchRelationOriginEntity> batchRelationOriginEntityList = batchRelationOriginQueryRepository.selectByCurrentCostBatchIdList(emptySupplierCostBatchIdList);
        if (!CollectionUtils.isEmpty(batchRelationOriginEntityList)) {
            List<Long> originBatchSupplierIdList = batchRelationOriginEntityList.stream().map(BatchRelationOriginEntity::getOriginSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            supplierIdList.addAll(originBatchSupplierIdList);
        }
        // 去重供应商
        List<Long> distinctSupplierIdList = supplierIdList.stream().distinct().collect(Collectors.toList());
        log.info("最终去重后供应商id列表：{}", distinctSupplierIdList);
        // 存在多供应商报错
        if (distinctSupplierIdList.size() > 1) {
            throw new net.xianmu.common.exception.BizException("代销入仓品不支持跨供应商转换");
        }
    }

    private void createBatchByMix(StockTransferItemOpBO opBO, Long warehouseNo, List<Long> itemOpIds, StockTransferItemEntity stockTransferItemDO) {
        TransferOutInfo maxBatch = opBO.getTransferOutInfos().stream().max(Comparator.comparing(TransferOutInfo::getTransferOutBatch)).orElse(null);
        if (Objects.isNull(maxBatch)) {
            return;
        }
        StoreRecord storeRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, maxBatch);

        // sql中需要orderBy id limit 1
        Long itemOpId = itemOpIds.get(NumberUtils.INTEGER_ZERO);
        StockTransferItemOpDO opDO = stockTransferItemOpDAOV1.selectById(itemOpId);
        StockTransferItemOpDetailEntity opDetailDO = stockTransferItemOpDetailQueryRepository.selectByBatch(itemOpId, maxBatch.getTransferOutBatch());

        // 计算成本,转入单价 = 转出单价*转出数量 / 转入数量
        double outAllCost = opBO.getTransferOutInfos().stream().mapToDouble(outInfo -> {
            StoreRecord outStoreRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, outInfo);
            return outStoreRecord.getCost().doubleValue() * outInfo.getTransferOutNum();
        }).sum();
        long outNum = opBO.getTransferOutInfos().stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
        Long transferInNum = calculate(outNum, opBO.getTransferRatio());
        BigDecimal cost = BigDecimal.valueOf(outAllCost).divide(BigDecimal.valueOf(transferInNum), 2, BigDecimal.ROUND_HALF_EVEN);

        // 保存新批次记录
        saveNewBatch(warehouseNo, opDetailDO, opBO.getTransferOutSku(), stockTransferItemDO.getTransferInSku(),
                cost, transferInNum, DateUtil.toLocalDate(opDO.getShelfLife()), DateUtil.toLocalDate(opDO.getProduceDate())
                , storeRecord.getTenantId());

        //库存转换证明处理
        transferProveHandle(opBO.getTransferOutSku(), maxBatch.getTransferOutBatch(), storeRecord.getProductionDate(),
                DateUtil.toLocalDate(opDO.getShelfLife()), DateUtil.toLocalDate(opDO.getProduceDate()), opDetailDO.getTransferInBatch(), stockTransferItemDO.getTransferInSku(), stockTransferItemDO.getStockTransferId());

        // 生成批次码
        createBatchCode(warehouseNo, DateUtil.toLocalDate(opDO.getProduceDate()), DateUtil.toLocalDate(opDO.getShelfLife()), stockTransferItemDO, opDetailDO);
    }

    private void saveNewBatch(Long warehouseNo, StockTransferItemOpDetailEntity opDetailDO,
                              String outSku, String inSku, BigDecimal newCost, Long transferInNum, LocalDate shelfLife,
                              LocalDate produceDate,
                              Long tenantId) {
        StoreRecord newStoreRecord = StoreRecord.builder()
                .cost(newCost)
                .batch(opDetailDO.getTransferInBatch())
                .sku(inSku)
                .type(StoreRecordType.TRANSFER_IN.getId())
                .qualityDate(shelfLife)
                .storeQuantity(transferInNum.intValue())
                .areaNo(warehouseNo.intValue())
                .updateTime(new Date())
                .recorder(baseService.getAdminName())
                .quantity(transferInNum.intValue())
                .productionDate(produceDate)
                .bizId(opDetailDO.getStockTransferItemOpId().toString())
                .tenantId(tenantId)
                .build();

        // 老代码迁移，判断是否在一个spu库存转换
        ArrayList<String> skus = Lists.newArrayList(outSku, inSku);
        List<Inventory> inventories = inventoryMapper.selectBySkuList(skus);
        Map<String, Long> pdIdMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku, Inventory::getPdId, (o1, o2) -> o1));
        if (!Objects.equals(pdIdMap.get(outSku), pdIdMap.get(inSku))) {
            newStoreRecord.setLotType(NumberUtils.INTEGER_ONE);
        }

        storeRecordMapper.insert(newStoreRecord);

        if (StringUtils.isNotEmpty(opDetailDO.getTransferOutCabinet())) {
            cabinetBatchInventoryFacade.addCabinetBatchInventory(CabinetBatchInventoryAddFacadeReq.builder()
                    .addDetailReqList(Lists.newArrayList(CabinetBatchInventoryAddDetailFacadeReq.builder()
                            .addQuantity(transferInNum.intValue())
                            .cabinetCode(opDetailDO.getTransferOutCabinet())
                            .produceDate(DateUtil.toDate(produceDate))
                            .qualityDate(DateUtil.toDate(shelfLife))
                            .skuBatchCode(opDetailDO.getTransferInBatch())
                            .skuCode(inSku)
                            .build()))
                    .orderNo(null)
                    .containerCode(null)
                    .orderTypeName(CabinetInventoryChangeTypeEnum.TRANSFORM.getTypeName())
                    .internalNo(opDetailDO.getStockTransferItemOpId().toString())
                    .operatorType(CabinetInventoryOperationType.TRANSFER_IN.getId())
                    .tenantId(tenantId)
                    .warehouseNo(warehouseNo.intValue())
                    .build());
        }
    }

    private void createBatchByOneWay(StockTransferItemOpBO opBO, Long warehouseNo, List<Long> itemOpIds, StockTransferItemEntity stockTransferItemDO) {
        // 精细化仓去批次
        if (Boolean.TRUE.equals(warehouseConfigService.openCabinetManagement(warehouseNo.intValue()))) {
            List<StockTransferItemOpDetailEntity> opDetailDos = stockTransferItemOpDetailQueryRepository.listByOpIdList(itemOpIds);
            Map<String, StockTransferItemOpDetailEntity> shelfLifeMap = opDetailDos.stream().collect(Collectors.toMap(item -> item.getProduceAt() + ":" + item.getShelfLife() + ":" + item.getTransferOutCabinet(), Function.identity(), (o1, o2) -> o1));
            List<StoreRecord> storeRecordList = new ArrayList<>();
            opBO.getTransferOutInfos().forEach(transferOutInfo -> {
                StoreRecord storeRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, transferOutInfo);
                storeRecordList.add(storeRecord);

                // sql中需要orderBy id limit 1
                StockTransferItemOpDetailEntity opDetailDO = shelfLifeMap.getOrDefault(transferOutInfo.getProduceTime() + ":" + transferOutInfo.getShelfLife()+ ":" + transferOutInfo.getTransferOutCabinetNo(), StockTransferItemOpDetailEntity.builder().build());
                StockTransferItemOpDO opDO = stockTransferItemOpDAOV1.selectById(opDetailDO.getStockTransferItemOpId());

                //库存转换证明处理
                transferProveHandle(opBO.getTransferOutSku(), transferOutInfo.getTransferOutBatch(),
                        storeRecord.getProductionDate(), DateUtil.toLocalDate(opDO.getShelfLife()),
                        DateUtil.toLocalDate(opDO.getProduceDate()), opDetailDO.getTransferInBatch(),
                        stockTransferItemDO.getTransferInSku(), stockTransferItemDO.getStockTransferId());

                // 生成批次码
                createBatchCode(warehouseNo, storeRecord.getProductionDate(), storeRecord.getQualityDate(), stockTransferItemDO, opDetailDO);
            });

            // 生成新批次
            Map<String, StoreRecord> storeRecordMap = storeRecordList.stream().collect(Collectors.toMap(item -> item.getSku() + ":" + item.getProductionDate() + ":" + item.getQualityDate(), Function.identity(), (o1, o2) -> o1));
            Map<String, Long> transferOutMap = opBO.getTransferOutInfos().stream().collect(Collectors.toMap(item -> getGroupKey(item.getTransferOutSku(), item.getTransferOutCabinetNo(), item.getProduceTime(), item.getShelfLife()), TransferOutInfo::getTransferOutNum, Long::sum));
            for (Map.Entry<String, Long> entry : transferOutMap.entrySet()) {
                String[] split = entry.getKey().split("_");
                String sku = split[0];
                String transferOutCabinetNo = split[1];
                String produceTime = split[2];
                String shelfLife = split[3];

                // sql中需要orderBy id limit 1
                StockTransferItemOpDetailEntity opDetailDO = shelfLifeMap.getOrDefault(produceTime + ":" + shelfLife+ ":" + transferOutCabinetNo, StockTransferItemOpDetailEntity.builder().build());

                // 计算成本
                Long transferOutNum = entry.getValue();
                Long transferInNum = calculate(transferOutNum, opBO.getTransferRatio());
                StoreRecord storeRecord = storeRecordMap.get(sku + ":" + DateUtil.toLocalDate(Long.valueOf(produceTime)) + ":" + DateUtil.toLocalDate(Long.valueOf(shelfLife)));
                BigDecimal cost = storeRecord.getCost().multiply(BigDecimal.valueOf(transferOutNum))
                        .divide(BigDecimal.valueOf(transferInNum), 2, BigDecimal.ROUND_HALF_EVEN);

                // 保存新批次记录
                saveNewBatch(warehouseNo, opDetailDO, opBO.getTransferOutSku(), stockTransferItemDO.getTransferInSku(),
                        cost, transferInNum, storeRecord.getQualityDate(), storeRecord.getProductionDate()
                        , storeRecord.getTenantId());
            }
            // 非精细化仓原逻辑
        } else {
            List<StockTransferItemOpDetailEntity> opDetailDos = stockTransferItemOpDetailQueryRepository.listByOpIdList(itemOpIds);
            Map<String, StockTransferItemOpDetailEntity> batchMap = opDetailDos.stream().collect(Collectors.toMap(item -> item.getTransferOutBatch() + ":" + item.getProduceAt() + ":" + item.getTransferOutCabinet(), Function.identity(), (o1, o2) -> o1));
            opBO.getTransferOutInfos().forEach(transferOutInfo -> {
                StoreRecord storeRecord = queryRecord(opBO.getTransferOutSku(), warehouseNo, transferOutInfo);

                // sql中需要orderBy id limit 1
                StockTransferItemOpDetailEntity opDetailDO = batchMap.getOrDefault(transferOutInfo.getTransferOutBatch() + ":" + transferOutInfo.getProduceTime() + ":" + transferOutInfo.getTransferOutCabinetNo(), StockTransferItemOpDetailEntity.builder().build());
                StockTransferItemOpDO opDO = stockTransferItemOpDAOV1.selectById(opDetailDO.getStockTransferItemOpId());

                // 计算成本
                Long transferInNum = calculate(transferOutInfo.getTransferOutNum(), opBO.getTransferRatio());
                BigDecimal cost = storeRecord.getCost().multiply(BigDecimal.valueOf(transferOutInfo.getTransferOutNum()))
                        .divide(BigDecimal.valueOf(transferInNum), 2, BigDecimal.ROUND_HALF_EVEN);

                // 保存新批次记录
                saveNewBatch(warehouseNo, opDetailDO, opBO.getTransferOutSku(), stockTransferItemDO.getTransferInSku(),
                        cost, transferInNum, storeRecord.getQualityDate(), storeRecord.getProductionDate()
                        , storeRecord.getTenantId());

                //库存转换证明处理
                transferProveHandle(opBO.getTransferOutSku(), transferOutInfo.getTransferOutBatch(),
                        storeRecord.getProductionDate(), DateUtil.toLocalDate(opDO.getShelfLife()),
                        DateUtil.toLocalDate(opDO.getProduceDate()), opDetailDO.getTransferInBatch(),
                        stockTransferItemDO.getTransferInSku(), stockTransferItemDO.getStockTransferId());

                // 生成批次码
                createBatchCode(warehouseNo, storeRecord.getProductionDate(), storeRecord.getQualityDate(), stockTransferItemDO, opDetailDO);
            });
        }
    }

    private void createBatchCode(Long warehouseNo, LocalDate productionDate, LocalDate qualityDate, StockTransferItemEntity stockTransferItemDO, StockTransferItemOpDetailEntity opDetailDO) {
        SkuBatchCode skuBatchCode = SkuBatchCode.builder()
                .sku(stockTransferItemDO.getTransferInSku())
                .purchaseNo(opDetailDO.getTransferInBatch())
                .productionDate(productionDate)
                .qualityDate(qualityDate)
                .build();
        // 判断是否降级转换任务和临保转换任务，是则修改batchType
        StockTransferEntity transferDO = stockTransferQueryRepository.selectById(stockTransferItemDO.getStockTransferId());
        Integer batchType = null;
        if (transferDO != null && TransferDimensionEnum.DOWN_GRADE.getCode().equals(transferDO.getTransferDimension())) {
            batchType = SkuBatchType.DOWN_GRADE.getType();
        } else if (transferDO != null && TransferExportConstant.NEAR_SHELF_LIFE.equals(transferDO.getRemark())) {
            batchType = SkuBatchType.NEAR_SHELF_LIFE.getType();
        } else if (transferDO != null && TransferExportConstant.DAMAGE.equals(transferDO.getRemark())) {
            batchType = SkuBatchType.DAMAGE.getType();
        }
        skuBatchCode.setBatchType(batchType);
        skuBatchCodeService.createBatchCode(skuBatchCode, warehouseNo.intValue());
    }

    /**
     * 库存转换证明处理
     *
     * @param inBatch 批次号
     */
    private void transferProveHandle(String outSku, String outBatch, LocalDate outProduceTime,
                                     LocalDate inShelfLife, LocalDate inProduceTime, String inBatch, String inSku, Long stockTransferId) {
        //库存转换同时代入四项证明
        BatchProveParam param = new BatchProveParam();
        param.setSku(outSku);
        param.setBatch(outBatch);
        param.setProductionDate(outProduceTime);
        List<WarehouseBatchProveRecordVO> proveRecordVos = warehouseBatchProveRecordMapper.selectByBatchAndSku(param);
        if (!org.springframework.util.CollectionUtils.isEmpty(proveRecordVos)) {

            log.info("四项证明查询条件：{}", JSON.toJSONString(param));
            WarehouseBatchProveRecordVO recordVO = proveRecordVos.get(NumberUtils.INTEGER_ZERO);
            log.info("四项证明查询结果：{}", JSON.toJSONString(recordVO));
            //校验四项证明,农残证是否存在
            if (Objects.nonNull(recordVO)) {
                recordVO.setBatch(inBatch);
                recordVO.setSku(inSku);
                recordVO.setProductionDate(inProduceTime);
                recordVO.setQualityDate(inShelfLife);
                recordVO.setCreator(baseService.getAdminId());
                recordVO.setType(WareHouseBatchProveRecordEnum.TRANSFER_IN.getId());
                recordVO.setSourceId(String.valueOf(stockTransferId));
                warehouseBatchProveRecordMapper.insert(recordVO);
            }
        }
    }

    @Override
    public Workbook excelTransfer(Long stockTransferId) {
        StockTransferEntity stockTransferDO = stockTransferQueryRepository.selectById(stockTransferId);
        if (Objects.isNull(stockTransferDO)) {
            return new HSSFWorkbook();
        }
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Integer index = excelTitle(stockTransferDO, sheet);
        excelContact(sheet, index, stockTransferId);
        return workbook;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateStockTransferInItemOnly(Long stockTransferId) {
        log.info("开始操作「仅转入」转换:{}", stockTransferId);
        StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
        Long warehouseNo = stockTransferEntity.getWarehouseNo();
        // find from db
        List<StockTransferItemEntity> stockTransferItemEntities = stockTransferItemQueryRepository.listByStockTransferId(stockTransferId);
        // key: inSkuCode
        Map<String, StockTransferItemEntity> stockTransferItemMapFromDb = stockTransferItemEntities.stream().collect(Collectors.toMap(StockTransferItemEntity::getTransferInSku, Function.identity(), (k1, k2) -> k1));
        List<StockTransferItemOpEntity> stockTransferItemOpEntities = stockTransferItemOpQueryRepository.listByItemIdList(stockTransferItemEntities.stream().map(StockTransferItemEntity::getId).collect(Collectors.toList()));
        // key: itemId
        Map<Long, List<StockTransferItemOpEntity>> stockTransferItemOpMapFromDb = stockTransferItemOpEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpEntity::getStockTransferItemId));
        // key: itemOpId
        List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntities = stockTransferItemOpDetailQueryRepository.listByOpIdList(stockTransferItemOpEntities.stream().map(StockTransferItemOpEntity::getId).collect(Collectors.toList()));
        Map<Long, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb = stockTransferItemOpDetailEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpDetailEntity::getStockTransferItemOpId));
        // 获取忽略转入sku列表
        List<String> ignoreSku = getIgnoreSkuForStockTransferInItemOnly(stockTransferItemMapFromDb, stockTransferItemOpMapFromDb, stockTransferItemOpDetailMapFromDb);

        stockTransferItemMapFromDb.forEach((inSkuCode, stockTransferItemEntity) -> {
            if (ignoreSku.contains(inSkuCode)) {
                log.info("忽略处理转入sku：{}", inSkuCode);
                return;
            }
            if (null == stockTransferItemEntity){
                log.info("库存转换任务明细不存在：{}", inSkuCode);
                return;
            }
            List<StockTransferItemOpEntity> stockTransferItemOpEntityList = stockTransferItemOpMapFromDb.get(stockTransferItemEntity.getId());
            if (CollectionUtils.isEmpty(stockTransferItemOpEntityList)){
                log.info("库存转换任务OP不存在：{}", inSkuCode);
                return;
            }
            stockTransferItemOpEntityList.forEach(op -> {
                List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntityList = stockTransferItemOpDetailMapFromDb.get(op.getId());
                if (TransferOpEnum.ONE_WAY.getCode().equals(op.getType())) {
                    log.info("【仅转入】进入单批次转换逻辑");
                    for (StockTransferItemOpDetailEntity opDetail : stockTransferItemOpDetailEntityList) {
                        log.info("【仅转入已循环到最底层】opDetail >>> {}", JSON.toJSONString(opDetail));
                        int num = calculate(opDetail.getTransferOutNum(), op.getTransferRatio()).intValue();
                        if (opDetail.getExternalTransferInNum() <= num){
                            log.info("三方回告转入数量 {} 小于等于 鲜沐系统操作的转入数量 {} ,不执行仅转入。 opDetail >>> {}", opDetail.getExternalTransferInNum(), num, JSON.toJSONString(opDetail));
                            continue;
                        }
                        // 三方转入多出来的差异数量，我们要进行插入库存操作
                        int onlyTransferInQuantity = opDetail.getExternalTransferInNum() - num;
                        // 更新库存 - area_store
                        this.updateInventoryOnlyIn(warehouseNo, inSkuCode, onlyTransferInQuantity, op.getId());
                        // 计算新的成本
                        BigDecimal newCost = this.calculateNewCostOfWhenExternalCallback(warehouseNo, op, opDetail, stockTransferItemOpDetailEntityList);
                        log.info("计算出新的成本为 {} >>>>> {}", newCost, JSON.toJSONString(opDetail));
                        // 更新批次库存 - store_record
                        this.saveNewBatchOnlyIn(warehouseNo, opDetail, op.getTransferOutSku(), inSkuCode, newCost, opDetail.getExternalTransferInNum(),
                                opDetail.getTransferInBatch(), DateUtil.toLocalDate(op.getProduceDate()), DateUtil.toLocalDate(op.getShelfLife()), WmsConstant.XIANMU_TENANT_ID);

                    }
                } else {
                    log.info("【仅转入】进入混合批次转换逻辑");
                    int externalTransferInNumTotal = stockTransferItemOpDetailEntityList.stream().mapToInt(StockTransferItemOpDetailEntity::getExternalTransferInNum).sum();
                    Long xianmuTransferOutNumTotal = stockTransferItemOpDetailEntityList.stream().mapToLong(StockTransferItemOpDetailEntity::getTransferOutNum).sum();
                    int xianmuTransferInNumTotal = calculate(xianmuTransferOutNumTotal, op.getTransferRatio()).intValue();
                    if (externalTransferInNumTotal <= xianmuTransferInNumTotal){
                        log.info("三方回告转入数量 {} 小于等于 鲜沐系统操作的转入数量 {}, 不执行仅转入。 opDetail >>> {}", externalTransferInNumTotal, xianmuTransferInNumTotal, JSON.toJSONString(stockTransferItemOpDetailEntityList));
                    }else {
                        // 三方转入多出来的差异数量，我们要进行插入库存操作
                        int onlyTransferInQuantity = externalTransferInNumTotal - xianmuTransferInNumTotal;
                        // 更新库存 - area_store
                        this.updateInventoryOnlyIn(warehouseNo, inSkuCode, onlyTransferInQuantity, op.getId());
                        // 计算新的成本
                        BigDecimal newCost = this.calculateNewCostOfWhenExternalCallback(warehouseNo, op, null, stockTransferItemOpDetailEntityList);
                        log.info("计算出新的成本为 {} >>>>> {}", newCost, JSON.toJSONString(stockTransferItemOpDetailEntityList));
                        // 更新批次库存 - store_record
                        this.saveNewBatchOnlyIn(warehouseNo, stockTransferItemOpDetailEntityList.get(0), op.getTransferOutSku(), inSkuCode, newCost, externalTransferInNumTotal,
                                stockTransferItemOpDetailEntityList.get(0).getTransferInBatch(), DateUtil.toLocalDate(op.getProduceDate()), DateUtil.toLocalDate(op.getShelfLife()), WmsConstant.XIANMU_TENANT_ID);
                    }
                }
            });
        });

        // 保存转换任务详情
        // List<Long> itemOpIds = saveTransferItemOpOnlyInMode(stockTransferItemOpBO);
        // 转换库存

    }

    /**
     * 计算新的成本
     * @param warehouseNo 库存仓号
     * @param op 当前循环到的op
     * @param opDetail 当前循环到的opDetail
     * @param opDetailList 当前循环到的 opDetail 对应的 op 下的所有 opDetail
     * @return 转入品的新成本
     */
    private BigDecimal calculateNewCostOfWhenExternalCallback(Long warehouseNo,
                                                              StockTransferItemOpEntity op,
                                                              StockTransferItemOpDetailEntity opDetail,
                                                              List<StockTransferItemOpDetailEntity> opDetailList){
        if (TransferOpEnum.MIX.getCode().equals(op.getType())){
            // 计算成本,转入单价 = 转出单价*转出数量 / 三方实际转入数量总和
            double outAllCost = opDetailList.stream().mapToDouble(opDetailSingle -> {
                TransferOutInfo transferOutInfo = new TransferOutInfo();
                transferOutInfo.setTransferOutBatch(opDetailSingle.getTransferOutBatch());
                transferOutInfo.setProduceTime(opDetailSingle.getProduceAt());
                transferOutInfo.setShelfLife(opDetailSingle.getShelfLife());
                StoreRecord outStoreRecord = queryRecord(op.getTransferOutSku(), warehouseNo, transferOutInfo);
                return outStoreRecord.getCost().doubleValue() * opDetailSingle.getTransferOutNum();
            }).sum();
            // 三方回告转入数量
            Long actualTransferInNum = opDetailList.stream().mapToLong(StockTransferItemOpDetailEntity::getExternalTransferInNum).sum();
            return BigDecimal.valueOf(outAllCost).divide(BigDecimal.valueOf(actualTransferInNum), 2, BigDecimal.ROUND_HALF_EVEN);
        }else if (TransferOpEnum.ONE_WAY.getCode().equals(op.getType())){
            TransferOutInfo transferOutInfo = new TransferOutInfo();
            transferOutInfo.setTransferOutBatch(opDetail.getTransferOutBatch());
            transferOutInfo.setProduceTime(opDetail.getProduceAt());
            transferOutInfo.setShelfLife(opDetail.getShelfLife());
            StoreRecord storeRecord = queryRecord(op.getTransferOutSku(), warehouseNo, transferOutInfo);
            Long actualTransferInNum = Long.valueOf(opDetail.getExternalTransferInNum());
            BigDecimal cost = storeRecord.getCost().multiply(BigDecimal.valueOf(opDetail.getTransferOutNum()))
                    .divide(BigDecimal.valueOf(actualTransferInNum), 2, BigDecimal.ROUND_HALF_EVEN);
            return cost;
        }
        return null;
    }


    /**
     * 仅转入场景 - 保存转换任务的op和opDetail
     * @param stockTransferItemOpBO 转换任务操作BO
     * @return opIdList
     */
    private List<Long> saveTransferItemOpOnlyInMode(StockTransferItemOpBO stockTransferItemOpBO) {
        ArrayList<StockTransferItemOpDO> opDos = Lists.newArrayList();
        TransferOutInfo outInfo = stockTransferItemOpBO.getTransferOutInfos().get(0);
        stockTransferItemOpBO.getTransferOnlyInInfos().forEach(inInfo -> {
            StockTransferItemOpDO stockTransferItemOpDO = StockTransferItemOpDO.builder()
                    .operator(stockTransferItemOpBO.getOperator())
                    .transferOutSku(stockTransferItemOpBO.getTransferOutSku())
                    .stockTransferItemId(stockTransferItemOpBO.getStockTransferItemId())
                    .transferRatio(stockTransferItemOpBO.getTransferRatio())
                    .produceDate(inInfo.getTransferOnlyInProduceTime())
                    .shelfLife(inInfo.getTransferOnlyInShelfLife())
                    .type(stockTransferItemOpBO.getType())
                    .build();
            stockTransferItemOpDAOV1.insert(stockTransferItemOpDO);
            opDos.add(stockTransferItemOpDO);

            StockTransferItemOpDetailParam itemOpDetailParam = StockTransferItemOpDetailParam.builder()
                    .stockTransferItemOpId(stockTransferItemOpDO.getId())
                    .transferOutBatch(outInfo.getTransferOutBatch())
                    .transferOutNum(outInfo.getTransferOutNum())
                    .produceAt(outInfo.getProduceTime())
                    .shelfLife(outInfo.getShelfLife())
                    .transferOutCabinet(outInfo.getTransferOutCabinetNo())
                    .transferInCabinet(outInfo.getTransferOutCabinetNo())
                    .transferInBatch(stockTakingService.createNewPurchasesNo())
                    .build();
            stockTransferItemOpDetailCommandRepository.insert(itemOpDetailParam);
        });
        return opDos.stream().map(StockTransferItemOpDO::getId).collect(Collectors.toList());
    }

    /**
     * 更新库存 - area_store
     * @param warehouseNo 库存仓号
     * @param skuCode sku编码
     * @param transferInNum 转入数量
     * @param itemOpId 转换op表的主键
     */
    private void updateInventoryOnlyIn(Long warehouseNo, String skuCode, int transferInNum, Long itemOpId) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        // 精细化仓不处理
        if (Boolean.TRUE.equals(warehouseConfigService.openCabinetManagement(warehouseNo.intValue()))) {
            log.info("暂时不支持精细化仓的仅转入库存操作");
            return;
        }
        AreaStore areaStoreIn = areaStoreMapper.selectOne(new AreaStore(warehouseNo.intValue(), skuCode));

        if (areaStoreIn.getSync() == 1) {
            //2.增加虚拟库存、增加仓库库存、增加入库记录
            areaStoreServiceV1.updateOnlineStockByStoreNo(true, transferInNum, skuCode,
                    warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, itemOpId.toString(), recordMap, NumberUtils.INTEGER_ONE);
        }
        areaStoreServiceV1.updateStoreStock4AutoTransfer(transferInNum, skuCode,
                warehouseNo.intValue(), OtherStockChangeTypeEnum.TRANSFORM_IN, StoreRecordType.TRANSFER_IN.getId(), itemOpId.toString(), recordMap);

        // 保存库存转换流水
        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    /**
     * 仅转入场景 - 保存 StoreRecord
     * @param warehouseNo 库存仓号
     * @param opDetail 转换的 opDetail
     * @param outSku 转出sku编码
     * @param inSku 转入sku编码
     * @param newCost 新的成本
     * @param newTransferInNum 新的转入数量
     * @param transferInBatch 转入批次号
     * @param produceDate 转入生产日期
     * @param shelfLife 转入的保质期
     * @param tenantId 租户id
     */
    private void saveNewBatchOnlyIn(Long warehouseNo, StockTransferItemOpDetailEntity opDetail,
                              String outSku, String inSku, BigDecimal newCost, Integer newTransferInNum,
                              String transferInBatch, LocalDate produceDate, LocalDate shelfLife,
                              Long tenantId) {
        // 先查询最近的 StoreRecord 记录
        StoreRecord select = new StoreRecord();
        select.setBatch(transferInBatch);
        select.setSku(inSku);
        select.setAreaNo(warehouseNo.intValue());
        select.setQualityDate(shelfLife);
        select.setProductionDate(produceDate);
        StoreRecord lasted = storeRecordMapper.selectOne(select);
        // 组装要插入的 StoreRecord 记录
        StoreRecord newStoreRecord = StoreRecord.builder()
                .cost(newCost)
                .batch(opDetail.getTransferInBatch())
                .sku(inSku)
                .type(StoreRecordType.TRANSFER_IN.getId())
                .qualityDate(shelfLife)
                .storeQuantity(newTransferInNum)
                .areaNo(warehouseNo.intValue())
                .updateTime(new Date())
                .recorder(baseService.getAdminName())
                .quantity(newTransferInNum - lasted.getQuantity())
                .productionDate(produceDate)
                .bizId(opDetail.getStockTransferItemOpId().toString())
                .tenantId(tenantId)
                .build();

        // 老代码迁移，判断是否在一个spu库存转换
        ArrayList<String> skus = Lists.newArrayList(outSku, inSku);
        List<Inventory> inventories = inventoryMapper.selectBySkuList(skus);
        Map<String, Long> pdIdMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku, Inventory::getPdId, (o1, o2) -> o1));
        if (!Objects.equals(pdIdMap.get(outSku), pdIdMap.get(inSku))) {
            newStoreRecord.setLotType(NumberUtils.INTEGER_ONE);
        }
        storeRecordMapper.insert(newStoreRecord);
    }

    /**
     * 仅转入时获取忽略转入sku列表
     * @param stockTransferItemMapFromDb
     * @param stockTransferItemOpMapFromDb
     * @param stockTransferItemOpDetailMapFromDb
     * @return
     */
    private List<String> getIgnoreSkuForStockTransferInItemOnly(Map<String, StockTransferItemEntity> stockTransferItemMapFromDb,
                                                               Map<Long, List<StockTransferItemOpEntity>> stockTransferItemOpMapFromDb,
                                                               Map<Long, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb) {
        List<String> ignoreSkuList = new ArrayList<>();
        Map<String, Integer> internalTransferSkuInNumMap = new HashMap<>();
        Map<String, Integer> externalTransferSkuInNumMap = new HashMap<>();
        stockTransferItemMapFromDb.forEach((inSkuCode, stockTransferItemEntity) -> {
            if (null == stockTransferItemEntity){
                log.info("库存转换任务明细不存在：{}", inSkuCode);
                return;
            }
            List<StockTransferItemOpEntity> stockTransferItemOpEntityList = stockTransferItemOpMapFromDb.get(stockTransferItemEntity.getId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(stockTransferItemOpEntityList)){
                log.info("库存转换任务OP不存在：{}", inSkuCode);
                return;
            }
            stockTransferItemOpEntityList.forEach(op -> {
                List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntityList = stockTransferItemOpDetailMapFromDb.get(op.getId());
                for (StockTransferItemOpDetailEntity opDetail : stockTransferItemOpDetailEntityList) {
                    int num = calculate(opDetail.getTransferOutNum(), op.getTransferRatio()).intValue();
                    internalTransferSkuInNumMap.merge(inSkuCode, num, Integer::sum);
                    if (null != opDetail.getExternalTransferInNum()) {
                        externalTransferSkuInNumMap.merge(inSkuCode, opDetail.getExternalTransferInNum(), Integer::sum);
                    }
                }
            });
        });
        externalTransferSkuInNumMap.forEach((inSkuCode, externalQuantity) -> {
            Integer internalQuantity = internalTransferSkuInNumMap.get(inSkuCode);
            if (null == internalQuantity) {
                return;
            }
            // 内部转入数量与外部转入数量一致则不需再处理
            if (externalQuantity.equals(internalQuantity)) {
                ignoreSkuList.add(inSkuCode);
            }
        });
        log.info("获取忽略处理转入sku列表：{}", JSON.toJSONString(ignoreSkuList));
        return ignoreSkuList;
    }

}
