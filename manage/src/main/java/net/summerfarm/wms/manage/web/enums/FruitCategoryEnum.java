package net.summerfarm.wms.manage.web.enums;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
public enum FruitCategoryEnum {

    FRESH(1,"鲜果"),

    FRESH_NOT(2,"非鲜果"),

    OTHER(3,"鲜果、非鲜果");


    private Integer type;

    private String desc;

    FruitCategoryEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
