package net.summerfarm.wms.domain.batch.domainobject.query;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 批次关系查询对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueryCostBatchRecord {
    /**
     * sku
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Long warehouseNo;

    /**
     * 来源id
     */
    private List<Long> sourceId;

    /**
     * 更新类型
     */
    private Integer sourceType;

    /**
     * 采购单号
     */
    private String purchaseNo;
}
