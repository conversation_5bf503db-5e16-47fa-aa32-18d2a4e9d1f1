package net.summerfarm.wms.application.materialManage.service.impl;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.wms.application.acl.mq.msg.WmsMaterialTaskNoticeMsg;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialTaskVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskCommandService;
import net.summerfarm.wms.application.materialManage.service.factory.WmsMaterialTaskReceiveAggregateFactory;
import net.summerfarm.wms.application.materialManage.service.factory.WmsMaterialTaskRestoreAggregateFactory;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialTaskReceiveAggregate;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialTaskRestoreAggregate;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.service.WmsMaterialTaskCommandDomainService;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class WmsMaterialTaskCommandServiceImpl implements WmsMaterialTaskCommandService {

    @Autowired
    private WmsMaterialTaskCommandDomainService wmsMaterialTaskCommandDomainService;

    @Autowired
    private GoodsReadFacade goodsReadFacade;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private MqProducer mqProducer;
    @Autowired
    private WarehouseLogisticsFacade warehouseLogisticsFacade;
    @Autowired
    private WarehouseStorageRepository warehouseStorageRepository;

    @Override
    public CommonResult<WmsMaterialTaskVO> receive(WmsMaterialTaskCommandInput input) {
        if (input == null || CollectionUtils.isEmpty(input.getDetailList())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }
        if (input.getWarehouseNo() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        input.setType(StockTaskTypeEnum.MATERIAL_TASK_RECEIVE.getId());

        // 合并请求明细
        Map<String, WmsMaterialTaskDetailCommandInput> detailCommandInputMap = input.getDetailList().stream()
              .filter(s -> s.getMaterialSku()!= null)
              .collect(Collectors.toMap(WmsMaterialTaskDetailCommandInput::getMaterialSku, s -> s,
                      (a, b) -> {
                            a.setQuantity(a.getQuantity() + b.getQuantity());
                            return a;
                      }));
        input.setDetailList(new ArrayList<>(detailCommandInputMap.values()));

        List<String> materialSkuCodeList = input.getDetailList().stream()
                .map(WmsMaterialTaskDetailCommandInput::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialSkuCodeList)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }

        Long materialFirstCategory = productRepository.getMaterialFirstCategory();
        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                input.getTenantId(), materialSkuCodeList);
        // 是否存在非原料
        Long materialSkuCount = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> materialFirstCategory != null &&
                            materialFirstCategory.equals(s.getFirstCategoryId()))
                .count();
        if (materialSkuCount.compareTo((long) materialGoodsInfoDTOMap.size()) != 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求sku存在非原料");
        }

        // 重复利用sku数量
        long reuseMaterialSkuCount = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> "是".equals(s.getPropertyValueByEnum(ProductsPropertyEnum.REUSE)))
                .count();
        if (reuseMaterialSkuCount > 0){
            if (StringUtils.isEmpty(input.getDestination())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "重复利用品需要输入领用用途(来源/去向)");
            }

            Integer storeNo = warehouseLogisticsFacade.queryStoreNoByName(input.getDestination());
            if (storeNo == null){
                WarehouseStorageCenterEntity warehouseStorageCenter =
                        warehouseStorageRepository.selectByWarehouseName(input.getTenantId(), input.getDestination());
                if (warehouseStorageCenter == null){
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "领用用途(来源/去向)输入非城配仓、库存仓名称");
                }
            }
        }

        WmsMaterialTaskReceiveAggregate receiveAggregate = WmsMaterialTaskReceiveAggregateFactory.build(
                input, materialGoodsInfoDTOMap);
        WmsMaterialTaskEntity taskEntity = wmsMaterialTaskCommandDomainService.receive(receiveAggregate);

        // 下发外部
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                mqProducer.send(WmsConstant.MQ_TOPIC, WmsConstant.WMS_MATERIAL_TASK_NOTICE,
                        WmsMaterialTaskNoticeMsg.builder()
                                .id(taskEntity.getId())
                                .build());
            }
        });

        return CommonResult.ok(WmsMaterialTaskAssembler.toWmsMaterialTaskVO(taskEntity));
    }

    @Override
    public CommonResult<WmsMaterialTaskVO> restore(WmsMaterialTaskCommandInput input) {
        if (input == null || CollectionUtils.isEmpty(input.getDetailList())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }
        if (input.getWarehouseNo() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        input.setType(StockTaskTypeEnum.MATERIAL_TASK_RESTORE.getId());

        List<String> skuCodeList = input.getDetailList().stream()
                .map(WmsMaterialTaskDetailCommandInput::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuCodeList)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }


        Long materialFirstCategory = productRepository.getMaterialFirstCategory();
        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                input.getTenantId(), skuCodeList);
        // 是否存在非原料
        Long materialSkuCount = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> materialFirstCategory != null &&
                        materialFirstCategory.equals(s.getFirstCategoryId()))
                .count();
        if (materialSkuCount.compareTo((long) materialGoodsInfoDTOMap.size()) != 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求sku存在非原料");
        }

        // 重复利用sku数量
        long reuseMaterialSkuCount = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> "是".equals(s.getPropertyValueByEnum(ProductsPropertyEnum.REUSE)))
                .count();
        if (reuseMaterialSkuCount > 0){
            if (StringUtils.isEmpty(input.getDestination())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "重复利用品需要输入领用用途(来源/去向)");
            }

            Integer storeNo = warehouseLogisticsFacade.queryStoreNoByName(input.getDestination());
            if (storeNo == null){
                WarehouseStorageCenterEntity warehouseStorageCenter =
                        warehouseStorageRepository.selectByWarehouseName(input.getTenantId(), input.getDestination());
                if (warehouseStorageCenter == null){
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "领用用途(来源/去向)非城配仓、库存仓名称");
                }
            }
        }

        WmsMaterialTaskRestoreAggregate restoreAggregate = WmsMaterialTaskRestoreAggregateFactory.build(
                input, materialGoodsInfoDTOMap);

        WmsMaterialTaskEntity taskEntity = wmsMaterialTaskCommandDomainService.restore(restoreAggregate);

        // 下发外部
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                mqProducer.send(WmsConstant.MQ_TOPIC, WmsConstant.WMS_MATERIAL_TASK_NOTICE,
                        WmsMaterialTaskNoticeMsg.builder()
                        .id(taskEntity.getId())
                        .build());
            }
        });

        return CommonResult.ok(WmsMaterialTaskAssembler.toWmsMaterialTaskVO(taskEntity));
    }


}