package net.summerfarm.wms.application.allocation.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.allocation.AllocationRoadCommandService;
import net.summerfarm.wms.application.allocation.dto.UpdateAllocationRoadQuantityDTO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.util.MessageExtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@MqOrderlyListener(topic = WmsConstant.TOPIC_INVENTORY_ORDERLY,
        tag = WmsConstant.TAG_ALLOCATION_ABNORMAL_UPDATE_ROAD_COST_BATCH,
        consumerGroup = WmsConstant.MQ_WMS_ORDERLY_GID, maxReconsumeTimes = 5)
public class AllocationAbnormalRoadConsumer extends AbstractMqListener<UpdateAllocationRoadQuantityDTO> {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AllocationRoadCommandService allocationRoadCommandService;

    @Override
    public void process(UpdateAllocationRoadQuantityDTO updateAllocationRoadQuantityDTO) {
        log.info("接收到调拨货损/调拨回库更新调拨在途批次库存消息，updateAllocationRoadQuantityDTO:{}", JSON.toJSONString(updateAllocationRoadQuantityDTO));
        if (updateAllocationRoadQuantityDTO == null) {
            return;
        }
        redisUtil.mqOnceLockExecute(MessageExtUtil.getMessageExt().getMsgId(), () -> allocationRoadCommandService.updateAllocationRoadQuantity(
                updateAllocationRoadQuantityDTO.getListNo(), Lists.newArrayList(updateAllocationRoadQuantityDTO)));
    }

}
