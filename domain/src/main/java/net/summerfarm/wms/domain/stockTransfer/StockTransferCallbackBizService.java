package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.dto.TransferCallbackDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/31 11:52
 * @Version 1.0
 */
public interface StockTransferCallbackBizService {

    /**
     * 仅转入操作 (进行库存操作，前提是三方回告的数量已经写入了opDetail表中)
     * @param transferCallbackDTO 转换任务id
     */
    void operateStockTransferInItemOnly(TransferCallbackDTO transferCallbackDTO);

    /**
     * 记录外部回告的数量
     * @param externalTransferInNumMap
     */
    void recordExternalCallbackNum(Map<Long, Integer> externalTransferInNumMap);
}
