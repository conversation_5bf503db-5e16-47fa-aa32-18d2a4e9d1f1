package net.summerfarm.wms.application.qms.service.qms.factory;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.command.QmsInspectionTaskCommitCommandInput;
import net.summerfarm.wms.application.sku.enums.StorageLocation;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.JSONUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.mission.delegate.MissionQueryDelegate;
import net.summerfarm.wms.domain.mission.domainobject.*;
import net.summerfarm.wms.domain.mission.enums.*;
import net.summerfarm.wms.domain.mission.param.command.WmsSubmitDetailExtendCommandParam;
import net.summerfarm.wms.domain.mission.repository.MissionDetailReadRepository;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.mission.util.MissionNoFactory;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionTaskEntity;
import net.summerfarm.wms.domain.qms.enums.QmsInspectRuleFieldTypeEnum;
import net.summerfarm.wms.domain.sku.SkuBarcodeRepository;
import net.summerfarm.wms.domain.sku.domainobject.Sku;
import net.summerfarm.wms.domain.sku.domainobject.SkuBarcode;
import net.summerfarm.wms.facade.purchase.PurchaseFacade;
import net.summerfarm.wms.facade.purchase.dto.PurchasePlanQueryReqDTO;
import net.summerfarm.wms.facade.purchase.dto.PurchasePlanResqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class QmsInspectMissionFactory {

    @Resource
    private MissionNoFactory missionNoFactory;
    @Resource
    private MissionRepository missionRepository;
    @Resource
    private MissionDetailReadRepository missionDetailReadRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private SkuBarcodeRepository skuBarcodeRepository;
    @Resource
    private MissionQueryDelegate missionQueryDelegate;
    @Resource
    private PurchaseFacade purchaseFacade;

    public Mission createMissionForQmsInspection(QmsInspectionTaskEntity qmsInspectionTaskEntity,
                                                 Long tenantId,
                                                 String operatorName,
                                                 Long operatorId) {

        List<String> skus = Collections.singletonList(qmsInspectionTaskEntity.getSku());
        List<Product> products = productRepository.findProductsFromGoodsCenter(
                QueryProduct.builder().warehouseNo(qmsInspectionTaskEntity.getWarehouseNo()).skus(skus).build());
        Map<String, Product> productMap = products.stream().collect(
                Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));
        List<SkuBarcode> skuBarcodes = skuBarcodeRepository.listBySkus(skus);
        Map<String, List<String>> barCodeMap = skuBarcodes.stream().collect(Collectors.groupingBy(SkuBarcode::getSku,
                Collectors.mapping(SkuBarcode::getBarcode, Collectors.toList())));

        // 获取供应商信息
        PurchasePlanResqDTO purchasePlanResqDTO = null;
        if (!StringUtils.isEmpty(qmsInspectionTaskEntity.getBatchNo()) &&
                !StringUtils.isEmpty(qmsInspectionTaskEntity.getSku())) {

            PurchasePlanQueryReqDTO request = new PurchasePlanQueryReqDTO();
            request.setPurchaseNo(qmsInspectionTaskEntity.getBatchNo());
            request.setSku(qmsInspectionTaskEntity.getSku());
            List<PurchasePlanQueryReqDTO> requestList = new ArrayList<>();
            requestList.add(request);

            Map<String, PurchasePlanResqDTO> purchasePlanResMap = purchaseFacade.mapPurchasePlanBatch(requestList);
            purchasePlanResqDTO = purchasePlanResMap.get(PurchaseFacade.mapPurchasePlanBatchKey(
                    qmsInspectionTaskEntity.getBatchNo(), qmsInspectionTaskEntity.getSku()
            ));
        }

        Integer sourceType = qmsInspectionTaskEntity.getInspType();

        // 上架任务
        Mission mission = Mission.builder()
                .missionNo(missionNoFactory.createMissionNo(MissionTypeEnum.INSPECT_TASK.getCode()))
                .warehouseNo(qmsInspectionTaskEntity.getWarehouseNo())
                .state(MissionStateEnum.WAIT_ASSIGN.getCode())
                .sourceType(sourceType)
                .sourceId(qmsInspectionTaskEntity.getId() == null ? null : qmsInspectionTaskEntity.getId().toString())
                .sourceOrderNo(qmsInspectionTaskEntity.getId() == null ? null : qmsInspectionTaskEntity.getId().toString())
//                .pMissionNo(qmsInspectionTaskEntity.getPMissionNo())
//                .pMissionType(qmsInspectionTaskEntity.getPMissionType())
                .cancelTime(0L)
                .creatorName(operatorName)
                .creatorId(operatorId == null ? null : operatorId.toString())
                .missionType(MissionTypeEnum.INSPECT_TASK.getCode())
                .missionQueryDelegate(missionQueryDelegate)
                .tenantId(tenantId)
                .build();

        MissionSourceProperty sourceProperty = MissionSourceProperty.builder()
                .missionType(mission.getMissionType())
                .missionNo(mission.getMissionNo())
                .sourceType(sourceType)
                .sourceOrderNo(qmsInspectionTaskEntity.getId().toString())
                .sourceId(qmsInspectionTaskEntity.getId().toString())
                .warehouseNo(mission.getWarehouseNo())
                .build();

        // 执行单元
        ExecuteUnit unit = ExecuteUnit.builder()
                .missionNo(mission.getMissionNo())
                .cancelTime(0L)
                .unitNo(missionNoFactory.createUnitNo())
                .missionType(mission.getMissionType())
                .sourceType(sourceType)
                .state(ExecuteUnitStateEnum.EXE.getCode())
                .sourceNo(qmsInspectionTaskEntity.getId().toString())
                .carrierCode(null)
                .carrierType(MissionCarrierTypeEnum.UNKNOWN.getCode())
                .warehouseNo(mission.getWarehouseNo())
                .state(ExecuteUnitStateEnum.INIT.getCode())
                .build();

        // 任务明细扩展
        MissionDetailExtend extend = null;
        Product product1 = productMap.get(qmsInspectionTaskEntity.getSku());
        if (product1 == null) {
            throw new BizException("产品SPU不存在" + qmsInspectionTaskEntity.getSku());
        }
        Sku skuInfo = Sku.builder()
                .sku(qmsInspectionTaskEntity.getSku())
                .pdName(product1.getPdName())
                .cargoType(Objects.equals(4, product1.getCategoryType()) ? "鲜果" : "标品")
                .packaging(product1.getPackaging())
                .specification(product1.getSpecification())
                .pic(product1.getPic())
                .temperature(StorageLocation.getTypeById(product1.getTemperature()))
                .barcode(String.join(",", barCodeMap.getOrDefault(qmsInspectionTaskEntity.getSku(), Lists.newArrayList())))
                .build();
        extend = MissionDetailExtend.builder()
                .missionNo(mission.getMissionNo())
                .sku(qmsInspectionTaskEntity.getSku())
                .extend(JSON.toJSONString(skuInfo))
                .warehouseNo(mission.getWarehouseNo())
                .supplierId(purchasePlanResqDTO == null ? null : purchasePlanResqDTO.getSupplierId())
                .pdId(product1.getId())
                .categoryId(product1.getCategoryId() == null ? null : product1.getCategoryId().intValue())
                .categoryName(product1.getCategory())
                .build();


        // 任务明细
        MissionDetail missionDetail = MissionDetail.builder()
                .shouldInQuantity(qmsInspectionTaskEntity.getShouldStoreQuantity())
                .warehouseNo(mission.getWarehouseNo())
                .sku(qmsInspectionTaskEntity.getSku())
                .unitNo(unit.getUnitNo())
                .shelfLife(qmsInspectionTaskEntity.getShelfLife() == null ? null :
                        qmsInspectionTaskEntity.getShelfLife())
                .produceTime(qmsInspectionTaskEntity.getProduceTime() == null ? null :
                        qmsInspectionTaskEntity.getProduceTime())
                .missionNo(mission.getMissionNo())
                .exeQuantity(0)
                .batchNo(qmsInspectionTaskEntity.getBatchNo())
                .extend(extend)
                .state(MissionDetailStateEnum.INIT.getCode())
                .build();

        List<MissionDetail> details = new ArrayList<>();
        details.add(missionDetail);

        mission.setMissionDetails(details);
        mission.setExecuteUnits(Lists.newArrayList(unit));
        mission.setSourceProperty(Lists.newArrayList(sourceProperty));
        return mission;
    }


    public Mission createMissionForCommit(QmsInspectionTaskCommitCommandInput command) {
        Mission mission = missionRepository.findMission(command.getMissionNo());
        if (mission == null) {
            throw new BizException("任务不存在");
        }
        if (MissionStateEnum.FINISH.equalsCode(mission.getState())){
            throw new BizException("任务已完成");
        }
        MissionDetail detail = missionDetailReadRepository.getById(command.getMissionDetailId());
        if (detail == null) {
            throw new BizException("任务明细不存在");
        }
        if (detail.waitExeNum() <= 0) {
            throw new BizException("提交失败,任务已提交请退出刷新重试");
        }

        // 货检默认全入完结任务，明细在扩展
        SubmitDetail submitDetail = SubmitDetail.builder()
                .tenantId(mission.getTenantId())
                .missionNo(mission.getMissionNo())
                .missionType(mission.getMissionType())
                .operatorId(AdminUtil.getCurrentLoginAdminIdV2().toString())
                .cargoOwner(null)
                .opreatorName(AdminUtil.getCurrentLoginAdminNameV2())
                .warehouseNo(mission.getWarehouseNo())
                .sku(detail.getSku())
                .batchNo(detail.getBatchNo())
                .produceTime(detail.getProduceTime())
                .shelfLife(detail.getShelfLife())
                .quantity(detail.getShouldInQuantity())
                .sourceCarrierCode(null)
                .sourceCarrierType(null)
                .targetCarrierCode(null)
                .targetCarrierType(null)
                .unitNo(null)
                .build();

        // 扩展
        WmsSubmitDetailExtendCommandParam submitDetailExtend = WmsSubmitDetailExtendCommandParam.builder()
                .missionSubmitId(null)
                .missionDetailId(detail.getId())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .sku(detail.getSku())
                .warehouseNo(mission.getWarehouseNo())
                .missionNo(mission.getMissionNo())
                .build();
        String submitExtend = JSONUtil.toJSONString(command);
        if (!StringUtils.isEmpty(submitExtend) && submitExtend.length() > WmsConstant.MAX_FIELD_VALUE_LENGTH_10000) {
            // 备注
            InputStream inputStream = new ByteArrayInputStream(submitExtend.getBytes(StandardCharsets.UTF_8));
            String fileName = "qms/QMS提交任务明细" + detail.getId() + mission.getMissionNo() + "_"
                    + DateUtil.formatDateBasic(LocalDateTime.now()) + ".txt";
            OssUploadResult ossUploadResult = OssUploadUtil.upload(fileName, inputStream, OSSExpiredLabelEnum.NO_EXPIRATION);

            submitDetailExtend.setExtend(ossUploadResult.getUrl());
            submitDetailExtend.setExtendType(QmsInspectRuleFieldTypeEnum.OSSADDRESS.getCode());
        } else {
            submitDetailExtend.setExtend(submitExtend);
            submitDetailExtend.setExtendType(QmsInspectRuleFieldTypeEnum.JSON.getCode());
        }

        mission.setSubmitDetails(Arrays.asList(submitDetail));
        mission.setSubmitDetailExtendList(Arrays.asList(submitDetailExtend));

        return mission;
    }

}
