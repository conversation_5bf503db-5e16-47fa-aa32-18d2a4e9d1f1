package net.summerfarm.wms.upload.materialManage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MaterialReceiveUploadDTO  extends ImportExcelBaseDTO implements Serializable {

    @NotEmpty(message = "仓库不存在")
    @ExcelProperty("*仓库")
    private String warehouseName;

    @NotEmpty(message = "物资编号不存在")
    @ExcelProperty("*物资编号")
    private String materialSkuCode;

    @ExcelProperty("物资名称")
    private String materialSkuName;

    @ExcelProperty("一级类目")
    private String firstCategory;

    @NotNull(message = "领用数量不存在")
    @ExcelProperty("*领用数量")
    private Integer quantity;

    @ExcelProperty("物料去向")
    private String destination;

    @ExcelProperty("备注")
    private String remark;
}
