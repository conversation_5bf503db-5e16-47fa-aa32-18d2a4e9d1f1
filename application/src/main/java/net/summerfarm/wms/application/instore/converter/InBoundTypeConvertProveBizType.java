package net.summerfarm.wms.application.instore.converter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.domain.instore.enums.InBoundOrderType;
import net.summerfarm.wms.domain.prove.enums.ProveBizType;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum InBoundTypeConvertProveBizType {
    /**
     * 证明业务类型
     */
    STORE_ALLOCATION_IN(InBoundOrderEnum.STORE_ALLOCATION_IN, ProveBizType.STORE_ALLOCATION_IN),
    PURCHASE_IN(InBoundOrderEnum.PURCHASE_IN, ProveBizType.PURCHASE_IN),
    RETURN_IN(InBoundOrderEnum.RETURN_IN, ProveBizType.RETURN_IN),
    ALLOCATION_ABNORMAL_IN(InBoundOrderEnum.ALLOCATION_ABNORMAL_IN, ProveBizType.ALLOCATION_ABNORMAL_IN),
    AFTER_SALE_IN_NEW(InBoundOrderEnum.AFTER_SALE_IN_NEW, ProveBizType.AFTER_SALE_IN_NEW),
    LACK_GOODS_APPROVED(InBoundOrderEnum.LACK_GOODS_APPROVED, ProveBizType.LACK_GOODS_APPROVED),
    SKIP_STORE_IN(InBoundOrderEnum.SKIP_STORE_IN, ProveBizType.SKIP_STORE_IN),
    STOP_STORE_IN(InBoundOrderEnum.STOP_STORE_IN, ProveBizType.STOP_STORE_IN),
    OUT_MORE_IN(InBoundOrderEnum.OUT_MORE_IN, ProveBizType.OUT_MORE_IN),
    CROSS_WAREHOUSE_IN(InBoundOrderEnum.CROSS_WAREHOUSE_IN, ProveBizType.CROSS_WAREHOUSE_IN),
    UNKNOWN(InBoundOrderEnum.UNKNOWN, ProveBizType.UNKNOWN),
    ;

    public static InBoundTypeConvertProveBizType convert(InBoundOrderEnum param) {
        return Arrays.stream(InBoundTypeConvertProveBizType.values())
                .filter(o -> o.getInBoundOrderEnum().equals(param))
                .findFirst().orElse(InBoundTypeConvertProveBizType.UNKNOWN);
    }

    InBoundOrderEnum inBoundOrderEnum;
    ProveBizType proveBizType;
}
