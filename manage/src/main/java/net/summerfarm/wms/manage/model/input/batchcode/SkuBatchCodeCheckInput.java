package net.summerfarm.wms.manage.model.input.batchcode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2024/1/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SkuBatchCodeCheckInput implements Serializable {
    private static final long serialVersionUID = 2256500550627301027L;

    /**
     * 仓库编号
     */
    @NotNull(message = "请指定仓库")
    private Long warehouseNo;

    /**
     * sku编码
     */
    @NotEmpty(message = "请指定sku")
    private String sku;
}
