<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.goods.WmsSkuBizDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="is_new" jdbcType="TINYINT" property="isNew"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, sku, is_new
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_sku_biz
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectBySku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_sku_biz
        where warehouse_no = #{warehouseNo} and sku = #{sku}
    </select>
    <select id="selectBySkus" resultMap="BaseResultMap">
        /*FORCE_MASTER*/  select
        <include refid="Base_Column_List"/>
        from wms_sku_biz
        where warehouse_no = #{warehouseNo} and sku in
        <foreach collection="skus" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_sku_biz
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO" useGeneratedKeys="true">
        insert into wms_sku_biz (create_time, update_time, warehouse_no,
                                 sku, is_new)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=BIGINT},
                #{sku,jdbcType=VARCHAR}, #{isNew,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO" useGeneratedKeys="true">
        insert into wms_sku_biz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="isNew != null">
                is_new,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="isNew != null">
                #{isNew,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO">
        update wms_sku_biz
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="isNew != null">
                is_new = #{isNew,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO">
        update wms_sku_biz
        set create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            sku          = #{sku,jdbcType=VARCHAR},
            is_new       = #{isNew,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO">
        update wms_sku_biz
        set is_new = #{isNew,jdbcType=TINYINT}
        where warehouse_no = #{warehouseNo}
          and sku = #{sku}
    </update>
</mapper>