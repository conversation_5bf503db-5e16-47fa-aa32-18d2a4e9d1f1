package net.summerfarm.wms.infrastructure.dao.materialManage.dataobject;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialTask {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * 类型，96-领用，97-归还
	 */
	private Integer type;

	/**
	 * 物料任务编码
	 */
	private String materialTaskCode;

	/**
	 * 领用用途
	 */
	private String destination;

	/**
	 * 来源类型
	 */
	private Integer sourceType;

	/**
	 * 来源id
	 */
	private Long sourceId;

	/**
	 * 来源单号
	 */
	private String sourceCode;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 备注
	 */
	private String remark;


}