package net.summerfarm.wms.manage.web.mapper.offline;

import net.summerfarm.wms.manage.model.domain.po.SkuSaleStatisticsPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/12/6
 */
@Repository
public interface SkuSaleStatisticsMapper {

    /**
     * 根据日期标识查询sku销量信息
     *
     * <AUTHOR>
     * @Date 2022/12/9 10:24
     * @param dateFlag 日期标识
     **/
    List<SkuSaleStatisticsPO> selectListByDateFlag(String dateFlag);
}
