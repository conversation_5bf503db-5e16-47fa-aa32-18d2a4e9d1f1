package net.summerfarm.wms.facade.wnc.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class WarehouseStorageFenceRuleRespDTO implements Serializable {

    /**
     * 仓库到客户的距离 km
     */
    private BigDecimal distance;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 截单时间
     */
    private LocalTime closeTime;

    /**
     * 配送截单日期时间
     */
    private LocalDateTime deliveryCloseTime;

    /**
     *是否日配的标识 0日配，1非日配
     */
    private Integer isEveryDayFlag;

    /** 商品子类类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer skuSubType;
}
