package net.summerfarm.wms.manage.web.convert;

import net.summerfarm.wms.manage.model.domain.GoodsCheckTaskDO;
import net.summerfarm.wms.manage.model.domain.po.StoreGoodsTaskPO;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
public class StoreGoodsTaskConvert {

    /**
     * 对象转换
     *
     * <AUTHOR>
     * @Date 2022/11/17 14:27
     * @param createDO 领域对象
     * @return net.summerfarm.model.domain.StoreGoodsTaskPO
     **/
    public static StoreGoodsTaskPO convertTaskPO(GoodsCheckTaskDO createDO) {
        return StoreGoodsTaskPO.builder()
                .taskSource(createDO.getTaskSource())
                .taskStatus(createDO.getTaskStatus())
                .warehouseNo(createDO.getWarehouseNo())
                .pdId(createDO.getPdId())
                .sku(createDO.getSku())
                .skuName(createDO.getSkuName())
                .weight(createDO.getWeight())
                .storageLocation(createDO.getStorageLocation())
                .volume(createDO.getVolume())
                .weightNum(createDO.getWeightNum())
                .pushStatus(createDO.getPushStatus())
                .creator(createDO.getCreator())
                .operator(createDO.getOperator())
                .gmtCreated(System.currentTimeMillis())
                .gmtModified(System.currentTimeMillis())
                .unit(createDO.getUnit())
                .isDomestic(createDO.getIsDomestic())
                .storageLocation(createDO.getStorageLocation())
                .qualityTime(createDO.getQualityTime())
                .qualityTimeUnit(createDO.getQualityTimeUnit())
                .qualityTimeType(createDO.getQualityTimeType())
                .build();
    }

}
