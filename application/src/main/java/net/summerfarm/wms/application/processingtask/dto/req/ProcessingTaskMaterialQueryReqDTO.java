package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProcessingTaskMaterialQueryReqDTO implements Serializable {


    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 加工任务原料编码
     */
    private String processingMaterialSkuCode;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

}
