package net.summerfarm.wms.domain.download.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:下载中心枚举类
 * date: 2023/1/9 14:06
 *
 * <AUTHOR>
 */

public interface WmsDownloadCenterEnum {

    @Getter
    @AllArgsConstructor
    enum Types{
        QI_NIU(0, "七牛云oss"),
        ALI(1, "阿里云oss"),
        ;

        private final Integer value;
        private final String content;
    }

    @Getter
    @AllArgsConstructor
    enum Status{
        WAIT_UPLOAD(0, "待上传"),
        SUCCESS(1, "上传成功"),
        FAIL(2, "上传失败"),
        EXPIRED(3, "已过期"),
        ;

        private final Integer value;
        private final String content;
    }



}
