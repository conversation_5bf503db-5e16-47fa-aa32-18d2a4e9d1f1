package net.summerfarm.wms.infrastructure.repository.order.converter;

import net.summerfarm.wms.domain.order.domainobject.OrderDelivery;
import net.summerfarm.wms.domain.order.domainobject.OrderHeyTea;
import net.summerfarm.wms.infrastructure.dao.order.dataobject.OrderDeliveryDO;
import net.summerfarm.wms.infrastructure.dao.order.dataobject.OrderHeyTeaDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderHeyTeaConverter {

    OrderHeyTeaConverter INSTANCE = Mappers.getMapper(OrderHeyTeaConverter.class);

    OrderHeyTea convert(OrderHeyTeaDO orderHeyTeaDO);

    List<OrderHeyTea> convertList(List<OrderHeyTeaDO> orderHeyTeaDOList);

    OrderDelivery convertDelivery(OrderDeliveryDO orderDeliveryDO);

    List<OrderDelivery> convertDeliveryList(List<OrderDeliveryDO> orderDeliveryDOList);


}
