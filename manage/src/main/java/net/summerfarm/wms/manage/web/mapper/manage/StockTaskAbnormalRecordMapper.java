package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StockTaskAbnormalRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/11/30
 */
@Repository
public interface StockTaskAbnormalRecordMapper {

    List<StockTaskAbnormalRecord> select(@Param("stockTaskId") Integer stockTaskId, @Param("sku") String sku);

    void insert(StockTaskAbnormalRecord record);

    Integer sumAbnormalQuantityByStockTaskId(@Param("stockTaskId") Integer stockTaskId);
}
