package net.summerfarm.wms.manage.web.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/8/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTaskDetailOutStatusQueryReq implements Serializable {
    private static final long serialVersionUID = 4031801564678169712L;

    private Integer stockTaskId;
}
