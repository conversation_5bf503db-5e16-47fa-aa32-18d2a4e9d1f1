package net.summerfarm.wms.manage.web.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.manage.web.enums.OptionFlagTypeEnum;
import net.summerfarm.wms.manage.web.enums.StockTaskMailPushResultEnum;
import net.summerfarm.wms.manage.model.domain.StockTask;
import net.summerfarm.wms.manage.model.domain.StockTaskMailPushRecord;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskMailPushRecordMapper;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskMapper;
import net.summerfarm.wms.manage.web.utils.OptionFlagUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Component
@Async
@Slf4j
public class StockTaskMailPush {

    @Resource
    private JavaMailSenderImpl mailSender;

    @Resource
    private StockTaskMapper stockTaskMapper;

    @Resource
    private StockTaskMailPushRecordMapper stockTaskMailPushRecordMapper;

    /**
     * 邮件推送状态掩码
     */
    private static final Integer MAIL_PUSH_STATE_MASK = 0x1;

    /**
     * 邮件推送状态反码
     */
    private static final Integer MAIL_PUSH_STATE_COMPLEMENT = 0xfffffffe;

    static {
        System.setProperty("mail.mime.splitlongparameters", "false");
        System.setProperty("mail.mime.charset", "UTF-8");
    }

    /**
     *
     * @param subject 标题
     * @param text 文本内容
     * @param tos 接收人
     * @param ccs 抄送人
     * @param excelParts excel附件 文件名 -> excel
     * @throws Exception
     */
    public void sendMailAsync(String subject, String text, String[] tos, String[] ccs, Map<String, Workbook> excelParts, Integer adminId , List<Integer> taskIds) throws Exception {
        log.info("开始发送邮件");
        MimeMessage message = mailSender.createMimeMessage();
        message.setFrom(new InternetAddress(Objects.requireNonNull(mailSender.getUsername())));
        for (String to: tos) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
        }
        if (ccs != null) {
            for (String cc: ccs) {
                message.addRecipients(Message.RecipientType.CC, cc);
            }
        }

        message.setSubject(subject);
        message.addHeader("charset", "UTF-8");

        Multipart multipart = new MimeMultipart();
        //文本内容
        log.info("文本内容:{}",text);
        if (!org.springframework.util.StringUtils.isEmpty(text)) {
            MimeBodyPart contentPart = new MimeBodyPart();
            contentPart.setText(text, "UTF-8");
            contentPart.setHeader("Content-Type","text/html; charset=UTF-8");
            multipart.addBodyPart(contentPart);
        }
        // 附件内容 文件名 -> excel
        if (!CollectionUtils.isEmpty(excelParts)) {
            log.info("处理附件内容开始");
            excelParts.forEach((fileName,workbook) -> {
                MimeBodyPart excelPart = new MimeBodyPart();
                ByteArrayOutputStream baos = null;
                ByteArrayInputStream bais = null;
                try {
                    baos = new ByteArrayOutputStream();
                    workbook.write(baos);
                    byte[] bt = baos.toByteArray();
                    bais = new ByteArrayInputStream(bt, 0, bt.length);
                    DataSource source = new ByteArrayDataSource(bais, "application/msexcel");
                    excelPart.setDataHandler(new DataHandler(source));
                    excelPart.setFileName(MimeUtility.encodeText(fileName));
                    multipart.addBodyPart(excelPart);
                } catch (Exception e) {
                    log.error(Global.collectExceptionStackMsg(e));
                } finally {
                    try {
                        Objects.requireNonNull(baos).close();
                        Objects.requireNonNull(bais).close();
                    } catch (IOException e) {
                        log.error(Global.collectExceptionStackMsg(e));
                    }
                }

            });
        }
        log.info("执行发送邮件");
        message.setContent(multipart);
        message.setSentDate(new Date());
        message.saveChanges();
        try {
            mailSender.send(message);
            if (!CollectionUtils.isEmpty(taskIds)) {
                List<StockTaskMailPushRecord> mailRecordList = new ArrayList<>();
                taskIds.forEach(id -> {
                    StockTaskMailPushRecord record = new StockTaskMailPushRecord()
                            .setAdminId(adminId)
                            .setCreateTime(LocalDateTime.now())
                            .setStockTaskNo(id)
                            .setState(StockTaskMailPushResultEnum.SUCCESS.getCode());
                    mailRecordList.add(record);
                });
                stockTaskMailPushRecordMapper.batchInsert(mailRecordList);
            }
        } catch (Exception e) {
            if (!CollectionUtils.isEmpty(taskIds)) {
                List<StockTaskMailPushRecord> mailRecordList = new ArrayList<>();
                taskIds.forEach(id -> {
                    StockTaskMailPushRecord record = new StockTaskMailPushRecord()
                            .setAdminId(adminId)
                            .setCreateTime(LocalDateTime.now())
                            .setStockTaskNo(id)
                            .setState(StockTaskMailPushResultEnum.FAILED.getCode())
                            .setFailReason(e.getMessage());
                    mailRecordList.add(record);
                    // 出库任务状态设置为未推送
                    StockTask stockTask = stockTaskMapper.selectOptionFlagById(id);
                    stockTask.setId(id);
                    stockTask.setOptionFlag(OptionFlagUtil.setValueIneffective(stockTask.getOptionFlag(),
                            OptionFlagTypeEnum.STOCK_TASK_MAIL_PUSH_STATE.getCode()));
                    stockTaskMapper.updateOptionFlagById(stockTask);
                });
                stockTaskMailPushRecordMapper.batchInsert(mailRecordList);
            }
        }
    }
}
