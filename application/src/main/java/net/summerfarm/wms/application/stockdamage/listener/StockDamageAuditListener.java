package net.summerfarm.wms.application.stockdamage.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.manage.web.service.StockDamageTaskService;
import net.summerfarm.wms.mq.enums.AuditTypeEnums;
import net.summerfarm.wms.mq.stockdamage.StockDamageAuditDTO;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: dongcheng
 * @date: 2023/12/1
 * 货损审批sql
 */
@Slf4j
@Component
@MqListener(topic = Global.STOCK_TASK,
        tag = "tag_wms_stock_task_damage_audit",
        consumerGroup = Global.MQ_GROUP)
public class StockDamageAuditListener extends AbstractMqListener<StockDamageAuditDTO> {

    @Resource
    private StockDamageTaskService stockDamageTaskService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void process(StockDamageAuditDTO dto) {
        log.info("货损审批开始:{}", JSON.toJSONString(dto));
        if (Objects.isNull(dto) || Objects.isNull(dto.getStockDamageTaskId())) {
            log.error("货损审批错误:审批内容没有任务id \n");
            return;
        }
        redisUtil.executeCallableByJustTryOneTime(RedisKeys.buildStockDamageAuditKey(dto.getStockDamageTaskId()), 2L,
                TimeUnit.MINUTES,
                () -> run(dto));
    }


    /**
     * 执行货损审批回调
     *
     * @param dto 货损审批回调参数
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean run(StockDamageAuditDTO dto) {
        try {
            if (AuditTypeEnums.AGREE.getCode().equals(dto.getAuditOperate())) {
                stockDamageTaskService.auditStockDamageSuccess(dto.getStockDamageTaskId(), dto.getAdminId());
            } else if (AuditTypeEnums.REFUSE.getCode().equals(dto.getAuditOperate())) {
                stockDamageTaskService.auditStockDamageRefuse(dto.getStockDamageTaskId(), dto.getAdminId());
            } else if (AuditTypeEnums.TERMINATE.getCode().equals(dto.getAuditOperate())) {
                stockDamageTaskService.auditStockDamageRefuse(dto.getStockDamageTaskId(), dto.getAdminId());
            } else {
                log.error("无法识别的货损审批消息 \n");
                return false;
            }
        } catch (Exception e) {
            log.error("货损审批异常", e);
            return false;
        }
        return true;
    }

}
