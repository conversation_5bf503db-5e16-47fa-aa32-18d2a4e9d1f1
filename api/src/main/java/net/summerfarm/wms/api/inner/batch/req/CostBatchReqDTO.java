package net.summerfarm.wms.api.inner.batch.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  15:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostBatchReqDTO {

    /**
     * 成本批次id
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 生产批次id
     */
    private Long produceBatchId;

    /**
     * 采购批次号
     */
    private String purchaseNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 操作类型
     */
    private Integer operationType;


    /**
     * 操作人名称
     */
    private String operationName;

    /**
     * 来源id
     */
    private Long sourceId;

    private String remake;

    /**
     * 成本批次类型
     * LotTypeEnum
     */
    private Integer lotType;

    /**
     * 金额
     */
    private BigDecimal cost;


    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    List<CostBatchDetailReqDTO> costBatchDetailResDTOList;


}
