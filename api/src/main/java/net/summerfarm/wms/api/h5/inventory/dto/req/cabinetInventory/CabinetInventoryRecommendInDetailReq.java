package net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetInventoryRecommendInDetailReq implements Serializable {

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private Integer warehouseNo;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

//    /**
//     * 库位编码，可空
//     */
//    @ApiModelProperty(value = "库位编码")
//    private String cabinetCode;

    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qualityDate;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次编码")
    private String skuBatchCode;

    /**
     * 新增库存
     */
    @ApiModelProperty(value = "占用库存")
    private Integer addQuantity;

    /**
     * 操作单据类型
     *
     * @see net.summerfarm.wms.domain.StoreRecord.enums.CabinetRecommendType
     */
    @ApiModelProperty(value = "操作单据类型")
    private Integer cabinetRecommendType;
}
