package net.summerfarm.wms.infrastructure.repository.qms;

import net.summerfarm.wms.infrastructure.converter.qms.QmsInspectionScaleConverter;
import net.summerfarm.wms.domain.qms.repository.QmsInspectionScaleCommandRepository;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionScaleEntity;
import net.summerfarm.wms.domain.qms.param.command.QmsInspectionScaleCommandParam;
import net.summerfarm.wms.infrastructure.dao.qms.QmsInspectionScaleMapper;
import net.summerfarm.wms.infrastructure.dao.qms.dataobject.QmsInspectionScale;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-04-02 09:34:21
* @version 1.0
*
*/
@Repository
public class QmsInspectionScaleCommandRepositoryImpl implements QmsInspectionScaleCommandRepository {

    @Autowired
    private QmsInspectionScaleMapper qmsInspectionScaleMapper;
    @Override
    public QmsInspectionScaleEntity insertSelective(QmsInspectionScaleCommandParam param) {
        QmsInspectionScale qmsInspectionScale = QmsInspectionScaleConverter.toQmsInspectionScale(param);
        qmsInspectionScaleMapper.insertSelective(qmsInspectionScale);
        return QmsInspectionScaleConverter.toQmsInspectionScaleEntity(qmsInspectionScale);
    }

    @Override
    public int updateSelectiveById(QmsInspectionScaleCommandParam param){
        return qmsInspectionScaleMapper.updateSelectiveById(QmsInspectionScaleConverter.toQmsInspectionScale(param));
    }


    @Override
    public int remove(Long id) {
        return qmsInspectionScaleMapper.remove(id);
    }

    @Override
    public int batchInsert(List<QmsInspectionScaleCommandParam> paramList) {
        List<QmsInspectionScale> qmsInspectionScaleList = QmsInspectionScaleConverter.toQmsInspectionScaleList(paramList);
        return qmsInspectionScaleMapper.batchInsertSelective(qmsInspectionScaleList);
    }

    @Override
    public int batchUpsetDeleted(List<Long> idList) {
        return qmsInspectionScaleMapper.batchUpsetDeleted(idList, System.currentTimeMillis());
    }
}