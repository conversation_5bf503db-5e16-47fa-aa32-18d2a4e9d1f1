package net.summerfarm.wms.api.h5.common;

import com.sun.org.apache.xpath.internal.operations.Bool;
import net.summerfarm.wms.api.h5.common.dto.req.CabinetCheckCommand;
import net.summerfarm.wms.api.h5.common.dto.req.ContainerCreateCommand;
import net.summerfarm.wms.api.h5.common.dto.req.ContainerUpdateCommand;
import net.summerfarm.wms.api.h5.common.dto.req.DefaultContainerCreateCommand;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/5
 */
public interface ContainerCommandService {

    /**
     * 创建容器
     * @param containerCreateCommand
     * @return
     */
    Boolean createContainer(ContainerCreateCommand containerCreateCommand);

    /**
     * 更新容器
     * @param containerUpdateCommand
     * @return
     */
    Boolean updateContainer(ContainerUpdateCommand containerUpdateCommand);

    /**
     * 占用容器
     * @param warehouseNo 仓库编码
     * @param containerCode 容器编码
     * @return true：占用成功，false：占用失败
     */
    Boolean occupyContainer(Integer warehouseNo, String containerCode);

    /**
     * 释放容器
     * @param warehouseNo 仓库编码
     * @param containerCode 容器编码
     * @return true：释放成功，false：释放失败
     */
    Boolean releaseContainer(Integer warehouseNo, String containerCode);

    /**
     * 创建默认容器
     * @param containerCreateCommand
     * @return
     */
    Boolean createDefaultContainer(DefaultContainerCreateCommand containerCreateCommand);

    /**
     * 校验容器
     * @param command c
     * @return r
     */
    Boolean checkContainer(CabinetCheckCommand command);
}
