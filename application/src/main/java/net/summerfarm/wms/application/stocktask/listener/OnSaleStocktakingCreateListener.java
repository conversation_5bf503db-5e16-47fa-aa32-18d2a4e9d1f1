package net.summerfarm.wms.application.stocktask.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.outstore.event.PickingFinishEvent;
import net.summerfarm.wms.application.stocktask.listener.event.OnSaleStocktakingCreateEvent;
import net.summerfarm.wms.application.stocktask.listener.handler.OnSaleStocktakingCreateHandler;
import net.summerfarm.wms.application.stocktask.listener.handler.PickingFinishHandler;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqPrimary;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/2
 */
@Slf4j
@Component
@MqListener(topic = Global.MQ_TOPIC,
        tag = WmsConstant.ONSALE_STOCKTAKING_EXE_TAG,
        consumerGroup = WmsConstant.MQ_WMS_GID)
public class OnSaleStocktakingCreateListener extends AbstractMqListener<OnSaleStocktakingCreateEvent> {

    @Resource
    private OnSaleStocktakingCreateHandler onSaleStocktakingCreateHandler;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(OnSaleStocktakingCreateEvent onSaleStocktakingCreateEvent) {
        log.info("动销盘点任务生成：{}", JSON.toJSONString(onSaleStocktakingCreateEvent));
        if (null == onSaleStocktakingCreateEvent || StringUtils.isBlank(onSaleStocktakingCreateEvent.getMissionNo())) {
            return;
        }
        redisUtil.executeCallableByJustTryOneTime(RedisKeys.buildOnSaleTakingConsumeExKey(onSaleStocktakingCreateEvent.getMissionNo()),
                5L,
                TimeUnit.MINUTES,
                () -> {
                    onSaleStocktakingCreateHandler.executeNow(onSaleStocktakingCreateEvent);
                    return true;
                });
        log.info("动销盘点任务生成消费完成：{}", onSaleStocktakingCreateEvent.getMissionNo());
    }


}
