server:
  tomcat:
    basedir: ../logs/tomcat
    accesslog:
      enabled: true
      pattern: "%t %a %r %s (%D ms)"
  servlet:
    session:
      timeout: 3600
    encoding:
      charset: UTF-8
    context-path: /summerfarm-wms
  port: 80
spring:
  application:
    id: summerfarm-wms
    name: summerfarm-wms
  profiles:
    active: dev2
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000

logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  ms:
    maximumSize: 200


# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

log-path: ${APP_LOG_DIR:../log}


