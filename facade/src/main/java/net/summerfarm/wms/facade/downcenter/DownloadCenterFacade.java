package net.summerfarm.wms.facade.downcenter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.common.exceptions.ErrorBizException;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.DubboResponseUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/12/15
 */
@Component
@Slf4j
public class DownloadCenterFacade {

    private final String BIZ_ERR_PRE = "DC-BIZ-";

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    /**
     * 初始化文件下载记录
     * 注意返回为空的话需要自己处理
     * bizType 类型对应 download_center_type_config 表中的类型
     *
     * @param adminId            操作人
     * @param bizType            业务类型文件
     * @param fileName           文件名称
     * @param fileExpiredDayEnum 文件过期时间
     * @param params             查询参数（json格式）
     * @return 返回文件id
     */
    public Long initDownLoadFileRecord(Long adminId, Integer bizType, String fileName, DownloadCenterEnum.FileExpiredDayEnum fileExpiredDayEnum, String params) {
        DownloadCenterInitReq downloadCenterInitReq = new DownloadCenterInitReq();
        downloadCenterInitReq.setAdminId(Objects.isNull(adminId) ? null : adminId);
        downloadCenterInitReq.setBizType(bizType);
        downloadCenterInitReq.setFileName(fileName);
        downloadCenterInitReq.setParams(params);
        downloadCenterInitReq.setFileExpiredDay(fileExpiredDayEnum);

        log.info("下载中心文件初始化入参: {}", JSON.toJSONString(downloadCenterInitReq));

        DubboResponse<DownloadCenterResp> downloadCenterRespDubboResponse = downloadCenterProvider.initRecord(downloadCenterInitReq);
        DubboResponseUtil.isSuccess(downloadCenterRespDubboResponse, ErrorCodeNew.SAAS_START_DOWNLOAD_FAIL);
        DownloadCenterResp data = downloadCenterRespDubboResponse.getData();
        if (Objects.isNull(data) || Objects.isNull(data.getResId())) {
            log.error("文件初始化失败: {}", JSON.toJSONString(data));
            throw new ErrorBizException(ErrorCodeNew.SAAS_START_DOWNLOAD_FAIL);
        }
        return data.getResId();
    }

    /**
     * 更新下载中心文件状态
     *
     * @param fileId    文件id
     * @param bizStatus 文件下载状态
     */
    public void updateDownLoadFileRecord(Long fileId, String filePath, DownloadCenterEnum.BizStatusEnum bizStatus) {
        log.info("下载中心文件状态更新入参:fileId:{}, filePath:{}, bizStatus:{}", fileId, filePath, bizStatus.getStatus());
        DownloadCenterUploadReq uploadReq = new DownloadCenterUploadReq();
        uploadReq.setResId(fileId);
        uploadReq.setBizStatus(bizStatus);
        uploadReq.setFilePath(filePath);
        DubboResponse<Boolean> booleanDubboResponse = downloadCenterProvider.uploadFile(uploadReq);
        log.info("下载中心文件状态更新返回:response:{}", JSON.toJSONString(booleanDubboResponse));
        if (!booleanDubboResponse.isSuccess()) {
            String code = booleanDubboResponse.getCode();
            if (StringUtils.isEmpty(code)) {
                throw new ErrorBizException(ErrorCodeNew.SAAS_START_DOWNLOAD_FAIL);
            }
            if (code.startsWith(BIZ_ERR_PRE)) {
                log.warn("下载中心业务异常，可以忽视");
                return;
            }
            // 下载中心调用失败 异常情况
            throw new ErrorBizException(ErrorCodeNew.SAAS_START_DOWNLOAD_FAIL);
        }
    }
}
