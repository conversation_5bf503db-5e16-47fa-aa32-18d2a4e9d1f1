package net.summerfarm.wms.infrastructure.repository.prove.impl;

import com.google.common.collect.Lists;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.domain.prove.domainobject.FindBatchProve;
import net.summerfarm.wms.domain.prove.domainobject.FindProve;
import net.summerfarm.wms.domain.prove.domainobject.Prove;
import net.summerfarm.wms.domain.prove.repository.ProveQueryRepository;
import net.summerfarm.wms.infrastructure.dao.prove.ProveDAO;
import net.summerfarm.wms.infrastructure.dao.prove.dataobject.BatchProveParamDO;
import net.summerfarm.wms.infrastructure.dao.prove.dataobject.ProveDO;
import net.summerfarm.wms.infrastructure.repository.prove.converter.ProveConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class ProveQueryRepositoryImpl implements ProveQueryRepository {

    @Resource
    private ProveDAO proveDAO;

    @Override
    public Prove findProve(FindProve findProve) {
        ProveDO proveDO = proveDAO.selectBySourceId(findProve.getSourceId(), findProve.getType(), findProve.getTenantId());
        return ProveConvert.INSTANCE.convert(proveDO);
    }

    @Override
    public List<Prove> findBatchProves(FindBatchProve findBatchProve) {
        List<ProveDO> proves = proveDAO.listBySkusAndBatches(findBatchProve.getSkus(), findBatchProve.getBatches());
        if (CollectionUtils.isEmpty(proves)) {
            return Lists.newArrayList();
        }
        List<Long> proveIds = proves.stream().map(ProveDO::getId).collect(Collectors.toList());
        List<ProveDO> proveDOS = proveDAO.listByIds(proveIds);
        return proveDOS.stream().map(ProveConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<Prove> findBatchProvesProductionDate(FindBatchProve findBatchProve) {
        List<ProveDO> proves = proveDAO.listBySkusAndBatchesProductionDate(findBatchProve.getSkus(), findBatchProve.getBatches(), findBatchProve.getProductionDates());
        if (CollectionUtils.isEmpty(proves)) {
            return Lists.newArrayList();
        }
        List<Long> proveIds = proves.stream().map(ProveDO::getId).collect(Collectors.toList());
        List<ProveDO> proveDOS = proveDAO.listByIds(proveIds);
        return proveDOS.stream().map(ProveConvert.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public Prove findBatchProve(String sku, String batch) {
        if (StringUtils.isEmpty(sku) || StringUtils.isEmpty(batch)){
            return null;
        }

        FindBatchProve findBatchProve = new FindBatchProve();
        findBatchProve.setSkus(Arrays.asList(sku));
        findBatchProve.setBatches(Arrays.asList(batch));
        List<Prove> proveList = findBatchProves(findBatchProve);
        return CollectionUtils.isEmpty(proveList) ? null : proveList.get(0);
    }

    /**
     * 根据批次sku查询证件信息
     *
     * @param findBatchProve 按照批次信息查询政内容内容
     * @return 返回证件信息列表
     */
    @Override
    public List<Prove> findByBatchAndSku(FindBatchProve findBatchProve) {
        if (Objects.isNull(findBatchProve)) {
            return Lists.newArrayList();
        }
        BatchProveParamDO batchProveParamDO = ProveConvert.INSTANCE.convert(findBatchProve);
        List<ProveDO> proveList = proveDAO.selectByBatchAndSku(batchProveParamDO);
        if (CollectionUtils.isEmpty(proveList)) {
            return Lists.newArrayList();
        }
        return proveList.stream()
                .map(ProveConvert.INSTANCE::convert)
                .collect(Collectors.toList());

    }

    /**
     * 根据批次sku查询证件信息
     *
     * @param findBatchProve 按照批次信息查询政内容内容
     * @return 返回证件信息列表
     */
    @Override
    public List<Prove> findByBatchAndSkuList(FindBatchProve findBatchProve) {
        if (Objects.isNull(findBatchProve)) {
            return Lists.newArrayList();
        }
        BatchProveParamDO batchProveParamDO = ProveConvert.INSTANCE.convert(findBatchProve);
        List<ProveDO> proveList = proveDAO.selectByBatchAndSkuList(batchProveParamDO);
        if (CollectionUtils.isEmpty(proveList)) {
            return Lists.newArrayList();
        }
        return proveList.stream()
                .map(ProveConvert.INSTANCE::convert)
                .collect(Collectors.toList());

    }
}
