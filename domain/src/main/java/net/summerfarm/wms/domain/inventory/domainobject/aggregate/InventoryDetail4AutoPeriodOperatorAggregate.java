package net.summerfarm.wms.domain.inventory.domainobject.aggregate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InventoryDetail4AutoPeriodOperatorAggregate implements Serializable {

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;
}
