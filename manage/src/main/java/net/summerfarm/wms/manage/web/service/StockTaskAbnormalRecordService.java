package net.summerfarm.wms.manage.web.service;

import net.summerfarm.wms.manage.model.domain.StockTaskAbnormalRecord;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/11/30
 */
public interface StockTaskAbnormalRecordService {

    /**
     * 查询异常记录
     * @param stockTaskId
     * @param sku
     * @return
     */
    List<StockTaskAbnormalRecord> select(Integer stockTaskId, String sku);

    /**
     * 添加异常记录
     * @param insert
     */
    void insert(StockTaskAbnormalRecord insert);

    /**
     * 统计异常数量
     *
     * @param stockTaskId 异常数量
     * @return 返回异常数量
     */
    int sumAbnormalQuantityByStockTaskId(Integer stockTaskId);
}
