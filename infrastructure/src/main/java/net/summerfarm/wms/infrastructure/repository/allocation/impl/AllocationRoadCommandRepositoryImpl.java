package net.summerfarm.wms.infrastructure.repository.allocation.impl;

import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.allocation.AllocationRoadCommandRepository;
import net.summerfarm.wms.domain.allocation.domainobject.InventoryAllocationRoadCostBatch;
import net.summerfarm.wms.infrastructure.dao.allocation.InventoryAllocationRoadCostBatchDAO;
import net.summerfarm.wms.infrastructure.dao.allocation.dataobject.InventoryAllocationRoadCostBatchDO;
import net.summerfarm.wms.infrastructure.repository.allocation.converter.AllocationRoadCostBatchConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AllocationRoadCommandRepositoryImpl implements AllocationRoadCommandRepository {

    @Autowired
    private InventoryAllocationRoadCostBatchDAO allocationRoadCostBatchDAO;

    @Override
    public void batchInsert(List<InventoryAllocationRoadCostBatch> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }
        List<InventoryAllocationRoadCostBatchDO> allocationRoadCostBatchDOList = AllocationRoadCostBatchConverter.toInventoryAllocationRoadCostBatchDOList(recordList);
        allocationRoadCostBatchDAO.batchInsert(allocationRoadCostBatchDOList);
    }

    @Override
    public int updateRoadQuantityById(Integer changeRoadQuantity, Long id) {
        ExceptionUtil.checkAndThrow(changeRoadQuantity != null, "更新的调拨在途库存数量不能为空");
        ExceptionUtil.checkAndThrow(id != null, "调拨在途批次库存id不能为空");
        return allocationRoadCostBatchDAO.updateRoadQuantityById(changeRoadQuantity, id);
    }

    @Override
    public void removeByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        allocationRoadCostBatchDAO.removeByIdList(idList);
    }
}
