package net.summerfarm.wms.application.acl.mq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.wms.StockNoticeCancelMessage;
import net.summerfarm.wms.api.h5.stocktask.StockTaskNoticeOrderCommandService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskNoticeOrderCancelCommand;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.outstore.dto.StockTaskNoticeOrderCreateMsgDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static net.summerfarm.wms.common.constant.Global.STOCK_TASK;

/**
 * @Description 出库通知单整单取消消息监听
 * @Date 2024/01/05 11:18
 * @<AUTHOR>
 */
@Slf4j
@Component
@MqOrderlyListener(topic = "topic_ofc_stock_notice_orderly", tag = "tag_stock_notice_cancel",
        consumerGroup = "GID_wms_common_orderly", maxReconsumeTimes = 8)
public class StockTaskNoticeOrderCancelListener extends AbstractMqListener<StockNoticeCancelMessage> {

    @Resource
    private StockTaskNoticeOrderCommandService noticeOrderCommandService;

    @Override
    public void process(StockNoticeCancelMessage stockNoticeMessage) {
        log.info("取消出库通知单:{}", JSON.toJSONString(stockNoticeMessage));
        // 取消出库通知单
        try {
            noticeOrderCommandService.cancelStockTaskNoticeOrder(StockTaskNoticeOrderCancelCommand.builder()
                    .warehouseNo(stockNoticeMessage.getWarehouseNo())
                    .goodsSupplyNo(stockNoticeMessage.getGoodsSupplyNo())
                    .build());
        } catch (BizException e) {
            // 告警降级
            if (e.getErrorCode().getCode().equals(ErrorCodeNew.NOTICE_CANT_CANCEL.getCode())) {
                log.warn("该货品供应单" + stockNoticeMessage.getGoodsSupplyNo() + "已取消");
                return;
            }
            throw e;
        }
    }
}
