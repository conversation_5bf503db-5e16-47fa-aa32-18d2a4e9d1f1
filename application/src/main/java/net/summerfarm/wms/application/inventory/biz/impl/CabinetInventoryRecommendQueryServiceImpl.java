package net.summerfarm.wms.application.inventory.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryRecommendQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.*;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryQueryRecommendOutDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryRecommendOutResp;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryRecommendOutResp;
import net.summerfarm.wms.api.h5.outstore.CrossWarehouseLineQueryService;
import net.summerfarm.wms.api.h5.outstore.dto.CrossWarehouseLineDTO;
import net.summerfarm.wms.api.h5.stocktask.MissionTaskItemMappingQueryService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.MissionTaskItemMappingSkuQuery;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingDTO;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingResp;
import net.summerfarm.wms.application.inventory.biz.converter.CabinetInventoryRecommendOutRespConverter;
import net.summerfarm.wms.application.inventory.biz.converter.SkuProductRangeQueryConverter;
import net.summerfarm.wms.application.inventory.biz.factory.CabinetInventoryRecommendFactory;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.CabinetPurposeEnum;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.StoreRecord.enums.StoreRecordType;
import net.summerfarm.wms.domain.base.Sort;
import net.summerfarm.wms.domain.common.domainobject.Cabinet;
import net.summerfarm.wms.domain.common.domainobject.CabinetQuery;
import net.summerfarm.wms.domain.common.domainobject.Zone;
import net.summerfarm.wms.domain.common.repository.CabinetRepository;
import net.summerfarm.wms.domain.common.repository.ZoneRepository;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventory;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.valueObject.CabinetInventoryRecommend;
import net.summerfarm.wms.domain.inventory.domainobject.query.CabinetBatchInventoryQuery;
import net.summerfarm.wms.domain.inventory.domainobject.query.CabinetInventoryQuery;
import net.summerfarm.wms.domain.inventory.handler.sorter.CabinetBatchInventorySorter;
import net.summerfarm.wms.domain.inventory.handler.sorter.CabinetInventorySorter;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryRepository;
import net.summerfarm.wms.domain.inventory.repository.CabinetInventoryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CabinetInventoryRecommendQueryServiceImpl implements CabinetInventoryRecommendQueryService {

    @Resource
    private CabinetInventoryRepository cabinetInventoryRepository;
    @Resource
    private CabinetBatchInventoryRepository cabinetBatchInventoryRepository;
    @Resource
    private CabinetRepository cabinetRepository;
    @Resource
    private ZoneRepository zoneRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private CrossWarehouseLineQueryService crossWarehouseLineQueryService;
    @Resource
    private MissionTaskItemMappingQueryService missionTaskItemMappingQueryService;

    @Override
    public CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOut(
            CabinetInventorySingleRecommendOutReq recommendOutReq) {
        if (recommendOutReq == null || recommendOutReq.getWarehouseNo() == null ||
                recommendOutReq.getSkuCode() == null || recommendOutReq.getPageIndex() == null ||
                recommendOutReq.getPageSize() == null) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求参数错误");
        }

        // 任务类型
        Integer stockTaskType = null;
        // 越库出库数量
        Map<String, Integer> crossLineSkuMap = new HashMap<>();
        // 是否已完成投线
        Boolean finishCrossOption = false;
        // 是否已完成入库
        Boolean finishStorageOption = false;
        if (null != recommendOutReq.getStockTaskId()) {
            StockTask stockTask = stockTaskRepository.queryStockTaskById(recommendOutReq.getStockTaskId());
            // 越库出库任务需判断投线数量
            if (null != stockTask) {
                stockTaskType = stockTask.getType();
                if (StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTask.getType()) {
                    finishCrossOption = crossWarehouseLineQueryService.queryStoreProductLineCompleteFlag(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                    log.info("越库出库任务：{}执行库位库存锁库，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，返回投线完成标识：{}", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode(), finishCrossOption);
                    List<CrossWarehouseLineDTO> crossWarehouseLineDTOList = crossWarehouseLineQueryService.queryStoreProductLine(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                    log.info("越库出库任务：{}执行库位库存锁库，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，返回投线结果：{}", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode(), JSON.toJSONString(crossWarehouseLineDTOList));
                    if (org.apache.commons.collections4.CollectionUtils.isEmpty(crossWarehouseLineDTOList)) {
                        log.info("出库任务：{}，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，无可用投线数量", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode());
                        finishStorageOption = crossWarehouseLineQueryService.queryStockTaskStorageCompleteFlag(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                        if (Boolean.FALSE.equals(finishStorageOption)) {
                            PageInfo<CabinetInventoryRecommendOutResp> result = new PageInfo<>();
                            result.setTotal(0);
                            result.setPages(0);
                            result.setList(new ArrayList<>());
                            return CommonResult.ok(result);
                        }
                    }
                    if (!CollectionUtils.isEmpty(crossWarehouseLineDTOList)) {
                        crossLineSkuMap = crossWarehouseLineDTOList.stream().collect(Collectors.toMap(item -> getGroupKey(item.getSku(), DefaultCabinetCodeEnum.DX.getCode(), DateUtil.toDate(item.getProduceDate()), DateUtil.toDate(item.getQualityDate())), CrossWarehouseLineDTO::getQuantity, Integer::sum));
                    }
                }
            }
        }

        // 无采购批次
        if (recommendOutReq.getSkuBatchCode() == null) {
            CabinetInventoryQuery query = new CabinetInventoryQuery();
            query.setTenantId(recommendOutReq.getTenantId());
            query.setWarehouseNo(recommendOutReq.getWarehouseNo());
            query.setSku(recommendOutReq.getSkuCode());
            query.setCabinetCode(recommendOutReq.getCabinetCode());
            query.setProduceDate(recommendOutReq.getProduceDate());
            query.setProduceDateGe(recommendOutReq.getProduceDateGe());
            query.setProduceDateLe(recommendOutReq.getProduceDateLe());
            query.setQualityDate(recommendOutReq.getQualityDate());
            query.setQualityDateLt(recommendOutReq.getQualityDateLt());
            query.setQualityDateGe(recommendOutReq.getQualityDateGe());
            if (null != recommendOutReq.getQuantityGtZero() && Boolean.FALSE.equals(recommendOutReq.getQuantityGtZero())) {
                // 不限制数量必须大于0
                query.setQuantityGtZero(null);
            } else {
                // 默认取大于0记录
                query.setQuantityGtZero(true);
                query.setSaleOut(false);
            }
            query.setCabinetPurposeList(Arrays.asList(
                    CabinetPurposeEnum.DELIVERY.getCode(), CabinetPurposeEnum.STORAGE.getCode()
            ));
            query.setSkuProductRangeQueryList(SkuProductRangeQueryConverter.INSTANCE.convertList(recommendOutReq.getSkuProductRangeInputs()));
            if (null != stockTaskType && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType) {
                query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
            } else {
                query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.DX.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
            }

            query.setSorts(Arrays.asList(
                    new Sort("quality_date", Sort.SortType.ASC.getName()),
                    new Sort("cabinet_purpose", Sort.SortType.ASC.getName()),
                    new Sort("cabinet_code", Sort.SortType.ASC.getName())
            ));

            PageInfo<CabinetInventory> pageInfo = cabinetInventoryRepository.pageInventory(
                    recommendOutReq.getPageIndex(), recommendOutReq.getPageSize(),
                    query);
            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                PageInfo<CabinetInventoryRecommendOutResp> result = new PageInfo<>();
                result.setTotal(pageInfo.getTotal());
                result.setPages(pageInfo.getPages());
                result.setList(Lists.newArrayList());
                return CommonResult.ok(result);
            }

            // 查询库位
            List<String> cabinetCodeList = pageInfo.getList().stream().map(CabinetInventory::getCabinetCode).distinct().collect(Collectors.toList());
            List<Cabinet> cabinetList = cabinetRepository.list(CabinetQuery.builder()
                    .warehouseNo(recommendOutReq.getWarehouseNo())
                    .cabinetCodes(cabinetCodeList)
                    .build());
            Map<String, Cabinet> cabinetMap = cabinetList.stream().collect(Collectors.toMap(Cabinet::getCabinetCode, Function.identity(), (a, b) -> a));
            // 通用排序
            List<CabinetInventory> sortedCabinetInventoryList = CabinetInventorySorter.sortCabinetInventory(pageInfo.getList(), cabinetMap);
            List<CabinetInventoryRecommendOutResp> cabinetInventoryRecommendOutResps = CabinetInventoryRecommendOutRespConverter.
                    INSTANCE.convertList2(sortedCabinetInventoryList);
            PageInfo<CabinetInventoryRecommendOutResp> result = new PageInfo<>();
            result.setTotal(pageInfo.getTotal());
            result.setPages(pageInfo.getPages());
            result.setList(CollectionUtils.isEmpty(cabinetInventoryRecommendOutResps) ? Lists.newArrayList() :
                    cabinetInventoryRecommendOutResps);

            // 越库出库类型重新处理可用库存
            if (!crossLineSkuMap.isEmpty()) {
                resetAvailableCabinetInventory(recommendOutReq.getStockTaskId(), stockTaskType, recommendOutReq.getSkuCode(), crossLineSkuMap, result, finishCrossOption);
            }

            return CommonResult.ok(result);
        }

        // 限定采购批次
        CabinetBatchInventoryQuery query = new CabinetBatchInventoryQuery();
        query.setTenantId(recommendOutReq.getTenantId());
        query.setWarehouseNo(recommendOutReq.getWarehouseNo());
        query.setSku(recommendOutReq.getSkuCode());
        query.setCabinetCode(recommendOutReq.getCabinetCode());
        query.setProduceDate(recommendOutReq.getProduceDate());
        query.setProduceDateGe(recommendOutReq.getProduceDateGe());
        query.setProduceDateLe(recommendOutReq.getProduceDateLe());
        query.setQualityDate(recommendOutReq.getQualityDate());
        query.setQualityDateLt(recommendOutReq.getQualityDateLt());
        query.setQualityDateGe(recommendOutReq.getQualityDateGe());
        query.setBatchNoList(Arrays.asList(recommendOutReq.getSkuBatchCode()));
        if (null != recommendOutReq.getQuantityGtZero() && Boolean.FALSE.equals(recommendOutReq.getQuantityGtZero())) {
            // 不限制数量必须大于0
            query.setQuantityGtZero(null);
        } else {
            // 默认取大于0记录
            query.setQuantityGtZero(true);
            query.setSaleOut(false);
        }
        query.setCabinetPurposeList(Arrays.asList(
                CabinetPurposeEnum.DELIVERY.getCode(), CabinetPurposeEnum.STORAGE.getCode()
        ));
        query.setSkuProductRangeQueryList(SkuProductRangeQueryConverter.INSTANCE.convertList(recommendOutReq.getSkuProductRangeInputs()));
        if (null != stockTaskType && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType) {
            query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
        } else {
            query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.DX.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
        }

        query.setSorts(Arrays.asList(
                new Sort("quality_date", Sort.SortType.ASC.getName()),
                new Sort("cabinet_purpose", Sort.SortType.ASC.getName()),
                new Sort("cabinet_code", Sort.SortType.ASC.getName()),
                new Sort("batch_no", Sort.SortType.ASC.getName())
        ));

        PageInfo<CabinetBatchInventory> pageInfo = cabinetBatchInventoryRepository.pageInventory(
                recommendOutReq.getPageIndex(), recommendOutReq.getPageSize(),
                query);

        PageInfo<CabinetInventoryRecommendOutResp> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        List<CabinetInventoryRecommendOutResp> cabinetInventoryRecommendOutResps = Optional.ofNullable(pageInfo.getList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(CabinetInventoryRecommendOutRespConverter.INSTANCE::convertResp)
                .collect(Collectors.toList());
        result.setList(cabinetInventoryRecommendOutResps);

        // 越库出库类型重新处理可用库存
        if (!crossLineSkuMap.isEmpty()) {
            resetAvailableCabinetInventory(recommendOutReq.getStockTaskId(), stockTaskType, recommendOutReq.getSkuCode(), crossLineSkuMap, result, finishCrossOption);
        }

        return CommonResult.ok(result);
    }

    /**
     * 库位批次库存推荐
     * copy by net.summerfarm.wms.application.inventory.biz.impl.CabinetInventoryRecommendQueryServiceImpl#queryRecommendOut(net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventorySingleRecommendOutReq)
     * @param recommendOutReq
     * @return
     */
    @Override
    public CommonResult<PageInfo<CabinetBatchInventoryRecommendOutResp>> queryBatchRecommendOut(
            CabinetInventorySingleRecommendOutReq recommendOutReq) {
        if (recommendOutReq == null || recommendOutReq.getWarehouseNo() == null ||
                recommendOutReq.getSkuCode() == null || recommendOutReq.getPageIndex() == null ||
                recommendOutReq.getPageSize() == null) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求参数错误");
        }

        // 任务类型
        Integer stockTaskType = null;
        // 越库出库数量
        Map<String, Integer> crossLineSkuMap = new HashMap<>();
        // 是否已完成投线
        Boolean finishCrossOption = false;
        // 是否已完成入库
        Boolean finishStorageOption = false;
        if (null != recommendOutReq.getStockTaskId()) {
            StockTask stockTask = stockTaskRepository.queryStockTaskById(recommendOutReq.getStockTaskId());
            // 越库出库任务需判断投线数量
            if (null != stockTask) {
                stockTaskType = stockTask.getType();
                if (StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTask.getType()) {
                    finishCrossOption = crossWarehouseLineQueryService.queryStoreProductLineCompleteFlag(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                    log.info("越库出库任务：{}执行库位库存锁库，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，返回投线完成标识：{}", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode(), finishCrossOption);
                    List<CrossWarehouseLineDTO> crossWarehouseLineDTOList = crossWarehouseLineQueryService.queryStoreProductLine(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                    log.info("越库出库任务：{}执行库位库存锁库，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，返回投线结果：{}", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode(), JSON.toJSONString(crossWarehouseLineDTOList));
                    if (org.apache.commons.collections4.CollectionUtils.isEmpty(crossWarehouseLineDTOList)) {
                        log.info("出库任务：{}，库存仓：{}，城配仓：{}，期望时间：{}，sku：{}，无可用投线数量", stockTask.getId(), stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), recommendOutReq.getSkuCode());
                        finishStorageOption = crossWarehouseLineQueryService.queryStockTaskStorageCompleteFlag(stockTask.getAreaNo().longValue(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), Collections.singletonList(recommendOutReq.getSkuCode()));
                        if (Boolean.FALSE.equals(finishStorageOption)) {
                            PageInfo<CabinetBatchInventoryRecommendOutResp> result = new PageInfo<>();
                            result.setTotal(0);
                            result.setPages(0);
                            result.setList(new ArrayList<>());
                            return CommonResult.ok(result);
                        }
                    }
                    if (!CollectionUtils.isEmpty(crossWarehouseLineDTOList)) {
                        crossLineSkuMap = crossWarehouseLineDTOList.stream().collect(Collectors.toMap(item -> getGroupKey(item.getSku(), DefaultCabinetCodeEnum.DX.getCode(), DateUtil.toDate(item.getProduceDate()), DateUtil.toDate(item.getQualityDate())), CrossWarehouseLineDTO::getQuantity, Integer::sum));
                    }
                }
            }
        }

        // 无采购批次
        if (recommendOutReq.getSkuBatchCode() == null) {
            CabinetBatchInventoryQuery query = new CabinetBatchInventoryQuery();
            query.setTenantId(recommendOutReq.getTenantId());
            query.setWarehouseNo(recommendOutReq.getWarehouseNo());
            query.setSku(recommendOutReq.getSkuCode());
            query.setCabinetCode(recommendOutReq.getCabinetCode());
            query.setProduceDate(recommendOutReq.getProduceDate());
            query.setProduceDateGe(recommendOutReq.getProduceDateGe());
            query.setProduceDateLe(recommendOutReq.getProduceDateLe());
            query.setQualityDate(recommendOutReq.getQualityDate());
            query.setQualityDateLt(recommendOutReq.getQualityDateLt());
            query.setQualityDateGe(recommendOutReq.getQualityDateGe());
            if (null != recommendOutReq.getQuantityGtZero() && Boolean.FALSE.equals(recommendOutReq.getQuantityGtZero())) {
                // 不限制数量必须大于0
                query.setQuantityGtZero(null);
            } else {
                // 默认取大于0记录
                query.setQuantityGtZero(true);
                query.setSaleOut(false);
            }
            query.setCabinetPurposeList(Arrays.asList(
                    CabinetPurposeEnum.DELIVERY.getCode(), CabinetPurposeEnum.STORAGE.getCode()
            ));
            query.setSkuProductRangeQueryList(SkuProductRangeQueryConverter.INSTANCE.convertList(recommendOutReq.getSkuProductRangeInputs()));
            if (null != stockTaskType && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType) {
                query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
            } else {
                query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.DX.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
            }

            query.setSorts(Arrays.asList(
                    new Sort("quality_date", Sort.SortType.ASC.getName()),
                    new Sort("cabinet_purpose", Sort.SortType.ASC.getName()),
                    new Sort("cabinet_code", Sort.SortType.ASC.getName())
            ));

            PageInfo<CabinetBatchInventory> pageInfo = cabinetBatchInventoryRepository.pageInventory(
                    recommendOutReq.getPageIndex(), recommendOutReq.getPageSize(),
                    query);
            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                PageInfo<CabinetBatchInventoryRecommendOutResp> result = new PageInfo<>();
                result.setTotal(pageInfo.getTotal());
                result.setPages(pageInfo.getPages());
                result.setList(Lists.newArrayList());
                return CommonResult.ok(result);
            }

            // 查询库位
            List<String> cabinetCodeList = pageInfo.getList().stream().map(CabinetBatchInventory::getCabinetCode).distinct().collect(Collectors.toList());
            List<Cabinet> cabinetList = cabinetRepository.list(CabinetQuery.builder()
                    .warehouseNo(recommendOutReq.getWarehouseNo())
                    .cabinetCodes(cabinetCodeList)
                    .build());
            Map<String, Cabinet> cabinetMap = cabinetList.stream().collect(Collectors.toMap(Cabinet::getCabinetCode, Function.identity(), (a, b) -> a));
            // 通用排序
            List<CabinetBatchInventory> sortedCabinetInventoryList = CabinetBatchInventorySorter.sortCabinetInventory(pageInfo.getList(), cabinetMap);
            List<CabinetBatchInventoryRecommendOutResp> cabinetInventoryRecommendOutResps = CabinetInventoryRecommendOutRespConverter.
                    INSTANCE.convertBatchList(sortedCabinetInventoryList);
            PageInfo<CabinetBatchInventoryRecommendOutResp> result = new PageInfo<>();
            result.setTotal(pageInfo.getTotal());
            result.setPages(pageInfo.getPages());
            result.setList(CollectionUtils.isEmpty(cabinetInventoryRecommendOutResps) ? Lists.newArrayList() :
                    cabinetInventoryRecommendOutResps);

            // 越库出库类型重新处理可用库存
            if (!crossLineSkuMap.isEmpty()) {
                resetAvailableCabinetBatchInventory(recommendOutReq.getStockTaskId(), stockTaskType, recommendOutReq.getSkuCode(), crossLineSkuMap, result, finishCrossOption);
            }

            return CommonResult.ok(result);
        }

        // 限定采购批次
        CabinetBatchInventoryQuery query = new CabinetBatchInventoryQuery();
        query.setTenantId(recommendOutReq.getTenantId());
        query.setWarehouseNo(recommendOutReq.getWarehouseNo());
        query.setSku(recommendOutReq.getSkuCode());
        query.setCabinetCode(recommendOutReq.getCabinetCode());
        query.setProduceDate(recommendOutReq.getProduceDate());
        query.setProduceDateGe(recommendOutReq.getProduceDateGe());
        query.setProduceDateLe(recommendOutReq.getProduceDateLe());
        query.setQualityDate(recommendOutReq.getQualityDate());
        query.setQualityDateLt(recommendOutReq.getQualityDateLt());
        query.setQualityDateGe(recommendOutReq.getQualityDateGe());
        query.setBatchNoList(Arrays.asList(recommendOutReq.getSkuBatchCode()));
        if (null != recommendOutReq.getQuantityGtZero() && Boolean.FALSE.equals(recommendOutReq.getQuantityGtZero())) {
            // 不限制数量必须大于0
            query.setQuantityGtZero(null);
        } else {
            // 默认取大于0记录
            query.setQuantityGtZero(true);
            query.setSaleOut(false);
        }
        query.setCabinetPurposeList(Arrays.asList(
                CabinetPurposeEnum.DELIVERY.getCode(), CabinetPurposeEnum.STORAGE.getCode()
        ));
        query.setSkuProductRangeQueryList(SkuProductRangeQueryConverter.INSTANCE.convertList(recommendOutReq.getSkuProductRangeInputs()));
        if (null != stockTaskType && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType) {
            query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
        } else {
            query.setCabinetCodeNotInList(Arrays.asList(DefaultCabinetCodeEnum.JJ.getCode(), DefaultCabinetCodeEnum.CY.getCode(), DefaultCabinetCodeEnum.DX.getCode(), DefaultCabinetCodeEnum.HJ.getCode()));
        }

        query.setSorts(Arrays.asList(
                new Sort("quality_date", Sort.SortType.ASC.getName()),
                new Sort("cabinet_purpose", Sort.SortType.ASC.getName()),
                new Sort("cabinet_code", Sort.SortType.ASC.getName()),
                new Sort("batch_no", Sort.SortType.ASC.getName())
        ));

        PageInfo<CabinetBatchInventory> pageInfo = cabinetBatchInventoryRepository.pageInventory(
                recommendOutReq.getPageIndex(), recommendOutReq.getPageSize(),
                query);

        PageInfo<CabinetBatchInventoryRecommendOutResp> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        List<CabinetBatchInventoryRecommendOutResp> cabinetInventoryRecommendOutResps = Optional.ofNullable(pageInfo.getList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(CabinetInventoryRecommendOutRespConverter.INSTANCE::convertBatchResp)
                .collect(Collectors.toList());
        result.setList(cabinetInventoryRecommendOutResps);

        // 越库出库类型重新处理可用库存
        if (!crossLineSkuMap.isEmpty()) {
            resetAvailableCabinetBatchInventory(recommendOutReq.getStockTaskId(), stockTaskType, recommendOutReq.getSkuCode(), crossLineSkuMap, result, finishCrossOption);
        }

        return CommonResult.ok(result);
    }

    @Override
    public CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOutExcludeAbnormalPeriod(CabinetInventorySingleRecommendOutReq recommendOutReq) {
        List<Product> products = productRepository.findProducts(QueryProduct.builder()
                .skus(Lists.newArrayList(recommendOutReq.getSkuCode()))
                .build());
        Product product = products.get(NumberUtils.INTEGER_ZERO);
        LocalDate qualityDateGe = LocalDate.now().plusDays(product.getWarnTime()).plusDays(1);
        recommendOutReq.setQualityDateLt(DateUtil.toDate(qualityDateGe));

        return queryRecommendOut(recommendOutReq);
    }

    @Override
    public CommonResult<List<CabinetInventoryRecommendOutResp>> matchBatchRecommendOut(
            CabinetInventoryBatchRecommendOutReq queryRecommendReq) {
        if (queryRecommendReq == null || queryRecommendReq.getWarehouseNo() == null || CollectionUtils.isEmpty(queryRecommendReq.getRecommendDetailReqList())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求参数错误");
        }


        List<CabinetBatchInventory> cabinetBatchInventoryList = assemblyExistCabinetBatchInventoryForOccupy(
                queryRecommendReq
        );
        List<CabinetInventory> cabinetInventoryList = assemblyExistCabinetInventoryForOccupy(
                queryRecommendReq, cabinetBatchInventoryList
        );

        // 库位库区
        List<String> cabinetCodeList = cabinetInventoryList
                .stream()
                .filter(s -> !StringUtils.isEmpty(s.getCabinetCode()))
                .map(CabinetInventory::getCabinetCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Cabinet> cabinetMap = cabinetRepository.mapByWarehouseNoAndCabinetCodes(
                queryRecommendReq.getWarehouseNo(), cabinetCodeList);
        List<Long> zoneIdList = cabinetMap.values().stream().map(Cabinet::getZoneId).distinct().collect(Collectors.toList());
        Map<Long, Zone> zoneMap = zoneRepository.mapByIds(zoneIdList);

        List<CabinetInventoryRecommend> recommendList = CabinetInventoryRecommendFactory.newInstance(
                queryRecommendReq, cabinetInventoryList, cabinetMap, zoneMap
        );

        List<CabinetInventoryRecommendOutResp> result = CabinetInventoryRecommendOutRespConverter
                .INSTANCE.convertList(recommendList);

        return CommonResult.ok(result);
    }

    public static String getCabinetInventoryUni(Long tenantId, Integer warehouseNo, String sku, String cabinetCode,
                                                Date produceDate, Date qualityDate) {
        return tenantId + ":" + warehouseNo + ":" + sku + ":" + cabinetCode
                + ":" + DateUtil.formatYmdDate(produceDate) + ":" + DateUtil.formatYmdDate(qualityDate);
    }

    /**
     * 组装已存在的库位库存
     *
     * @param req
     * @param cabinetBatchInventoryList
     */
    public List<CabinetInventory> assemblyExistCabinetInventoryForOccupy(CabinetInventoryBatchRecommendOutReq req,
                                                                         List<CabinetBatchInventory> cabinetBatchInventoryList1) {
        List<CabinetBatchInventory> cabinetBatchInventoryList = JSONObject.parseArray(
                JSONObject.toJSONString(cabinetBatchInventoryList1), CabinetBatchInventory.class);
        Map<String, CabinetBatchInventory> batchInventoryMap = cabinetBatchInventoryList.stream()
                .collect(Collectors.toMap(
                        s -> getCabinetInventoryUni(s.getTenantId(), s.getWarehouseNo(), s.getSku(), s.getCabinetCode(),
                                s.getProduceDate(), s.getQualityDate()),
                        Function.identity(),
                        (a, b) -> {
                            a.setQuantity(a.getQuantity() + b.getQuantity());
                            a.setLockQuantity(a.getLockQuantity() + b.getLockQuantity());
                            a.setAvailableQuantity(a.getAvailableQuantity() + b.getAvailableQuantity());
                            return a;
                        }
                ));

        Long tenantId = req.getTenantId();
        Integer warehouseNo = req.getWarehouseNo();
        List<String> skuList = req.getRecommendDetailReqList()
                .stream()
                .map(CabinetInventoryQueryRecommendOutDetailReq::getSkuCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> cabinetList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !StringUtils.isEmpty(s.getCabinetCode()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getCabinetCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> excludeCabinetList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !CollectionUtils.isEmpty(s.getExcludeCabinetCodes()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getExcludeCabinetCodes)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<Date> produceDateList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDate() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDate)
                .distinct()
                .collect(Collectors.toList());
        Date produceDateGe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDateGe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDateGe)
                .min(Comparator.comparing(date -> date))
                .orElse(null);
        Date produceDateLe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDateLe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDateLe)
                .max(Comparator.comparing(Function.identity()))
                .orElse(null);

        List<Date> qualityDateList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDate() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDate)
                .distinct()
                .collect(Collectors.toList());

        Date qualityDateGe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDateGe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDateGe)
                .min(Comparator.comparing(date -> date))
                .orElse(null);
        Date qualityDateLt = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDateLt() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDateLt)
                .max(Comparator.comparing(date -> date))
                .orElse(null);
        List<String> skuBatchCodeList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !StringUtils.isEmpty(s.getSkuBatchCode()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getSkuBatchCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, List<CabinetInventoryQueryRecommendOutDetailReq>> cabinetInventorySet = req.getRecommendDetailReqList().stream()
                .collect(Collectors.groupingBy(s -> s.getSkuCode()));

        List<CabinetInventory> existCabinetInventoryList = cabinetInventoryRepository
                .listByWnoAndSkuAndCabinetAndPickGtZero(warehouseNo, skuList, cabinetList,
                        produceDateList, produceDateGe, produceDateLe,
                        qualityDateList, qualityDateGe, qualityDateLt, skuBatchCodeList, excludeCabinetList);
        existCabinetInventoryList = existCabinetInventoryList.stream()
                .filter(s -> {
                            List<CabinetInventoryQueryRecommendOutDetailReq> list = cabinetInventorySet.get(s.getSku());
                            if (CollectionUtils.isEmpty(list)) {
                                return false;
                            }
                            for (CabinetInventoryQueryRecommendOutDetailReq cabinetInventoryOccupyDetailReq : list) {
                                if (cabinetInventoryOccupyDetailReq.getCabinetCode() != null &&
                                        !cabinetInventoryOccupyDetailReq.getCabinetCode().equals(s.getCabinetCode())) {
                                    continue;
                                }

                                if (cabinetInventoryOccupyDetailReq.getProduceDate() != null &&
                                        cabinetInventoryOccupyDetailReq.getProduceDate().compareTo(s.getProduceDate()) != 0) {
                                    continue;
                                }
                                if (cabinetInventoryOccupyDetailReq.getProduceDateGe() != null &&
                                        cabinetInventoryOccupyDetailReq.getProduceDateGe().compareTo(s.getProduceDate()) > 0) {
                                    continue;

                                }

                                if (cabinetInventoryOccupyDetailReq.getProduceDateLe() != null &&
                                        cabinetInventoryOccupyDetailReq.getProduceDateLe().compareTo(s.getProduceDate()) < 0) {
                                    continue;
                                }

                                if (cabinetInventoryOccupyDetailReq.getQualityDate() != null &&
                                        cabinetInventoryOccupyDetailReq.getQualityDate().compareTo(s.getQualityDate()) != 0) {
                                    continue;
                                }
                                if (cabinetInventoryOccupyDetailReq.getQualityDateGe() != null &&
                                        cabinetInventoryOccupyDetailReq.getQualityDateGe().compareTo(s.getQualityDate()) > 0) {
                                    continue;
                                }

                                if (cabinetInventoryOccupyDetailReq.getQualityDateLt() != null &&
                                        cabinetInventoryOccupyDetailReq.getQualityDateLt().compareTo(s.getQualityDate()) <= 0) {
                                    continue;
                                }

                                CabinetBatchInventory cabinetBatchInventory = batchInventoryMap.get(
                                        getCabinetInventoryUni(
                                                s.getTenantId(), s.getWarehouseNo(), s.getSku(), s.getCabinetCode(),
                                                s.getProduceDate(), s.getQualityDate()));
                                if (cabinetInventoryOccupyDetailReq.getSkuBatchCode() != null &&
                                        cabinetBatchInventory == null) {
                                    continue;
                                }

                                return true;
                            }

                            return false;
                        }
                )
                .peek(s -> {
                    CabinetBatchInventory cabinetBatchInventory = batchInventoryMap.get(
                            getCabinetInventoryUni(
                                    s.getTenantId(), s.getWarehouseNo(), s.getSku(), s.getCabinetCode(),
                                    s.getProduceDate(), s.getQualityDate()));
                    if (cabinetBatchInventory != null) {
                        s.setQuantity(cabinetBatchInventory.getQuantity());
                        s.setAvailableQuantity(cabinetBatchInventory.getAvailableQuantity());
                        s.setLockQuantity(cabinetBatchInventory.getLockQuantity());
                    }
                })
                .collect(Collectors.toList());
        return existCabinetInventoryList;
    }

    public List<CabinetBatchInventory> assemblyExistCabinetBatchInventoryForOccupy(
            CabinetInventoryBatchRecommendOutReq req) {
        Long tenantId = req.getTenantId();
        Integer warehouseNo = req.getWarehouseNo();
        List<String> skuList = req.getRecommendDetailReqList()
                .stream()
                .map(CabinetInventoryQueryRecommendOutDetailReq::getSkuCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> cabinetList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !StringUtils.isEmpty(s.getCabinetCode()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getCabinetCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> excludeCabinetList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !CollectionUtils.isEmpty(s.getExcludeCabinetCodes()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getExcludeCabinetCodes)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<Date> produceDateList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDate() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDate)
                .distinct()
                .collect(Collectors.toList());
        Date produceDateGe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDateGe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDateGe)
                .min(Comparator.comparing(date -> date))
                .orElse(null);
        Date produceDateLe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getProduceDateLe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getProduceDateLe)
                .max(Comparator.comparing(Function.identity()))
                .orElse(null);

        List<Date> qualityDateList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDate() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDate)
                .distinct()
                .collect(Collectors.toList());

        Date qualityDateGe = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDateGe() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDateGe)
                .min(Comparator.comparing(date -> date))
                .orElse(null);
        Date qualityDateLt = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> s.getQualityDateLt() != null)
                .map(CabinetInventoryQueryRecommendOutDetailReq::getQualityDateLt)
                .max(Comparator.comparing(Function.identity()))
                .orElse(null);

        List<String> skuBatchCodeList = req.getRecommendDetailReqList()
                .stream()
                .filter(s -> !StringUtils.isEmpty(s.getSkuBatchCode()))
                .map(CabinetInventoryQueryRecommendOutDetailReq::getSkuBatchCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, List<CabinetInventoryQueryRecommendOutDetailReq>> cabinetInventorySet = req.getRecommendDetailReqList().stream()
                .collect(Collectors.groupingBy(s -> s.getSkuCode()));

        List<CabinetBatchInventory> existCabinetBatchInventoryList = cabinetBatchInventoryRepository
                .listByWnoAndSkuAndCabinetAndPickAndAvailableGtZero(warehouseNo, skuList, cabinetList,
                        produceDateList, produceDateGe, produceDateLe,
                        qualityDateList, qualityDateGe, qualityDateLt, skuBatchCodeList, excludeCabinetList);
        existCabinetBatchInventoryList = existCabinetBatchInventoryList.stream()
                .filter(s -> {
                    List<CabinetInventoryQueryRecommendOutDetailReq> list = cabinetInventorySet.get(s.getSku());
                    if (CollectionUtils.isEmpty(list)) {
                        return false;
                    }
                    for (CabinetInventoryQueryRecommendOutDetailReq cabinetInventoryOccupyDetailReq : list) {
                        if (cabinetInventoryOccupyDetailReq.getCabinetCode() != null &&
                                !cabinetInventoryOccupyDetailReq.getCabinetCode().equals(s.getCabinetCode())) {
                            continue;
                        }

                        if (cabinetInventoryOccupyDetailReq.getSkuBatchCode() != null &&
                                !cabinetInventoryOccupyDetailReq.getSkuBatchCode().equals(s.getBatchNo())) {
                            continue;
                        }

                        if (cabinetInventoryOccupyDetailReq.getProduceDate() != null &&
                                cabinetInventoryOccupyDetailReq.getProduceDate().compareTo(s.getProduceDate()) != 0) {
                            continue;
                        }
                        if (cabinetInventoryOccupyDetailReq.getProduceDateGe() != null &&
                                cabinetInventoryOccupyDetailReq.getProduceDateGe().compareTo(s.getProduceDate()) > 0) {
                            continue;
                        }

                        if (cabinetInventoryOccupyDetailReq.getProduceDateLe() != null &&
                                cabinetInventoryOccupyDetailReq.getProduceDateLe().compareTo(s.getProduceDate()) < 0) {
                            continue;
                        }

                        if (cabinetInventoryOccupyDetailReq.getQualityDate() != null &&
                                cabinetInventoryOccupyDetailReq.getQualityDate().compareTo(s.getQualityDate()) != 0) {
                            continue;
                        }
                        if (cabinetInventoryOccupyDetailReq.getQualityDateGe() != null &&
                                cabinetInventoryOccupyDetailReq.getQualityDateGe().compareTo(s.getQualityDate()) > 0) {
                            continue;
                        }

                        if (cabinetInventoryOccupyDetailReq.getQualityDateLt() != null &&
                                cabinetInventoryOccupyDetailReq.getQualityDateLt().compareTo(s.getQualityDate()) <= 0) {
                            continue;
                        }


                        if (!CollectionUtils.isEmpty(cabinetInventoryOccupyDetailReq.getSkuProductRangeInputs())) {
                            Boolean matchOne = false;
                            for (SkuProductRangeReq skuProductRangeReq : cabinetInventoryOccupyDetailReq.getSkuProductRangeInputs()) {
                                if (skuProductRangeReq.getProduceDateGe() != null &&
                                        skuProductRangeReq.getProduceDateGe().compareTo(s.getProduceDate()) > 0) {
                                    matchOne = false;
                                    continue;
                                }
                                if (skuProductRangeReq.getProduceDateLe() != null &&
                                        skuProductRangeReq.getProduceDateLe().compareTo(s.getProduceDate()) < 0) {
                                    matchOne = false;
                                    continue;
                                }
                                matchOne = true;
                                break;
                            }
                            if (!matchOne) {
                                continue;
                            }
                        }

                        return true;
                    }

                    return false;
                })
                .collect(Collectors.toList());
        return existCabinetBatchInventoryList;
    }

    private void resetAvailableCabinetInventory(Integer stockTaskId, Integer stockTaskType, String sku, Map<String, Integer> crossLineSkuMap, PageInfo<CabinetInventoryRecommendOutResp> result, Boolean finishCrossOption) {
        // 越库出库类型重新处理可用库存
        if (null != stockTaskId && null != stockTaskType
                && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType
                && !CollectionUtils.isEmpty(result.getList())) {
            log.info("越库出库任务根据投线数量重新处理可用库位库存，任务id：{}，原查询记录：{}", stockTaskId, JSON.toJSONStringWithDateFormat(result.getList(), DateUtil.YYYY_MM_DD));
            // 获取已生成拣货数量
            Map<String, Integer> missionTaskItemMappingMap = new HashMap<>();
            MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
            missionTaskItemMappingSkuQuery.setStockTaskId(stockTaskId.toString());
            missionTaskItemMappingSkuQuery.setSkus(Collections.singletonList(sku));
            MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryAllMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
            if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                missionTaskItemMappingMap = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(Collectors.toMap(item -> getGroupKey(item.getSku(), item.getCabinetCode(), item.getProductionDate(), item.getQualityDate()), MissionTaskItemMappingDTO::getShouldPickQuantity, Integer::sum));
            }
            // 根据库位重排序
            List<CabinetInventoryRecommendOutResp> cabinetCodeSortedCabinetInventoryList = result.getList().stream().sorted(Comparator.comparing(item -> DefaultCabinetCodeEnum.DX.getCode().equals(item.getCabinetCode()) ? 0 : 1)).collect(Collectors.toList());
            log.info("重排序后数据：{}", JSON.toJSONStringWithDateFormat(cabinetCodeSortedCabinetInventoryList, DateUtil.YYYY_MM_DD));
            for (CabinetInventoryRecommendOutResp recommendOutResp : cabinetCodeSortedCabinetInventoryList) {
                // 投线数量
                Integer crossLineQuantity = crossLineSkuMap.get(getGroupKey(recommendOutResp.getSkuCode(), DefaultCabinetCodeEnum.DX.getCode(), recommendOutResp.getProduceDate(), recommendOutResp.getQualityDate()));
                // 已生成应拣数量
                Integer shouldPickQuantity = missionTaskItemMappingMap.get(getGroupKey(recommendOutResp.getSkuCode(), DefaultCabinetCodeEnum.DX.getCode(), recommendOutResp.getProduceDate(), recommendOutResp.getQualityDate()));
                // 如果存在投线数量&投线数量小于查询出的可用库位库存，取投线数量
                if (null != crossLineQuantity && crossLineQuantity < recommendOutResp.getAvailableQuantity()) {
                    // 需扣掉已生成应拣数量
                    if (null == shouldPickQuantity) {
                        shouldPickQuantity = 0;
                    }
                    if (crossLineQuantity >= shouldPickQuantity) {
                        recommendOutResp.setAvailableQuantity(crossLineQuantity - shouldPickQuantity);
                    } else {
                        recommendOutResp.setAvailableQuantity(0);
                    }
                    // 非DX01库位
                } else if (null == crossLineQuantity) {
                    // 未完成投线处理为0，不能用普通库位库存
                    if (Boolean.FALSE.equals(finishCrossOption)) {
                        // 不存在投线数量，处理可用为0
                        recommendOutResp.setAvailableQuantity(0);
                    }
                }
            }
            result.setList(cabinetCodeSortedCabinetInventoryList);
            log.info("越库出库任务根据投线数量重新处理可用库位库存，任务id：{}，处理后记录：{}", stockTaskId, JSON.toJSONStringWithDateFormat(result.getList(), DateUtil.YYYY_MM_DD));
            List<CabinetInventoryRecommendOutResp> filterList = result.getList().stream().filter(item -> item.getAvailableQuantity() > 0).collect(Collectors.toList());
            result.setList(filterList);
        }
    }

    private void resetAvailableCabinetBatchInventory(Integer stockTaskId, Integer stockTaskType, String sku, Map<String, Integer> crossLineSkuMap, PageInfo<CabinetBatchInventoryRecommendOutResp> result, Boolean finishCrossOption) {
        // 越库出库类型重新处理可用库存
        if (null != stockTaskId && null != stockTaskType
                && StoreRecordType.CROSS_WAREHOUSE_OUT.getId() == stockTaskType
                && !CollectionUtils.isEmpty(result.getList())) {
            log.info("越库出库任务根据投线数量重新处理可用库位库存，任务id：{}，原查询记录：{}", stockTaskId, JSON.toJSONStringWithDateFormat(result.getList(), DateUtil.YYYY_MM_DD));
            // 获取已生成拣货数量
            Map<String, Integer> missionTaskItemMappingMap = new HashMap<>();
            MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
            missionTaskItemMappingSkuQuery.setStockTaskId(stockTaskId.toString());
            missionTaskItemMappingSkuQuery.setSkus(Collections.singletonList(sku));
            MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryAllMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
            if (null != missionTaskItemMappingResp && !CollectionUtils.isEmpty(missionTaskItemMappingResp.getMissionTaskItemMappings())) {
                missionTaskItemMappingMap = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(Collectors.toMap(item -> getGroupKey(item.getSku(), item.getCabinetCode(), item.getProductionDate(), item.getQualityDate()), MissionTaskItemMappingDTO::getShouldPickQuantity, Integer::sum));
            }
            // 根据库位重排序
            List<CabinetBatchInventoryRecommendOutResp> cabinetCodeSortedCabinetInventoryList = result.getList().stream().sorted(Comparator.comparing(item -> DefaultCabinetCodeEnum.DX.getCode().equals(item.getCabinetCode()) ? 0 : 1)).collect(Collectors.toList());
            log.info("重排序后数据：{}", JSON.toJSONStringWithDateFormat(cabinetCodeSortedCabinetInventoryList, DateUtil.YYYY_MM_DD));
            for (CabinetBatchInventoryRecommendOutResp recommendOutResp : cabinetCodeSortedCabinetInventoryList) {
                // 投线数量
                Integer crossLineQuantity = crossLineSkuMap.get(getGroupKey(recommendOutResp.getSkuCode(), DefaultCabinetCodeEnum.DX.getCode(), recommendOutResp.getProduceDate(), recommendOutResp.getQualityDate()));
                // 已生成应拣数量
                Integer shouldPickQuantity = missionTaskItemMappingMap.get(getGroupKey(recommendOutResp.getSkuCode(), DefaultCabinetCodeEnum.DX.getCode(), recommendOutResp.getProduceDate(), recommendOutResp.getQualityDate()));
                // 如果存在投线数量&投线数量小于查询出的可用库位库存，取投线数量
                if (null != crossLineQuantity && crossLineQuantity < recommendOutResp.getAvailableQuantity()) {
                    // 需扣掉已生成应拣数量
                    if (null == shouldPickQuantity) {
                        shouldPickQuantity = 0;
                    }
                    if (crossLineQuantity >= shouldPickQuantity) {
                        recommendOutResp.setAvailableQuantity(crossLineQuantity - shouldPickQuantity);
                    } else {
                        recommendOutResp.setAvailableQuantity(0);
                    }
                    // 非DX01库位
                } else if (null == crossLineQuantity) {
                    // 未完成投线处理为0，不能用普通库位库存
                    if (Boolean.FALSE.equals(finishCrossOption)) {
                        // 不存在投线数量，处理可用为0
                        recommendOutResp.setAvailableQuantity(0);
                    }
                }
            }
            result.setList(cabinetCodeSortedCabinetInventoryList);
            log.info("越库出库任务根据投线数量重新处理可用库位库存，任务id：{}，处理后记录：{}", stockTaskId, JSON.toJSONStringWithDateFormat(result.getList(), DateUtil.YYYY_MM_DD));
            List<CabinetBatchInventoryRecommendOutResp> filterList = result.getList().stream().filter(item -> item.getAvailableQuantity() > 0).collect(Collectors.toList());
            result.setList(filterList);
        }
    }

    private String getGroupKey(String sku, String cabinetCode, Date produceTime, Date shelfLife) {
        return sku + WmsConstant.SPLIT_U + cabinetCode + WmsConstant.SPLIT_U + produceTime + WmsConstant.SPLIT_U + shelfLife;
    }

}
