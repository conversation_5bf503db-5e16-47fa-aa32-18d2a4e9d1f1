package net.summerfarm.wms.api.h5.crosswarehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/10/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrossWarehouseSortInfoDTO implements Serializable {
    private static final long serialVersionUID = 2843612169884523038L;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 批次号
     */
    private String psoNo;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku
     */
    private String sku;

    /**
     * 数量信息
     */
    private Integer quantity;

    /**
     * 履约单号
     */
    private String fulfillmentNo;
}
