package net.summerfarm.wms.domain.prove.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ProveBizType {
    /**
     * 证明业务类型
     */
    INIT_IN(8, "期初入库"),
    ARRANGE_IN(9, "预约入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    PURCHASE_IN(11, "采购入库"),
    TRANSFER_IN(12, "转换入库"),
    RETURN_IN(13, "拒收入库任务"),
    ALLOCATION_ABNORMAL_IN(18, "调拨异常回库"),
    AFTER_SALE_IN_NEW(19, "退货入库"),
    LACK_GOODS_APPROVED(20, "缺货入库"),
    SKIP_STORE_IN(21, "越仓入库"),
    STOP_STORE_IN(22, "拦截入库"),
    OUT_MORE_IN(23, "多出入库"),

    CROSS_WAREHOUSE_IN(25, "越库入库"),

    PROCESSSING_TASK_IN(88, "加工入库"),



    UNKNOWN(999, "未知"),
    ;

    public static ProveBizType convert(Integer param) {
        return Arrays.stream(ProveBizType.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(ProveBizType.UNKNOWN);
    }

    Integer code;
    String desc;
}
