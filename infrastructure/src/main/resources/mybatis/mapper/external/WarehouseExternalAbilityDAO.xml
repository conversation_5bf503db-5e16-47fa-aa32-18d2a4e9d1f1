<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.external.WarehouseExternalAbilityDAO">
    <resultMap id="WarehouseExternalAbilityMap"
               type="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalAbilityDO">
        <id property="id" column="id"/>
        <result property="abilityCode" column="ability_code"/>
        <result property="abilityName" column="ability_name"/>
        <result property="abilityDesc" column="ability_desc"/>
        <result property="abilityStatus" column="ability_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="table_name">
        wms_warehouse_external_ability
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `ability_code`, `ability_name`, `ability_desc`, `ability_status`, `create_time`, `update_time`
    </sql>

    <sql id="values_exclude_id">
        #{abilityCode}, #{abilityName}, #{abilityDesc}, #{abilityStatus}, #{createTime}, #{updateTime}
    </sql>

    <sql id="query">
        <where>
            <if test="abilityCode != null">AND `ability_code` = #{abilityCode}</if>
            <if test="abilityName != null">AND `ability_name` = #{abilityName}</if>
            <if test="abilityDesc != null">AND `ability_desc` = #{abilityDesc}</if>
            <if test="abilityStatus != null">AND `ability_status` = #{abilityStatus}</if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
        </where>
    </sql>

    <insert id="create"
            parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalAbilityDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="abilityCode != null">`ability_code`,</if>
            <if test="abilityName != null">`ability_name`,</if>
            <if test="abilityDesc != null">`ability_desc`,</if>
            <if test="abilityStatus != null">`ability_status`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="abilityCode != null">#{abilityCode},</if>
            <if test="abilityName != null">#{abilityName},</if>
            <if test="abilityDesc != null">#{abilityDesc},</if>
            <if test="abilityStatus != null">#{abilityStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="creates"
            parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalAbilityDO"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.abilityCode}, #{i.abilityName}, #{i.abilityDesc}, #{i.abilityStatus}, #{i.createTime}, #{i.updateTime})
        </foreach>
    </insert>

    <update id="update"
            parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalAbilityDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="abilityCode != null">`ability_code` = #{abilityCode},</if>
            <if test="abilityName != null">`ability_name` = #{abilityName},</if>
            <if test="abilityDesc != null">`ability_desc` = #{abilityDesc},</if>
            <if test="abilityStatus != null">`ability_status` = #{abilityStatus},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="WarehouseExternalAbilityMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="WarehouseExternalAbilityMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalAbilityQueryParam"
            resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalAbilityQueryParam"
            resultMap="WarehouseExternalAbilityMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalAbilityQueryParam"
            resultMap="WarehouseExternalAbilityMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

</mapper>
