package net.summerfarm.wms.infrastructure.repository.arrivalNotice.impl;

import net.summerfarm.wms.domain.arrivalNotice.ArrivalNoticeRepository;
import net.summerfarm.wms.domain.arrivalNotice.domainobject.ArrivalNotice;
import net.summerfarm.wms.domain.arrivalNotice.domainobject.FindNotice;
import net.summerfarm.wms.infrastructure.dao.config.ArrivalNoticeDAO;
import net.summerfarm.wms.infrastructure.dao.config.dataobject.ArrivalNoticeDO;
import net.summerfarm.wms.infrastructure.repository.arrivalNotice.converter.ArrivalNoticeConvert;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ArrivalNoticeRepositoryImpl implements ArrivalNoticeRepository {

    @Resource
    private ArrivalNoticeDAO arrivalNoticeDAO;

    @Override
    public List<ArrivalNotice> findNoticeBySku(FindNotice findNotice) {
        List<ArrivalNoticeDO> arrivalNoticeDOS = arrivalNoticeDAO.selectBySku(findNotice.getSku(), findNotice.getWarehouseNo());
        return arrivalNoticeDOS.stream().map(ArrivalNoticeConvert.INSTANCE::convert).collect(Collectors.toList());
    }
}
