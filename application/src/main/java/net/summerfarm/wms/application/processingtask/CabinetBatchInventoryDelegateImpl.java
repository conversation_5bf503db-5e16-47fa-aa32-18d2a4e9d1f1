package net.summerfarm.wms.application.processingtask;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetBatchInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryAddReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryAddResp;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryReduceResp;
import net.summerfarm.wms.application.processingtask.converter.CabinetBatchInventoryAddReqConverter;
import net.summerfarm.wms.application.processingtask.converter.CabinetBatchInventoryReduceReqConverter;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.processingtask.delegate.CabinetBatchInventoryDelegate;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduce;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/6
 */
@Slf4j
@Component
public class CabinetBatchInventoryDelegateImpl implements CabinetBatchInventoryDelegate {

    @Resource
    private CabinetBatchInventoryCommandService cabinetBatchInventoryCommandService;

    @Override
    public String reduceCabinetBatchInventory(CabinetBatchInventoryReduce cabinetBatchInventoryReduce) {
        if (null == cabinetBatchInventoryReduce) {
            return null;
        }
        CabinetBatchInventoryReduceReq cabinetBatchInventoryReduceReq = CabinetBatchInventoryReduceReqConverter.INSTANCE.convert(cabinetBatchInventoryReduce);
        log.info("扣减库位批次库存代理入参：{}", JSON.toJSONStringWithDateFormat(cabinetBatchInventoryReduceReq, DateUtil.YYYY_MM_DD));
        CabinetBatchInventoryReduceResp cabinetBatchInventoryReduceResp = cabinetBatchInventoryCommandService.reduceCabinetBatchInventory(cabinetBatchInventoryReduceReq);
        log.info("扣减库位批次库存代理返回：{}", JSON.toJSONStringWithDateFormat(cabinetBatchInventoryReduceResp, DateUtil.YYYY_MM_DD));
        return JSON.toJSONString(cabinetBatchInventoryReduceResp);
    }

    @Override
    public String addCabinetBatchInventory(CabinetBatchInventoryAdd cabinetBatchInventoryAdd) {
        if (null == cabinetBatchInventoryAdd) {
            return null;
        }
        CabinetBatchInventoryAddReq cabinetBatchInventoryAddReq = CabinetBatchInventoryAddReqConverter.INSTANCE.convert(cabinetBatchInventoryAdd);
        log.info("增加库位批次库存代理入参：{}", JSON.toJSONStringWithDateFormat(cabinetBatchInventoryAddReq, DateUtil.YYYY_MM_DD));
        CabinetBatchInventoryAddResp cabinetBatchInventoryAddResp = cabinetBatchInventoryCommandService.addCabinetBatchInventory(cabinetBatchInventoryAddReq);
        log.info("增加库位批次库存代理返回：{}", JSON.toJSONStringWithDateFormat(cabinetBatchInventoryAddResp, DateUtil.YYYY_MM_DD));
        return JSON.toJSONString(cabinetBatchInventoryAddResp);
    }
}
