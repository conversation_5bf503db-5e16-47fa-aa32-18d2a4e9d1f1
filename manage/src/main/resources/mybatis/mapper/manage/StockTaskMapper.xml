<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskMapper">
    <select id="selectAllotIn" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq"
            resultType="net.summerfarm.wms.manage.model.vo.StockTaskVO">
        SELECT st.id,st.area_no areaNo,st.type,st.state,st.addtime,st.expect_time
        expectTime,st.updatetime,st.remark,st.dimension,st.category,st.task_no taskNo
        FROM stock_task st
        <if test="sku != null or pdId != null">
            INNER JOIN stock_storage_item sti ON st.id = sti.stock_task_id
        </if>
        <where>
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sti.sku = #{sku}
            </if>
            <if test="pdId != null">
                AND sti.sku in
                <foreach collection="querySkus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="state != null">
                AND st.state = #{state}
            </if>
            <if test="remark != null">
                AND st.remark = #{remark}
            </if>
            <if test="addtime != null">
                AND st.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="states != null and states.size!=0 ">
                AND st.state IN
                <foreach collection="states" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                AND st.expect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND st.expect_time <![CDATA[<]]> #{endTime}
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="selectAllotOut" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq"
            resultType="net.summerfarm.wms.manage.model.vo.StockTaskVO">
        SELECT st.id,st.task_no taskNo,st.area_no areaNo,st.out_store_no outStoreNo,st.type,st.expect_time
        expectTime,st.state,st.addtime,if(st.state in (1,2),updatetime,null) updatetime,st.process_state
        processState,st.category,st.option_flag optionFlag
        FROM stock_task st
        <if test="sku != null or pdId != null">
            INNER JOIN stock_shipment_item sti ON st.id = sti.stock_task_id
        </if>
        <where>
            <!--st.tenant_id = IFNULL(#{tenantId},1)-->
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size!=0 ">
                AND st.area_no IN
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sti.sku = #{sku}
            </if>
            <if test="pdId != null">
                AND sti.sku in
                <foreach collection="querySkus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="state != null">
                AND st.state = #{state}
            </if>
            <if test="remark != null">
                AND st.remark = #{remark}
            </if>
            <if test="addtime != null">
                AND st.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="states != null and states.size!=0 ">
                AND st.state IN
                <foreach collection="states" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startAddTime != null">
                AND st.addtime <![CDATA[>=]]> #{startAddTime}
            </if>
            <if test="endAddTime != null">
                AND st.addtime <![CDATA[<=]]> #{endAddTime}
            </if>
            <if test="taskNo != null">
                AND st.task_no = #{taskNo}
            </if>
            <if test="storeNo != null">
                AND st.out_store_no = #{storeNo}
            </if>
            <if test="mailPushState != null">
                AND (st.option_flag &amp; -9223372036854775807) = #{mailPushState}
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="selectWithItem" resultType="net.summerfarm.wms.manage.model.vo.StockTaskVO">
        SELECT t.id,t.task_no taskNo,t.area_no areaNo,t.out_store_no outStoreNo,t.type,t.expect_time
        expectTime,t.state,t.addtime,if(t.state in (1,2),updatetime,null) updatetime,t.process_state
        processState,t.category,t.option_flag optionFlag,t.outbound_category outboundCategory,t.external_warehouse_no externalWarehouseNo
        FROM stock_task t
        <if test="supplierId != null">
            LEFT JOIN purchases_plan pp on t.task_no = pp.purchase_no
        </if>
        <if test="sku != null or (querySkus != null and querySkus.size > 0)">
            INNER JOIN stock_task_item sti ON t.id=sti.stock_task_id
        </if>
        WHERE
        t.type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="stateList != null and stateList.size!=0 ">
            AND t.state IN
            <foreach collection="stateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="typeList != null and typeList.size!=0 ">
            AND t.type IN
            <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null">
            AND t.id = #{id}
        </if>
        <if test="sku != null">
            AND sti.sku LIKE concat('%',#{sku} ,'%')
        </if>
        <if test="querySkus != null and querySkus.size > 0">
            AND sti.sku in
            <foreach collection="querySkus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="areaNo != null">
            AND t.area_no = #{areaNo}
        </if>
        <if test="warehouseNoList != null and warehouseNoList.size!=0 ">
            AND t.area_no IN
            <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="state != null">
            AND t.state = #{state}
        </if>
        <if test="taskNo != null">
            AND t.task_no = #{taskNo}
        </if>
        <if test="storeNo != null">
            AND t.out_store_no = #{storeNo}
        </if>
        <if test="processState != null">
            AND t.process_state = #{processState}
        </if>
        <if test="supplierId != null">
            AND pp.supplier_id = #{supplierId}
        </if>
        <if test="startTime != null">
            AND t.expect_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND t.expect_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="mailPushState != null">
            AND (t.option_flag &amp; -9223372036854775807) = #{mailPushState}
        </if>
        <if test="startAddTime != null">
            AND t.addtime <![CDATA[>=]]> #{startAddTime}
        </if>
        <if test="endAddTime != null">
            AND t.addtime <![CDATA[<=]]> #{endAddTime}
        </if>
        <if test="outboundCategory != null">
            AND t.outbound_category = #{outboundCategory}
        </if>
        <!--AND t.tenant_id = IFNULL(#{tenantId},1)-->
        GROUP BY t.id
        ORDER BY t.id DESC
    </select>

    <select id="selectByPrimaryKey" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        /*FORCE_MASTER*/
        SELECT st.id,
               st.task_no            taskNo,
               st.out_store_no       outStoreNo,
               st.type,
               st.expect_time        expectTime,
               st.state,
               st.addtime,
               st.updatetime,
               st.area_no            areaNo,
               st.out_type           outType,
               st.remark,
               st.dimension,
               st.process_state      processState,
               st.remark,
               st.mismatch_reason as mismatchReason,
               task_type          as taskType,
               category,
               close_reason          closeReason,
               st.option_flag        optionFlag,
               st.tenant_id          tenantId,
               st.system_source      systemSource,
               st.out_order_no       outOrderNo,
               st.external_warehouse_no externalWarehouseNo
        FROM stock_task st
        WHERE st.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectOptionFlagByIdList" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT st.id, st.option_flag optionFlag
        FROM stock_task st
        WHERE st.id IN
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateOptionFlagById" parameterType="net.summerfarm.wms.manage.model.domain.StockTask">
        UPDATE stock_task
        SET option_flag = #{optionFlag}
        WHERE id = #{id}
    </update>

    <select id="selectListByPrimaryKey" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT st.id,
        st.task_no taskNo,
        st.out_store_no outStoreNo,
        st.type,
        st.expect_time expectTime,
        st.state,
        st.addtime,
        st.updatetime,
        st.area_no areaNo,
        st.out_type outType,
        st.remark,
        st.dimension,
        st.process_state processState,
        st.remark,
        st.mismatch_reason as mismatchReason,
        task_type as taskType,
        category,
        close_reason closeReason,
        st.option_flag optionFlag,
        st.system_source systemSource,
        st.external_warehouse_no externalWarehouseNo
        FROM stock_task st
        WHERE st.id IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectOptionFlagById" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT st.id, st.option_flag optionFlag
        FROM stock_task st
        WHERE st.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectOne" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        /*FORCE_MASTER*/ SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime, system_source systemSource
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
        limit 1
    </select>

    <update id="update" parameterType="net.summerfarm.wms.manage.model.domain.StockTask">
        UPDATE stock_task
        SET state = #{state},
        <if test="processState != null">
            process_state = #{processState},
        </if>
        <if test="mismatchReason != null">
            mismatch_reason = #{mismatchReason},
        </if>
        <if test="adminId != null">
            admin_id = #{adminId},
        </if>
        updatetime = #{updatetime}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByTaskNo" parameterType="net.summerfarm.wms.manage.model.domain.StockTaking">
        UPDATE stock_task
        SET state       = #{state},
            updatetime  = #{updatetime},
            expect_time = #{expectTime}
        WHERE task_no = #{taskNo}
    </update>

    <select id="selectStoreingSku" resultType="net.summerfarm.wms.manage.model.domain.StockTaskItem">
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type IN (12, 53)
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type = 51
          AND st.out_type = 0
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type IN (82, 56)
          AND sti.sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type = 51
          AND st.out_type = 1
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        select spi.sku
        from stock_task st
                 inner join stock_shipment_item spi on st.id = spi.stock_task_id
        where sku = #{sku}
          AND st.state IN (0, 1)
          and st.area_no = #{areaNo}
          and actual_quantity != quantity
        UNION ALL
        select sti.sku
        from stock_task st
                 inner join stock_storage_item sti on st.id = sti.stock_task_id
        where sku = #{sku}
          AND st.state IN (0, 1)
          and st.area_no = #{areaNo}
          and actual_quantity != quantity
    </select>

    <select id="selectPurchasingSku" resultType="net.summerfarm.wms.manage.model.domain.PurchasesPlan">
        SELECT pp.id,
               pp.purchase_no  purchaseNo,
               pp.title,
               pp.specification,
               pp.sku,
               pp.price,
               pp.quantity,
               pp.pack,
               pp.unit,
               pp.supplier,
               pp.supplier_id  supplierId,
               pp.check_report checkReport,
               pp.quality_date qualityDate,
               pp.in_quantity  inQuantity,
               pp.in_price     inPrice,
               pp.origin_id    originId
        FROM stock_task st
                 INNER JOIN purchases p ON st.task_no = p.purchase_no
                 INNER JOIN purchases_plan pp ON p.purchase_no = pp.purchase_no and pp.plan_status = 1
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND pp.sku = #{sku}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.manage.model.domain.StockTask">
        INSERT INTO stock_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                task_no,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="outStoreNo != null">
                out_store_no,
            </if>
            <if test="outType != null">
                out_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="dimension != null">
                dimension,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="externalWarehouseNo != null">
                external_warehouse_no,
            </if>
            <if test="optionFlag != null">
                option_flag,
            </if>
            inventory_locked
            <if test="outboundCategory != null">
                ,outbound_category
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                #{taskNo},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="expectTime != null">
                #{expectTime},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="outStoreNo != null">
                #{outStoreNo},
            </if>
            <if test="outType != null">
                #{outType},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="dimension != null">
                #{dimension},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="category != null">
                #{category},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="externalWarehouseNo != null">
                #{externalWarehouseNo},
            </if>
            <if test="optionFlag != null">
                #{optionFlag},
            </if>
            0
            <if test="outboundCategory != null">
                ,#{outbound_category}
            </if>
        </trim>
    </insert>

    <select id="queryWaveConfig" resultType="net.summerfarm.wms.manage.model.domain.StockTaskWaveConfig">
        select store_no     as storeNo,
               warehouse_no as warehouseNo,
               wave_time    as waveTime,
               wave_sku_option as waveSkuOption
        from wms_stock_task_wave_config
        where wave_status = 1
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.manage.model.domain.StockTask">
        UPDATE stock_task
        <set>
            <if test="taskNo != null">
                task_no = #{taskNo},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="closeReason != null">
                close_reason = #{closeReason},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectBySku" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE
        st.type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_shipment_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_storage_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        inner join wms_damage_stock_task sti on sti.stock_task_id =st.id
        left JOIN wms_damage_stock_item stid ON sti.id=stid.damage_stock_task_id
        and stid.quantity!=stid.should_quantity AND stid.sku =#{sku}
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
    </select>

    <select id="selectQuantitySumExcludeProducts" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq"
            resultType="net.summerfarm.wms.manage.model.vo.StoreRecordVO">
        select *
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (select sai.sku, sai.out_quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_allocation_list sal on st.task_no = sal.list_no
        left join stock_allocation_item sai on st.task_no = sai.list_no
        <where>
            and st.type = 50
            and st.state = 2
            <if test="areaNo != null">
                and sal.out_store = #{areaNo}
            </if>
            <if test="inStoreNo != null">
                <choose>
                    <when test="areaNo == inStoreNo">
                        and sal.in_store is null
                    </when>
                    <otherwise>
                        and sal.in_store = #{inStoreNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and sai.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and st.updatetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and st.updatetime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        union all
        select stpd.sku, stpd.quantity quantity, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            and st.type = 51
            and st.state in (1, 2)
            <if test="areaNo != null">
                <choose>
                    <when test="inStoreNo == null">
                        and st.area_no = #{areaNo}
                    </when>
                    <when test="areaNo == inStoreNo">
                        and st.area_no = #{areaNo}
                        and st.out_store_no is null
                    </when>
                    <otherwise>
                        and st.area_no = #{inStoreNo}
                        and st.out_store_no = #{areaNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectSaleTaskQuantitySumEx" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq"
            resultType="net.summerfarm.wms.manage.model.vo.StoreRecordVO">
        select *
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (
        select stpd.sku, stpd.quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            st.state in (1, 2)
            and ( (st.area_no = #{areaNo} and st.out_store_no = #{storeNo}) or (st.area_no = #{areaNo} and
            st.out_store_no is null) )
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectSaleTaskQuantitySumExCount" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq" resultType="java.lang.Integer">
        select count(*)
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (
        select stpd.sku, stpd.quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            st.state in (1, 2)
            and ( (st.area_no = #{areaNo} and st.out_store_no = #{storeNo}) or (st.area_no = #{areaNo} and
            st.out_store_no is null) )
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        ) tt
    </select>

    <select id="selectList" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT
        id,
        task_no taskNo,
        area_no areaNo,
        type,
        expect_time expectTime,
        state,
        addtime,
        updatetime,
        dimension
        dimension,
        tenant_id tenantId,
        inventory_locked as inventoryLocked,
        system_source systemSource
        FROM stock_task
        WHERE task_no  in
        <foreach collection="taskNoList" item="taskNo" open="(" close=")" separator=",">
            #{taskNo}
        </foreach>
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectListByOutStoreNo" resultType="net.summerfarm.wms.manage.model.domain.StockTask">
        SELECT
        id,
        task_no taskNo,
        area_no areaNo,
        out_store_no outStoreNo,
        type,
        expect_time expectTime,
        state,
        addtime,
        updatetime,
        dimension,
        tenant_id tenantId,
        inventory_locked inventoryLocked,
        option_flag optionFlag,
        system_source systemSource
        FROM stock_task
        WHERE area_no = #{areaNo}
        AND out_store_no = #{outStoreNo}
        AND expect_time = #{expectTime}
        AND type = #{type}
    </select>

    <select id="selectWaveNotOccupyTaskIds" resultType="java.lang.Integer">
        SELECT DISTINCT t1.id FROM (
            SELECT id FROM `stock_task`
            WHERE `option_flag` = 2
            AND `out_store_no` IN
            <foreach collection="outStoreNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND `type` = #{type}
            AND `expect_time` = #{expectTime}
        ) t1 LEFT JOIN `wms_stock_task_wave_sku_occupy` t2 ON t1.id = t2.`stock_task_id`
        WHERE t2.`stock_task_id` IS NULL
    </select>

    <select id="waitOutAllocationTask" resultType="net.summerfarm.wms.manage.model.vo.StockSubscribeVO">
        SELECT sai.sku, sai.out_quantity quantity
        FROM stock_allocation_list sal
                 INNER JOIN stock_allocation_item sai ON sal.list_no = sai.list_no
            AND sal.in_store = #{areaNo}
            AND sal.status = 3
            AND DATE (sal.expect_time) = #{expectTime}
    </select>

    <select id="selectQuantitySumEx" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq"
            resultType="net.summerfarm.wms.manage.model.vo.StoreRecordVO">
        select *
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (select sai.sku, sai.out_quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_allocation_list sal on st.task_no = sal.list_no
        left join stock_allocation_item sai on st.task_no = sai.list_no
        <where>
            and st.type = 50
            and st.state = 2
            <if test="areaNo != null">
                and sal.out_store = #{areaNo}
            </if>
            <if test="inStoreNo != null">
                <choose>
                    <when test="areaNo == inStoreNo">
                        and sal.in_store is null
                    </when>
                    <otherwise>
                        and sal.in_store = #{inStoreNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and sai.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and st.updatetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and st.updatetime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        union all
        select stpd.sku, stpd.quantity quantity, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            and st.type = 51
            and st.state in (1, 2)
            <if test="areaNo != null">
                <choose>
                    <when test="inStoreNo == null">
                        and st.area_no = #{areaNo}
                    </when>
                    <when test="areaNo == inStoreNo">
                        and st.area_no = #{areaNo}
                        and st.out_store_no is null
                    </when>
                    <otherwise>
                        and st.area_no = #{inStoreNo}
                        and st.out_store_no = #{areaNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectQuantitySumExCount" parameterType="net.summerfarm.wms.manage.web.req.StockTaskReq" resultType="java.lang.Integer">
        select count(*)
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (select sai.sku, sai.out_quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_allocation_list sal on st.task_no = sal.list_no
        left join stock_allocation_item sai on st.task_no = sai.list_no
        <where>
            and st.type = 50
            and st.state = 2
            <if test="areaNo != null">
                and sal.out_store = #{areaNo}
            </if>
            <if test="inStoreNo != null">
                <choose>
                    <when test="areaNo == inStoreNo">
                        and sal.in_store is null
                    </when>
                    <otherwise>
                        and sal.in_store = #{inStoreNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and sai.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and st.updatetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and st.updatetime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        union all
        select stpd.sku, stpd.quantity quantity, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            and st.type = 51
            and st.state in (1, 2)
            <if test="areaNo != null">
                <choose>
                    <when test="inStoreNo == null">
                        and st.area_no = #{areaNo}
                    </when>
                    <when test="areaNo == inStoreNo">
                        and st.area_no = #{areaNo}
                        and st.out_store_no is null
                    </when>
                    <otherwise>
                        and st.area_no = #{inStoreNo}
                        and st.out_store_no = #{areaNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
    </select>
</mapper>