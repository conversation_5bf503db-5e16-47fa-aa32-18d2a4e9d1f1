package net.summerfarm.wms.manage.web.enums;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
public enum QualityTimeUnitEnum {

    DAY("day", "日"),
    MONTH("month", "月"),
    ;

    private String code;
    private String desc;

    QualityTimeUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (QualityTimeUnitEnum qualityTimeUnitEnum : QualityTimeUnitEnum.values()) {
            if (qualityTimeUnitEnum.getCode().equals(code)) {
                return qualityTimeUnitEnum.getDesc();
            }
        }
        return "";
    }

}
