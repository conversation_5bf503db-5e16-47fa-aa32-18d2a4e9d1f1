package net.summerfarm.wms.application.external.handler;

import com.alibaba.fastjson.JSON;
import com.kingdee.service.data.entity.InboundSaveReq;
import com.kingdee.service.data.entity.RtnSaveReq;
import com.kingdee.service.data.entity.SalInBoundSaveReq;
import com.kingdee.service.data.entity.SalOutBoundSaveReq;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.context.ExternalAbilitySpec;
import net.summerfarm.wms.application.external.context.ExternalRouteSpec;
import net.summerfarm.wms.application.external.factory.KDStockInReqFactory;
import net.summerfarm.wms.application.external.factory.KDStockOutReqFactory;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.enums.ExternalStatusEnum;
import net.summerfarm.wms.domain.external.entity.WmsExternalBusinessTransferRecordEntity;
import net.summerfarm.wms.domain.external.param.WmsExternalOrderMappingCommandParam;
import net.summerfarm.wms.domain.external.repository.WmsExternalBusinessTransferRecordCommandRepository;
import net.summerfarm.wms.domain.external.repository.WmsExternalBusinessTransferRecordQueryRepository;
import net.summerfarm.wms.domain.external.repository.WmsExternalOrderMappingCommandRepository;
import net.summerfarm.wms.domain.external.repository.WmsExternalOrderMappingQueryRepository;
import net.summerfarm.wms.domain.instore.domainobject.InBoundOrder;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.facade.openapi.ExternalStockInFacade;
import net.summerfarm.wms.facade.openapi.ExternalStockOutFacade;
import net.summerfarm.wms.openapi.stockin.xm.req.StockInCreateNoticeReq;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateNoticeReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 金蝶外部路由处理器
 * @date 2024/4/29
 */
@Slf4j
@Component
@ExternalRouteSpec(appKey = ExternalCodeConstant.KD_APP_KEY, desc = "三方金蝶")
public class KDExternalAbilityHandler {

    @Resource
    private ExternalStockOutFacade externalStockOutFacade;
    @Resource
    private ExternalStockInFacade externalStockInFacade;
    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private KDStockOutReqFactory kdStockOutReqFactory;
    @Resource
    private KDStockInReqFactory kdStockInReqFactory;
    @Resource
    private WmsExternalBusinessTransferRecordQueryRepository wmsExternalBusinessTransferRecordQueryRepository;
    @Resource
    private WmsExternalBusinessTransferRecordCommandRepository wmsExternalBusinessTransferRecordCommandRepository;
    @Resource
    private WmsExternalOrderMappingCommandRepository wmsExternalOrderMappingCommandRepository;
    @Resource
    private WmsExternalOrderMappingQueryRepository wmsExternalOrderMappingQueryRepository;
    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private InBoundOrderQueryRepository inBoundOrderQueryRepository;

    @ExternalAbilitySpec(code = ExternalCodeConstant.STOCK_OUT_BILL_NOTICE, desc = "出库单据推送")
    public void stockOutNotice(StockOutCreateNoticeReq stockOutCreateNoticeReq) {
        log.info("三方金蝶出库单保存推送通知，{}", JSON.toJSONString(stockOutCreateNoticeReq));
        if (null == stockOutCreateNoticeReq) {
            return;
        }
        // 任务校验
        StockTask stockTask = stockTaskRepository.findOutStockTask(Long.valueOf(stockOutCreateNoticeReq.getStockOutNo()));
        if (null == stockTask) {
            log.info("出库任务不存在，出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
            return;
        }
        // 状态校验
        boolean thisOrderHasSend = wmsExternalBusinessTransferRecordQueryRepository.hasSend(
                ExternalCodeConstant.KD_APP_KEY,
                ExternalCodeConstant.STOCK_OUT_BILL_NOTICE,
                stockTask.getType(),
                stockOutCreateNoticeReq.getStockOutNo()
        );
        if (thisOrderHasSend) {
            log.info("已下发外部>>> 过滤返回>>> 出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
            return;
        }
        // 组装通知
        SalOutBoundSaveReq salOutBoundSaveReq = kdStockOutReqFactory.buildStockOutNotice(stockOutCreateNoticeReq, stockTask);
        // 出库单据推送金蝶
        String externalOrder = externalStockOutFacade.kdStockOutBillNotice(salOutBoundSaveReq);
        if (StringUtils.isBlank(externalOrder)) {
            log.info("三方金蝶出库单保存推送失败，出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
            return;
        }
        // 保存外部单据号
        wmsExternalOrderMappingCommandRepository.insertSelective(WmsExternalOrderMappingCommandParam.builder()
                .externalNo(externalOrder)
                .bizNo(stockOutCreateNoticeReq.getStockOutItems().get(0).getOutOrderNo())
                .internalNo(stockTask.getId().toString())
                .type(stockTask.getType())
                .appKey(ExternalCodeConstant.KD_APP_KEY)
                .warehouseNo(stockTask.getAreaNo())
                .tenantId(stockTask.getTenantId())
                .warehouseTenantId(stockTask.getTenantId())
                .createTime(LocalDateTime.now())
                .build());
        // 保存推送状态
        wmsExternalBusinessTransferRecordCommandRepository.insert(WmsExternalBusinessTransferRecordEntity.builder()
                .externalAppKey(ExternalCodeConstant.KD_APP_KEY)
                .abilityCode(ExternalCodeConstant.STOCK_OUT_BILL_NOTICE)
                .orderType(stockTask.getType())
                .bizCode(stockOutCreateNoticeReq.getStockOutNo())
                .externalStatus(ExternalStatusEnum.REPORTED.getCode())
                .build());
        log.info("三方金蝶出库单保存推送完成，出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
    }

    @ExternalAbilitySpec(code = ExternalCodeConstant.PURCHASE_OUT_BILL_NOTICE, desc = "采退出库单据推送")
    public void purchaseOutNotice(StockOutCreateNoticeReq stockOutCreateNoticeReq) {
        log.info("三方金蝶采退出库单保存推送通知，{}", JSON.toJSONString(stockOutCreateNoticeReq));
        if (null == stockOutCreateNoticeReq) {
            return;
        }
        // 任务校验
        StockTask stockTask = stockTaskRepository.findOutStockTask(Long.valueOf(stockOutCreateNoticeReq.getStockOutNo()));
        if (null == stockTask) {
            log.info("出库任务不存在，出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
            return;
        }
        // 组装通知
        List<RtnSaveReq> rtnSaveReqList = kdStockOutReqFactory.buildPurchaseOutNotice(stockOutCreateNoticeReq, stockTask);
        if (CollectionUtils.isEmpty(rtnSaveReqList)) {
            return;
        }
        for (RtnSaveReq rtnSaveReq : rtnSaveReqList) {
            // 状态校验
            boolean thisOrderHasSend = wmsExternalBusinessTransferRecordQueryRepository.hasSend(
                    ExternalCodeConstant.KD_APP_KEY,
                    ExternalCodeConstant.PURCHASE_OUT_BILL_NOTICE,
                    stockTask.getType(),
                    rtnSaveReq.getBillNo()
            );
            if (thisOrderHasSend) {
                log.info("已下发外部>>> 过滤返回>>> 采退出库组合编号：{}", rtnSaveReq.getBillNo());
                continue;
            }
            // 出库单据推送金蝶
            String externalOrder = externalStockOutFacade.kdPurchaseOutBillNotice(rtnSaveReq);
            if (StringUtils.isBlank(externalOrder)) {
                log.info("三方金蝶采退出库单保存推送失败，采退出库组合编号：{}", rtnSaveReq.getBillNo());
                continue;
            }
            // 保存外部单据号
            wmsExternalOrderMappingCommandRepository.insertSelective(WmsExternalOrderMappingCommandParam.builder()
                    .externalNo(externalOrder)
                    .bizNo(stockTask.getTaskNo())
                    .internalNo(rtnSaveReq.getBillNo())
                    .type(stockTask.getType())
                    .appKey(ExternalCodeConstant.KD_APP_KEY)
                    .warehouseNo(stockTask.getAreaNo())
                    .tenantId(stockTask.getTenantId())
                    .warehouseTenantId(stockTask.getTenantId())
                    .createTime(LocalDateTime.now())
                    .build());
            // 保存推送状态
            wmsExternalBusinessTransferRecordCommandRepository.insert(WmsExternalBusinessTransferRecordEntity.builder()
                    .externalAppKey(ExternalCodeConstant.KD_APP_KEY)
                    .abilityCode(ExternalCodeConstant.PURCHASE_OUT_BILL_NOTICE)
                    .orderType(stockTask.getType())
                    .bizCode(rtnSaveReq.getBillNo())
                    .externalStatus(ExternalStatusEnum.REPORTED.getCode())
                    .build());
        }
        log.info("三方金蝶采退出库单保存推送完成，出库任务id：{}", stockOutCreateNoticeReq.getStockOutNo());
    }

    @ExternalAbilitySpec(code = ExternalCodeConstant.STOCK_IN_BILL_NOTICE, desc = "入库单据推送")
    public void stockInNotice(StockInCreateNoticeReq stockInCreateNoticeReq) {
        log.info("三方金蝶入库单保存推送通知，{}", JSON.toJSONString(stockInCreateNoticeReq));
        if (null == stockInCreateNoticeReq) {
            return;
        }
        // 任务校验
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(Long.valueOf(stockInCreateNoticeReq.getInboundTaskId()));
        if (null == stockTaskStorage) {
            log.info("入库任务不存在，入库任务id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 状态校验
        boolean thisOrderHasSend = wmsExternalBusinessTransferRecordQueryRepository.hasSend(
                ExternalCodeConstant.KD_APP_KEY,
                ExternalCodeConstant.STOCK_IN_BILL_NOTICE,
                stockInCreateNoticeReq.getInboundType(),
                stockInCreateNoticeReq.getInboundTaskId()
        );
        if (thisOrderHasSend) {
            log.info("已下发外部>>> 过滤返回>>> 入库任务id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 组装通知
        SalInBoundSaveReq salInBoundSaveReq = kdStockInReqFactory.buildStockInNotice(stockInCreateNoticeReq, stockTaskStorage);
        // 入库下发金蝶
        String externalOrder = externalStockInFacade.kdStockInBillNotice(salInBoundSaveReq);
        if (StringUtils.isBlank(externalOrder)) {
            log.info("三方金蝶入库单保存推送失败，入库任务id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 保存外部单据号
        wmsExternalOrderMappingCommandRepository.insertSelective(WmsExternalOrderMappingCommandParam.builder()
                .externalNo(externalOrder)
                .bizNo(stockTaskStorage.getSourceId())
                .internalNo(stockTaskStorage.getId().toString())
                .type(stockTaskStorage.getType())
                .appKey(ExternalCodeConstant.KD_APP_KEY)
                .warehouseNo(stockTaskStorage.getInWarehouseNo())
                .tenantId(stockTaskStorage.getTenantId())
                .warehouseTenantId(stockTaskStorage.getTenantId())
                .createTime(LocalDateTime.now())
                .build());
        // 更新下发状态
        wmsExternalBusinessTransferRecordCommandRepository.insert(WmsExternalBusinessTransferRecordEntity.builder()
                .externalAppKey(ExternalCodeConstant.KD_APP_KEY)
                .abilityCode(ExternalCodeConstant.STOCK_IN_BILL_NOTICE)
                .orderType(stockInCreateNoticeReq.getInboundType())
                .bizCode(stockInCreateNoticeReq.getInboundTaskId())
                .externalStatus(ExternalStatusEnum.REPORTED.getCode())
                .build());
        log.info("三方金蝶入库单保存推送完成，入库任务id：{}", stockInCreateNoticeReq.getInboundTaskId());
    }

    @ExternalAbilitySpec(code = ExternalCodeConstant.PURCHASE_IN_BILL_NOTICE, desc = "采购入库单据推送")
    public void purchaseInNotice(StockInCreateNoticeReq stockInCreateNoticeReq) {
        log.info("三方金蝶采购入库单保存推送通知，{}", JSON.toJSONString(stockInCreateNoticeReq));
        if (null == stockInCreateNoticeReq) {
            return;
        }
        // 任务校验
        InBoundOrder inBoundOrder = inBoundOrderQueryRepository.findInBoundOrder(Long.valueOf(stockInCreateNoticeReq.getInboundTaskId()));
        if (null == inBoundOrder) {
            log.info("入库单不存在，入库单id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(inBoundOrder.getStockStorageTaskId());
        if (null == stockTaskStorage) {
            log.info("入库任务不存在，入库单id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 状态校验
        boolean thisOrderHasSend = wmsExternalBusinessTransferRecordQueryRepository.hasSend(
                ExternalCodeConstant.KD_APP_KEY,
                ExternalCodeConstant.PURCHASE_IN_BILL_NOTICE,
                stockInCreateNoticeReq.getInboundType(),
                inBoundOrder.getId().toString()
        );
        if (thisOrderHasSend) {
            log.info("已下发外部>>> 过滤返回>>> 入库单id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 组装通知
        InboundSaveReq inboundSaveReq = kdStockInReqFactory.buildPurchaseInNotice(stockInCreateNoticeReq, stockTaskStorage);
        // 入库下发金蝶
        String externalOrder = externalStockInFacade.kdPurchaseInBillNotice(inboundSaveReq);
        if (StringUtils.isBlank(externalOrder)) {
            log.info("三方金蝶采购入库单保存推送失败，入库单id：{}", stockInCreateNoticeReq.getInboundTaskId());
            return;
        }
        // 保存外部单据号
        wmsExternalOrderMappingCommandRepository.insertSelective(WmsExternalOrderMappingCommandParam.builder()
                .externalNo(externalOrder)
                .bizNo(stockTaskStorage.getSourceId())
                .internalNo(inBoundOrder.getId().toString())
                .type(stockTaskStorage.getType())
                .appKey(ExternalCodeConstant.KD_APP_KEY)
                .warehouseNo(stockTaskStorage.getInWarehouseNo())
                .tenantId(stockTaskStorage.getTenantId())
                .warehouseTenantId(stockTaskStorage.getTenantId())
                .createTime(LocalDateTime.now())
                .build());
        // 更新下发状态
        wmsExternalBusinessTransferRecordCommandRepository.insert(WmsExternalBusinessTransferRecordEntity.builder()
                .externalAppKey(ExternalCodeConstant.KD_APP_KEY)
                .abilityCode(ExternalCodeConstant.PURCHASE_IN_BILL_NOTICE)
                .orderType(stockInCreateNoticeReq.getInboundType())
                .bizCode(stockInCreateNoticeReq.getInboundTaskId())
                .externalStatus(ExternalStatusEnum.REPORTED.getCode())
                .build());
        log.info("三方金蝶采购入库单保存推送完成，入库单id：{}", stockInCreateNoticeReq.getInboundTaskId());
    }


}
