package net.summerfarm.wms;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.wms.inbound.InboundTaskCreateMessage;
import net.summerfarm.ofc.client.common.message.wms.inbound.InboundTaskItemMessage;
import net.summerfarm.wms.api.h5.backup.req.StorageItemDetailKeyCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStorageCommandDTO;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStorageItemCommandDTO;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStorageItemDetailCommandDTO;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStoragePrintDetailQuery;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStoragePrintDetailResDTO;
import net.summerfarm.wms.api.inner.instore.StockStorageInnerService;
import net.summerfarm.wms.application.acl.mq.SaaSAfterSaleInStoreListener;
import net.summerfarm.wms.facade.warehouse.WarehouseWmsFacade;
import net.summerfarm.wms.infrastructure.dao.instore.StockTaskStorageDAO;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemCreateDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemDetailCreateDTO;
import net.summerfarm.wms.starter.WmsApplication;
import net.summerfarm.wms.web.controller.backup.BackupController;
import net.summerfarm.wms.web.controller.instore.StockStorageController;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class StockTaskStorageTest {

    @Resource
    private SaaSAfterSaleInStoreListener saaSAfterSaleInStoreListener;
    @Resource
    private WarehouseWmsFacade warehouseWmsFacade;
    @Resource
    private StockStorageInnerService stockStorageCommandBizService;
    @Resource
    private StockStorageController stockStorageController;
    @Resource
    private BackupController backupController;
    @Resource
    private StockTaskStorageDAO stockTaskStorageDAO;

    @Test
    public void testCreateSaaSInStore() {
        InboundTaskCreateMessage create = new InboundTaskCreateMessage();
        create.setAfterSaleOrderNo("1234567890");
        create.setExpectTime(LocalDateTime.of(2023, 4, 26, 10, 43, 24, 738));
        create.setFulfillmentNo("12345");
        create.setInWarehouseNo(240);
        create.setMName("廉思意测试勿动");
        create.setOperator("廉思意测试勿动");
        create.setSourceNo("123456789");
        create.setTenantId(2L);


        List<InboundTaskItemMessage> itemList = new ArrayList<>();
        InboundTaskItemMessage item = new InboundTaskItemMessage();
        item.setQuantity(12);
        item.setSku("1029710618644");
        item.setAfterSaleReason("包装问题");
        itemList.add(item);
        create.setItemList(itemList);

        saaSAfterSaleInStoreListener.process(create);
    }

    @Test
    public void testWarehouse() {
        List<Integer> tenantWarehouseNoList = warehouseWmsFacade.getTenantWarehouseNoList(1L);
        System.out.println(JSON.toJSONString(tenantWarehouseNoList));
    }

    @Test
    public void testCreate() {
        String dataStr = "{\"addStore\":false,\"expectTime\":\"2023-05-25T00:00:00\",\"inWarehouseNo\":4,\"inWarehouseServiceName\":\"杭州鲜沐科技有限公司\",\"operatorName\":\"kimer\",\"remark\":\"\",\"sourceNo\":\"2023052533662004\",\"stockTaskId\":201821,\"storageItemCreateDTOList\":[{\"categoryType\":2,\"packaging\":\"包\",\"pdName\":\"芜湖芜湖芜湖芜湖\",\"quantity\":100,\"sku\":\"858317005357\",\"skuType\":0,\"specification\":\"1mL*1mL/一级/1\",\"storageItemDetailCreateDTOList\":[{\"productionDate\":\"2023-05-25\",\"purchaseNo\":\"2023052533662004\",\"qualityDate\":\"2023-06-24\",\"quantity\":10,\"sku\":\"858317005357\"},{\"productionDate\":\"2023-06-08\",\"purchaseNo\":\"2023052533662004\",\"qualityDate\":\"2023-07-08\",\"quantity\":90,\"sku\":\"858317005357\"}],\"supplier\":\"供应商E\",\"supplierId\":22,\"tenantId\":1}],\"tenantId\":1,\"type\":11}";
        log.info("开始处理入库任务生成消息:[{}]", dataStr);
        StockStorageCreateReqDTO createReqDTO = JSON.parseObject(dataStr, StockStorageCreateReqDTO.class);

        StockStorageCommandDTO commandDTO = StockStorageCommandDTO.builder()
                .adminId(createReqDTO.getAdminId())
                .userOperatorName(createReqDTO.getOperatorName())
                .type(createReqDTO.getType())
                .inWarehouseNo(createReqDTO.getInWarehouseNo())
                .outWarehouseNo(createReqDTO.getOutWarehouseNo())
                .inWarehouseName(createReqDTO.getInWarehouseName())
                .outWarehouseName(createReqDTO.getOutWarehouseName())
                .expectTime(createReqDTO.getExpectTime())
                .sourceNo(createReqDTO.getSourceNo())
                .tenantId(createReqDTO.getTenantId())
                .afterSaleOrderNo(createReqDTO.getAfterSaleOrderNo())
                .outWarehouseNo(createReqDTO.getOutWarehouseNo()).build();
        List<StockStorageItemCreateDTO> storageItemCreateDTOList = createReqDTO.getStorageItemCreateDTOList();

        List<StockStorageItemCommandDTO> stockStorageItemCommandDTOList = storageItemCreateDTOList.stream().map(createDTO -> {
            List<StockStorageItemDetailCreateDTO> detailCreateDTOS = createDTO.getStorageItemDetailCreateDTOList();
            //item
            StockStorageItemCommandDTO itemCommandDTO = StockStorageItemCommandDTO.builder().quantity(createDTO.getQuantity())
                    .sku(createDTO.getSku())
                    .pdName(createDTO.getPdName())
                    .supplierId(createDTO.getSupplierId())
                    .supplier(createDTO.getSupplier())
                    .specification(createDTO.getSpecification())
                    .packaging(createDTO.getPackaging())
                    .skuType(createDTO.getSkuType())
                    .categoryType(createDTO.getCategoryType())
                    .temperature(createDTO.getTemperature())
                    .tenantId(createDTO.getTenantId())
                    .afterSaleOrderNo(createDTO.getAfterSaleOrderNo())
                    .build();
            List<StockStorageItemDetailCommandDTO> itemDetailCommandDTOS = new ArrayList<>();

            //detail
            if (!CollectionUtils.isEmpty(detailCreateDTOS)) {
                itemDetailCommandDTOS = detailCreateDTOS.stream().map(detailCreate -> StockStorageItemDetailCommandDTO.builder().
                        sku(detailCreate.getSku())
                        .productionDate(detailCreate.getProductionDate())
                        .qualityDate(detailCreate.getQualityDate())
                        .quantity(detailCreate.getQuantity())
                        .pdName(detailCreate.getPdName())
                        .purchaseNo(detailCreate.getPurchaseNo())
                        .tenantId(detailCreate.getTenantId())
                        .build()).collect(Collectors.toList());
            }
            itemCommandDTO.setItemDetailCommandDTOList(itemDetailCommandDTOS);
            return itemCommandDTO;
        }).collect(Collectors.toList());
        commandDTO.setAutoCompete(createReqDTO.getAddStore());
        commandDTO.setTaskId(createReqDTO.getStockTaskId());
        commandDTO.setOwnership(createReqDTO.getMName());
        commandDTO.setStockStorageItemCommandDTOList(stockStorageItemCommandDTOList);
        commandDTO.setRemark(createReqDTO.getRemark());
        stockStorageCommandBizService.createStockStorage(commandDTO);
    }

    @Test
    public void testPrint() {
        CommonResult<StockStoragePrintDetailResDTO> result = stockStorageController.printDetail(
                StockStoragePrintDetailQuery.builder().taskId(Long.valueOf("480540"))
                        .build());
        System.out.println(result);
    }

    @Test
    public void testSetItemId() {
        CommonResult<String> booleanCommonResult = backupController.upsertQueryStorageItemDetailKey(StorageItemDetailKeyCommand.builder().id("300000").build());
        System.out.println(booleanCommonResult);
    }
}
