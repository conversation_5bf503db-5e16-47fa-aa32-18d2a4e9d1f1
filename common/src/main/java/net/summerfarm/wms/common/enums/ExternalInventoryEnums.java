package net.summerfarm.wms.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * 外部库存记录表中的枚举
 */
public interface ExternalInventoryEnums {

    /**
     * 数据状态
     */
    @Getter
    @AllArgsConstructor
    enum Status implements Enum2Args {

        INVALID(0, "废弃"),
        VALID(1, "启用")
        ;

        private Integer value;
        private String content;

        public static Status getEnum(Integer value){
            for (Status enums : Status.values()){
                if (enums.value.equals(value)){
                    return enums;
                }
            }
            return null;
        }
    }

    /**
     * 差异标识（0:无差异，1:有差异）
     */
    @Getter
    @AllArgsConstructor
    enum DifferOption implements Enum2Args {

        NO_DIFFERENCE(0, "无差异"),
        HAVE_DIFFERENCE(1, "有差异")
        ;

        private Integer value;
        private String content;

        public static DifferOption getEnum(Integer value){
            for (DifferOption enums : DifferOption.values()){
                if (enums.value.equals(value)){
                    return enums;
                }
            }
            return null;
        }
    }



}
