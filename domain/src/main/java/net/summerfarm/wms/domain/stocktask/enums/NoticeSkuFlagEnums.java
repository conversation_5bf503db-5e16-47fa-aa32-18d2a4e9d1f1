package net.summerfarm.wms.domain.stocktask.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知单状态标记
 * @author: xdc
 * @date: 2024/3/11
 **/
@Getter
@AllArgsConstructor
public enum NoticeSkuFlagEnums {

    /**
     * 非代销不入仓品
     */
    ALL_NORMAL_SKU(1, "全部普通的sku"),

    /**
     * 1-非代销不入仓品 2-代销不入仓品 3-混合品
     */
    ALL_DX_NOT_IN_STORE_SKU(2, "全部代销不入仓品"),

    /**
     * 有代销不入仓的品 有非代销不入仓的品
     */
    MIX_SKU(3, "混合品"),


    /**
     * POP品
     */
    POP_SKU(4, "POP品"),
    ;



    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.code).equals(input);
    }
}
