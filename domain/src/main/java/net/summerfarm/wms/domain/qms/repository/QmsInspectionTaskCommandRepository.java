package net.summerfarm.wms.domain.qms.repository;



import net.summerfarm.wms.domain.qms.entity.QmsInspectionTaskEntity;
import net.summerfarm.wms.domain.qms.param.command.QmsInspectionTaskCommandParam;




/**
*
* <AUTHOR>
* @date 2024-04-02 09:34:21
* @version 1.0
*
*/
public interface QmsInspectionTaskCommandRepository {

    QmsInspectionTaskEntity insertSelective(QmsInspectionTaskCommandParam param);

    int updateSelectiveById(QmsInspectionTaskCommandParam param);

    int remove(Long id);

    void updateInspStatus(Long id, Integer inspState);
}