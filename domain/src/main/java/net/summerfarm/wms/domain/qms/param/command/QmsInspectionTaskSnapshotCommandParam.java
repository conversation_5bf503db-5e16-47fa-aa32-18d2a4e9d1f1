package net.summerfarm.wms.domain.qms.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-04-18 11:01:07
 * @version 1.0
 *
 */
@Data
public class QmsInspectionTaskSnapshotCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * qms验货任务id
	 */
	private Long qmsInspectionTaskId;

	/**
	 * 验货规则快照，json/ossaddress, 1-json,2-ossaddress
	 */
	private Integer inspRuleSnapshotType;

	/**
	 * 验货规则快照
	 */
	private String inspRuleSnapshot;

	/**
	 * 验货比例快照，json/ossaddress, 1-json,2-ossaddress
	 */
	private Integer inspScaleSnapshotType;

	/**
	 * 验货比例快照
	 */
	private String inspScaleSnapshot;

	

	
}