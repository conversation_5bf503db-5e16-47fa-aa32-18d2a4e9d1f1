<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stocktaking.StocktakingItemDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StockTakingItemDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="taking_id" jdbcType="BIGINT" property="stocktakingId"/>
        <result column="store_quantity" jdbcType="BIGINT" property="stockNum"/>
        <result column="weight" jdbcType="VARCHAR" property="specification"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="pd_name" jdbcType="VARCHAR" property="pdName"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,taking_id,store_quantity,weight,pd_name,sku,created_at,updated_at,warehouse_no
    </sql>

    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StockTakingItemDO">
        insert into stock_taking_item(taking_id, store_quantity, weight, pd_name, sku, warehouse_no)
        values (#{stocktakingId},
                #{stockNum},
                #{specification},
                #{pdName},
                #{sku},
                #{warehouseNo})
    </insert>

    <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into stock_taking_item(
        taking_id,
        store_quantity,
        weight,
        pd_name,
        sku,
        warehouse_no,
        tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.stocktakingId},
            #{item.stockNum},
            #{item.specification},
            #{item.pdName},
            #{item.sku},
            #{item.warehouseNo},
            <choose>
                <when test="item.tenantId != null">
                    #{item.tenantId,jdbcType=BIGINT}
                </when>
                <otherwise>
                    DEFAULT
                </otherwise>
            </choose>)
        </foreach>
    </insert>

    <select id="selectIds" resultType="java.lang.Long">
        select id
        from stock_taking_item
        where taking_id = #{stocktakingId}
    </select>

    <select id="selectByTakingId" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_item
        where taking_id = #{stocktakingId}
    </select>

    <select id="selectById" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_item
        where id = #{id}
    </select>

    <select id="selectSkuAndItemId"
            resultType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.SkuItemIdDO">
        select sku,
               id
        from stock_taking_item
        where taking_id = #{stocktakingId}
        order by id desc
    </select>

    <select id="selectByIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_item
        where taking_id in
        <foreach collection="list" item="stocktakingId" open="(" close=")" separator=",">
            #{stocktakingId}
        </foreach>
    </select>

    <select id="selectBySku" resultType="java.lang.Long">
        select distinct(taking_id)
        from stock_taking_item
        <where>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo}
            </if>
            <if test="sku != null">
                and sku like concat(#{sku},'%')
            </if>
            <if test="pdName != null">
                and pd_name like concat(#{pdName},'%')
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId,jdbcType=BIGINT}
            </if>
            <if test="tenantWarehouseNoList != null and tenantWarehouseNoList.size() != 0">
                and warehouse_no in
                <foreach collection="tenantWarehouseNoList" item="warehouseNo" separator="," open="(" close=")">
                    #{warehouseNo}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectItemIdByTakingIdAndSku" resultType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.SkuItemIdDO">
        select id, sku
        from stock_taking_item
        where taking_id = #{stocktakingId}
        and sku = #{sku}
        limit 1
    </select>

</mapper>