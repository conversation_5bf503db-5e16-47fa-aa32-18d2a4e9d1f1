package net.summerfarm.wms.manage.model.domain.bo;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.manage.model.domain.GoodsLocationInfo;
import net.summerfarm.wms.manage.model.domain.TransferOutInfo;
import net.summerfarm.wms.manage.web.enums.TransferOpEnum;

import java.util.List;
import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/12/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOpBO {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 转换任务id
     */
    Long stockTransferId;

    /**
     * 转换实例编号
     */
    Long stockTransferItemId;

    /**
     * 转换操作类型
     *
     * @see TransferOpEnum
     */
    Integer type;

    /**
     * 生产日期
     */
    Long produceDate;

    /**
     * 转换比例
     */
    String transferRatio;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 库存转出信息
     */
    List<TransferOutInfo> transferOutInfos;

    /**
     * 操作人
     */
    String operator;

    /**
     * 货位信息，后续拿掉
     */
    List<GoodsLocationInfo> glInfos;

    /**
     * 仅转入场景的转入信息
     */
    List<TransferOnlyInInfoBO> transferOnlyInInfos;

    public Integer getType() {
        if (Objects.isNull(type)) {
            return TransferOpEnum.ONE_WAY.getCode();
        }
        return type;
    }

    public String calTransferRatio() {
        String[] split = transferRatio.split(":");
        int commonDivisor = getCommonDivisor(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
        return Integer.parseInt(split[0]) / commonDivisor + ":" + Integer.parseInt(split[1]) / commonDivisor;
    }

    private int getCommonDivisor(int a, int b) {
        while (b != 0) {
            int temp = a % b;
            a = b;
            b = temp;
        }
        return a;
    }

}
