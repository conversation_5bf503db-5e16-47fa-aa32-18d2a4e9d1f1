package net.summerfarm.wms.api.h5.stocktaking.dto.res;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 盘点任务详情
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTakingDetailResDTO implements Serializable {
    private static final long serialVersionUID = 8759714625407118365L;
    /**
     * 任务id
     */
    Long taskId;

    /**
     * 仓库编号
     */
    Long warehouseNo;

    /**
     * 周期
     */
    Integer cycle;

    /**
     * 盘点维度
     */
    Integer dimension;

    /**
     * 盘点方式
     */
    Integer method;

    /**
     * 任务状态
     */
    Integer state;

    /**
     * 审核状态
     */
    Integer auditState;

    /**
     * 创建时间
     */
    Long createdAt;
}
