package net.summerfarm.wms.api.h5.outstore.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/9/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CrossWarehouseLineDetailListPdaQuery implements Serializable {

    private static final long serialVersionUID = 3128559653175557098L;
    /**
     * 仓库号
     */
    @NotNull(message = "仓库号不可为空")
    Long warehouseNo;

    /**
     * 任务编号
     */
    @NotNull(message = "任务编号不可为空")
    String missionNo;

    /**
     * 容器编号
     */
    @NotNull(message = "容器编号不能为空")
    String containerNo;

    /**
     * 货品条码
     */
    String goodsCode;

    /**
     * 收货仓列表
     */
    List<Long> storeNoList;

    /**
     * 分页条目
     */
    @NotNull(message = "请指定分页条目")
    Integer pageIndex;

    /**
     * 分页数量
     */
    @NotNull(message = "请指定分页数量")
    Integer pageSize;
}
