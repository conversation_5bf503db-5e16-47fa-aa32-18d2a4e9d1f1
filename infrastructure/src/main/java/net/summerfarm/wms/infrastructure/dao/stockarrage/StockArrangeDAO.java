package net.summerfarm.wms.infrastructure.dao.stockarrage;

import net.summerfarm.wms.infrastructure.dao.stockarrage.dataobject.StockArrangeDO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ct
 * create at:  2022/12/14  10:42
 */
@Repository
@Deprecated
public interface StockArrangeDAO {


    /**
     * 根据id查询预约单
     * @param stockTaskId
     * @return
     */
    StockArrangeDO selectByPrimaryKey(Integer stockTaskId);
}
