package net.summerfarm.wms.domain.products;

import net.summerfarm.wms.domain.products.domainobject.BoardPosition;

import java.util.List;

public interface BoardPositionCommandRepository {
    /**
     * 批量保存
     * @param boardPositions list
     */
    void batchSave(List<BoardPosition> boardPositions);

    /**
     * 编辑
     * @param boardPosition b
     */
    void edit(BoardPosition boardPosition);
}
