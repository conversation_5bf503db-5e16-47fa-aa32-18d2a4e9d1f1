package net.summerfarm.wms.application.mission.bizobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 上架任务创建事件
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ShelvingMissionCreateDTO {

    /**
     * 幂等键
     */
    String idempotentNo;

    /**
     * 任务号
     */
    String pMissionNo;

    /**
     * 任务类型
     */
    Integer pMissionType;

    /**
     * 业务类型
     */
    Integer sourceType;

    /**
     * 业务订单号
     */
    String sourceOrderNo;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 操作人id
     */
    String operatorId;

    /**
     * 操作人名称
     */
    String operatorName;

    /**
     * 容器编号
     */
    String containerNo;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * 明细
     */
    List<ShelvingMissionDetailDTO> details;

    Boolean isPda;

    /**
     * 自动上架
     */
    Boolean autoShelving;
}
