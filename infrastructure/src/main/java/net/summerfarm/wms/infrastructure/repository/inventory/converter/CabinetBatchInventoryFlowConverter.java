package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryFlow;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryFlowDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/28
 */
@Mapper
public interface CabinetBatchInventoryFlowConverter {

    CabinetBatchInventoryFlowConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryFlowConverter.class);

    CabinetBatchInventoryFlowDO convert(CabinetBatchInventoryFlow cabinetBatchInventoryFlow);

    CabinetBatchInventoryFlow convert(CabinetBatchInventoryFlowDO cabinetBatchInventoryFlowDO);

}
