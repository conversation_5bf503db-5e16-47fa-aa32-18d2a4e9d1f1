package net.summerfarm.wms.application.inventory.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryInitCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.InventoryCabinetOpenReturnResp;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.inventory.dto.InventoryCabinetOpenDTO;
import net.summerfarm.wms.inventory.dto.InventoryCabinetOpenReturnDTO;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@MqListener(topic = InventoryCabinetOpenInitListener.INV_CABINET_OPEN_INIT_TOPIC,
        tag = InventoryCabinetOpenInitListener.INV_CABINET_OPEN_INIT_RECEIVE_TAG,
        consumerGroup = InventoryCabinetOpenInitListener.INV_CABINET_OPEN_INIT_RECEIVE_GID)
@Slf4j
public class InventoryCabinetOpenInitListener extends AbstractMqListener<InventoryCabinetOpenDTO> {

    public final static String INV_CABINET_OPEN_INIT_TOPIC = "topic_wms_stock_task";
    public final static String INV_CABINET_OPEN_INIT_RECEIVE_TAG = "tag_inventory_cabinet_open_init";
    public final static String INV_CABINET_OPEN_INIT_RECEIVE_GID = "GID_inventory_cabinet_open_init";

    public final static String INV_CABINET_OPEN_INIT_RETURN_TAG = "tag_inventory_cabinet_open_return";


    @Resource
    private CabinetInventoryInitCommandService cabinetInventoryInitCommandService;
    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(InventoryCabinetOpenDTO inventoryCabinetOpenDTO) {
        if (inventoryCabinetOpenDTO.getWarehouseNo() == null){
            InventoryCabinetOpenReturnDTO returnDTO = new InventoryCabinetOpenReturnDTO();
            returnDTO.setIsSuccess(Boolean.FALSE);
            returnDTO.setFailMsg("请求参数缺失warehouseNo");
            mqProducer.send(INV_CABINET_OPEN_INIT_TOPIC, INV_CABINET_OPEN_INIT_RETURN_TAG, returnDTO);
            return;
        }

        Integer result = redisUtil.executeCallableByJustTryOneTime(
                "InventoryCabinetOpenInitListener:process:" + inventoryCabinetOpenDTO.getWarehouseNo(),
                1L,
                TimeUnit.HOURS,
                () -> {
                    log.info("库位精细化库位库存开启：{}", inventoryCabinetOpenDTO.getWarehouseNo());

                    // 确认仓库状态锁定
                    if (!warehouseConfigRepository.changeOngoingCabinetManagement(inventoryCabinetOpenDTO.getWarehouseNo().intValue())){
                        InventoryCabinetOpenReturnDTO returnDTO = new InventoryCabinetOpenReturnDTO();
                        returnDTO.setIsSuccess(Boolean.FALSE);
                        returnDTO.setWarehouseNo(inventoryCabinetOpenDTO.getWarehouseNo());
                        returnDTO.setFailMsg("请求仓库不在切仓中");
                        mqProducer.send(INV_CABINET_OPEN_INIT_TOPIC, INV_CABINET_OPEN_INIT_RETURN_TAG, returnDTO);
                        log.warn("库位精细化库位库存开启失败：{} {}", inventoryCabinetOpenDTO.getWarehouseNo(), JSONObject.toJSONString(returnDTO));
                        return 1;
                    }

                    // 分页查询需要初始化的仓库库存
                    InventoryCabinetOpenReturnResp resp = cabinetInventoryInitCommandService
                            .syncCabinetBatchInventoryCreate(inventoryCabinetOpenDTO.getWarehouseNo().intValue());

                    InventoryCabinetOpenReturnDTO returnDTO = new InventoryCabinetOpenReturnDTO();
                    returnDTO.setIsSuccess(resp.getIsSuccess());
                    returnDTO.setWarehouseNo(Long.valueOf(resp.getWarehouseNo()));
                    returnDTO.setFailMsg(resp.getFailMsg());
                    mqProducer.send(INV_CABINET_OPEN_INIT_TOPIC, INV_CABINET_OPEN_INIT_RETURN_TAG, returnDTO);

                    log.warn("库位精细化库位库存关闭完成：\n {} \n {}", inventoryCabinetOpenDTO.getWarehouseNo(), JSONObject.toJSONString(returnDTO));
                    return 1;
                });
        if (result == null){
            log.warn("库位精细化库位库存关闭并发阻塞");
        }
    }
}
