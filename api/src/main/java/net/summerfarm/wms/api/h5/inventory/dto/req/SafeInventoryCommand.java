package net.summerfarm.wms.api.h5.inventory.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SafeInventoryCommand implements Serializable {

    private static final long serialVersionUID = 1122584852991023982L;

    /**
     * 库存仓
     */
    private Long warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 锁安全库存数量
     */
    private Integer safeQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 是否系统自动执行
     */
    private Boolean systemExe;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否旧版安全库存更新
     */
    private Boolean oldSafeInventoryUpdate = true;
}
