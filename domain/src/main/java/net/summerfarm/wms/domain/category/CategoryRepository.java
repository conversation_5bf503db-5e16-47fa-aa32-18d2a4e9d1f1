package net.summerfarm.wms.domain.category;

import net.summerfarm.wms.domain.category.domainobject.Category;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 后续改为rpc调用
 * 暂时使用
 */
public interface CategoryRepository {
    List<Long> getAllSubNodeId(List<Long> categoryIds);

    List<Category> queryCategoryByIds(List<Long> categoryIds);

    Map<Long, Category> mapCategoryByIds(List<Long> categoryIds);

}
