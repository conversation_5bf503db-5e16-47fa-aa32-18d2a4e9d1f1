package net.summerfarm.wms.domain.StoreRecord.enums;

import net.summerfarm.wms.domain.areaStore.domainobject.StockChangeType;

/**
 * @Package: net.summerfarm.enums
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/6/12
 */
public enum StoreRecordType implements StockChangeType {

    /**
     * 任务类型
     */
    INIT_IN(8, "期初入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    PURCHASE_IN(11, "采购入库"),
    AFTER_SALE_IN(12, "退货入库"),
    RETURN_IN(13, "拒收入库任务"),
    STORE_ALLOCATION_NOT_IN(14, "（实收+拒收小于实发的情况)调拨未收入库"),
    STOCK_TAKING_IN(15, "盘盈入库"),
    TRANSFER_IN(16, "转换入库"),
    ALLOCATION_END_IN(17, "终止调拨回库"),
    ALLOCATION_ABNORMAL_IN(18, "调拨异常回库"),
    AFTER_SALE_IN_NEW(19, "退货入库"),
    LACK_GOODS_APPROVED(20, "缺货入库"),
    SKIP_STORE_IN(21, "越仓入库"),
    INTERCEPT_STORE_IN(22, "拦截入库"),
    OUT_MORE_IN(23, "多出入库"),
    OTHER_IN(24, "其他入库"),
    CROSS_WAREHOUSE_IN(25, "越库入库"),

    BATCH_TRIM(30, "批次调整"),
    SAFE_QUANTITY(31, "安全库存"),

    STORE_ALLOCATION_OUT(50, "调拨出库"),
    SALE_OUT(51, "销售出库"),
    DEMO_OUT(52, "出样出库"),
    DAMAGE_OUT(53, "货损出库"),
    STOCK_TAKING_OUT(54, "盘亏出库"),
    TRANSFER_OUT(55, "转换出库"),
    PURCHASES_BACK(56, "采购退货出库"),
    SUPPLY_AGAIN_TASK(57, "补货出库"),
    OWN_SALE_OUT(58, "销售自提出库"),
    ALLOCATION_DAMAGE_OUT(59, "调拨货损出库"),
    SKIP_STORE_OUT(60, "越仓出库"),
    OTHER_OUT(61, "其他出库"),
    DEMO_SELF_OUT(62, "样品自提出库"),
    CROSS_WAREHOUSE_OUT(63, "越库出库"),
    TRANSFER_DAMAGE_OUT(64, "转换货损出库"),

    STOCK_TAKING(100, "盘点新增批次"),

    PROCESSING_TASK_MATERIAL_REDUCE(86, "加工出库"),
    PROCESSING_TASK_MATERIAL_INCREASE(87, "加工回库"),
    PROCESSING_TASK_PRODUCT_INCREASE(88, "加工入库"),

    BATCH_DATA_FIX(127, "批次数据订正"),

    // 打印（不细分任务类型）与批次码业务类型枚举保持一致【SkuBatchCodeBizTypeEnums】
    IN_STORE_TASK(110, "入库任务打印"),
    STOCK_TRANSFER_TASK(120, "转换任务打印"),

    // 盘点业务
    STOCK_TAKING_SKU(200, "sku盘点"),
    STOCK_TAKING_BATCH(201, "批次盘点"),


    UNKNOWN(999, "未知"),
    ;


    StoreRecordType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    private final int id;

    private final String name;

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(int id) {
        for (StoreRecordType storeRecordType : StoreRecordType.values()) {
            if (storeRecordType.getId() == id) {
                return storeRecordType.getName();
            }
        }
        return "";
    }

    public static StoreRecordType getById(int id) {
        for (StoreRecordType storeRecordType : StoreRecordType.values()) {
            if (storeRecordType.getId() == id) {
                return storeRecordType;
            }
        }
        return StoreRecordType.UNKNOWN;
    }

    @Override
    public String getTypeName() {
        return getName();
    }
}
