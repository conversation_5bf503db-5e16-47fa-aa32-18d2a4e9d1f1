package net.summerfarm.wms.domain.instore.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum InBoundOrderType {

    /**
     * 入库类型
     */
    INIT_IN(8, "期初入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    PURCHASE_IN(11, "采购入库"),
    RETURN_IN(13, "拒收入库"),
    ALLOCATION_ABNORMAL_IN(18, "调拨回库"),
    AFTER_SALE_IN_NEW(19, "退货入库"),
    LACK_GOODS_APPROVED(20, "缺货入库"),
    SKIP_STORE_IN(21, "越仓入库"),
    STOP_STORE_IN(22, "拦截入库"),
    OUT_MORE_IN(23, "多出入库"),
    OTHER_IN(24, "其他入库"),
    CROSS_WAREHOUSE_IN(25, "越库入库"),

    UNKNOWN(999, "未知"),
    ;

    public static InBoundOrderType convert(Integer param) {
        return Arrays.stream(InBoundOrderType.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(InBoundOrderType.UNKNOWN);
    }

    Integer code;
    String desc;
}
