package net.summerfarm.wms.application.areastore.impl;

import com.github.pagehelper.PageInfo;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryCommand;
import net.summerfarm.wms.application.areastore.AreaStoreNewService;
import net.summerfarm.wms.application.areastore.dto.enums.AreaStoreQueryTypeEnums;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreQuery;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreSafeStockUpdateDto;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreSkuQuery;
import net.summerfarm.wms.application.areastore.dto.res.AreaStoreDTO;
import net.summerfarm.wms.application.areastore.dto.res.AreaStoreSkuRes;
import net.summerfarm.wms.application.sku.enums.StorageLocation;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.StoreRecord.StoreRecordRepository;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.areaStore.enums.StockSyncStatus;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.goods.dto.GoodsPropertyDTO;
import net.summerfarm.wms.facade.inventory.SupplierWarehouseInventoryQueryFacade;
import net.summerfarm.wms.facade.inventory.dto.SupplierWarehouseInventory;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/9/4
 */
@Service
@Slf4j
public class AreaStoreNewServiceImpl implements AreaStoreNewService {

    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private StoreRecordRepository storeRecordRepository;
    @Resource
    private ProductRepository productRepository;

    @Resource
    private SafeInventoryCommandService safeInventoryCommandService;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private SupplierWarehouseInventoryQueryFacade supplierWarehouseInventoryQueryFacade;

    /**
     * 查询有库存的仓列表
     *
     * @param areaStoreQuery 查询条件内容
     * @return 返回查询到的条件
     **/
    @Override
    public List<AreaStoreDTO> selectAreaStockList(AreaStoreQuery areaStoreQuery) {
        if (Objects.isNull(areaStoreQuery)) {
            return Lists.newArrayList();
        }
        List<AreaStoreDTO> result = Lists.newArrayList();
        List<AreaStore> areaStoreList = areaStoreRepository.listAreaStoreBySku(areaStoreQuery.getSku());
        areaStoreList = areaStoreList.stream().filter(areaStore -> areaStore.getQuantity() > 0)
                .sorted(Comparator.comparing(AreaStore::getQuantity).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaStoreList)) {
            return result;
        }
        // 聚合库存名称信息
        List<WarehouseStorageCenterEntity> storageCenterList = warehouseStorageRepository.queryOpenWarehouseByWarehouseNoList(
                areaStoreList.stream()
                        .map(AreaStore::getWarehouseNo)
                        .filter(Objects::nonNull)
                        .map(Long::intValue).collect(Collectors.toList())
        );
        if (AreaStoreQueryTypeEnums.XIANMU.getCode().equals(areaStoreQuery.getType())) {
            // 过滤非鲜沐仓
            storageCenterList = storageCenterList.stream().filter(it -> WmsConstant.XIANMU_TENANT_ID.equals(it.getTenantId()))
                    .collect(Collectors.toList());
        } else if (AreaStoreQueryTypeEnums.SAAS.getCode().equals(areaStoreQuery.getType())) {
            // 过滤非鲜沐仓 就是saas仓
            storageCenterList = storageCenterList.stream().filter(it -> !WmsConstant.XIANMU_TENANT_ID.equals(it.getTenantId()))
                    .collect(Collectors.toList());
        }

        Map<Integer, WarehouseStorageCenterEntity> centerMap = storageCenterList.stream()
                .collect(Collectors.toMap(WarehouseStorageCenterEntity::getWarehouseNo, Function.identity(), (a, b) -> a));
        areaStoreList.forEach(areaStore -> {
            WarehouseStorageCenterEntity center = centerMap.get(areaStore.getWarehouseNo().intValue());
            if (Objects.isNull(center)) {
                return;
            }

            AreaStoreDTO storeStockVO = AreaStoreDTO.builder()
                    .sku(areaStore.getSku())
                    .warehouseNo(areaStore.getWarehouseNo())
                    .warehouseName(Optional.ofNullable(center.getWarehouseName()).orElse(StringUtil.EMPTY_STRING))
                    .stockQuantity(areaStore.getQuantity())
                    .build();
            result.add(storeStockVO);
        });
        return result;
    }

    /**
     * 查询库存商品列表
     */
    @Override
    public PageInfo<AreaStoreSkuRes> selectAreaStockSkuList(AreaStoreSkuQuery areaStoreQuery) {
        // 根据仓查询有批次的sku信息
        int skuCount = storeRecordRepository.listSkuListCount(areaStoreQuery.getWarehouseNo());
        if (skuCount == 0) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList());
        }

        // 查询货品中心
        List<Product> productList = Lists.newArrayList();
        List<String> skuList = Lists.newArrayList();
        if (Objects.nonNull(areaStoreQuery.getPdId()) || !StringUtils.isBlank(areaStoreQuery.getSku())) {
            ArrayList<Long> pdIdList = Lists.newArrayList();
            if (Objects.nonNull(areaStoreQuery.getPdId())) {
                pdIdList.add(areaStoreQuery.getPdId());
            }
            if (!StringUtils.isBlank(areaStoreQuery.getSku())) {
                skuList.add(areaStoreQuery.getSku());
            }
            QueryProduct queryProduct = QueryProduct.builder()
                    .pdIds(pdIdList)
                    .skus(skuList)
                    .warehouseNo(Long.valueOf(areaStoreQuery.getWarehouseNo()))
                    .build();
            productList = productRepository.findProductsFromGoodsCenter(queryProduct);
            // 没有查询到对应的货品信息
            if (CollectionUtils.isEmpty(productList)) {
                return PageInfoHelper.createPageInfo(Lists.newArrayList());
            }
            // 获取sku列表
            skuList = productList.stream().map(Product::getSku).collect(Collectors.toList());
        }
        Map<String, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getSku, Function.identity()));
        // 查询库存信息
        PageInfo<String> skuPage = storeRecordRepository.pageSkuByAreaNoAndSku(areaStoreQuery.getWarehouseNo(),
                skuList, areaStoreQuery.getPageIndex(), areaStoreQuery.getPageSize());
        skuList = skuPage.getList();
        if (CollectionUtils.isEmpty(skuList)) {
            PageInfo<AreaStoreSkuRes> pageResp = new PageInfo<>();
            pageResp.setPageNum(areaStoreQuery.getPageIndex());
            pageResp.setPageSize(areaStoreQuery.getPageSize());
            pageResp.setList(Lists.newArrayList());
            pageResp.setPages(0);
            pageResp.setSize(0);
            pageResp.setTotal(0);
            return pageResp;
        }
        // 获取sku列表
        List<String> tmpSkuList = Lists.newArrayList();
        for (String sku : skuList) {
            if (productMap.containsKey(sku)) {
                continue;
            }
            // 需要查询货品中心的sku列表
            tmpSkuList.add(sku);
        }
        // 查询货品中心商品信息
        if (!CollectionUtils.isEmpty(tmpSkuList)) {
            QueryProduct queryProduct = QueryProduct.builder()
                    .skus(tmpSkuList)
                    .warehouseNo(Long.valueOf(areaStoreQuery.getWarehouseNo()))
                    .build();
            productList = productRepository.findProductsFromGoodsCenter(queryProduct);
            productMap.putAll(productList.stream().collect(Collectors.toMap(Product::getSku, Function.identity())));
        }
        List<AreaStoreSkuRes> pageResList = skuList.stream().map(item -> {
            Product product = productMap.getOrDefault(item, Product.builder().build());
            String firstCategory = product.getFirstCategory();
            String specification = product.getSpecification();
            return AreaStoreSkuRes.builder()
                    .sku(item)
                    .warehouseNo(areaStoreQuery.getWarehouseNo())
                    .firstCategory(firstCategory)
                    .firstCategoryType(product.getCategoryType())
                    .pdName(product.getPdName())
                    .storageLocation(StorageLocation.getTypeById(product.getStorageLocation()))
                    .specification(specification)
                    .skuType(product.getSkuType())
                    .isDomestic(product.getIsDomestic())
                    .imageUrl(product.getPic())
                    .packing(product.getPackaging())
                    .qualityTime(product.getEffectiveTime())
                    .qualityTimeType(product.getQualityTimeType())
                    .qualityTimeUnit(product.getTimeUnit())
                    .weight(product.getSpecification())
                    .build();
        }).collect(Collectors.toList());
        PageInfo<AreaStoreSkuRes> pageResp = new PageInfo<>();
        pageResp.setPageNum(areaStoreQuery.getPageIndex());
        pageResp.setPageSize(areaStoreQuery.getPageSize());
        pageResp.setList(pageResList);
        pageResp.setPages(skuPage.getPages());
        pageResp.setTotal(skuPage.getTotal());
        return pageResp;
    }

    @Override
    public CommonResult<String> updateSafeStock(AreaStoreSafeStockUpdateDto updateDto) {
        if (updateDto == null || updateDto.getWarehouseNo() == null
                || updateDto.getSafeQuantity() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        if (updateDto.getSafeQuantity() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "锁定库存为空");
        }
        if (updateDto.getSkuCode() == null && updateDto.getSaasSkuId() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求sku参数为空");
        }
        Long tenantId = updateDto.getTenantId();
        if (tenantId == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求租户id为空");
        }
        if (updateDto.getSafeQuantity() < 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "锁定库存不能小于0");
        }
        WarehouseStorageCenterEntity warehouseStorageCenter = warehouseStorageRepository
                .selectByWarehouseNo(updateDto.getWarehouseNo().intValue());
        if (warehouseStorageCenter == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "仓库编码不存在");
        }
        if (updateDto.getSaasSkuId() != null){
            GoodsInfoDTO goodsInfoDTO = goodsReadFacade.getGoodsInfoByTidAndSaasSkuIdList(
                    tenantId, updateDto.getSaasSkuId());
            if (goodsInfoDTO == null){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "货品ID不存在");
            }
            if (!WmsConstant.XIANMU_TENANT_ID.equals(tenantId) &&
                    !tenantId.equals(goodsInfoDTO.getTenantId())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "货品ID不是代仓品");
            }
            updateDto.setSkuCode(goodsInfoDTO.getSku());
        }

        // 查询库存
        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(
                updateDto.getWarehouseNo(), updateDto.getSkuCode());
        if (areaStore == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求仓库库存不存在");
        }

        // saas判断
        if (!WmsConstant.XIANMU_TENANT_ID.equals(tenantId)){
            if (StockSyncStatus.NO_SYNC.getStatus() == areaStore.getSync()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "销售库存=自定义的货品不支持修改锁定库存");
            }
            if (updateDto.getSafeQuantity() > areaStore.getQuantity() - areaStore.getLockQuantity()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "库存数值超过(仓库库存-冻结库存)");
            }
        }

        // 屏蔽代销不入仓品的修改安全库存
        Map<String, GoodsPropertyDTO> goodsPropertyDTOMap = goodsReadFacade
                .mapGoodsPropertyBySkuList(Arrays.asList(updateDto.getSkuCode()));
        GoodsPropertyDTO goodsPropertyDTO = goodsPropertyDTOMap.get(updateDto.getSkuCode());
        if (goodsPropertyDTO != null && goodsPropertyDTO.getIsSale2PurchaseGoods()) {
            SupplierWarehouseInventory supplierWarehouseInventory =  supplierWarehouseInventoryQueryFacade
                    .queryQuantityByWnoAndSku(updateDto.getWarehouseNo().intValue(), updateDto.getSkuCode());
            // 修改数量不能超过供应商库存量
            Integer maxChangeSafeQuantity = areaStore.getOnlineQuantity() -
                    supplierWarehouseInventory.getQuantity();
            Integer changeSafeQuantity = updateDto.getSafeQuantity() - areaStore.getSafeQuantity();
            if (changeSafeQuantity > maxChangeSafeQuantity){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "代销不入仓货品最大可修改安全库存" + maxChangeSafeQuantity);
            }
            if (supplierWarehouseInventory.getSafeQuantity() > 0 &&
                    updateDto.getSafeQuantity() < supplierWarehouseInventory.getSafeQuantity()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "代销不入仓货品最小可修改安全库存" +
                        supplierWarehouseInventory.getSafeQuantity());
            }
        }

        // 修改安全库存
        SafeInventoryCommand safeInventoryCommand = SafeInventoryCommand.builder()
                .warehouseNo(updateDto.getWarehouseNo())
                .sku(updateDto.getSkuCode())
                .safeQuantity(updateDto.getSafeQuantity())
                .remark(updateDto.getRemark())
//                .systemExe(true)
                .operator(updateDto.getOperator())
                .operatorId(updateDto.getOperatorId())
                .tenantId(tenantId)
                .oldSafeInventoryUpdate(updateDto.getOldSafeInventoryUpdate())
                .build();
        safeInventoryCommandService.updateSafeInventory(safeInventoryCommand);

        return CommonResult.ok();
    }

}
