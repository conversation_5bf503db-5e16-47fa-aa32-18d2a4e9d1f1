package net.summerfarm.wms.domain.batch.factory.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.batch.converter.BatchRecordConverter;
import net.summerfarm.wms.domain.batch.domainobject.BatchRelation;
import net.summerfarm.wms.domain.batch.domainobject.BatchRelationBuildCommand;
import net.summerfarm.wms.domain.batch.domainobject.CostBatchRecord;
import net.summerfarm.wms.domain.batch.domainobject.query.QueryCostBatchRecord;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.batch.factory.BatchRelationBuilder;
import net.summerfarm.wms.domain.batch.factory.DeliveryCostFormulaFactory;
import net.summerfarm.wms.domain.batch.repository.CostBatchRecordRepository;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductSubmitRecord;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialReceiveRecordRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskProductRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskProductSubmitRecordRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProcessingTaskBatchRelationBuilderImpl implements BatchRelationBuilder {

    @Resource
    private ProcessingTaskMaterialReceiveRecordRepository processingTaskMaterialReceiveRecordRepository;

    @Resource
    private ProcessingTaskMaterialRepository materialRepository;
    @Resource
    private ProcessingTaskProductRepository processingTaskProductRepository;

    @Resource
    private ProcessingTaskProductSubmitRecordRepository processingTaskProductSubmitRecordRepository;

    @Resource
    private CostBatchRecordRepository costBatchRecordRepository;

    @Resource
    private DeliveryCostFormulaFactory deliveryCostFormulaFactory;

    @Override
    public Integer getType() {
        return OperationType.PROCESSING_TASK_PRODUCT_INCREASE.getId();
    }

    @Override
    public List<BatchRelation> buildBatchRelation(BatchRelationBuildCommand batchRelationBuild) {

        ProcessingTaskProductSubmitRecord submitRecord = processingTaskProductSubmitRecordRepository.queryById(batchRelationBuild.getSourceId());
        if (Objects.isNull(submitRecord)) {
            log.info("查询不到加工成品提交记录:{}", JSON.toJSONString(batchRelationBuild));
            return Lists.newArrayList();
        }
        ProcessingTaskProduct processingTaskProduct = processingTaskProductRepository.queryById(submitRecord.getProcessingTaskProductId());
        if (Objects.isNull(processingTaskProduct)) {
            log.info("查询不到加工成品:{}", JSON.toJSONString(batchRelationBuild));
            return Lists.newArrayList();
        }

        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap =
                processingTaskMaterialReceiveRecordRepository.mapByProductIdsGroupMaterialId(
                        Collections.singletonList(submitRecord.getProcessingTaskProductId()));

        List<BatchRelation> result = new ArrayList<>();

        List<ProcessingTaskMaterial> materialList = materialRepository.listByProductId(
                processingTaskProduct.getProcessingTaskCode(), processingTaskProduct.getId());
        for (ProcessingTaskMaterial material : materialList) {
            List<ProcessingTaskMaterialReceiveRecord> processingTaskMaterialReceiveRecords =
                    materialReceiveRecordMap.get(material.getId());
            if (CollectionUtils.isEmpty(processingTaskMaterialReceiveRecords)) {
                log.info("查询不到加工任务的原料明细:{}", JSON.toJSONString(batchRelationBuild));
                return Lists.newArrayList();
            }

            List<Long> materialReceiveIds = processingTaskMaterialReceiveRecords.stream()
                    .filter(s -> !s.getMaterialSkuReceiveQuantity().equals(s.getMaterialSkuRestoreQuantity()))
                    .map(ProcessingTaskMaterialReceiveRecord::getId)
                    .distinct()
                    .collect(Collectors.toList());
            String materialSkuCode = processingTaskMaterialReceiveRecords.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuCode)
                    .distinct()
                    .findFirst()
                    .orElse(null);

            // 根据原料领取明细id查原料出库时扣减的成本批次id
            List<CostBatchRecord> costBatchRecords = costBatchRecordRepository.findCostBatchRecords(QueryCostBatchRecord.builder()
                    .sku(materialSkuCode)
                    .sourceId(materialReceiveIds)
//                .purchaseNo(batchRelationBuild.getPurchaseNo())
                    .sourceType(OperationType.PROCESSING_TASK_MATERIAL_REDUCE.getId())
                    .warehouseNo(batchRelationBuild.getWarehouseNo())
                    .build());

            List<BatchRelation> batchRelations = costBatchRecords.stream()
                    .map(BatchRecordConverter.INSTANCE::convert)
                    .distinct()
                    .map(item -> BatchRelation.builder()
                            .currentBatchId(batchRelationBuild.getCurrentCostBatchId())
                            .originBatchId(item.getWarehouseCostBatchId())
                            .ratio(BigDecimal.valueOf(material.getMaterialSkuRatioNum())
                                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 8, RoundingMode.UP))
                            //.costFormula(deliveryCostFormulaFactory.processingTaskDeliveryCostFormula(costBatchRecords,
//                                 processingTaskProduct.getProductSkuWeight().divide(processingTaskProduct.getMaterialSkuWeight(),
//                                       2, BigDecimal.ROUND_HALF_EVEN)))
                            .sourceType(batchRelationBuild.getSourceType())
                            .sourceId(batchRelationBuild.getSourceId())
                            .build())
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(batchRelations)) {
                result.addAll(batchRelations);
            }
        }

        return result;
    }
}
