package net.summerfarm.wms.web.controller.sku.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * Description: 溯源码批量打印查询<br/>
 * date: 2024/8/7 14:54<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceOnlyQueryInput {

    /**
     * 批次唯一溯源码
     */
    @NotBlank(message = "批次唯一溯源码不能为空")
    private String skuBatchTraceCode;
}
