package net.summerfarm.wms.manage.web.service.impl;

import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.StockTaskSkuTypeEnum;
import net.summerfarm.tms.util.DateUtils;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wms.common.util.ExcelUtils;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.manage.model.domain.Category;
import net.summerfarm.wms.manage.model.domain.StockTaskPick;
import net.summerfarm.wms.manage.model.domain.StockTaskPickDetail;
import net.summerfarm.wms.manage.model.domain.StockTaskPickItem;
import net.summerfarm.wms.manage.model.domain.StoreRecord;
import net.summerfarm.wms.manage.model.vo.StockTaskPickVO;
import net.summerfarm.wms.manage.web.enums.StorageLocation;
import net.summerfarm.wms.manage.model.vo.StoreRecordVO;
import net.summerfarm.wms.manage.web.mapper.manage.*;
import net.summerfarm.wms.manage.web.req.StockTaskReq;
import net.summerfarm.wms.manage.web.service.StockTaskPickService;
import net.summerfarm.wms.manage.web.service.WarehouseStorageServiceV1;
import net.summerfarm.wms.manage.web.service.WmsBuilderService;
import net.summerfarm.wms.manage.web.utils.ExtTypeUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Service
@Slf4j
public class StockTaskPickServiceImpl implements StockTaskPickService {

    private static final Integer ALL_DATE = 0;

    private static final Integer STORE_DATE = 1;

    @Resource
    private WmsBuilderService wmsBuilderService;
    @Resource
    private StockTaskPickMapper stockTaskPickMapper;
    @Resource
    private StockTaskPickItemMapper stockTaskPickItemMapper;
    @Resource
    private WarehouseStorageServiceV1 warehouseStorageServiceV1;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private AdminUtil adminUtil;
    @Resource
    private StockTaskPickDetailMapper stockTaskPickDetailMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;


    @Override
    public AjaxResult selectTaskPick(Integer pageIndex, Integer pageSize, StockTaskReq req) {

        LocalDate deliveryDate = req.getEndTime().toLocalDate().plusDays(1);
        Integer type = req.getPickType();
        Integer areaNo = req.getAreaNo();
        Integer categoryType = req.getCategoryType();
        String closeOrderTime = StringUtils.isEmpty(req.getCloseOrderTime()) ? null : req.getCloseOrderTime();
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTaskPickVO> res = selectTaskPickByTypeAndAssmble(deliveryDate, type, areaNo, categoryType, closeOrderTime);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(res));
    }

    private List<StockTaskPickVO> selectTaskPickByTypeAndAssmble(LocalDate deliveryDate, Integer type, Integer areaNo, Integer categoryType, String closeOrderTime) {
        List<StockTaskPickVO> stockTaskPickList = stockTaskPickMapper.selectTaskPickByType(type, areaNo, deliveryDate, categoryType, closeOrderTime);
        List<String> skus = stockTaskPickList.stream().map(StockTaskPickVO::getSku).distinct().collect(Collectors.toList());
        List<Product> productsFromGoodsCenter = productRepository.findProductsFromGoodsCenter(QueryProduct.builder().warehouseNo(areaNo.longValue()).skus(skus).build());
        Map<String, Product> productMap = productsFromGoodsCenter.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));
        return stockTaskPickList.stream()
                .peek(item -> {
                    Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                    item.setWeight(product.getSpecification());
                    item.setUnit(product.getPackaging());
                    item.setCategoryType(product.getCategoryType());
                    item.setSkuType(product.getSkuType());
                    item.setExtType(product.getSkuExeType());
                    item.setNameRemakes(adminUtil.getNameRemarks(product.getInventoryAdminId()));
                    item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                    item.setStorageArea(StorageLocation.getTypeById(product.getTemperature()));
                    item.setPacking(product.getPackaging());
                    item.setSku(product.getSku());
                    item.setCategoryType(product.getCategoryType());
                    item.setSecondLevelCategory(product.getSecondCategory());
                })
                .filter(item -> {
                    if (categoryType != null && categoryType == 0) {
                        return Objects.equals(item.getCategoryType(), 4);
                    }
                    if (categoryType != null && categoryType == 1) {
                        return !Objects.equals(item.getCategoryType(), 4);
                    }
                    return true;
                }).collect(Collectors.toList());
    }

    @Override
    public AjaxResult createDown(Integer storeNo, LocalDate deliveryTime, Integer type, String closeOrderTime, HttpServletResponse response) {

        Workbook workbook = new HSSFWorkbook();

        String closeTime = StringUtils.isEmpty(closeOrderTime) ? null : closeOrderTime;
        //捡获单信息
        List<StockTaskPickVO> stockTaskPickList = stockTaskPickMapper.selectTaskPickByType(type, storeNo, deliveryTime, null, closeTime);

        if (CollectionUtils.isEmpty(stockTaskPickList)) {
            return AjaxResult.getOK("暂无捡货数据", "暂无捡货数据");
        }
        // 数据汇总
        allData(workbook, stockTaskPickList, ALL_DATE);

        //城市仓数据 捡货条目信息
        List<StockTaskPickVO> stockTaskPickItems = stockTaskPickItemMapper.selectList(type, deliveryTime, storeNo, closeTime);
        Map<Integer, List<StockTaskPickVO>> storeStockTaskPick = stockTaskPickItems.stream().collect(Collectors.groupingBy(StockTaskPickVO::getOutStoreNo));
        Set<Integer> storeStockTaskSet = storeStockTaskPick.keySet();
        for (Integer store : storeStockTaskSet) {
            allData(workbook, storeStockTaskPick.get(store), STORE_DATE);
        }
        WarehouseStorageCenter center = warehouseStorageServiceV1.selectByWarehouseNo(storeNo);
        String fileName = center.getWarehouseName() + "捡货单" + DateUtils.date2String(new Date()) + ".xls";
        //导出excel
        try {
            ExcelUtils.outputExcel(workbook, fileName, response);
        } catch (Exception e) {
            throw new DefaultServiceException(e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("workbook close error", e);
            }
        }
        return null;
    }

    private void allData(Workbook workbook, List<StockTaskPickVO> stockTaskPickList, Integer type) {

        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);

        CellStyle baseCellyStyle = workbook.createCellStyle();
        baseCellyStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        baseCellyStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        baseCellyStyle.setFont(titleFont);

        CellStyle alignmentStype = workbook.createCellStyle();
        alignmentStype.setAlignment(HorizontalAlignment.LEFT);

        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 10);

        Integer storeNo = stockTaskPickList.get(0).getStoreNo();
        LocalDate deliveryTime = stockTaskPickList.get(0).getDeliveryTime();
        Integer outStoreNo = stockTaskPickList.get(0).getOutStoreNo();
        String areaName = "";
        //总捡货单没有城配仓
        if (outStoreNo != null) {
            WarehouseLogisticsCenter center = warehouseLogisticsService.selectByStoreNo(outStoreNo);
            areaName = center.getStoreName();
        }
        WarehouseStorageCenter center = warehouseStorageServiceV1.selectByWarehouseNo(storeNo);

        Sheet sheet;
        if (type.intValue() == 1) {
            sheet = workbook.createSheet(areaName + "捡货单");
        } else {
            sheet = workbook.createSheet("总捡货单");
        }
        sheet.setColumnWidth(0, 3500);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 3500);
        sheet.setColumnWidth(5, 3000);
        sheet.setColumnWidth(6, 3000);
        sheet.setColumnWidth(7, 6000);
        sheet.setColumnWidth(8, 5000);
        sheet.setColumnWidth(9, 5000);
        sheet.setColumnWidth(10, 3000);
        sheet.setColumnWidth(11, 3000);
        int index = 0;
        Row first = sheet.createRow(index++);
        first.createCell(0).setCellValue("捡货仓：");
        first.createCell(1).setCellValue(center.getWarehouseName());

        if (Objects.equals(type.intValue(), 1)) {
            first.createCell(2).setCellValue("城配仓：");
            first.createCell(3).setCellValue(areaName);
        }

        Row second = sheet.createRow(index++);
        second.createCell(0).setCellValue("预计出库时间：");
        second.createCell(1).setCellValue(deliveryTime.toString());

        //捡货类型:大客户-水果
        List<StockTaskPickVO> bigMerchantList = stockTaskPickList.stream().filter(x -> x.getAdminId() != null).collect(Collectors.toList());
        //大客户id分组
        if (!CollectionUtils.isEmpty(bigMerchantList)) {

            Map<Integer, List<StockTaskPickVO>> collect = bigMerchantList.stream().collect(Collectors.groupingBy(StockTaskPick::getAdminId));
            Set<Integer> keySet = collect.keySet();
            for (Integer adminId : keySet) {
                if (type.intValue() == 0) {
                    index = createIndex(sheet, index, collect.get(adminId));
                } else {
                    index = storeData(sheet, index, collect.get(adminId));
                }
            }
        }
        //捡货类型:其他
        List<StockTaskPickVO> merchantPickList = stockTaskPickList.stream().filter(x -> x.getAdminId() == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(merchantPickList)) {
            if (type.intValue() == 0) {
                createIndex(sheet, index, merchantPickList);
            } else {
                storeData(sheet, index, merchantPickList);
            }
        }
    }

    //总捡货单数据处理
    private Integer createIndex(Sheet sheet, int index, List<StockTaskPickVO> list) {

        String adminName = list.get(0).getAdminName();
        Row first = sheet.createRow(index++);
        first.createCell(0).setCellValue("商品归属");
        if (!StringUtils.isEmpty(adminName)) {
            first.createCell(1).setCellValue("大客户_" + adminName);
        } else {
            first.createCell(1).setCellValue("其他");
        }
        Row data = sheet.createRow(index++);
        data.createCell(0).setCellValue("一级类目");
        data.createCell(1).setCellValue("二级类目");
        data.createCell(2).setCellValue("sku");
        data.createCell(3).setCellValue("商品名称");
        data.createCell(4).setCellValue("规格");
        data.createCell(5).setCellValue("商品归属");
        data.createCell(6).setCellValue("储存区域");
        data.createCell(7).setCellValue("包装");
        data.createCell(8).setCellValue("应出总数量");
        data.createCell(9).setCellValue("批次");
        data.createCell(10).setCellValue("生产日期");
        data.createCell(11).setCellValue("保质期");
        data.createCell(12).setCellValue("应出数量");
        data.createCell(13).setCellValue("实出数量");

        wmsBuilderService.batchBuildWMSInfo(list);
        list = wmsBuilderService.sortedFruitPriority(list);

        for (StockTaskPickVO stockTaskPickVO : list) {

            List<StockTaskPickDetail> stockTaskPickDetails = stockTaskPickVO.getStockTaskPickDetails();
            if (!CollectionUtils.isEmpty(stockTaskPickDetails)) {
                int size = stockTaskPickDetails.size();
                if (size > 1) {
                    merge(sheet, index, size);
                }
                for (StockTaskPickDetail stockTaskPickDetail : stockTaskPickDetails) {
                    Row detailRow = sheet.createRow(index);
                    detailRow.createCell(0).setCellValue(stockTaskPickVO.getFirstLevelCategory());
                    detailRow.createCell(1).setCellValue(stockTaskPickVO.getCategoryName());
                    detailRow.createCell(2).setCellValue(stockTaskPickVO.getSku());
                    detailRow.createCell(3).setCellValue(stockTaskPickVO.getPdName() + ExtTypeUtil.getExtStr(stockTaskPickVO.getExtType()));
                    detailRow.createCell(4).setCellValue(stockTaskPickVO.getWeight());
                    String productsType = skuType(stockTaskPickVO);
                    detailRow.createCell(5).setCellValue(productsType);
                    detailRow.createCell(6).setCellValue(stockTaskPickVO.getStorageArea());
                    detailRow.createCell(7).setCellValue(stockTaskPickVO.getUnit());
                    detailRow.createCell(8).setCellValue(stockTaskPickVO.getAmount());
                    detailRow.createCell(9).setCellValue(stockTaskPickDetail.getListNo());
                    String production = stockTaskPickDetail.getProductionDate() == null ? " " : stockTaskPickDetail.getProductionDate().toString();
                    String quality = stockTaskPickDetail.getQualityDate() == null ? " " : stockTaskPickDetail.getQualityDate().toString();
                    detailRow.createCell(10).setCellValue(production);
                    detailRow.createCell(11).setCellValue(quality);
                    detailRow.createCell(12).setCellValue(stockTaskPickDetail.getShouldQuantity());
                    index++;
                }
            } else {
                Row stockRow = sheet.createRow(index);
                stockRow.createCell(0).setCellValue(stockTaskPickVO.getFirstLevelCategory());
                stockRow.createCell(1).setCellValue(stockTaskPickVO.getCategoryName());
                stockRow.createCell(2).setCellValue(stockTaskPickVO.getSku());
                stockRow.createCell(3).setCellValue(stockTaskPickVO.getPdName() + ExtTypeUtil.getExtStr(stockTaskPickVO.getExtType()));
                stockRow.createCell(4).setCellValue(stockTaskPickVO.getWeight());
                String productsType = skuType(stockTaskPickVO);
                stockRow.createCell(5).setCellValue(productsType);
                stockRow.createCell(6).setCellValue(stockTaskPickVO.getStorageArea());
                stockRow.createCell(7).setCellValue(stockTaskPickVO.getUnit());
                stockRow.createCell(8).setCellValue(stockTaskPickVO.getAmount());
                index++;
            }
        }
        return index;
    }

    //城配仓数据处理
    private Integer storeData(Sheet sheet, int index, List<StockTaskPickVO> list) {
        String adminName = list.get(0).getAdminName();
        Row first = sheet.createRow(index++);
        first.createCell(0).setCellValue("商品归属");
        if (!StringUtils.isEmpty(adminName)) {
            first.createCell(1).setCellValue("大客户_" + adminName);
        } else {
            first.createCell(1).setCellValue("其他");
        }
        Row data = sheet.createRow(index++);
        data.createCell(0).setCellValue("一级类目");
        data.createCell(1).setCellValue("二级类目");
        data.createCell(2).setCellValue("sku");
        data.createCell(3).setCellValue("商品名称");
        data.createCell(4).setCellValue("规格");
        data.createCell(5).setCellValue("商品归属");
        data.createCell(6).setCellValue("储存区域");
        data.createCell(7).setCellValue("包装");
        data.createCell(8).setCellValue("应出总数量");
        data.createCell(9).setCellValue("实发数量");

        wmsBuilderService.batchBuildWMSInfo(list);
        list = wmsBuilderService.sortedFruitPriority(list);

        for (StockTaskPickVO stockTaskPickVO : list) {
            Row stockRow = sheet.createRow(index++);
            stockRow.createCell(0).setCellValue(stockTaskPickVO.getFirstLevelCategory());
            stockRow.createCell(1).setCellValue(stockTaskPickVO.getCategoryName());
            stockRow.createCell(2).setCellValue(stockTaskPickVO.getSku());
            stockRow.createCell(3).setCellValue(stockTaskPickVO.getPdName() + ExtTypeUtil.getExtStr(stockTaskPickVO.getExtType()));
            stockRow.createCell(4).setCellValue(stockTaskPickVO.getWeight());
            String productsType = skuType(stockTaskPickVO);
            stockRow.createCell(5).setCellValue(productsType);
            stockRow.createCell(6).setCellValue(stockTaskPickVO.getStorageArea());
            stockRow.createCell(7).setCellValue(stockTaskPickVO.getUnit());
            stockRow.createCell(8).setCellValue(stockTaskPickVO.getAmount());
        }
        return index;
    }

    //合并单元格
    private void merge(Sheet sheet, int index, int size) {

        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, 5, 5));
    }

    //数据处理
    private List<StockTaskPick> dataIntegration(List<StockTaskPick> stockTaskPickList) {
        List<StockTaskPick> resultStockTaskPick = new ArrayList<>();
        List<StockTaskPick> bigMerchantPickList = stockTaskPickList.stream().filter(x -> x.getAdminId() != null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bigMerchantPickList)) {
            Map<Integer, List<StockTaskPick>> collect = bigMerchantPickList.stream().collect(Collectors.groupingBy(StockTaskPick::getAdminId));
            collect.forEach((adminId, stockTaskPicks) -> resultStockTaskPick.addAll(handlePickList(stockTaskPicks)));
        }
        List<StockTaskPick> merchantPickList = stockTaskPickList.stream().filter(x -> x.getAdminId() == null).collect(Collectors.toList());
        resultStockTaskPick.addAll(handlePickList(merchantPickList));
        return resultStockTaskPick;

    }

    //合并相同sku 的数据
    private List<StockTaskPick> handlePickList(List<StockTaskPick> stockTaskPickList) {
        List<StockTaskPick> resultStockTaskPick = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stockTaskPickList)) {
            Map<String, List<StockTaskPick>> bigMerchantSkuMap = stockTaskPickList.stream().collect(Collectors.groupingBy(StockTaskPick::getSku));
            bigMerchantSkuMap.forEach((sku, stockTaskPicks) -> {
                int sum = stockTaskPicks.stream().mapToInt(StockTaskPick::getAmount).sum();
                StockTaskPick stockTaskPick = stockTaskPicks.get(0);
                stockTaskPick.setAmount(sum);
                resultStockTaskPick.add(stockTaskPick);
            });
        }
        return resultStockTaskPick;

    }

    private String skuType(StockTaskPickVO stockTaskPickVO) {
        String skuType = stockTaskPickVO.getSkuType().equals(StockTaskSkuTypeEnum.SELFSUPPORT.getSkuType()) ? StockTaskSkuTypeEnum.SELFSUPPORT.getSkuTypeValue() : StockTaskSkuTypeEnum.AGENTWAREHOUSE.getSkuTypeValue();
        String productsType = StringUtils.isEmpty(stockTaskPickVO.getNameRemakes()) ? skuType : skuType + "-" + stockTaskPickVO.getNameRemakes();
        return productsType;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createStockTaskPick(String closeTime) {
        log.info(closeTime + "开始生成捡货任务");
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        List<StockTaskPick> stockTaskPicks = new LinkedList<>();
        List<WarehouseLogisticsCenterVO> centerVOS = warehouseLogisticsService.selectLogisticsAll(WarehouseStorageCenterEnum.Status.VALID.ordinal());
        //遍历 支持大客户提前截单仓
        centerVOS.forEach(centerVO -> {
            //支持大客户提前截单

            if(centerVO.getCloseOrderType().intValue() == STORE_DATE){
                //遍历
                Integer storeNo = centerVO.getStoreNo();
                List<WarehouseStorageCenter> storageCenters = warehouseLogisticsService.selectStorageByMapping(storeNo);
                for (WarehouseStorageCenter center : storageCenters) {
                    Integer warehouseNo = center.getWarehouseNo();
                    List<StockTaskPick> stockTaskPickList = gainAllStockTaskPick(closeTime,deliveryDate,storeNo,warehouseNo);
                    stockTaskPickList.forEach(x ->{
                        x.setAddTime(LocalDateTime.now());
                        x.setOutStoreNo(storeNo);
                        x.setStoreNo(warehouseNo);
                        x.setType(ALL_DATE);
                        x.setDeliveryTime(deliveryDate);
                        x.setCloseOrderTime(closeTime);
                    });

                    log.info("storeNo={},ware={}",storeNo,warehouseNo);
                    if(!CollectionUtils.isEmpty(stockTaskPickList)){

                        List<StockTaskPickItem> stockTaskPickItems = handleStorePickList(stockTaskPickList, ALL_DATE,closeTime);
                        //插入城配仓信息
                        stockTaskPickItemMapper.insertBatch(stockTaskPickItems);
                        //数据整合
                        List<StockTaskPick> dataIntegrationPicks = dataIntegration(stockTaskPickList);
                        //插入捡货任务
                        stockTaskPickMapper.insertBatch(dataIntegrationPicks);
                        //生成捡货详情,插入数据
                        List<StockTaskPickDetail> stockTaskPickDetails = stockTaskPickList(dataIntegrationPicks);
                        if(!CollectionUtils.isEmpty(stockTaskPickDetails)){
                            stockTaskPickDetailMapper.insertBatch(stockTaskPickDetails);
                        }
                        stockTaskPicks.addAll(dataIntegrationPicks);
                    }

                }
            }
        });
        log.info("捡货任务生成完毕");
    }

    /**
     * 获取所有捡货信息
     */
    private List<StockTaskPick> gainAllStockTaskPick(String closeTime,LocalDate deliveryDate,Integer storeNo,Integer trustStoreNo){

        //销售出库
        List<StockTaskPick> stockTaskPickList = ordersMapper.selectFruitClosingOrder(deliveryDate, storeNo, trustStoreNo, Boolean.FALSE,closeTime);

        //出样出库
        List<StockTaskPick> sampleTaskPickList = sampleApplyMapper.selectSampleByType(storeNo, deliveryDate, trustStoreNo,closeTime);
        if(!CollectionUtils.isEmpty(sampleTaskPickList)){
            stockTaskPickList.addAll(sampleTaskPickList);
        }
        //补货出库
        List<StockTaskPick> afterSalePickList = afterSaleDeliveryPathMapper.afterSalePick(deliveryDate, storeNo, trustStoreNo, closeTime);
        if (!CollectionUtils.isEmpty(afterSalePickList)){
            stockTaskPickList.addAll(afterSalePickList);
        }

        return stockTaskPickList;
    }

    //城市合并数据
    private List<StockTaskPickItem> handleStorePickList(List<StockTaskPick> taskPickList,Integer type,String closeTime ){
        //配送仓数据
        List<StockTaskPickItem> stockTaskItems = new ArrayList<>();

        //大客户水果数据拆分
        if(type.intValue() == 0){
            Map<Integer, List<StockTaskPick>> stock = taskPickList.stream().collect(Collectors.groupingBy(StockTaskPick::getAdminId));
            stock.forEach((adminId,stocks) -> {
                StockTaskPick stockTaskPick = stocks.get(0);
                Map<String, List<StockTaskPick>> skuPick = stocks.stream().collect(Collectors.groupingBy(StockTaskPick::getSku));
                skuPick.forEach((sku,skuPicks) ->{
                    int all = skuPicks.stream().mapToInt(x -> x.getAmount()).sum();
                    StockTaskPickItem stockTaskPickItem = new StockTaskPickItem();
                    stockTaskPickItem.setAddTime(LocalDateTime.now());
                    stockTaskPickItem.setAdminId(adminId);
                    stockTaskPickItem.setAdminName(stockTaskPick.getAdminName());
                    stockTaskPickItem.setAmount(all);
                    stockTaskPickItem.setSku(sku);
                    stockTaskPickItem.setDeliveryTime(stockTaskPick.getDeliveryTime());
                    stockTaskPickItem.setStoreNo(stockTaskPick.getStoreNo());
                    stockTaskPickItem.setOutStoreNo(stockTaskPick.getOutStoreNo());
                    stockTaskPickItem.setType(type);
                    stockTaskPickItem.setCloseOrderTime(closeTime);
                    stockTaskItems.add(stockTaskPickItem);
                });

            });
        } else {
            Map<String, List<StockTaskPick>> skuPick = taskPickList.stream().collect(Collectors.groupingBy(StockTaskPick::getSku));
            skuPick.forEach((sku,skuPicks) -> {
                int sum = skuPicks.stream().mapToInt(taskPick -> taskPick.getAmount()).sum();
                StockTaskPick stockTask = skuPicks.get(0);
                StockTaskPickItem stockTaskPickItem = new StockTaskPickItem();
                stockTaskPickItem.setAddTime(LocalDateTime.now());
                stockTaskPickItem.setAdminName(stockTask.getAdminName());
                stockTaskPickItem.setAmount(sum);
                stockTaskPickItem.setDeliveryTime(stockTask.getDeliveryTime());
                stockTaskPickItem.setStoreNo(stockTask.getStoreNo());
                stockTaskPickItem.setSku(stockTask.getSku());
                stockTaskPickItem.setOutStoreNo(stockTask.getOutStoreNo());
                stockTaskPickItem.setType(type);
                stockTaskPickItem.setCloseOrderTime(closeTime);
                stockTaskItems.add(stockTaskPickItem);
            });
        }


        return stockTaskItems;
    }


    /**
     * 拼装捡货单数据
     */
    private List<StockTaskPickDetail> stockTaskPickList(List<StockTaskPick> stockTaskPicks){
        if (CollectionUtils.isEmpty(stockTaskPicks)){
            return null ;
        }
        ArrayList<StockTaskPickDetail> resultList = new ArrayList<>();
        //根据sku 分组
        Map<String, List<StockTaskPick>>  collect = stockTaskPicks.stream().collect(Collectors.groupingBy(StockTaskPick::getSku));
        collect.forEach((sku,stockTaskPickList) -> {
            //合并数据,一共要出的数量
            resultList.addAll(pickDetailNotGlNo(stockTaskPickList,sku));
        });
        return resultList;
    }

    /**
     * 没有货位
     */
    private List<StockTaskPickDetail> pickDetailNotGlNo(List<StockTaskPick> stockTaskPicks,String sku){

        Integer storeNo = stockTaskPicks.get(0).getStoreNo();
        ArrayList<StockTaskPickDetail> stockTaskPickDetails = new ArrayList<>();

        StoreRecordVO select = new StoreRecordVO();
        select.setSku(sku);
        select.setAreaNo(storeNo);
        select.setStoreQuantityMin(1);
        //批次信息
        List<StoreRecord> storeRecords = storeRecordMapper.selectLasted(select);
        for (StockTaskPick stockTaskPick : stockTaskPicks) {
            Integer amount = stockTaskPick.getAmount();

            for (StoreRecord storeRecord : storeRecords) {
                Integer detailQuantity = storeRecord.getStoreQuantity();
                if(amount <= 0){
                    break;
                }
                if(detailQuantity <= 0){
                    continue;
                }
                int lockQuantity = amount >= detailQuantity ? detailQuantity:amount;
                StockTaskPickDetail stockTaskPickDetail = new StockTaskPickDetail();
                stockTaskPickDetail.setListNo(storeRecord.getBatch());
                stockTaskPickDetail.setSku(sku);
                stockTaskPickDetail.setQualityDate(storeRecord.getQualityDate());
                stockTaskPickDetail.setStockTaskPickId(stockTaskPick.getId());
                stockTaskPickDetail.setShouldQuantity(lockQuantity);
                stockTaskPickDetail.setProductionDate(storeRecord.getProductionDate());
                amount = amount - lockQuantity;
                stockTaskPickDetails.add(stockTaskPickDetail);
                storeRecord.setQuantity(storeRecord.getQuantity() - lockQuantity);
            }
        }
        return stockTaskPickDetails;
    }

}
