package net.summerfarm.wms.application.mission.impl;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryAddDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryAddReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceReq;
import net.summerfarm.wms.application.inventory.event.CabinetInventoryAddEvent;
import net.summerfarm.wms.application.inventory.event.CabinetInventoryReduceEvent;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.EventPublisher;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.admin.domainobject.AdminOperator;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.mission.MissionTriggerBizService;
import net.summerfarm.wms.domain.mission.domainobject.AbnormalSubmitDetail;
import net.summerfarm.wms.domain.mission.domainobject.Mission;
import net.summerfarm.wms.domain.mission.domainobject.MissionSourceProperty;
import net.summerfarm.wms.domain.mission.domainobject.SubmitDetail;
import net.summerfarm.wms.domain.mission.enums.MissionTypeEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ShelvingMissionTriggerBizServiceImpl implements MissionTriggerBizService {

    @Resource
    private EventPublisher eventPublisher;

    @Resource
    private MqProducer mqProducer;

    @Override
    public Integer getMissionType() {
        return MissionTypeEnum.SHELVING.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void trigger(Mission mission, List<SubmitDetail> submitDetails, List<AbnormalSubmitDetail> abnormalSubmitDetails, AdminOperator adminOperator) {
        MissionSourceProperty sourceProperty;
        List<MissionSourceProperty> sourcePropertys = mission.getSourceProperty();
        if (CollectionUtils.isEmpty(sourcePropertys)) {
            // 兼容
            if (mission.getSourceType() == null && mission.getSourceOrderNo() == null) {
                return;
            }
            sourceProperty = MissionSourceProperty.builder()
                    .sourceType(mission.getSourceType())
                    .sourceOrderNo(mission.getSourceOrderNo())
                    .missionNo(mission.getMissionNo())
                    .missionType(mission.getMissionType())
                    .build();
        } else {
            sourceProperty = sourcePropertys.get(NumberUtils.INTEGER_ZERO);
        }
        if (Objects.equals(OperationType.SKIP_STORE_IN.getId(), sourceProperty.getSourceType())) {
            return;
        }
        // 收货暂存库位库存减少
        sendTemporaryCabinetInventoryReduceEvent(mission, sourceProperty, submitDetails, adminOperator);
        // 目标库位库存增加
        sendTargetCabinetInventoryAddEvent(mission, sourceProperty, submitDetails, adminOperator);
    }

    private void sendTargetCabinetInventoryAddEvent(Mission mission, MissionSourceProperty sourceProperty,
                                                    List<SubmitDetail> submitDetails, AdminOperator adminOperator) {
        if (CollectionUtils.isEmpty(submitDetails)) {
            return;
        }
        SubmitDetail submitDetail = submitDetails.get(0);
        List<CabinetBatchInventoryAddDetailReq> collect = submitDetails.stream()
                .map(item -> CabinetBatchInventoryAddDetailReq.builder()
                        .addQuantity(item.getQuantity())
                        .cabinetCode(item.getTargetCarrierCode())
                        .produceDate(DateUtil.toDate(item.getProduceTime()))
                        .qualityDate(DateUtil.toDate(item.getShelfLife()))
                        .skuBatchCode(item.getBatchNo())
                        .skuCode(item.getSku())
                        .build()).collect(Collectors.toList());
        CabinetBatchInventoryAddReq build = CabinetBatchInventoryAddReq.builder()
                .addDetailReqList(collect)
                .orderNo(sourceProperty.getSourceOrderNo())
                .containerCode(submitDetail.getSourceCarrierCode())
                .orderTypeName(CabinetInventoryChangeTypeEnum.getTypeName(sourceProperty.getSourceType()))
                .internalNo(mission.getMissionNo())
                .operatorType(CabinetInventoryOperationType.SHELF_ON.getId())
                .tenantId(mission.getTenantId())
                .warehouseNo(mission.getWarehouseNo().intValue())
                .operatorName(StringUtils.isEmpty(adminOperator.getName()) ? AdminUtil.getCurrentLoginAdminNameV2() : adminOperator.getName())
                .build();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                mqProducer.send(WmsConstant.MQ_TOPIC, WmsConstant.CABINET_INVENTORY_ADD_TAG, build);
            }
        });
    }

    private void sendTemporaryCabinetInventoryReduceEvent(Mission mission, MissionSourceProperty sourceProperty,
                                                          List<SubmitDetail> submitDetails, AdminOperator adminOperator) {
        if (CollectionUtils.isEmpty(submitDetails)) {
            return;
        }
        SubmitDetail submitDetail = submitDetails.get(0);
        List<CabinetBatchInventoryReduceDetailReq> collect = submitDetails.stream()
                .map(item -> CabinetBatchInventoryReduceDetailReq.builder()
                        .reduceQuantity(item.getQuantity())
                        .cabinetCode(DefaultCabinetCodeEnum.SH.getCode())
                        .produceDate(DateUtil.toDate(item.getProduceTime()))
                        .qualityDate(DateUtil.toDate(item.getShelfLife()))
                        .skuBatchCode(item.getBatchNo())
                        .skuCode(item.getSku())
                        .build()).collect(Collectors.toList());
        CabinetBatchInventoryReduceReq build = CabinetBatchInventoryReduceReq.builder()
                .reduceDetailReqList(collect)
                .orderNo(sourceProperty.getSourceOrderNo())
                .containerCode(submitDetail.getSourceCarrierCode())
                .orderTypeName(CabinetInventoryChangeTypeEnum.getTypeName(sourceProperty.getSourceType()))
                .internalNo(mission.getMissionNo())
                .operatorType(CabinetInventoryOperationType.SHELF_ON.getId())
                .tenantId(mission.getTenantId())
                .warehouseNo(mission.getWarehouseNo().intValue())
                .operatorName(StringUtils.isEmpty(adminOperator.getName()) ? AdminUtil.getCurrentLoginAdminNameV2() : adminOperator.getName())
                .build();
        eventPublisher.publishe(CabinetInventoryReduceEvent.builder()
                .data(JSON.toJSONString(build))
                .build());
    }
}
