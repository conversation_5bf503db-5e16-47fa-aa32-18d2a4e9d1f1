package net.summerfarm.wms.manage.web.service.impl;

import cn.hutool.core.util.ReflectUtil;
import net.summerfarm.wms.manage.model.domain.InventoryWMSInfo;
import net.summerfarm.wms.manage.web.service.InventoryService;
import net.summerfarm.wms.manage.web.service.WmsBuilderService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Service
public class WMSBuilderServiceImpl implements WmsBuilderService {

    private static final String FIELD_SKU = "sku";
    private static final String FIELD_STORAGE_AREA = "storageArea";
    private static final String FIELD_PACKING = "packing";
    private static final String FIELD_FIRST_CATEGORY = "firstLevelCategory";
    private static final String FIELD_SECOND_CATEGORY = "secondLevelCategory";
    private static final String FIELD_WEIGHT = "weight";

    @Autowired
    @Lazy
    private InventoryService inventoryService;

    @Override
    public <T> void batchBuildWMSInfo(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> skuList = list.stream()
                .filter(item -> Objects.nonNull(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .map(item -> String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .distinct()
                .collect(Collectors.toList());
        Map<String, InventoryWMSInfo> infoMap = inventoryService.batchQueryWMSInfo(skuList);
        if (MapUtils.isEmpty(infoMap)) {
            return;
        }

        list.forEach(item -> {
            String sku = String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU));
            Optional.ofNullable(infoMap.get(sku)).ifPresent(info -> {
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_STORAGE_AREA))) {
                    ReflectUtil.setFieldValue(item, FIELD_STORAGE_AREA, info.getStorageArea());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_PACKING))) {
                    ReflectUtil.setFieldValue(item, FIELD_PACKING, info.getPacking());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_FIRST_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_FIRST_CATEGORY, info.getFirstLevelCategory());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_SECOND_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_SECOND_CATEGORY, info.getSecondLevelCategory());
                }
            });

            Optional.ofNullable(ReflectUtil.getFieldValue(item, FIELD_WEIGHT)).ifPresent(value ->
                    ReflectUtil.setFieldValue(item, FIELD_WEIGHT, String.valueOf(value))
            );
        });
    }

    @Override
    public <T> void batchBuildWMSInfoFromGoodsCenter(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> skuList = list.stream()
                .filter(item -> Objects.nonNull(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .map(item -> String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .distinct()
                .collect(Collectors.toList());
        Map<String, InventoryWMSInfo> infoMap = inventoryService.batchQueryWMSInfoFromGoodsCenter(skuList);
        if (MapUtils.isEmpty(infoMap)) {
            return;
        }

        list.forEach(item -> {
            String sku = String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU));
            Optional.ofNullable(infoMap.get(sku)).ifPresent(info -> {
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_STORAGE_AREA))) {
                    ReflectUtil.setFieldValue(item, FIELD_STORAGE_AREA, info.getStorageArea());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_PACKING))) {
                    ReflectUtil.setFieldValue(item, FIELD_PACKING, info.getPacking());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_FIRST_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_FIRST_CATEGORY, info.getFirstLevelCategory());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_SECOND_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_SECOND_CATEGORY, info.getSecondLevelCategory());
                }
            });

            Optional.ofNullable(ReflectUtil.getFieldValue(item, FIELD_WEIGHT)).ifPresent(value ->
                    ReflectUtil.setFieldValue(item, FIELD_WEIGHT, String.valueOf(value))
            );
        });
    }

    @Override
    public <T> void batchBuildWMSInfoFromGoodsCenter(Long warehouseNo, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> skuList = list.stream()
                .filter(item -> Objects.nonNull(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .map(item -> String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU)))
                .distinct()
                .collect(Collectors.toList());
        Map<String, InventoryWMSInfo> infoMap = inventoryService.batchQueryWMSInfoFromGoodsCenter(warehouseNo, skuList);
        if (MapUtils.isEmpty(infoMap)) {
            return;
        }

        list.forEach(item -> {
            String sku = String.valueOf(ReflectUtil.getFieldValue(item, FIELD_SKU));
            Optional.ofNullable(infoMap.get(sku)).ifPresent(info -> {
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_STORAGE_AREA))) {
                    ReflectUtil.setFieldValue(item, FIELD_STORAGE_AREA, info.getStorageArea());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_PACKING))) {
                    ReflectUtil.setFieldValue(item, FIELD_PACKING, info.getPacking());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_FIRST_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_FIRST_CATEGORY, info.getFirstLevelCategory());
                }
                if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_SECOND_CATEGORY))) {
                    ReflectUtil.setFieldValue(item, FIELD_SECOND_CATEGORY, info.getSecondLevelCategory());
                }
            });

            Optional.ofNullable(ReflectUtil.getFieldValue(item, FIELD_WEIGHT)).ifPresent(value ->
                    ReflectUtil.setFieldValue(item, FIELD_WEIGHT, String.valueOf(value))
            );
        });
    }

    @Override
    public <T> void buildWMSInfo(T item) {
        if (Objects.isNull(item)) {
            return;
        }

        Object sku = ReflectUtil.getFieldValue(item, FIELD_SKU);
        if (Objects.isNull(sku)) {
            return;
        }

        Optional.ofNullable(inventoryService.queryWMSInfo(String.valueOf(sku))).ifPresent(info -> {
            if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_STORAGE_AREA))) {
                ReflectUtil.setFieldValue(item, FIELD_STORAGE_AREA, info.getStorageArea());
            }
            if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_PACKING))) {
                ReflectUtil.setFieldValue(item, FIELD_PACKING, info.getPacking());
            }
            if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_FIRST_CATEGORY))) {
                ReflectUtil.setFieldValue(item, FIELD_FIRST_CATEGORY, info.getFirstLevelCategory());
            }
            if (Objects.nonNull(ReflectUtil.getField(item.getClass(), FIELD_SECOND_CATEGORY))) {
                ReflectUtil.setFieldValue(item, FIELD_SECOND_CATEGORY, info.getSecondLevelCategory());
            }
        });

        Optional.ofNullable(ReflectUtil.getFieldValue(item, FIELD_WEIGHT)).ifPresent(value ->
                ReflectUtil.setFieldValue(item, FIELD_WEIGHT, String.valueOf(value))
        );
    }

    @Override
    public <T> List<T> sortedFruitPriority(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        list = list.stream().sorted(Comparator.comparingInt(obj -> {
            Object flcValue =  ReflectUtil.getFieldValue(obj, FIELD_FIRST_CATEGORY);
            if (Objects.isNull(flcValue)) {
                return 100;
            }
            return StringUtils.equals("鲜果", String.valueOf(flcValue)) ? 1 : 10;
        })).collect(Collectors.toList());
        return list;
    }

}
