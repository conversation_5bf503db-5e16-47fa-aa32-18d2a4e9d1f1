package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description wanwei对接配置
 * @date 2024/06/15
 */
@NacosConfigurationProperties(prefix = "ww.external.connect", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Configuration
@Data
public class WWExternalConnectConfig {

    /**
     * appkey
     */
    private String appKey;

    /**
     * 密钥
     */
    private String appSecret;

}
