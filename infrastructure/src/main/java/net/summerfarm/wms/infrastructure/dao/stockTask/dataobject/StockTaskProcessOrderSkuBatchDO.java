package net.summerfarm.wms.infrastructure.dao.stockTask.dataobject;


import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * wms_stock_task_process_order_sku_batch
 * <AUTHOR>
@Data
public class StockTaskProcessOrderSkuBatchDO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 出库单ID
     */
    private Long stockTaskProcessId;

    /**
     * 出库任务ID
     */
    private Long stockTaskId;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 货品供应单号
     */
    private String goodsSupplyNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * sku数量
     */
    private Integer quantity;

    /**
     * 采购批次
     */
    private String batch;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否软删
     */
    private Byte isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 配送时间
     */
    private Date deliveryDate;

    private static final long serialVersionUID = 1L;
}