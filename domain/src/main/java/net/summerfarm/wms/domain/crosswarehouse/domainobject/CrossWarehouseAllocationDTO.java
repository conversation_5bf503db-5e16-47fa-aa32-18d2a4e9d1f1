package net.summerfarm.wms.domain.crosswarehouse.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dongcheng
 * @date: 2023/10/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossWarehouseAllocationDTO {

    /**
     * 库存仓
     */
    private Long warehouseNo;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 分配数量
     */
    private Integer allocationNum;

    /**
     * 待分配数量
     */
    private Integer waitAllocationNum;

    /**
     * 供应单号
     */
    private String psoNo;
}
