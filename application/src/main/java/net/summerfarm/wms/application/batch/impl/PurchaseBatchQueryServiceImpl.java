package net.summerfarm.wms.application.batch.impl;

import com.google.common.collect.Lists;
import net.summerfarm.wms.api.h5.batch.PurchaseBatchQueryService;
import net.summerfarm.wms.api.h5.batch.req.PurchaseBatchReqDTO;
import net.summerfarm.wms.domain.batch.domainobject.query.WarehouseBatchQuery;
import net.summerfarm.wms.api.h5.batch.res.PurchaseBatchResDTO;
import net.summerfarm.wms.api.h5.goods.SkuConvertCommandService;
import net.summerfarm.wms.api.h5.stocktaking.dto.req.StockTakingBatchListQuery;
import net.summerfarm.wms.api.h5.stocktaking.dto.res.StockTakingBatchRespDTO;
import net.summerfarm.wms.api.h5.stocktaking.dto.res.StockTakingBatchRespDTO.*;
import net.summerfarm.wms.api.inner.batch.req.CostBatchDetailReqDTO;
import net.summerfarm.wms.api.inner.batch.req.CostBatchReqDTO;
import net.summerfarm.wms.api.inner.batch.req.ProduceBatchQueryReqDTO;
import net.summerfarm.wms.api.inner.batch.res.ProduceBatchResDTO;
import net.summerfarm.wms.api.inner.batch.ProduceBatchInnerService;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.MapUtil;
import net.summerfarm.wms.domain.batch.domainobject.CostBatch;
import net.summerfarm.wms.domain.batch.domainobject.CostBatchDetail;
import net.summerfarm.wms.domain.batch.domainobject.ProduceBatch;
import net.summerfarm.wms.domain.batch.enums.CostTypeEnum;
import net.summerfarm.wms.domain.batch.repository.CostBatchDetailRepository;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import net.summerfarm.wms.domain.batch.repository.ProduceBatchRepository;
import net.summerfarm.wms.domain.inventory.InventoryRepository;
import net.summerfarm.wms.domain.inventory.domainobject.InventoryRelation;
import net.summerfarm.wms.domain.stocktaking.domainobject.StocktakingItemBatch;
import net.summerfarm.wms.domain.stocktaking.domainobject.query.QueryItemBatch;
import net.summerfarm.wms.domain.stocktaking.repository.StocktakingQueryRepository;
import net.summerfarm.wms.facade.product.ProductFacade;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PurchaseBatchQueryServiceImpl implements PurchaseBatchQueryService {

    @Resource
    private ProduceBatchInnerService produceBatchBizService;

    @Resource
    private StocktakingQueryRepository stocktakingQueryRepository;

    @Resource
    private SkuConvertCommandService skuConvertCommandService;

    @Resource
    private CostBatchRepository costBatchRepository;

    @Resource
    private ProduceBatchRepository produceBatchRepository;

    @Resource
    private CostBatchDetailRepository costBatchDetailRepository;

    @Resource
    private InventoryRepository inventoryRepository;

    @Resource
    private ProductFacade productFacade;

    @Override
    public List<PurchaseBatchResDTO> queryPurchaseBatch(PurchaseBatchReqDTO produceBatchReqDTO) {

        List<StocktakingItemBatch> details = stocktakingQueryRepository.findItemBatchByItemIds(QueryItemBatch.builder()
                .itemIds(Lists.newArrayList(produceBatchReqDTO.getItemId()))
                .build());

        ProduceBatchResDTO produceBatchRes = produceBatchBizService.queryProduceBatch(ProduceBatchQueryReqDTO.builder()
                .warehouseNo(produceBatchReqDTO.getWarehouseNo().intValue())
                .produceAt(produceBatchReqDTO.getProduceAt())
                .shelfLife(produceBatchReqDTO.getShelfLife())
                .sku(produceBatchReqDTO.getSku())
                .build());
        if (Objects.isNull(produceBatchRes)) {
            return queryOne(produceBatchReqDTO, details);
        }


        return Optional.ofNullable(produceBatchRes.getCostBatches()).orElse(Lists.newArrayList()).stream()
                .filter(batch -> details.stream().noneMatch(item -> batch.getPurchaseNo().equals(item.getBatch())))
                .filter(batch -> details.stream().noneMatch(item -> !produceBatchReqDTO.getProduceAt().equals(item.getProduceAt()) &&
                        !produceBatchReqDTO.getShelfLife().equals(item.getShelfLife())))
                .map(batch -> PurchaseBatchResDTO.builder()
                        .batchNum(produceBatchRes.getQuantity())
                        .purchaseNo(batch.getPurchaseNo())
                        .cost(batch.getCostBatchDetailResDTOList().stream()
                                .filter(item -> CostTypeEnum.PURCHASE_COST.getType().equals(item.getType()))
                                .findFirst()
                                .orElse(CostBatchDetailReqDTO.builder().cost(BigDecimal.ZERO).build())
                                .getCost().toString())
                        .produceBatch(produceBatchRes.getId())
                        .produceAt(produceBatchRes.getProduceAt())
                        .shelfLife(produceBatchRes.getShelfLife())
                        .sku(produceBatchReqDTO.getSku())
                        .warehouseNo(produceBatchReqDTO.getWarehouseNo())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public StockTakingBatchRespDTO listStockTakingBatch(StockTakingBatchListQuery stockTakingBatchListQuery) {
        // sku转换
        List<Long> saasSkuIdList = stockTakingBatchListQuery.getSaasSkuIdList();
        List<String> skuList = skuConvertCommandService.saasSkuIdList2Sku(saasSkuIdList);
        if (CollectionUtils.isEmpty(skuList)) {
            return StockTakingBatchRespDTO.builder()
                    .stockTakingBatchDTOList(Lists.newArrayList())
                    .build();
        }
        // 查询成本批次信息
        List<CostBatch> costBatchList = costBatchRepository.queryByWareNoAndSkuList(stockTakingBatchListQuery.getWarehouseNo(), skuList);
        if (CollectionUtils.isEmpty(costBatchList)) {
            return StockTakingBatchRespDTO.builder()
                    .stockTakingBatchDTOList(Lists.newArrayList())
                    .build();
        }
        return StockTakingBatchRespDTO.builder()
                .stockTakingBatchDTOList(this.buildStockTakingBatch(costBatchList))
                .build();

    }

    private List<StockTakingBatchDTO> buildStockTakingBatch(List<CostBatch> costBatchList) {
        // 生产批次信息
        List<Long> produceBatchIdList = costBatchList.stream().map(CostBatch::getProduceBatchId).distinct().collect(Collectors.toList());
        List<ProduceBatch> produceBatchList = produceBatchRepository.queryProduceBatchByIdList(produceBatchIdList);
        Map<Long, ProduceBatch> produceBatchMap = produceBatchList.stream().collect(Collectors.toMap(ProduceBatch::getId, Function.identity(), (a, b) -> a));
        // 成本价信息
        List<Long> costBatchIdList = costBatchList.stream().map(CostBatch::getId).collect(Collectors.toList());
        List<CostBatchDetail> detailList = costBatchDetailRepository.queryListByCostBatchIds(costBatchIdList);
        Map<Long, List<CostBatchDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(CostBatchDetail::getCostBatchId));

        List<StockTakingBatchDTO> result = Lists.newArrayList();
        Map<String, List<CostBatch>> costBatchMap = costBatchList.stream().collect(Collectors.groupingBy(batch ->
                MapUtil.generateKey(batch.getWarehouseNo(), batch.getSku(), batch.getPurchaseNo(), batch.getProduceBatchId())));
        // 货品信息
        List<String> skuList = costBatchList.stream().map(CostBatch::getSku).distinct().collect(Collectors.toList());
        List<InventoryRelation> inventoryRelationList = inventoryRepository.selectInventoryRelationBySkuCodes(skuList);
        Map<String, InventoryRelation> inventoryMap = inventoryRelationList.stream().collect(Collectors.toMap(InventoryRelation::getSku, Function.identity(), (a, b) -> a));
        List<Long> categoryIdList = inventoryRelationList.stream().map(InventoryRelation::getCategoryId).distinct().collect(Collectors.toList());
        Map<Long, String> categoryNameMap = productFacade.batchSelectWholeCategoryName(categoryIdList);

        costBatchMap.forEach((key, batchList) -> {
            if (CollectionUtils.isEmpty(batchList)) {
                return;
            }
            CostBatch costBatch = batchList.stream()
                    .sorted(Comparator.comparing(CostBatch::getId).reversed()).collect(Collectors.toList()).get(0);
            // 成本
            Long costBatchId = costBatch.getId();
            List<CostBatchDetail> details = detailMap.get(costBatchId);
            BigDecimal cost = details.stream()
                    .filter(detail -> CostTypeEnum.PURCHASE_COST.getType().equals(detail.getType()))
                    .findFirst()
                    .orElse(CostBatchDetail.builder().cost(BigDecimal.ZERO).build()).getCost();
            // 库存量
            Integer quantity = batchList.stream().mapToInt(CostBatch::getQuantity).sum();
            // 类目
            String sku = costBatch.getSku();
            InventoryRelation inventoryRelation = inventoryMap.get(sku);
            String categoryName = StringUtils.EMPTY;
            if (Objects.nonNull(inventoryRelation)) {
                categoryName = categoryNameMap.get(inventoryRelation.getCategoryId());
            }
            ProduceBatch produceBatch = produceBatchMap.get(costBatch.getProduceBatchId());
            StockTakingBatchDTO batchDTO = StockTakingBatchDTO.builder()
                    .sku(costBatch.getSku())
                    .warehouseNo(costBatch.getWarehouseNo())
                    .purchaseNo(costBatch.getPurchaseNo())
                    .productionDate(Objects.nonNull(produceBatch) ? DateUtil.toLocalDate(produceBatch.getProduceAt()) : null)
                    .qualityDate(Objects.nonNull(produceBatch) ? DateUtil.toLocalDate(produceBatch.getShelfLife()) : null)
                    .batchQuantity(quantity)
                    .cost(cost)
                    .category(categoryName)
                    .build();
            result.add(batchDTO);
        });
        if (CollectionUtils.isNotEmpty(result)) {
            skuConvertCommandService.setSaasSkuIdForList(result);
        }
        return result.stream().sorted(Comparator.comparing(StockTakingBatchDTO::getSaasSkuId)).collect(Collectors.toList());
    }

    private ArrayList<PurchaseBatchResDTO> queryOne(PurchaseBatchReqDTO produceBatchReqDTO, List<StocktakingItemBatch> details) {
        List<ProduceBatchResDTO> produceBatchList = produceBatchBizService.queryProduceBatchList(ProduceBatchQueryReqDTO.builder()
                .warehouseNo(produceBatchReqDTO.getWarehouseNo().intValue())
                .skuList(Lists.newArrayList(produceBatchReqDTO.getSku()))
                .build());
        if (CollectionUtils.isEmpty(produceBatchList)) {
            return Lists.newArrayList();
        }
        ProduceBatchResDTO produceBatchResDTO = produceBatchList.stream()
                .max(Comparator.comparing(ProduceBatchResDTO::getId))
                .orElse(ProduceBatchResDTO.builder().build());

        CostBatchReqDTO lastlyCostBatch = produceBatchResDTO.getCostBatches().stream()
                .filter(batch -> details.stream().noneMatch(item -> !produceBatchReqDTO.getProduceAt().equals(item.getProduceAt()) &&
                        !produceBatchReqDTO.getShelfLife().equals(item.getShelfLife())))
                .filter(batch -> details.stream().noneMatch(item -> batch.getPurchaseNo().equals(item.getBatch())))
                .max(Comparator.comparing(CostBatchReqDTO::getPurchaseNo))
                .orElse(CostBatchReqDTO.builder().build());

        if (StringUtils.isEmpty(lastlyCostBatch.getPurchaseNo())) {
            return Lists.newArrayList();
        }

        BigDecimal cost = lastlyCostBatch.getCostBatchDetailResDTOList().stream()
                .filter(item -> CostTypeEnum.PURCHASE_COST.getType().equals(item.getType()))
                .findFirst()
                .orElse(CostBatchDetailReqDTO.builder().cost(BigDecimal.ZERO).build())
                .getCost();


        return Lists.newArrayList(PurchaseBatchResDTO.builder()
                .batchNum(produceBatchResDTO.getQuantity())
                .purchaseNo(lastlyCostBatch.getPurchaseNo())
                .cost(cost.toString())
                .produceBatch(produceBatchResDTO.getId())
                .produceAt(produceBatchResDTO.getProduceAt())
                .shelfLife(produceBatchResDTO.getShelfLife())
                .sku(produceBatchReqDTO.getSku())
                .warehouseNo(produceBatchReqDTO.getWarehouseNo())
                .build()
        );
    }
}
