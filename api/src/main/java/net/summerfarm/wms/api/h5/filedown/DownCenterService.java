package net.summerfarm.wms.api.h5.filedown;

import java.io.File;
import java.util.function.BiConsumer;

/**
 * @author: dongcheng
 * @date: 2023/12/18
 */
public interface DownCenterService {

    /**
     * 下载中心初始化下载
     *
     * @param fileName  文件名称
     * @param fileType  文件类型
     * @param expireDay 过期时间
     * @return 返回文件导出id 返回的id可能为空
     */
    Long startUpload(String fileName, Integer fileType, net.summerfarm.common.client.enums.DownloadCenterEnum.FileExpiredDayEnum expireDay);

    /**
     * 下载中心初始化下载
     *
     * @param fileName  文件名称
     * @param fileType  文件类型
     * @param expireDay 过期时间
     * @param params    查询参数（json格式）
     * @return 返回文件导出id 返回的id可能为空
     */
    Long startUpload(String fileName, Integer fileType, net.summerfarm.common.client.enums.DownloadCenterEnum.FileExpiredDayEnum expireDay, String params);

    /**
     * 文件下载结束
     *
     * @param fileId   文件id
     * @param callable 调用方法
     * @param <T>      查询条件信息
     * @return
     */
    <T> void endUpload(Long fileId, String fileName,
                       net.summerfarm.common.client.enums.DownloadCenterEnum.FileExpiredDayEnum expireDay,
                       T t,
                       BiConsumer<File, T> callable);
}
