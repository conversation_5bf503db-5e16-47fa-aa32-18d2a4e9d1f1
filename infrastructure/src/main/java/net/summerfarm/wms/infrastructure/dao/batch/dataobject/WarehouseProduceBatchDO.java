package net.summerfarm.wms.infrastructure.dao.batch.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/9/27  15:39
 * <p>
 * 生产批次信息实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseProduceBatchDO {


    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * sku
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 保质期 时间戳
     */
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    private Long produceAt;

    /**
     * 租户id
     */
    private Long tenantId;

}
