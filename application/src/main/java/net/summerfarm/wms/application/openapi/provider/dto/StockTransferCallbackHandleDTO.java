package net.summerfarm.wms.application.openapi.provider.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * 处理三方转换回告的dto
 * <AUTHOR>
 * @Date 2024/7/29 15:01
 * @Version 1.0
 */
@AllArgsConstructor
@Data
public class StockTransferCallbackHandleDTO {

    /**
     * opDetail应该写入数据库的三方回告数量 key 是 opDetail的id，value是三方回告数量
     */
    private Map<Long, Integer> externalTransferInNumMap;

    /**
     * sku维度三方回告转入数量
     */
    private Map<String, Integer> externalTransferSkuInNumMap;

    /**
     * 需要进行告警的转出sku的数量
     * key 是 outSku + outSkuProductionDate + outSkuExpireDate
     */
    private Map<String, Integer> needAlarmOutSkuNum;

    /**
     * 需要进行告警的转入sku的效期和数量
     * key 是 outSku + inSku + outSkuProductionDate + outSkuExpireDate
     */
    private Map<String, Integer> callBackNotInDbTransferInNumMap;

    /**
     * 转换任务id
     */
    private Long stockTransferId;
}
