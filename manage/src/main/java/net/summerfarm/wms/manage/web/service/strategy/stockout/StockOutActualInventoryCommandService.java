package net.summerfarm.wms.manage.web.service.strategy.stockout;

import net.summerfarm.wms.manage.web.service.strategy.stockout.input.StockOutActualInventoryCommandInput;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/12
 */
public interface StockOutActualInventoryCommandService {

    /**
     * 处理实物库存
     * @param stockOutActualInventoryCommandInput
     */
    void handleActualInventoryNormal(StockOutActualInventoryCommandInput stockOutActualInventoryCommandInput);

    /**
     * 处理波次任务占用（非库位管理）
     * @param stockOutActualInventoryCommandInput
     */
    void handleStockTaskWaveOccupyReduceForNoCabinet(StockOutActualInventoryCommandInput stockOutActualInventoryCommandInput);
}
