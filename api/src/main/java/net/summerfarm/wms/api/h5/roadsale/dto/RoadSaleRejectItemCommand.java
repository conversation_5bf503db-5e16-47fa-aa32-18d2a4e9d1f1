package net.summerfarm.wms.api.h5.roadsale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * @Description
 * @Date 2023/9/14 16:31
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoadSaleRejectItemCommand {
    /**
     * sku编码
     */
    @NotBlank(message = "拒收的sku编码不能为空")
    private String sku;
    /**
     * 拒收数量
     */
    @NotNull(message = "拒收数量不能为空")
    @Positive(message = "拒收数量必须大于0")
    private Integer rejectQuantity;

}
