package net.summerfarm.wms.web.controller.sku;

import net.summerfarm.wms.api.h5.sku.SkuBarcodeCommandService;
import net.summerfarm.wms.api.h5.sku.SkuBarcodeQueryService;
import net.summerfarm.wms.api.h5.sku.dto.req.SkuBarcodePrintQuery;
import net.summerfarm.wms.api.h5.sku.dto.req.SkuBarcodeQuery;
import net.summerfarm.wms.api.h5.sku.dto.req.SkuBarcodeUpsertCommand;
import net.summerfarm.wms.api.h5.sku.dto.res.SkuBarcodePrintCheckRes;
import net.summerfarm.wms.api.h5.sku.dto.res.SkuBarcodePrintRes;
import net.summerfarm.wms.api.h5.sku.dto.res.SkuBarcodeRes;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * sku条码
 */
@RestController
@RequestMapping("/sku-barcode")
public class SkuBarcodeController {

    @Resource
    private SkuBarcodeCommandService skuBarcodeCommandService;
    @Resource
    private SkuBarcodeQueryService skuBarcodeQueryService;

    /**
     * 创建/更新sku条码
     */
    @PostMapping(value = "/upsert")
    public CommonResult<Boolean> createSkuBarcode(@Valid @RequestBody SkuBarcodeUpsertCommand skuBarcodeUpsertCommand) {
        return CommonResult.ok(skuBarcodeCommandService.upsertSkuBarcode(skuBarcodeUpsertCommand));
    }

    /**
     * 查询sku条码
     */
    @PostMapping(value = "/query")
    public CommonResult<SkuBarcodeRes> querySkuBarcode(@Valid @RequestBody SkuBarcodeQuery skuBarcodeQuery) {
        return CommonResult.ok(skuBarcodeQueryService.querySkuBarcodes(skuBarcodeQuery));
    }

    /**
     * sku条码打印校验
     */
    @PostMapping(value = "/query/print-check")
    public CommonResult<List<SkuBarcodePrintCheckRes>> checkBarcodePrint(@Valid @RequestBody SkuBarcodePrintQuery skuBarcodeQuery) {
        return CommonResult.ok(skuBarcodeQueryService.checkBarcodePrint(skuBarcodeQuery));
    }


    /**
     * 查询sku条码
     */
    @PostMapping(value = "/query/print")
    public CommonResult<SkuBarcodePrintRes> querySkuBarcodePrint(@Valid @RequestBody SkuBarcodeQuery skuBarcodeQuery) {
        return CommonResult.ok(skuBarcodeQueryService.querySkuBarcodePrint(skuBarcodeQuery));
    }
}
