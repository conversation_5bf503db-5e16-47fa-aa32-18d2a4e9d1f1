package net.summerfarm.wms.domain.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseQuery implements Serializable {

    private static final long serialVersionUID = 665144150308423100L;

    private List<Sort> sorts = new ArrayList<>();
}
