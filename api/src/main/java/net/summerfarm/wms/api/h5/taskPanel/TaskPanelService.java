package net.summerfarm.wms.api.h5.taskPanel;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.taskPanel.dto.resp.TaskPanelResponseVO;

/**
 * 任务面板查询相关接口
 * <AUTHOR>
 * @Date 2024/9/25 10:43
 * @Version 1.0
 */
public interface TaskPanelService {

    /**
     * 查询任务面板
     * @param warehouseNo
     * @return
     */
    TaskPanelResponseVO queryTaskPanel(Long warehouseNo);

    /**
     * 查询任务面板 - 弹窗
     * @param warehouseNo - 仓库编号
     * @param type - 类型枚举
     * @param pageNum - 页码
     * @param pageSize - 每页数量
     * @return 返回结果
     */
    PageInfo<String> pageWindowInfo(Long warehouseNo, Integer type, Integer pageNum, Integer pageSize);
}
