package net.summerfarm.wms.manage.web.service;

import net.summerfarm.wms.manage.web.req.StockTaskSaleOutReq;
import net.summerfarm.wms.manage.model.domain.StockTask;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
public interface SupplyAgainTaskService extends StockTaskStrategy {

    /**
     * 操作入库单个sku
     *
     * @param req
     * @param adminName
     * @param stockTask
     * @param processId
     */
    void handleStockTaskSaleOutReq(StockTaskSaleOutReq req, String adminName, StockTask stockTask, Integer processId, Set<String> missionNoSet, Map<String, Integer> skuHandledMap, List<Long> updateStatusMappingIdList);
}
