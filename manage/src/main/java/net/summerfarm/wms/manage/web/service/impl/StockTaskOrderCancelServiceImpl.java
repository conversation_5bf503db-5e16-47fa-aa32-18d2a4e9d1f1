package net.summerfarm.wms.manage.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.api.h5.stocktask.MissionTaskItemMappingQueryService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.MissionTaskItemMappingSkuQuery;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingDTO;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingResp;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItem;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.stocktask.OutNoticeAbnormalDetailQueryRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderAbnormalDetail;
import net.summerfarm.wms.domain.stocktask.enums.NoticeOrderSupplyModeEnum;
import net.summerfarm.wms.domain.stocktask.enums.NoticeStockTaskOrderTypeEnum;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemCreateDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryReleaseReqDTO;
import net.summerfarm.wms.manage.model.domain.*;
import net.summerfarm.wms.manage.model.vo.StockTaskItemVO;
import net.summerfarm.wms.manage.web.enums.OptionFlagTypeEnum;
import net.summerfarm.wms.manage.web.enums.StockTaskType;
import net.summerfarm.wms.manage.web.enums.impl.OtherStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.facade.CabinetInventoryFacade;
import net.summerfarm.wms.manage.web.factory.StockTaskWaveSkuOccupyFactory;
import net.summerfarm.wms.manage.web.mapper.manage.AreaStoreMapper;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskItemMapper;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskStorageMapper;
import net.summerfarm.wms.manage.web.reposity.StockTaskOrderSkuRepositoryV1;
import net.summerfarm.wms.manage.web.reposity.StockTaskWaveSkuOccupyRepository;
import net.summerfarm.wms.manage.web.enums.impl.SaleStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.enums.impl.StoreRecordType;
import net.summerfarm.wms.manage.web.factory.StockTaskItemDetailUpdateFactory;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskMapper;
import net.summerfarm.wms.manage.web.reposity.StockTaskItemCabinetOccupyRepositoryV1;
import net.summerfarm.wms.manage.web.service.*;
import net.summerfarm.wms.manage.web.utils.OptionFlagUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/7/23
 */
@Service
@Slf4j
public class StockTaskOrderCancelServiceImpl implements StockTaskOrderCancelService {

    @Resource
    private StockTaskWaveSkuOccupyRepository stockTaskWaveSkuOccupyRepository;
    @Resource
    private StockTaskOrderSkuRepositoryV1 stockTaskOrderSkuRepositoryV1;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private AreaStoreServiceV1 areaStoreServiceV1;
    @Resource
    private QuantityChangeRecordServiceV1 quantityChangeRecordServiceV1;
    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;
    @Resource
    private StockTaskItemCabinetOccupyRepositoryV1 stockTaskItemCabinetOccupyRepositoryV1;
    @Resource
    private MissionTaskItemMappingQueryService missionTaskItemMappingQueryService;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private StockTaskOrderSkuRepositoryV1 stockTaskOrderSkuRepository;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private StockTaskStorageMapper stockTaskStorageMapper;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private ProductsService productsService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private OutNoticeAbnormalDetailQueryRepository outNoticeAbnormalDetailQueryRepository;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private StockStorageItemRepository stockStorageItemRepository;

    @Override
    public void handleStockTaskWaveOccupyInit(Integer stockTaskId) {
        if (stockTaskId == null) {
            return;
        }

        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (!OptionFlagUtil.hasValue(stockTask.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            return;
        }
        List<StockTaskWaveSkuOccupyDO> stockTaskWaveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository
                .selectListByStockTaskId(stockTaskId, null);
        if (!CollectionUtils.isEmpty(stockTaskWaveSkuOccupyDOList)) {
            return;
        }

        List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepositoryV1.selectListByTaskId(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
            return;
        }

        Integer warehouseNo = stockTask.getAreaNo();
        Integer storeNo = stockTask.getOutStoreNo();
        LocalDate deliveryDate = stockTask.getExpectTime().toLocalDate();

        // 出库任务类型
        Integer type = stockTask.getType();
        // 通知单订单类型
        Integer noticeOrderType = NoticeStockTaskOrderTypeEnum.getOrderTypeByStockTaskType(type);
        // 供应方式
        Integer supplyMode = net.summerfarm.wms.base.enums.StockTaskType.CROSS_OUT.getId() == type ? NoticeOrderSupplyModeEnum.NOT_IN_STORE.getCode() : NoticeOrderSupplyModeEnum.IN_STORE.getCode();
        // 订单
        List<String> orderNoList = stockTaskOrderSkuDOList.stream().map(StockTaskOrderSkuDO::getOutOrderNo).distinct().collect(Collectors.toList());
        // 查询订单取消异常明细
        List<StockTaskNoticeOrderAbnormalDetail> stockTaskNoticeOrderAbnormalDetailList = outNoticeAbnormalDetailQueryRepository.findByExceptTimeAndWarehouseStore(warehouseNo, storeNo, deliveryDate, noticeOrderType, supplyMode, orderNoList);
        // 根据异常明细初始化波次任务占用明细
        List<StockTaskWaveSkuOccupyDO> createWaveSkuOccupyList = StockTaskWaveSkuOccupyFactory.createWithNotice(stockTask, stockTaskOrderSkuDOList, stockTaskNoticeOrderAbnormalDetailList);
        stockTaskWaveSkuOccupyRepository.insertList(createWaveSkuOccupyList);
    }

    @Override
    public void handleStockTaskWaveOccupyReduce(Integer stockTaskId, String sku, Integer changeQuantity, String operatorName) {
        if (stockTaskId == null) {
            return;
        }

        // 波次出库任务在截单处理冻结
        StockTask task = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (task == null ||
                !OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            return;
        }

        // 出库波次SKU占用
        List<StockTaskWaveSkuOccupyDO> waveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository.selectListByStockTaskId(
                stockTaskId, Arrays.asList(sku));
        if (CollectionUtils.isEmpty(waveSkuOccupyDOList)) {
            return;
        }
        StockTaskWaveSkuOccupyDO stockTaskWaveSkuOccupyDO = waveSkuOccupyDOList.get(0);
        if (changeQuantity > stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity() +
                stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity()) {
            String error = String.format("变更数量超过剩余订单锁定数量 %d %d %d %d", stockTaskId,
                    changeQuantity,
                    stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity(),
                    stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
            log.error(error);
            throw new BizException("变更数量超过剩余订单锁定数量");
        }

        Integer remainQuantity = changeQuantity;

        Integer remainOccupyQuantityReduce = 0;
        if (stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity() == 0){
            remainOccupyQuantityReduce = 0;
        } else if (remainQuantity <= stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity()) {
            remainOccupyQuantityReduce = remainQuantity;
            remainQuantity = 0;
        } else {
            remainOccupyQuantityReduce = stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity();
            remainQuantity = remainQuantity - remainOccupyQuantityReduce;
        }

        Integer remainNotOccupyQuantityReduce = 0;
        if (remainQuantity > 0) {
            if (remainQuantity <= stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity()) {
                remainNotOccupyQuantityReduce = remainQuantity;
            } else {
                log.error("波次任务{}变更数量超过订单锁定剩余数量，变更数量：{}，剩余占用数量：{}，剩余无需占用数量：{}，最终差异数量：{}", stockTaskId, changeQuantity, stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity(), stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity(), remainQuantity);
                throw new BizException("变更数量超过订单锁定剩余数量");
            }
        }

        stockTaskWaveSkuOccupyRepository.updatePickChange(
                stockTaskWaveSkuOccupyDO.getId(),
                remainOccupyQuantityReduce,
                remainNotOccupyQuantityReduce
        );

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();

        Integer type = task.getType();
        Integer warehouseNo = task.getAreaNo();
        Integer storeNo = task.getOutStoreNo();

        if (remainOccupyQuantityReduce != 0){
            if (type.equals(StoreRecordType.SALE_OUT.getId()) || type.equals(StoreRecordType.OWN_SALE_OUT.getId())) {
                // 释放冻结库存
                areaStoreServiceV1.updateLockStockByWarehouseNoWithOperatorName(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.SALE_OUT,
                        task.getId() + "", recordMap, operatorName);
            } else if (type.equals(StoreRecordType.DEMO_OUT.getId())) {
                //减锁定库存
                areaStoreServiceV1.updateLockStockByWarehouseNoWithOperatorName(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.DEMO_OUT,
                        task.getId() + "", recordMap, operatorName);
            } else if (type.equals(StoreRecordType.SUPPLY_AGAIN_TASK.getId())) {
                //减锁定库存
                areaStoreServiceV1.updateLockStockByWarehouseNoWithOperatorName(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.RECOVER_OUT,
                        task.getId() + "", recordMap, operatorName);
            } else if (type.equals(StoreRecordType.CROSS_OUT.getId())) {
                // 释放冻结库存
                areaStoreServiceV1.updateLockStockByWarehouseNoWithOperatorName(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.CROSS_OUT,
                        task.getId() + "", recordMap, operatorName);
            }
        }

        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleCabinetReleaseByStockTaskIdFinish(Integer stockTaskId, String adminName) {
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            return;
        }
        List<StockTaskItemCabinetOccupyDO> cabinetOccupyDOList = stockTaskItemCabinetOccupyRepositoryV1
                .selectListByStockTaskIdOccupyedExcludedJJV1(stockTaskId, null);
        if (CollectionUtils.isEmpty(cabinetOccupyDOList)) {
            return;
        }

        cabinetOccupyDOList = cabinetOccupyDOList.stream()
                .filter(s -> s.getOccupyQuantity() > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cabinetOccupyDOList)) {
            return;
        }

        CabinetInventoryReleaseReqDTO releaseReqDTO = StockTaskItemDetailUpdateFactory
                .newInstance(stockTask, cabinetOccupyDOList);
        cabinetInventoryFacade.releaseCabinetInventory(releaseReqDTO);

        for (StockTaskItemCabinetOccupyDO stockTaskItemCabinetOccupyDO : cabinetOccupyDOList) {
            stockTaskItemCabinetOccupyRepositoryV1.updateReleaseChange(
                    stockTaskItemCabinetOccupyDO.getId(),
                    stockTaskItemCabinetOccupyDO.getOccupyQuantity()
            );
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void releaseInventoryLockForCrossTask(Integer stockTaskId, List<String> skuList) {
        if (null == stockTaskId || CollectionUtils.isEmpty(skuList)) {
            return;
        }
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            throw new BizException("未查到出库任务，完成出库失败");
        }
        List<StockTaskItemVO> stockTaskItemVOList = stockTaskItemMapper.selectById(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskItemVOList)) {
            throw new BizException("未查到出库任务明细，完成出库失败");
        }
        MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
        missionTaskItemMappingSkuQuery.setStockTaskId(stockTaskId.toString());
        missionTaskItemMappingSkuQuery.setSkus(skuList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryAllMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
        if (null == missionTaskItemMappingResp.getMissionTaskItemMappings()) {
            missionTaskItemMappingResp.setMissionTaskItemMappings(new ArrayList<>());
        }
        // sku已生成拣货任务数量
        Map<String, Integer> missionTaskItemMap = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(Collectors.toMap(MissionTaskItemMappingDTO::getSku, MissionTaskItemMappingDTO::getShouldPickQuantity, Integer::sum));
        // 出库任务明细数量
        Map<String, Integer> stockTaskItemMap = stockTaskItemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, StockTaskItemVO::getQuantity, Integer::sum));
        // 库存操作记录
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        // 异常通知单明细
        List<StockTaskNoticeOrderAbnormalDetail> stockTaskNoticeOrderAbnormalDetailList = outNoticeAbnormalDetailQueryRepository.findByStockTaskId(stockTaskId.longValue());
        Map<String, Integer> noticeOrderAbnormalSkuMap = stockTaskNoticeOrderAbnormalDetailList.stream().collect(Collectors.toMap(StockTaskNoticeOrderAbnormalDetail::getSku, StockTaskNoticeOrderAbnormalDetail::getQuantity, Integer::sum));
        // 释放未出库冻结
        stockTaskItemMap.forEach((sku, quantity) -> {
            // 存在异常数量处理
            Integer noticeOrderAbnormalQuantity = null == noticeOrderAbnormalSkuMap.get(sku) ? 0 : noticeOrderAbnormalSkuMap.get(sku);
            // 缺出数量>异常数量【缺出>退单】（只操作缺出-异常部分的虚拟和冻结，剩余部分由多出入库驱动处理）
            log.info("存在缺出库存处理，实出大于取消数量，出库任务：{}，需调整数量：{}", stockTaskId, quantity - noticeOrderAbnormalQuantity);
            Integer shouldPickQuantity = missionTaskItemMap.get(sku);
            if (null == shouldPickQuantity) {
                if (quantity > noticeOrderAbnormalQuantity) {
                    Integer doQuantity = quantity - noticeOrderAbnormalQuantity;
                    // 释放缺拣或缺出产生差异冻结库存
                    areaStoreServiceV1.updateLockStockByWarehouseNo(-doQuantity, sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.CROSS_OUT, stockTask.getId() + "", recordMap);
                    log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，完成出库执行差异冻结释放，未进行拣货，释放数量：" + doQuantity);
                }
            } else if (quantity > shouldPickQuantity) {
                Integer diffQuantity = quantity - shouldPickQuantity;
                if (diffQuantity > noticeOrderAbnormalQuantity) {
                    Integer doQuantity = diffQuantity - noticeOrderAbnormalQuantity;
                    // 释放缺拣或缺出产生差异冻结库存
                    areaStoreServiceV1.updateLockStockByWarehouseNo(-doQuantity, sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.CROSS_OUT, stockTask.getId() + "", recordMap);
                    log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，完成出库执行差异冻结释放，部分拣货，释放数量：" + doQuantity);
                }
            } else {
                log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，完成出库执行差异冻结释放，已全部拣货，无差异冻结释放，过滤");
            }
        });
        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void releaseInventoryLockForSale(Integer stockTaskId, SaleStockChangeTypeEnum changeType) {
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            throw new BizException("未查到出库任务，完成出库失败");
        }
        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            // 精细化仓释放冻结
            releaseInventoryLockForCabinetSale(stockTaskId, changeType);
        } else {
            // 非精细化仓释放冻结
            releaseInventoryLockForNoCabinetSale(stockTaskId, changeType);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void releaseInventoryLockForCabinetSale(Integer stockTaskId, SaleStockChangeTypeEnum changeType) {
        if (null == stockTaskId || null == changeType) {
            return;
        }
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            throw new BizException("未查到出库任务，完成出库失败");
        }
        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        if (Boolean.FALSE.equals(openCabinetManagement)) {
            log.info("出库任务：{}，仓库：{}为非精细化仓，暂不支持", stockTaskId, stockTask.getAreaNo());
            return;
        }
        List<StockTaskItemVO> stockTaskItemVOList = stockTaskItemMapper.selectById(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskItemVOList)) {
            throw new BizException("未查到出库任务明细，完成出库失败");
        }
        List<String> skuList = stockTaskItemVOList.stream().map(StockTaskItemVO::getSku).collect(Collectors.toList());
        MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery = new MissionTaskItemMappingSkuQuery();
        missionTaskItemMappingSkuQuery.setStockTaskId(stockTaskId.toString());
        missionTaskItemMappingSkuQuery.setSkus(skuList);
        MissionTaskItemMappingResp missionTaskItemMappingResp = missionTaskItemMappingQueryService.queryAllMissionTaskItemMapping(missionTaskItemMappingSkuQuery);
        if (null == missionTaskItemMappingResp.getMissionTaskItemMappings()) {
            missionTaskItemMappingResp.setMissionTaskItemMappings(new ArrayList<>());
        }
        // sku已生成拣货任务数量
        Map<String, Integer> missionTaskItemMap = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(Collectors.toMap(MissionTaskItemMappingDTO::getSku, MissionTaskItemMappingDTO::getShouldPickQuantity, Integer::sum));
        // sku已拣货任务数量
        Map<String, Integer> missionTaskItemAutalQuantityMap = missionTaskItemMappingResp.getMissionTaskItemMappings().stream().collect(Collectors.toMap(MissionTaskItemMappingDTO::getSku, MissionTaskItemMappingDTO::getActualQuantity, Integer::sum));
        // 出库任务明细数量
        Map<String, Integer> stockTaskItemMap = stockTaskItemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, StockTaskItemVO::getQuantity, Integer::sum));
        // 库存操作记录
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        // 异常通知单明细
        List<StockTaskNoticeOrderAbnormalDetail> stockTaskNoticeOrderAbnormalDetailList = outNoticeAbnormalDetailQueryRepository.findByStockTaskId(stockTaskId.longValue());
        Map<String, Integer> noticeOrderAbnormalSkuMap = stockTaskNoticeOrderAbnormalDetailList.stream().collect(Collectors.toMap(StockTaskNoticeOrderAbnormalDetail::getSku, StockTaskNoticeOrderAbnormalDetail::getQuantity, Integer::sum));
        // 释放未出库冻结
        stockTaskItemMap.forEach((sku, quantity) -> {
            // 存在异常数量处理
            int noticeOrderAbnormalQuantity = null == noticeOrderAbnormalSkuMap.get(sku) ? 0 : noticeOrderAbnormalSkuMap.get(sku);
            // 缺出数量>异常数量【缺出>退单】（只操作缺出-异常部分的虚拟和冻结，剩余部分由多出入库驱动处理）
            log.info("完成出库存在缺出库存处理，实出与取消数量不同，出库任务：{}，需调整数量：{}", stockTaskId, quantity - noticeOrderAbnormalQuantity);
            // 应拣总数量
            Integer shouldPickQuantity = null == missionTaskItemMap.get(sku) ? 0 : missionTaskItemMap.get(sku);
            // 总实出
            int actualQuantity = null == missionTaskItemAutalQuantityMap.get(sku) ? 0 : missionTaskItemAutalQuantityMap.get(sku);
            if (actualQuantity >= noticeOrderAbnormalQuantity && noticeOrderAbnormalQuantity > 0) {
                log.info("完成出库存在缺出库存处理，实出 > 退货数量，不需要处理，出库任务：{}", stockTaskId);
                return;
            }
            // 需释放数量（总应出-总应拣）
            int diffQuantity = quantity - shouldPickQuantity;
            if (diffQuantity != noticeOrderAbnormalQuantity) {
                Integer doQuantity = diffQuantity - noticeOrderAbnormalQuantity;
                // 应出-应拣 < 退单
                if (doQuantity < 0) {
                    doQuantity = diffQuantity;
                    log.info("完成出库存在缺出库存处理，应出-应拣 < 退单，出库任务：{}，需调整数量：{}", stockTask.getId(), doQuantity);
                }
                // 应出-应拣 > 退单
                if (diffQuantity > noticeOrderAbnormalQuantity) {
                    log.info("完成出库存在缺出库存处理，应出-应拣 > 退单，出库任务：{}，需调整冻结数量：{}", stockTask.getId(), doQuantity);
                    // 释放缺拣或缺出产生差异冻结库存
                    areaStoreServiceV1.updateLockStockByWarehouseNo(-doQuantity, sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), changeType, stockTask.getId() + "", recordMap);
                }
                List<Integer> stockTaskTypeList = Arrays.asList(StockTaskType.SALE_OUT.getId(), StockTaskType.DEMO_OUT.getId(), StockTaskType.SUPPLY_AGAIN_TASK.getId());
                AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(stockTask.getAreaNo(), sku));
                if (areaStore.getSync() == 1 && stockTaskTypeList.contains(stockTask.getType())) {
                    areaStoreServiceV1.updateOnlineStockByStoreNo(true, doQuantity, sku, stockTask.getAreaNo(), changeType, stockTask.getId() + "", recordMap, NumberUtils.INTEGER_ONE);
                }
                log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，完成出库执行差异冻结释放，释放数量：" + doQuantity);
            } else {
                log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，完成出库执行差异冻结释放，已全部拣货，无差异冻结释放，过滤");
            }
        });
        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void releaseInventoryLockForNoCabinetSale(Integer stockTaskId, SaleStockChangeTypeEnum changeType) {
        if (null == stockTaskId || null == changeType) {
            return;
        }
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            throw new BizException("未查到出库任务，强制完成出库失败");
        }
        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            log.info("出库任务：{}，仓库：{}为精细化仓，暂不支持", stockTaskId, stockTask.getAreaNo());
            return;
        }
        List<StockTaskItemVO> stockTaskItemVOList = stockTaskItemMapper.selectById(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskItemVOList)) {
            throw new BizException("未查到出库任务明细，强制完成出库失败");
        }
        // 出库任务明细数量
        Map<String, Integer> stockTaskItemMap = stockTaskItemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, item -> item.getQuantity() - item.getActualQuantity(), Integer::sum));
        // 出库任务明细实出数量
        Map<String, Integer> stockTaskItemActualQuantityMap = stockTaskItemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, StockTaskItem::getActualQuantity, Integer::sum));
        // 库存操作记录
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        // 异常通知单明细
        List<StockTaskNoticeOrderAbnormalDetail> stockTaskNoticeOrderAbnormalDetailList = outNoticeAbnormalDetailQueryRepository.findByStockTaskId(stockTaskId.longValue());
        Map<String, Integer> noticeOrderAbnormalSkuMap = stockTaskNoticeOrderAbnormalDetailList.stream().collect(Collectors.toMap(StockTaskNoticeOrderAbnormalDetail::getSku, StockTaskNoticeOrderAbnormalDetail::getQuantity, Integer::sum));
        // 释放未出库冻结
        stockTaskItemMap.forEach((sku, diffQuantity) -> {
            // 存在异常数量处理
            int noticeOrderAbnormalQuantity = null == noticeOrderAbnormalSkuMap.get(sku) ? 0 : noticeOrderAbnormalSkuMap.get(sku);
            // 实出数量
            int actualQuantity = null == stockTaskItemActualQuantityMap.get(sku) ? 0 : stockTaskItemActualQuantityMap.get(sku);
            if (actualQuantity >= noticeOrderAbnormalQuantity && noticeOrderAbnormalQuantity > 0) {
                log.info("完成出库存在缺出库存处理，实出 > 退货数量，不需要处理，出库任务：{}", stockTaskId);
                return;
            }
            // 缺出数量>异常数量【缺出>退单】（只操作缺出-异常部分的虚拟和冻结，剩余部分由多出入库驱动处理）
            log.info("完成出库存在缺出库存处理，实出与取消数量不同，出库任务：{}，需调整数量：{}", stockTaskId, diffQuantity - noticeOrderAbnormalQuantity);
            if (diffQuantity != noticeOrderAbnormalQuantity) {
                // 需释放数量（总剩余未出数量-总退单数量）
                int doQuantity = diffQuantity - noticeOrderAbnormalQuantity;
                // 应出-实出 < 退单
                if (doQuantity < 0) {
                    doQuantity = diffQuantity;
                    log.info("完成出库存在缺出库存处理，应出-实出 < 退单，出库任务：{}，需调整数量：{}", stockTask.getId(), doQuantity);
                }
                // 应出-实出 > 退单
                if (diffQuantity > noticeOrderAbnormalQuantity) {
                    log.info("完成出库存在缺出库存处理，应出-实出 > 退单，出库任务：{}，需调整冻结数量：{}", stockTask.getId(), doQuantity);
                    // 释放缺出产生差异冻结库存
                    areaStoreServiceV1.updateLockStockByWarehouseNo(-doQuantity, sku, stockTask.getOutStoreNo(), stockTask.getAreaNo(), changeType, stockTask.getId() + "", recordMap);
                }
                List<Integer> stockTaskTypeList = Arrays.asList(StockTaskType.SALE_OUT.getId(), StockTaskType.DEMO_OUT.getId(), StockTaskType.SUPPLY_AGAIN_TASK.getId());
                AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(stockTask.getAreaNo(), sku));
                if (areaStore.getSync() == 1 && stockTaskTypeList.contains(stockTask.getType())) {
                    areaStoreServiceV1.updateOnlineStockByStoreNo(true, doQuantity, sku, stockTask.getAreaNo(), changeType, stockTask.getId() + "", recordMap, NumberUtils.INTEGER_ONE);
                }
                log.info("出库任务：" + stockTaskId + "，sku：" + sku + "，强制完成出库执行差异冻结释放，释放数量：" + doQuantity);
            }
        });
        quantityChangeRecordServiceV1.insertRecord(recordMap);
    }

    @Override
    public List<OrderItem> getNotCreateSaleOrderTaskItem(Integer warehouseNo, Integer storeNo,
                                                         LocalDate deliveryDate,
                                                         List<OrderItem> orderItems, Integer taskType) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        List<String> orderNoList = orderItems.stream().map(OrderItem::getOrderNo).distinct().collect(Collectors.toList());

        List<String> existOrderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByOutOrderNoList(
                warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate),
                orderNoList, StoreRecordType.SALE_OUT.getId());

        List<OrderItem> notCreateTaskOrderItems = orderItems.stream()
                .filter(orderItem -> !existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.toList());

        // 已生成过的比对差值
        Map<String, List<OrderItem>> createdTaskOrderItemMap = orderItems.stream()
                .filter(orderItem -> existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.groupingBy(OrderItem::getOrderNo));
        for (Map.Entry<String, List<OrderItem>> createdTaskOrderItemEntry : createdTaskOrderItemMap.entrySet()) {

            String orderNo = createdTaskOrderItemEntry.getKey();

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<OrderItem> orderItemList = createdTaskOrderItemEntry.getValue();
            if (CollectionUtils.isEmpty(orderItemList)) {
                continue;
            }

            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    OrderItem::getSku, OrderItem::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                // 部分取消，多出入库任务兜底
                if (beforeQuantity.compareTo(nowQuantity) > 0) {
                    continue;
                } else if (beforeQuantity.compareTo(nowQuantity) < 0) {
                    OrderItem beforeOrderItem = null;
                    for (OrderItem orderItem : orderItemList) {
                        if (orderItem.getSku().equals(sku)) {
                            beforeOrderItem = orderItem;
                            break;
                        }
                    }
                    if (beforeOrderItem == null) {
                        log.error("多生成的出库任务无法匹配到SKU订单明细行 {} {}\n换行告警", sku, orderItemList);
                        throw new BizException("多生成的出库任务无法匹配到SKU订单明细行");
                    }

                    OrderItem orderItemTmp = new OrderItem();
                    BeanUtils.copyProperties(beforeOrderItem, orderItemTmp);
                    orderItemTmp.setAmount(nowQuantity - beforeQuantity);

                    notCreateTaskOrderItems.add(orderItemTmp);
                }
            }
        }
        return notCreateTaskOrderItems;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleSaleCancelOrderReturnInbound(Integer warehouseNo, Integer storeNo,
                                                   LocalDate deliveryDate, List<OrderItem> orderItems,
                                                   Integer taskType) {
        Map<String, List<OrderItem>> orderItemMap = CollectionUtils.isEmpty(orderItems) ? new HashMap<>() :
                orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderNo));
        // 获取已生成的所有任务
        List<String> orderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByNotCancel(
                warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate), taskType);

        Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap = new HashMap<>();
        for (String orderNo : orderNoList) {

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            // 检查幂等
            List<Long> stockTaskIdList = stockTaskOrderSkuDOList.stream()
                    .map(StockTaskOrderSkuDO::getStockTaskId)
                    .distinct()
                    .collect(Collectors.toList());
            Long maxStockTaskId = stockTaskIdList.stream()
                    .sorted(Comparator.reverseOrder())
                    .findFirst()
                    .orElse(null);
            if (!CollectionUtils.isEmpty(stockTaskIdList)) {
                List<StockTaskStorageDO> stockTaskStorageDOList = stockTaskStorageMapper.queryMoreInBySourceId("" + maxStockTaskId);
                if (!CollectionUtils.isEmpty(stockTaskStorageDOList)) {
                    log.warn("多出反库任务幂等过滤订单 {} {} {}", orderNo, stockTaskIdList, maxStockTaskId);
                    continue;
                }
            }

            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<OrderItem> orderItemList = orderItemMap.get(orderNo);
            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    OrderItem::getSku, OrderItem::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                Integer diffQuantity = 0;
                // 全部取消
                if (beforeQuantity.compareTo(nowQuantity) >= 0) {
                    diffQuantity = beforeQuantity - nowQuantity;
                }

                if (diffQuantity != 0) {
                    List<OrderReturnInboundDO> list = stockTaskReturnMap.get(maxStockTaskId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new ArrayList<>();
                    }
                    list.add(
                            OrderReturnInboundDO.builder()
                                    .warehouseNo(warehouseNo)
                                    .storeNo(storeNo)
                                    .deliveryDate(deliveryDate)
                                    .orderNo(orderNo)
                                    .sku(sku)
                                    .quantity(diffQuantity)
                                    .build()
                    );

                    stockTaskReturnMap.put(maxStockTaskId, list);
                }
            }
        }

        // 发送多出返库入库
        if (!CollectionUtils.isEmpty(stockTaskReturnMap)) {
            sendCreateReturnInbound(warehouseNo, storeNo, deliveryDate,
                    stockTaskReturnMap);
        }
    }

    private void sendCreateReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                         Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap) {
        if (CollectionUtils.isEmpty(stockTaskReturnMap)) {
            return;
        }

        for (Map.Entry<Long, List<OrderReturnInboundDO>> longListEntry : stockTaskReturnMap.entrySet()) {
            Long maxStockTaskId = longListEntry.getKey();
            List<OrderReturnInboundDO> orderReturnInboundDOList = longListEntry.getValue();
            if (CollectionUtils.isEmpty(orderReturnInboundDOList)) {
                continue;
            }

            // 库存仓
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo);
            String warehouseName = warehouseStorageCenter == null ? null : warehouseStorageCenter.getWarehouseName();

            // 城配仓
            WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(storeNo);
            String storeName = warehouseLogisticsCenter == null ? null : warehouseLogisticsCenter.getStoreName();

            // 备注
            String remarkJson = JSONObject.toJSONString(orderReturnInboundDOList);
            InputStream inputStream = new ByteArrayInputStream(remarkJson.getBytes(StandardCharsets.UTF_8));
            String fileName = "多出返库任务信息" + warehouseName + "_" + storeName + "_" +
                    DateUtil.formatYmdDate(deliveryDate) + "_"
                    + DateUtil.formatDateBasic(LocalDateTime.now()) + ".txt";
            OssUploadResult ossUploadResult = OssUploadUtil.upload(fileName, inputStream, OSSExpiredLabelEnum.NO_EXPIRATION);
            String remarkUrl = ossUploadResult.getUrl();

            List<String> skuCodeList = orderReturnInboundDOList.stream()
                    .map(OrderReturnInboundDO::getSku)
                    .distinct()
                    .collect(Collectors.toList());
            List<Inventory> inventoryList = inventoryService.selectBySkuList(skuCodeList);
            Map<String, Inventory> inventoryMap = inventoryList.stream().collect(Collectors.toMap(Inventory::getSku, Function.identity(), (x1, x2) -> x1));

            List<Long> pdIdList = inventoryList.stream()
                    .map(Inventory::getPdId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Products> productsList = productsService.selectListByIdList(pdIdList);
            Map<Long, Products> productsMap = productsList.stream().collect(Collectors.toMap(Products::getPdId, Function.identity(), (x1, x2) -> x1));

            List<Integer> categoryIdList = productsList.stream()
                    .map(Products::getCategoryId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Category> categoryList = categoryService.selectTypeByIds(categoryIdList);
            Map<Integer, Category> categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getId, Function.identity(), (x1, x2) -> x1));


            // 创建差异入库单
            StockStorageCreateReqDTO build = StockStorageCreateReqDTO.builder()
                    .adminId(0L)
                    .operatorName("系统默认")
                    .type(StockTaskType.OUT_MORE_IN.getId())
                    .inWarehouseNo(warehouseNo)
                    .inWarehouseName(warehouseName)
                    .outWarehouseNo(storeNo)
                    .outWarehouseName(storeName)
                    .expectTime(DateUtil.toLocalDateTime(DateUtil.toDate(deliveryDate)))
                    .sourceNo("" + maxStockTaskId)
                    .tenantId(WmsConstant.XIANMU_TENANT_ID)
                    .remark(remarkUrl)
                    .build();

            List<StockStorageItemCreateDTO> storageItemCreateDTOList = new ArrayList<>();
            for (OrderReturnInboundDO orderReturnInboundDO : orderReturnInboundDOList) {
                Inventory sku = inventoryMap.get(orderReturnInboundDO.getSku());
                if (sku == null) {
                    log.error("返库查询SKU不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }
                Products spu = productsMap.get(sku.getPdId());
                if (spu == null) {
                    log.error("返库查询spu不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }

                Category category = categoryMap.get(spu.getCategoryId());
                if (category == null) {
                    log.error("返库查询类目不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }

                storageItemCreateDTOList.add(
                        StockStorageItemCreateDTO.builder()
                                .category(category.getCategory())
                                .tenantId(WmsConstant.XIANMU_TENANT_ID)
                                .sku(orderReturnInboundDO.getSku())
                                // 1 全部,2乳制品,3非乳制品,4水果
                                .categoryType(category.getType())
                                .packaging(sku.getUnit())
                                .pdName(spu.getPdName())
                                // sku 归属类型   0 自营 1 代仓
                                .skuType(sku.getType())
                                // 应入数
                                .quantity(orderReturnInboundDO.getQuantity())
                                .specification(sku.getWeight())
                                .temperature(spu.getStorageLocation())

                                .build()
                );
            }

            Map<String, StockStorageItemCreateDTO> map = storageItemCreateDTOList.stream().collect(Collectors.toMap(
                    s -> s.getSku(), Function.identity(), (a, b) -> {
                        a.setQuantity(a.getQuantity() + b.getQuantity());
                        return a;
                    }));

            build.setStorageItemCreateDTOList(new ArrayList<>(map.values()));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        mqProducer.send("wms-list", "tag_wms_stock_storage_task_create", build);
                    } catch (Throwable throwable){
                        log.error("mqProducer send tag_wms_stock_storage_task_create throwable", throwable);
                    }
                }
            });
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleCancelSampleOrderReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                                     List<SampleSku> sampleSkus, Integer taskType) {
        Map<String, List<SampleSku>> orderItemMap = CollectionUtils.isEmpty(sampleSkus) ? new HashMap<>() :
                sampleSkus.stream().collect(Collectors.groupingBy(s -> "" + s.getSampleId()));
        // 获取已生成的所有任务
        List<String> orderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByNotCancel(
                warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate), taskType);

        Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap = new HashMap<>();
        for (String orderNo : orderNoList) {

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            // 检查幂等
            List<Long> stockTaskIdList = stockTaskOrderSkuDOList.stream()
                    .map(StockTaskOrderSkuDO::getStockTaskId)
                    .distinct()
                    .collect(Collectors.toList());
            Long maxStockTaskId = stockTaskIdList.stream()
                    .sorted(Comparator.reverseOrder())
                    .findFirst()
                    .orElse(null);
            if (!CollectionUtils.isEmpty(stockTaskIdList)) {
                List<StockTaskStorageDO> stockTaskStorageDOList = stockTaskStorageMapper.queryMoreInBySourceId("" + maxStockTaskId);
                if (!CollectionUtils.isEmpty(stockTaskStorageDOList)) {
                    log.warn("多出反库任务幂等过滤订单 {} {} {}", orderNo, stockTaskIdList, maxStockTaskId);
                    continue;
                }
            }


            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<SampleSku> orderItemList = orderItemMap.get(orderNo);
            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    SampleSku::getSku, SampleSku::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                Integer diffQuantity = 0;
                // 全部取消
                if (nowQuantity == null) {
                    diffQuantity = beforeQuantity;
                }
                // 部分取消
                else if (beforeQuantity.compareTo(nowQuantity) > 0) {
                    diffQuantity = beforeQuantity - nowQuantity;
                } else if (beforeQuantity.compareTo(nowQuantity) < 0) {
                    log.error("退货数量异常，订单应出数量超过上一次的应出数量, {} {} {} {} {} \n换行告警",
                            orderNo, sku, deliveryDate, beforeQuantity, nowQuantity);
                    continue;
                }

                if (diffQuantity != 0) {
                    List<OrderReturnInboundDO> list = stockTaskReturnMap.get(maxStockTaskId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new ArrayList<>();
                    }
                    list.add(
                            OrderReturnInboundDO.builder()
                                    .warehouseNo(warehouseNo)
                                    .storeNo(storeNo)
                                    .deliveryDate(deliveryDate)
                                    .orderNo(orderNo)
                                    .sku(sku)
                                    .quantity(diffQuantity)
                                    .build()
                    );

                    stockTaskReturnMap.put(maxStockTaskId, list);
                }
            }
        }

        // 发送多出返库入库
        if (!CollectionUtils.isEmpty(stockTaskReturnMap)) {
            sendCreateReturnInbound(warehouseNo, storeNo, deliveryDate,
                    stockTaskReturnMap);
        }
    }

    @Override
    public List<OrderItem> getNotCreateSaleOrderTaskItemForCross(Integer warehouseNo, Integer storeNo,
                                                                 LocalDate deliveryDate,
                                                                 List<OrderItem> orderItems, Integer taskType) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        List<String> orderNoList = orderItems.stream().map(OrderItem::getOrderNo).distinct().collect(Collectors.toList());

        List<String> existOrderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByOutOrderNoList(
                warehouseNo, storeNo, DateUtil.formatYmdDate(deliveryDate),
                orderNoList, taskType);

        List<OrderItem> notCreateTaskOrderItems = orderItems.stream()
                .filter(orderItem -> !existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.toList());

        return notCreateTaskOrderItems;
    }

    @Override
    public void waveStockTaskOccupyInit(List<Integer> stockTaskIds, List<Integer> storeNoList, Integer type) {
        if (null == type) {
            return;
        }
        try {
            // 根据生成出库任务id进行波次占用初始化
            if (!CollectionUtils.isEmpty(stockTaskIds)) {
                List<StockTask> stockTaskList = stockTaskMapper.selectListByPrimaryKey(stockTaskIds);
                if (!CollectionUtils.isEmpty(stockTaskList)) {
                    for (StockTask stockTask : stockTaskList) {
                        // 根据库存仓、城配仓、类型、截单时间查询生成出库任务
                        List<StockTask> expStockTaskList = stockTaskMapper.selectListByOutStoreNo(stockTask.getAreaNo(), stockTask.getOutStoreNo(), stockTask.getExpectTime().toLocalDate(), stockTask.getType());
                        // 过滤出波次任务
                        List<StockTask> waveStockTask = expStockTaskList.stream().filter(item -> OptionFlagUtil.hasValue(item.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(waveStockTask)) {
                            log.info("截单任务初始化波次占用开始：{}", JSON.toJSONString(waveStockTask));
                            // 勾起波次占用初始化
                            waveStockTask.forEach(item -> handleStockTaskWaveOccupyInit(item.getId()));
                        }
                        log.info("截单任务初始化波次占用完成，截单任务id：{}", stockTask.getId());
                    }
                }
            }
            // 兜底
            if (!CollectionUtils.isEmpty(storeNoList)) {
                log.info("无截单任务兜底处理，城配仓：{}", storeNoList);
                List<Integer> waveNotOccupyTaskIdList = stockTaskMapper.selectWaveNotOccupyTaskIds(storeNoList, LocalDate.now().plusDays(1), type);
                log.info("无截单任务兜底处理查询未初始化数据：{}", waveNotOccupyTaskIdList);
                if (!CollectionUtils.isEmpty(waveNotOccupyTaskIdList)) {
                    // 勾起波次占用初始化
                    waveNotOccupyTaskIdList.forEach(this::handleStockTaskWaveOccupyInit);
                }
            }
        } catch (Exception e) {
            log.error("截单任务初始化波次占用异常", e);
        }
    }

    @Override
    public void checkOutMoreInQuantity(Integer stockTaskId) {
        if (null == stockTaskId) {
            return;
        }
        // 多出入库任务
        List<StockTaskStorage> stockTaskStorageList = stockTaskStorageQueryRepository.selectListBySourceIdAndType(StockTaskStorage.builder().sourceId(String.valueOf(stockTaskId)).type(StockTaskType.OUT_MORE_IN.getId()).build());
        if (CollectionUtils.isEmpty(stockTaskStorageList)) {
            return;
        }
        // 多出入库明细
        List<StockStorageItem> stockStorageItemList = stockStorageItemRepository.queryStockStorageItems(stockTaskStorageList.stream().map(StockTaskStorage::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(stockStorageItemList)) {
            return;
        }
        // 实际入库数量
        Map<String, Integer> inSkuActualQuantityMap = stockStorageItemList.stream().collect(Collectors.toMap(StockStorageItem::getSku, StockStorageItem::getActualQuantity, Integer::sum));
        // 出库任务条目
        List<StockTaskItemVO> stockTaskItemVOList = stockTaskItemMapper.selectById(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskItemVOList)) {
            return;
        }
        // 实际出库数量
        Map<String, Integer> outSkuActualQuantityMap = stockTaskItemVOList.stream().collect(Collectors.toMap(StockTaskItemVO::getSku, StockTaskItemVO::getActualQuantity, Integer::sum));
        // 遍历校验
        inSkuActualQuantityMap.forEach((sku, inActualQuantity) -> {
            Integer outActualQuantity = outSkuActualQuantityMap.get(sku);
            if (null != outActualQuantity) {
                int diffQuantity = inActualQuantity - outActualQuantity;
                // 实际入库比出库多告警
                if (diffQuantity > 0) {
                    log.error("\n出库任务【{}】sku【{}】完结后多出入库数量【{}】比出库数量【{}】多，库存异常，\n", stockTaskId, sku, inActualQuantity, outActualQuantity);
                }
            }
        });

    }

}
