package net.summerfarm.wms.api.h5.mission.req;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionCategoryQuery extends BasePageInput implements Serializable {

    /**
     * 任务类型
     * 10-上架任务 20-移库任务 30-入库任务 40-拣货任务 50-盘点任务 60-越库投线任务,70-货检任务,999-未知
     */
    Integer missionType;
}
