package net.summerfarm.wms.application.acl.provider.impl.processingTask;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.processingtask.dto.req.*;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigProductSkuSpecDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigQueryResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigUpsetResDTO;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingConfigTypeEnum;
import net.summerfarm.wms.starter.WmsApplication;
import net.summerfarm.wms.web.controller.processingtask.ProcessingConfigController;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class ProcessingConfigControllerTest {

    @BeforeAll
    public static void beforeAll() {
        UserBase userBase = new UserBase();
        userBase.setTenantId(1L);
        userBase.setBizUserId(4631);
        userBase.setNickname("直列");
        AdminUtil.USER_BASE.set(userBase);
    }

    @Resource
    private ProcessingConfigController processingConfigController;

    @Test
    public void testCreate(){
        ProcessingConfigUpsetReqDTO upsetReqDTO = new ProcessingConfigUpsetReqDTO();
//        upsetReqDTO.setId(1238L);
        upsetReqDTO.setSkipRatioAndWeightCheck(true);
        upsetReqDTO.setWarehouseNo(1);
        upsetReqDTO.setType(ProcessingConfigTypeEnum.GOODS_PROCESSING.getValue());
//        upsetReqDTO.setMaterialSkuCode("289373634631");

//        upsetReqDTO.setProductSkuCode("18101438781");
        upsetReqDTO.setProductSkuCode("5400007836");
        upsetReqDTO.setProductSkuWeight(BigDecimal.valueOf(1));
        upsetReqDTO.setProductSkuUnit("只");
        upsetReqDTO.setProductSkuSpecList(Arrays.asList(
                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(1), "只")
//                BigDecimal.valueOf(1.50),
//                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(3), "包"),
//                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(10), "箱")
        ));
        upsetReqDTO.setProductSkuRatioNum(2);
        List<ProcessingMaterialConfigUpsetReqDTO> processingMaterialConfigList = new ArrayList<>();

        ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO1 = new ProcessingMaterialConfigUpsetReqDTO();
        materialConfigUpsetReqDTO1.setMaterialSkuCode("5400007265");
        materialConfigUpsetReqDTO1.setMaterialSkuWeight(BigDecimal.valueOf(0.5));
        materialConfigUpsetReqDTO1.setMaterialSkuUnit("只");
        materialConfigUpsetReqDTO1.setMaterialSkuRatioNum(2);

        processingMaterialConfigList.add(materialConfigUpsetReqDTO1);

        ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO2 = new ProcessingMaterialConfigUpsetReqDTO();
        materialConfigUpsetReqDTO2.setMaterialSkuCode("1052142106211");
        materialConfigUpsetReqDTO2.setMaterialSkuWeight(BigDecimal.valueOf(0.5));
        materialConfigUpsetReqDTO2.setMaterialSkuUnit("只");
        materialConfigUpsetReqDTO2.setMaterialSkuRatioNum(1);

        processingMaterialConfigList.add(materialConfigUpsetReqDTO2);

        upsetReqDTO.setProcessingMaterialConfigList(processingMaterialConfigList);


        String ss = "{\"id\":\"1576\",\"warehouseNo\":1,\"type\":2,\"productSkuCode\":\"976415014183\",\"productSkuWeight\":\"6\",\"productSkuUnit\":\"KG\",\"productSkuSpecList\":[{\"productSkuSpecWeight\":\"6\",\"productSkuSpecUnit\":\"KG\"}],\"processingMaterialConfigList\":[{\"pdId\":43483,\"materialSkuName\":\"张测试1746782248218\",\"materialSkuCode\":\"976247362806\",\"materialSkuWeight\":4,\"materialSkuUnit\":\"KG\",\"materialSkuRatioNum\":3}],\"skipRatioAndWeightCheck\":false}";
        upsetReqDTO = JSON.parseObject(ss, ProcessingConfigUpsetReqDTO.class);
        CommonResult<ProcessingConfigUpsetResDTO> commonResult = processingConfigController.upsert(upsetReqDTO);
        log.error(JSONObject.toJSONString(commonResult));
        Assert.assertNotNull(commonResult.getData());
    }

    @Test
    public void testUpdate(){
        ProcessingConfigUpsetReqDTO upsetReqDTO = new ProcessingConfigUpsetReqDTO();
        upsetReqDTO.setId(1219L);
        upsetReqDTO.setWarehouseNo(1);
        upsetReqDTO.setType(ProcessingConfigTypeEnum.ASSEMBLY_PROCESSING.getValue());
//        upsetReqDTO.setMaterialSkuCode("970480642171");
//        upsetReqDTO.setMaterialSkuUnit("kg");
//        upsetReqDTO.setMaterialSkuWeight(BigDecimal.valueOf(0.5));
        upsetReqDTO.setProductSkuCode("64836815625");
        upsetReqDTO.setProductSkuUnit("kg");
        upsetReqDTO.setProductSkuWeight(BigDecimal.valueOf(1));
        upsetReqDTO.setProductSkuSpecList(Arrays.asList(
                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(1.00), "kg"),
//                BigDecimal.valueOf(1.50),
                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(2.00), "kg"),
                new ProcessingConfigProductSkuSpecDTO(BigDecimal.valueOf(3.00), "kg")
        ));

        List<ProcessingMaterialConfigUpsetReqDTO> processingMaterialConfigList = new ArrayList<>();

        ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO1 = new ProcessingMaterialConfigUpsetReqDTO();
        materialConfigUpsetReqDTO1.setMaterialSkuCode("970480642171");
        materialConfigUpsetReqDTO1.setMaterialSkuWeight(BigDecimal.valueOf(0.5));
        materialConfigUpsetReqDTO1.setMaterialSkuUnit("只");
        materialConfigUpsetReqDTO1.setMaterialSkuRatioNum(2);

        processingMaterialConfigList.add(materialConfigUpsetReqDTO1);

        upsetReqDTO.setProcessingMaterialConfigList(processingMaterialConfigList);


        CommonResult<ProcessingConfigUpsetResDTO> commonResult = processingConfigController.upsert(upsetReqDTO);
        Assert.assertNotNull(commonResult.getData());
    }

    @Test
    public void testPage() {
        ProcessingConfigQueryReqDTO reqDTO = new ProcessingConfigQueryReqDTO();
        reqDTO.setPageIndex(1);
        reqDTO.setPageSize(50);
//        reqDTO.setMaterialSkuCode("970480642171");
        reqDTO.setProductSkuSaasId(113135L);

        CommonResult<PageInfo<ProcessingConfigQueryResDTO>> page = processingConfigController.page(reqDTO);
        System.out.println(JSON.toJSONString(page));
    }

    @Test
    public void testDetail() {
        CommonResult<ProcessingConfigQueryResDTO> detail = processingConfigController.detail(new ProcessingConfigDetailReqDTO(1575L));
        System.out.println(JSON.toJSONString(detail));
    }

    @Test
    public void testInvalid() {
        CommonResult<Long> invalid = processingConfigController.invalid(new ProcessingConfigInvalidReqDTO().setId(1219L));
        System.out.println(JSON.toJSONString(invalid));
    }
}
