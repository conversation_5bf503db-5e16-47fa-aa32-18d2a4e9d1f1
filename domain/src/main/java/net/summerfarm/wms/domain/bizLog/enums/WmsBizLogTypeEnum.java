package net.summerfarm.wms.domain.bizLog.enums;

import lombok.Getter;

@Getter
public enum WmsBizLogTypeEnum {

    QMS_INSPECT_RULE("QMS_INSPECT_RULE", "QMS验货规则"),
    INVENTORY_LOCK("INVENTORY_LOCK", "库存锁定"),
    INVENTORY_UNLOCK("INVENTORY_UNLOCK", "库存锁定释放"),

    ;
    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    WmsBizLogTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean equalsCode(String input) {
        return this.code.equals(input);
    }

}