package net.summerfarm.store;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.storealert.FreezeInvImbalanceService;
import net.summerfarm.wms.api.h5.storealert.InventoryImbalanceAlarmService;
import net.summerfarm.wms.scheduler.FreezeInvImbalanceAlarmJob;
import net.summerfarm.wms.scheduler.InventoryImbalanceAlarmJob;
import net.summerfarm.wms.starter.WmsApplication;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @author: dongcheng
 * @date: 2023/11/15
 * 仓库库存对账
 */
@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class StoreAlarmTest {

    @Resource
    private InventoryImbalanceAlarmService inventoryImbalanceAlarmService;
    @Resource
    private InventoryImbalanceAlarmJob inventoryImbalanceAlarmJob;
    @Resource
    private FreezeInvImbalanceAlarmJob freezeInvImbalanceAlarmJob;

    /**
     * 仓库库存和库位库存
     */
    @Test
    public void queryStoreInvAndCabinetInvImbalanceAlarmTest() {
        inventoryImbalanceAlarmService.queryStoreInvAndCabinetInvImbalanceAlarm(2);
    }

    /**
     * 批次库存和仓库库存
     */
    @Test
    public void queryStoreInvAndBatchInvImbalanceAlarmTest() {
        inventoryImbalanceAlarmService.queryStoreInvAndBatchInvImbalanceAlarm(2);
    }

    @Test
    public void queryStoreInvAndOnlineImbalanceAlarmTest() {
        inventoryImbalanceAlarmService.queryStoreInvAndOnlineImbalanceAlarm(2);
    }

    @Test
    public void queryAllocationRoadImbalanceAlarmTest() {
        inventoryImbalanceAlarmService.queryAllocationRoadImbalanceAlarm(2);
    }

    /**
     * 测试对账不平数据
     */
    @Test
    public void inventoryImbalanceAlarmJobTest() throws Exception {
        inventoryImbalanceAlarmJob.processResult(new XmJobInput());
    }

    @Test
    public void freezeInvImbalanceAlarmJobTest() throws Exception {
        freezeInvImbalanceAlarmJob.processResult(new XmJobInput());
    }
}
