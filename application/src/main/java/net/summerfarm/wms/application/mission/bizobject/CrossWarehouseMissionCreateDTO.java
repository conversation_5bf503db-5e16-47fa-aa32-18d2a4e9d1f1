package net.summerfarm.wms.application.mission.bizobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 越库投线任务创建事件
 *
 * @author: dongcheng
 * @date: 2023/10/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrossWarehouseMissionCreateDTO implements Serializable {

    private static final long serialVersionUID = -428369387339665445L;

    /**
     * 任务号
     */
    String pMissionNo;

    /**
     * 任务类型
     */
    Integer pMissionType;

    /**
     * 业务类型
     */
    Integer sourceType;

    /**
     * 业务订单号
     */
    String sourceOrderNo;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 操作人id
     */
    String operatorId;

    /**
     * 操作人名称
     */
    String operatorName;

    /**
     * 容器编号
     */
    String containerNo;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * ofc的批次号
     */
    String psoNo;

    /**
     * 操作来源
     */
    Boolean isPda;

    /**
     * 入库任务状态
     */
    Integer state;

    /**
     * 明细
     */
    List<CrossWarehouseMissionDetailDTO> details;

}
