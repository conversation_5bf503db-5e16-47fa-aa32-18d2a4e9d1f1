package net.summerfarm.wms.domain.processingtask.delegate.converter;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProcessingTaskMaterialReceiveRecordConverter {
    ProcessingTaskMaterialReceiveRecordConverter INSTANCE = Mappers.getMapper(ProcessingTaskMaterialReceiveRecordConverter.class);

    ProcessingTaskMaterialReceiveRecord convert(ProcessingTaskMaterialReceiveRecord processingTaskMaterialReceiveRecord);
}
