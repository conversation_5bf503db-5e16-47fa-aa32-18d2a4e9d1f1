package net.summerfarm.wms.manage.web.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.wms.manage.model.domain.AreaSku;
import net.summerfarm.wms.manage.model.domain.bo.PriceInfoBO;
import net.summerfarm.wms.manage.web.service.PriceService;
import org.springframework.stereotype.Service;

/**
 * @author: dongcheng
 * @date: 2023/11/30
 */
@Service
@Slf4j
public class PriceServiceImpl implements PriceService {


    @Override
    public PriceInfoBO getNormalPrice(AreaSku areaSku) {
        if (areaSku == null) {
            throw new DefaultServiceException("区域SKU参数错误");
        }
        return getNormalPrice(areaSku, false);
    }

    @Override
    public PriceInfoBO getNormalPrice(AreaSku areaSku, boolean isActivitySku) {
        if (areaSku == null) {
            throw new DefaultServiceException("区域SKU参数错误");
        }
        // 判断sku是否在活动中
        String sku = areaSku.getSku();
        PriceInfoBO priceInfoBO = new PriceInfoBO();
        priceInfoBO.setSku(sku);
        priceInfoBO.setPrice(areaSku.getPrice());
        return priceInfoBO;
    }

}
