package net.summerfarm.wms.application.skushare.service;


import net.summerfarm.wms.application.skushare.dto.AddSkuShareCommand;
import net.summerfarm.wms.application.skushare.dto.CancelSkuShareCommand;
import net.summerfarm.wms.application.skushare.dto.ModifySkuShareCommand;
import net.summerfarm.wms.application.skushare.dto.UpdateSkuShareTotalQuantityCommand;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * sku规格共享功能接口
 *
 * <AUTHOR>
 */
public interface SkuShareCommandService {
    /**
     * 新增sku规格共享，同一个订单同一个sku重复新增时会忽略这个sku
     *
     * @param addSkuShareCommand
     * @return
     */
    void addSkuShare(@Valid AddSkuShareCommand addSkuShareCommand);

    /**
     * 取消sku规格共享
     *
     * @param cancelSkuShareCommand
     */
    void cancelSkuShare(@Valid CancelSkuShareCommand cancelSkuShareCommand);

    /**
     * 更新sku规格共享总数量
     *
     * @param updateSkuShareTotalQuantityCommand
     */
    void updateSkuShareTotalQuantity(@Valid UpdateSkuShareTotalQuantityCommand updateSkuShareTotalQuantityCommand);

    /**
     * 清零仓库下所有的sku规格共享
     *
     * @param warehouseNo
     */
    void clearSkuShare(@NotNull Integer warehouseNo);

    /**
     * 校验sku规格共享比例的修改
     *
     * @param modifySkuShareCommand
     * @return 修改后超卖的sku和超卖数量（包含转出sku）
     */
    Map<String, Integer> checkModifySkuShare(@Valid ModifySkuShareCommand modifySkuShareCommand);

    /**
     * 修改sku规格共享比例
     *
     * @param modifySkuShareCommand
     */
    void modifySkuShare(@Valid ModifySkuShareCommand modifySkuShareCommand);

}
