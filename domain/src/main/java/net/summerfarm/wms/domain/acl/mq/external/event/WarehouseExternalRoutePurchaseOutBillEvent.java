package net.summerfarm.wms.domain.acl.mq.external.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarehouseExternalRoutePurchaseOutBillEvent implements Serializable {

    private static final long serialVersionUID = 8665354672341257967L;

    /**
     * 幂等单
     */
    private String idempotentNo;

    /**
     * 外部路由配置id
     */
    private Long warehouseExternalRouteId;

    /**
     * 出库任务id
     */
    private Long stockTaskId;

}
