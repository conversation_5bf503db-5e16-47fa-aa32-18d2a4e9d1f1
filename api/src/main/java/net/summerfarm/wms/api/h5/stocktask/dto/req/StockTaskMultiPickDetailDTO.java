package net.summerfarm.wms.api.h5.stocktask.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/8/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskMultiPickDetailDTO implements Serializable {

    private static final long serialVersionUID = -1120288632166683491L;

    /**
     * 任务id
     */
    private Integer stockTaskId;

    /**
     * sku内容
     */
    private List<String> skuList;

}
