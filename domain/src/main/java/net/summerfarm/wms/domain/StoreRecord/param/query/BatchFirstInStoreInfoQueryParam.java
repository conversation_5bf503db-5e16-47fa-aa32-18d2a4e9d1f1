package net.summerfarm.wms.domain.StoreRecord.param.query;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class BatchFirstInStoreInfoQueryParam {
    /**
     * 仓库号
     */
    private Integer warehouseNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 批次号列表
     */
    private List<String> purchaseBatchList;
    /**
     * 生产日期列表
     */
    private List<LocalDate> productionDateList;
    /**
     * 保质期列表
     */
    private List<LocalDate> qualityDateList;
}
