package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.enums.AfterSaleOrderStatus;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
public class AfterSaleOrder implements Serializable {
    private static final long serialVersionUID = -1365559510536033217L;

    /**
     * 未到货售后
     */
    public final static int DELIVERY_NOT_RECEIVED = 0;

    /**
     * 已到货售后
     */
    public final static int DELIVERY_RECEIVED = 1;

    @ApiModelProperty(value = "售后订单id")
    private Integer id;

    @ApiModelProperty(value = "售后订单编号")
    private String afterSaleOrderNo;

    @ApiModelProperty(value = "商户编号mId")
    private Long mId;

    /**
     * 售后单类型：0 普通售后单，1 拦截售后单'
     */
    private Integer afterSaleOrderStatus;

    @ApiModelProperty(value = "子账号id")
    private Long accountId;

    @ApiModelProperty(value = "订单编号")
    @NotNull(groups = {Add.class})
    private String orderNo;

    @ApiModelProperty(value = "sku编号")
    @NotNull(groups = {Add.class})
    private String sku;
    /**
     * 商品类型：0、普通商品 1、赠品 2、换购商品
     */
    private Integer productType;

    private String afterSaleUnit;

    private Boolean isFull;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    @ApiModelProperty(value = "售后订单类型：0普通售后，1极速退款", hidden = true)
    private Integer type;

    @ApiModelProperty(value = "等级", hidden = true)
    private Integer grade;

    @ApiModelProperty(value = "售后类型:0未到货售后，1已到货售后", hidden = true)
    private Integer deliveryed;

    private Integer suitId;

    private Integer times;

    private Integer view;

    /**
     * 关单人
     */
    private String closer;

    /**
     * 关单时间
     */
    private LocalDateTime closeTime;

    /**
     * {@link AfterSaleOrderStatus}
     */
    private Integer status;

    /**
     * 配送计划ID
     */
    private Integer deliveryId;

    /**
     * 是否需要回收费 0 不需要 1 需要
     */
    private Integer recoveryType;

    /**
     * 售后类型
     */
    private Integer afterSaleRemarkType;
    /**
     * 售后类型备注
     */
    private String afterSaleRemark;

    /**
     * 补发是否带货回来：0没带，1带了
     */
    private Integer carryingGoods;
}
