package net.summerfarm.wms.manage.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
public class ProductsPropertyValue implements Serializable {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * pd_id
     */
    private Integer pdId;

    /**
     * sku
     */
    private String sku;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public String findPdId() {
        if (Objects.isNull(pdId)) {
            return null;
        }
        return pdId.toString();
    }
}
