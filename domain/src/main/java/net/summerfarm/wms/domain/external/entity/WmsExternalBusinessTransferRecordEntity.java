package net.summerfarm.wms.domain.external.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 仓库外部路由下发状态
 * @date 2024/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmsExternalBusinessTransferRecordEntity implements Serializable {

    private static final long serialVersionUID = 3482937226606591870L;

    /**
     * id
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 外部appkey
     */
    private String externalAppKey;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 单据类型（销售等）
     */
    private Integer orderType;

    /**
     * 能力点
     */
    private String abilityCode;

    /**
     * 单据下发外部状态（1:初始，2:已下发，3:已回告）
     */
    private Integer externalStatus;
}
