package net.summerfarm.wms.infrastructure.repository.instore.converter;

import net.summerfarm.wms.domain.instore.domainobject.StockTaskAbnormalRecord;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> ct
 * create at:  2022/11/18  16:46
 */
@Mapper
public interface StockTaskAbnormalRecordConvert {

    StockTaskAbnormalRecordConvert INSTANCE= Mappers.getMapper(StockTaskAbnormalRecordConvert.class);

    StockTaskAbnormalRecord convert(StockTaskAbnormalRecordDO recordDO);

    StockTaskAbnormalRecordDO convert(StockTaskAbnormalRecord record);
}
