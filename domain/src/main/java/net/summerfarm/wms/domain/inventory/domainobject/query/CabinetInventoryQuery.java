package net.summerfarm.wms.domain.inventory.domainobject.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.domain.base.BaseQuery;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetInventoryQuery extends BaseQuery implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 关联库位id
     */
    private Long cabinetId;

    /**
     * 库存数量
     */
    private Integer quantity;

    /**
     * 冻结数量
     */
    private Integer lockQuantity;

    /**
     * 可用数量
     */
    private Integer availableQuantity;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 所属库区编码
     */
    private String zoneCode;

    /**
     * 库区类型（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    private Integer zoneType;

    /**
     * 库位类型（1：高位货架，2：轻型货架，3：地堆）
     */
    private Integer cabinetType;


    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    private Integer cabinetPurpose;

    /**
     * 库位属性列表（1：拣货区，2：存储区，3：暂存区）
     */
    private List<Integer> cabinetPurposeList;

    /**
     * sku编码列表
     */
    private List<String> skuList;

    /**
     * 仓库编码列表
     */
    private List<Integer> warehouseNoList;

    /**
     * 库位编码列表
     */
    private List<String> cabinetCodeList;

    /**
     * 生产日期列表
     */
    private List<Date> produceDateList;

    /**
     * 生产日期大于等于
     */
    private Date produceDateGe;

    /**
     * 生产日期小于等于
     */
    private Date produceDateLe;


    /**
     * 保质期
     */
    private List<Date> qualityDateList;

    /**
     * 保质期大于等于
     */
    private Date qualityDateGe;

    /**
     * 保质期小于
     */
    private Date qualityDateLt;

    /**
     * 数量大于0
     */
    private Boolean quantityGtZero;


    /**
     * 是否售罄
     */
    private Boolean saleOut;

    /**
     * 批次号列表
     */
    private List<String> batchNoList;

    /**
     * 冻结数量大于0
     */
    private Boolean lockQuantityGtZero;

    /**
     * 生产日期范围列表
     */
    private List<SkuProductRangeQuery> skuProductRangeQueryList;

    /**
     * ids
     */
    private List<Long> idList;

    /**
     * 商品名称
     */
    private String pdNameLike;

    /**
     * 通过pdId查询出来的skus
     */
    private List<String> skusByPdId;

    /**
     * 过滤库位编码
     */
    private List<String> cabinetCodeNotInList;

    /**
     * 库位编码左匹配
     */
    private String cabinetCodeLike;

    /**
     * sku+库位查询
     */
    private List<CabinetInventorySkuQuery> cabinetSkuList;
}
