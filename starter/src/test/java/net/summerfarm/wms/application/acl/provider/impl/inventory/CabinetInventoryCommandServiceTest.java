package net.summerfarm.wms.application.acl.provider.impl.inventory;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.*;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryOccupyResp;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.StoreRecord.enums.OtherStockChangeType;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.starter.WmsApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class CabinetInventoryCommandServiceTest {

    @Resource
    private CabinetInventoryCommandService cabinetInventoryCommandService;

    @Test
    public void testoccupyCabinetInventory(){
        CabinetInventoryOccupyReq cabinetBatchInventoryAddReq = new CabinetInventoryOccupyReq();
        cabinetBatchInventoryAddReq.setTenantId(1L);
        cabinetBatchInventoryAddReq.setWarehouseNo(2);
        cabinetBatchInventoryAddReq.setOrderNo("ORDER002");
        cabinetBatchInventoryAddReq.setContainerCode("CONTAINER001");
        cabinetBatchInventoryAddReq.setInternalNo("INTERNALNO001");
        cabinetBatchInventoryAddReq.setOrderTypeName(OtherStockChangeType.PURCHASE_IN.getTypeName());
        cabinetBatchInventoryAddReq.setOperatorType(CabinetInventoryOperationType.STOCK_IN.getId());

        List<CabinetInventoryOccupyDetailReq> addDetailReqList = new ArrayList<>();

        CabinetInventoryOccupyDetailReq addDetailReq = new CabinetInventoryOccupyDetailReq();
//        addDetailReq.setCabinetCode("Z99-01-01-01");
        addDetailReq.setSkuCode("5417115233");
//        addDetailReq.setSkuBatchCode("20230315459935014");
//        addDetailReq.setProduceDate(DateUtil.parseStartDate("2023-03-15", DateUtil.YYYY_MM_DD));
//        addDetailReq.setQualityDate(DateUtil.parseStartDate("2023-07-14", DateUtil.YYYY_MM_DD));
        addDetailReq.setOccupyQuantity(1);

        addDetailReqList.add(addDetailReq);

        cabinetBatchInventoryAddReq.setOccupyDetailReqList(addDetailReqList);

        List<CabinetInventoryOccupyResp> respList = cabinetInventoryCommandService.occupyCabinetInventory(cabinetBatchInventoryAddReq);
        System.out.println("result: " + JSONObject.toJSONString(respList));
    }

    @Test
    public void testoccupyReduceCabinetInventory(){
        CabinetInventoryOccupyReduceReq req = new CabinetInventoryOccupyReduceReq();
        req.setTenantId(1L);
        req.setWarehouseNo(2);
        req.setOrderNo("ORDER002");
        req.setContainerCode("CONTAINER001");
        req.setInternalNo("INTERNALNO001");
        req.setOrderTypeName(OtherStockChangeType.PURCHASE_IN.getTypeName());
        req.setOperatorType(CabinetInventoryOperationType.STOCK_IN.getId());

        List<CabinetInventoryOccupyReduceDetailReq> addDetailReqList = new ArrayList<>();

        CabinetInventoryOccupyReduceDetailReq addDetailReq = new CabinetInventoryOccupyReduceDetailReq();
//        addDetailReq.setCabinetCode("JH01");
        addDetailReq.setSkuCode("5417115233");
//        addDetailReq.setSkuBatchCode("2022011712373050");
//        addDetailReq.setProduceDate(DateUtil.parseStartDate("2023-03-15", DateUtil.YYYY_MM_DD));
//        addDetailReq.setQualityDate(DateUtil.parseStartDate("2022-01-17", DateUtil.YYYY_MM_DD));
        addDetailReq.setReduceOccupyQuantity(1);

        addDetailReqList.add(addDetailReq);

        req.setOccupyReduceDetailReqList(addDetailReqList);

        String s = "{\"internalNo\":\"null\",\"occupyReduceDetailReqList\":[{\"cabinetCode\":\"JH001\",\"qualityDate\":1685894400000,\"reduceOccupyQuantity\":1,\"skuCode\":\"5417115233\"}],\"operatorType\":23,\"orderNo\":\"203147\",\"orderTypeName\":\"调拨出库\",\"warehouseNo\":2}";
        req = JSONObject.parseObject(s, CabinetInventoryOccupyReduceReq.class);
        cabinetInventoryCommandService.occupyReduceCabinetInventory(req);
    }

    @Test
    public void testreduceCabinetInventory(){
        CabinetInventoryReduceReq req = new CabinetInventoryReduceReq();
        req.setTenantId(1L);
        req.setWarehouseNo(2);
        req.setOrderNo("ORDER002");
        req.setContainerCode("CONTAINER001");
        req.setInternalNo("INTERNALNO001");
        req.setOrderTypeName(OtherStockChangeType.PURCHASE_IN.getTypeName());
        req.setOperatorType(CabinetInventoryOperationType.STOCK_IN.getId());

        List<CabinetInventoryReduceDetailReq> addDetailReqList = new ArrayList<>();

        CabinetInventoryReduceDetailReq addDetailReq = new CabinetInventoryReduceDetailReq();
//        addDetailReq.setCabinetCode("Z99-01-01-01");
        addDetailReq.setSkuCode("5417115233");
//        addDetailReq.setSkuBatchCode("20230315459935014");
        addDetailReq.setProduceDate(DateUtil.parseStartDate("2023-03-15", DateUtil.YYYY_MM_DD));
//        addDetailReq.setQualityDate(DateUtil.parseStartDate("2023-07-14", DateUtil.YYYY_MM_DD));
        addDetailReq.setReduceQuantity(1);

        addDetailReqList.add(addDetailReq);

        req.setReduceDetailReqList(addDetailReqList);

        cabinetInventoryCommandService.reduceCabinetInventory(req);
    }

    @Test
    public void testreleaseCabinetInventory(){
        CabinetInventoryReleaseReq cabinetInventoryReleaseReq = new CabinetInventoryReleaseReq();
        cabinetInventoryReleaseReq.setTenantId(1L);
        cabinetInventoryReleaseReq.setWarehouseNo(2);
        cabinetInventoryReleaseReq.setOrderNo("ORDER002");
        cabinetInventoryReleaseReq.setContainerCode("CONTAINER001");
        cabinetInventoryReleaseReq.setInternalNo("INTERNALNO001");
        cabinetInventoryReleaseReq.setOrderTypeName(OtherStockChangeType.PURCHASE_IN.getTypeName());
        cabinetInventoryReleaseReq.setOperatorType(CabinetInventoryOperationType.STOCK_IN.getId());

        List<CabinetInventoryReleaseDetailReq> addDetailReqList = new ArrayList<>();

        CabinetInventoryReleaseDetailReq addDetailReq = new CabinetInventoryReleaseDetailReq();
//        addDetailReq.setCabinetCode("JH01");
        addDetailReq.setSkuCode("5417115233");
//        addDetailReq.setSkuBatchCode("20230315459935014");
//        addDetailReq.setProduceDate(DateUtil.parseStartDate("2023-03-15", DateUtil.YYYY_MM_DD));
//        addDetailReq.setQualityDate(DateUtil.parseStartDate("2023-07-14", DateUtil.YYYY_MM_DD));
        addDetailReq.setReleaseQuantity(1);

        addDetailReqList.add(addDetailReq);

        cabinetInventoryReleaseReq.setReleaseDetailReqList(addDetailReqList);
        System.out.println("req: " + JSONObject.toJSONString(cabinetInventoryReleaseReq));

        cabinetInventoryCommandService.releaseCabinetInventory(cabinetInventoryReleaseReq);
    }
}
