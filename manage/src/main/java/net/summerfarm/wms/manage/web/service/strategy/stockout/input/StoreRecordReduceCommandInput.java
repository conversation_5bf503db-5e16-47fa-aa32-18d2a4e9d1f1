package net.summerfarm.wms.manage.web.service.strategy.stockout.input;

import lombok.Data;
import net.summerfarm.wms.manage.web.service.strategy.stockout.dto.StoreRecordReduceDetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/4
 */
@Data
public class StoreRecordReduceCommandInput implements Serializable {

    private static final long serialVersionUID = 8422877506106431314L;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批次扣减明细
     */
    private List<StoreRecordReduceDetail> storeRecordReduceDetails;

}
