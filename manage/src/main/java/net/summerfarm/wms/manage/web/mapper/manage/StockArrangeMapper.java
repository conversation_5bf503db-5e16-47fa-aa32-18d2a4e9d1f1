package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StockArrange;
import net.summerfarm.wms.manage.model.vo.StockArrangeVO;
import org.springframework.stereotype.Repository;

/**
 * @author: dongcheng
 * @date: 2023/12/1
 */
@Repository
public interface StockArrangeMapper {

    /**
     * 根据任务id修改预约单
     * @param update
     */
    void updateByTaskId(StockArrange update);

    /**
     * 根据任务id查询预约单
     * @param id
     * @return
     */
    StockArrangeVO selectByTaskId(Integer id);

}
