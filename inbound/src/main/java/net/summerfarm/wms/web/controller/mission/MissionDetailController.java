package net.summerfarm.wms.web.controller.mission;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.mission.inbound.controller.mission.vo.MissionDetailPageResDTO;
import net.summerfarm.wms.application.mission.service.mission.MissionCommandService;
import net.summerfarm.wms.application.mission.service.mission.MissionDetailQueryService;
import net.summerfarm.wms.api.h5.mission.req.*;
import net.summerfarm.wms.api.h5.mission.res.*;
import net.summerfarm.wms.domain.aftersale.entity.WarehouseSkuAfterSaleRateType;
import net.summerfarm.wms.domain.aftersale.repository.WarehouseSkuAfterSaleRateQueryRepository;
import net.summerfarm.wms.domain.mission.enums.MissionStateEnum;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 任务明细控制层
 */
@RestController
@RequestMapping("/mission/detail")
@Slf4j
public class MissionDetailController {

    @Resource
    private MissionCommandService missionCommandService;

    @Resource
    private MissionDetailQueryService missionDetailQueryService;
    @Resource
    private WarehouseSkuAfterSaleRateQueryRepository warehouseSkuAfterSaleRateQueryRepository;

    /**
     * 分页查询任务明细类目
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_category")
    public CommonResult<PageInfo<MissionDetailCategoryResDTO>> pageMissionCategory(@RequestBody @Valid MissionDetailPageQuery query) {
        PageInfo<MissionDetailCategoryResDTO> pageInfo = missionDetailQueryService.pageMissionCategoryPda(query);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 分页查询任务明细供应商
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_supplier")
    public CommonResult<PageInfo<MissionDetailSupplierResDTO>> pageMissionSupplier(@RequestBody @Valid MissionDetailPageQuery query) {
        PageInfo<MissionDetailSupplierResDTO> pageInfo = missionDetailQueryService.pageMissionSupplierPda(query);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 分页查询任务明细pc
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_page_pc")
    public CommonResult<PageInfo<MissionDetailPageResDTO>> pageMissionDetailPc(@RequestBody @Valid MissionDetailPageQuery query) {
        PageInfo<MissionDetailPageResDTO> missionPageResDTOPageInfo = missionDetailQueryService.pageDetailMissionPc(query);
        return CommonResult.ok(missionPageResDTOPageInfo);
    }


    /**
     * 分页查询任务明细pda
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_page_pda")
    public CommonResult<PageInfo<MissionDetailPageResDTO>> pageMissionDetailPda(@RequestBody @Valid MissionDetailPageQuery query) {
        PageInfo<MissionDetailPageResDTO> missionPageResDTOPageInfo = missionDetailQueryService.pageDetailMissionPda(query);
        return CommonResult.ok(missionPageResDTOPageInfo);
    }

    /**
     * 分页查询任务明细pda待完成
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_page_pda/wait")
    public CommonResult<PageInfo<MissionDetailPageResDTO>> pageMissionDetailPdaWait(@RequestBody @Valid MissionDetailPageQuery query) {
        // 去除操作人
        query.setOperatorName(null);
        PageInfo<MissionDetailPageResDTO> missionPageResDTOPageInfo = missionDetailQueryService.pageDetailMissionPda(query);
        return CommonResult.ok(missionPageResDTOPageInfo);
    }

    /**
     * 分页查询任务明细pda已完成
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_page_pda/complete")
    public CommonResult<PageInfo<MissionDetailPageResDTO>> pageMissionDetailPdaComplete(@RequestBody @Valid MissionDetailPageQuery query) {
        // 去除操作人，默认时间区域，已完成任务
        query.setOperatorName(null);
        query.setUpdateStartTime(LocalDate.now());
        query.setUpdateEndTime(LocalDate.now().plusDays(1));
        query.setState(Arrays.asList(MissionStateEnum.FINISH.getCode()));
        PageInfo<MissionDetailPageResDTO> missionPageResDTOPageInfo = missionDetailQueryService.pageDetailMissionPc(query);
        missionPageResDTOPageInfo.getList().stream()
                .peek(item -> {
                    if (StringUtils.isEmpty(item.getOperatorName())) {
                        return;
                    }
                    item.setOperatorName(String.join(",", JSON.parseArray(item.getOperatorName(), String.class)));
                })
                .collect(Collectors.toList());
        return CommonResult.ok(missionPageResDTOPageInfo);
    }

    /**
     * 查询任务明细pc
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_detail_pc")
    public CommonResult<MissionDetailPageResDTO> queryMissionDetailPc(@RequestBody @Valid MissionDetailQuery query) {
        return missionDetailQueryService.queryMissionDetailPc(query);
    }

    /**
     * 查询任务明细pda
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/mission_detail_pda")
    public CommonResult<MissionDetailPageResDTO> queryMissionDetailPda(@RequestBody @Valid MissionDetailQuery query) {
        CommonResult<MissionDetailPageResDTO> commonResult = missionDetailQueryService.queryMissionDetailPda(query);
        if (commonResult == null ||
                !ResultStatusEnum.OK.getStatus().equals(commonResult.getStatus()) ||
                commonResult.getData() == null){
            return commonResult;
        }

        try {
            MissionDetailPageResDTO missionDetailPageResDTO = commonResult.getData();
            BigDecimal afterSaleRate = warehouseSkuAfterSaleRateQueryRepository.getAfterSaleRateByWnoAndSku(
                    missionDetailPageResDTO.getWarehouseNo(), missionDetailPageResDTO.getSku(),
                    WarehouseSkuAfterSaleRateType.POST_3D_AFTER_SALE_RATE.getCode()
            );
            missionDetailPageResDTO.setAfterSalesRate(afterSaleRate);
            return CommonResult.ok(missionDetailPageResDTO);
        } catch (Exception ex){
            log.error("查询任务明细pda售后率异常", ex);
            return commonResult;
        }
    }
}
