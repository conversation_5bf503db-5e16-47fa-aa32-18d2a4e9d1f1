package net.summerfarm.wms.application.safeinventorylock.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 安全库存释放锁定输入
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmsSafeInventoryUnlockInput implements Serializable {

    private static final long serialVersionUID = -4940016467510282028L;

    /**
     * 锁定id
     */
    @NotNull(message = "锁定id不能为空")
    private Long id;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码不能为空")
    private Integer warehouseNo;

    /**
     * sku
     */
    @NotBlank(message = "SKU不能为空")
    private String sku;

    /**
     * 释放数量
     */
    @NotBlank(message = "释放数量不能为空")
    private Integer unlockQuantity;

    /**
     * 锁定类型
     */
    @NotNull(message = "锁定类型不能为空")
    private Integer lockType;

    /**
     * 释放原因
     */
    private String unlockReason;
}
