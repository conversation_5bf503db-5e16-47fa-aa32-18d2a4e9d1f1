package net.summerfarm.wms.domain.common.repository;

import net.summerfarm.wms.domain.common.domainobject.StoreTaskLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/05/14
 */
public interface StoreTaskLogRepository {

    /**
     * 插入操作记录
     * @param storeTaskLog 操作记录
     * @return 影响行数
     */
    Long create(StoreTaskLog storeTaskLog);

    /**
     * 批量插入操作记录
     * @param storeTaskLogs 操作记录
     * @return 影响行数
     */
    Long createBatch(List<StoreTaskLog> storeTaskLogs);

    /**
     * 按照类型和业务id查询操作日志
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 返回操作日志列表
     */
    List<StoreTaskLog> queryOprLogByBizIdAndType(String bizId, Integer bizType);
}
