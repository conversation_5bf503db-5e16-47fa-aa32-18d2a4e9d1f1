<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.AfterSaleProofMapper">

<select id="getRejectOrderInfo" resultType="net.summerfarm.wms.manage.model.vo.RejectSaleNeedStockVo">
    SELECT rs.orderNo          orderNo,
           rs.sku              sku,
           rs.mId              mId,
           rs.amount           amount,
           rs.afterSaleOrderNo afterSaleOrderNo,
           dp.`order_store_no` outStoreNo,
           wip.`warehouse_no`  areaNo
    FROM (SELECT aso.`order_no`      orderNo,
                 aso.`after_sale_order_no` afterSaleOrderNo,
                 aso.sku             sku,
                 aso.`m_id`          mId,
                 SUM(asp.`quantity`) amount
          FROM `after_sale_order` aso
                   left JOIN after_sale_proof asp ON asp.after_sale_order_no = aso.after_sale_order_no
          WHERE asp.handle_type IN (9, 10)
            AND asp.`status` = 2
            AND aso.`status` = 2
            AND asp.auditetime &gt;= #{time}
          GROUP BY aso.order_no, aso.after_sale_order_no, aso.sku) rs
             LEFT JOIN delivery_plan dp ON rs.orderNo = dp.order_no
             LEFT JOIN warehouse_inventory_mapping wip ON wip.sku = rs.sku AND dp.order_store_no = wip.store_no
    GROUP BY rs.orderNo, rs.afterSaleOrderNo, rs.sku, rs.mId, rs.amount, dp.`order_store_no`, wip.`warehouse_no`
</select>

</mapper>