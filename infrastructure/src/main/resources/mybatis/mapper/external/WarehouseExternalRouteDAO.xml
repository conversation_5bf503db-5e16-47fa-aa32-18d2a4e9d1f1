<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.external.WarehouseExternalRouteDAO">
    <resultMap id="WarehouseExternalRouteMap"
               type="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalRouteDO">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="warehouseTenantId" column="warehouse_tenant_id"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="externalWarehouseNo" column="external_warehouse_no"/>
        <result property="externalOwnerNo" column="external_owner_no"/>
        <result property="externalAppKey" column="external_app_key"/>
        <result property="warehouseType" column="warehouse_type"/>
        <result property="orderType" column="order_type"/>
        <result property="abilityId" column="ability_id"/>
        <result property="abilityCode" column="ability_code"/>
        <result property="routeStatus" column="route_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="table_name">
        wms_warehouse_external_route
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `tenant_id`, `warehouse_tenant_id`, `warehouse_no`, `external_warehouse_no`, `external_owner_no`,
        `external_app_key`, `warehouse_type`, `order_type`, `ability_id`, `ability_code`, `route_status`, `create_time`,
        `update_time`
    </sql>

    <sql id="values_exclude_id">
        #{tenantId}, #{warehouseTenantId}, #{warehouseNo}, #{externalWarehouseNo}, #{externalOwnerNo},
        #{externalAppKey}, #{warehouseType}, #{orderType}, #{abilityId}, #{abilityCode}, #{routeStatus}, #{createTime},
        #{updateTime}
    </sql>

    <sql id="query">
        <where>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="warehouseTenantId != null">AND `warehouse_tenant_id` = #{warehouseTenantId}</if>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="externalWarehouseNo != null">AND `external_warehouse_no` = #{externalWarehouseNo}</if>
            <if test="externalOwnerNo != null">AND `external_owner_no` = #{externalOwnerNo}</if>
            <if test="externalAppKey != null">AND `external_app_key` = #{externalAppKey}</if>
            <if test="warehouseType != null">AND `warehouse_type` = #{warehouseType}</if>
            <if test="orderType != null">AND `order_type` = #{orderType}</if>
            <if test="abilityId != null">AND `ability_id` = #{abilityId}</if>
            <if test="abilityCode != null">AND `ability_code` = #{abilityCode}</if>
            <if test="routeStatus != null">AND `route_status` = #{routeStatus}</if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="warehouseNos != null and warehouseNos.size() > 0">
                AND `warehouse_no` in
                <foreach collection="warehouseNos" item="warehouse_no1" open="(" close=")" separator=",">
                    #{warehouse_no1}
                </foreach>
            </if>
        </where>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalRouteDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">`tenant_id`,</if>
            <if test="warehouseTenantId != null">`warehouse_tenant_id`,</if>
            <if test="warehouseNo != null">`warehouse_no`,</if>
            <if test="externalWarehouseNo != null">`external_warehouse_no`,</if>
            <if test="externalOwnerNo != null">`external_owner_no`,</if>
            <if test="externalAppKey != null">`external_app_key`,</if>
            <if test="warehouseType != null">`warehouse_type`,</if>
            <if test="orderType != null">`order_type`,</if>
            <if test="abilityId != null">`ability_id`,</if>
            <if test="abilityCode != null">`ability_code`,</if>
            <if test="routeStatus != null">`route_status`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">#{tenantId},</if>
            <if test="warehouseTenantId != null">#{warehouseTenantId},</if>
            <if test="warehouseNo != null">#{warehouseNo},</if>
            <if test="externalWarehouseNo != null">#{externalWarehouseNo},</if>
            <if test="externalOwnerNo != null">#{externalOwnerNo},</if>
            <if test="externalAppKey != null">#{externalAppKey},</if>
            <if test="warehouseType != null">#{warehouseType},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="abilityId != null">#{abilityId},</if>
            <if test="abilityCode != null">#{abilityCode},</if>
            <if test="routeStatus != null">#{routeStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalRouteDO"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.tenantId}, #{i.warehouseTenantId}, #{i.warehouseNo}, #{i.externalWarehouseNo}, #{i.externalOwnerNo},
            #{i.externalAppKey}, #{i.warehouseType}, #{i.orderType}, #{i.abilityId}, #{i.abilityCode}, #{i.routeStatus},
            #{i.createTime}, #{i.updateTime})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalRouteDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="tenantId != null">`tenant_id` = #{tenantId},</if>
            <if test="warehouseTenantId != null">`warehouse_tenant_id` = #{warehouseTenantId},</if>
            <if test="warehouseNo != null">`warehouse_no` = #{warehouseNo},</if>
            <if test="externalWarehouseNo != null">`external_warehouse_no` = #{externalWarehouseNo},</if>
            <if test="externalOwnerNo != null">`external_owner_no` = #{externalOwnerNo},</if>
            <if test="externalAppKey != null">`external_app_key` = #{externalAppKey},</if>
            <if test="warehouseType != null">`warehouse_type` = #{warehouseType},</if>
            <if test="orderType != null">`order_type` = #{orderType},</if>
            <if test="abilityId != null">`ability_id` = #{abilityId},</if>
            <if test="abilityCode != null">`ability_code` = #{abilityCode},</if>
            <if test="routeStatus != null">`route_status` = #{routeStatus},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="WarehouseExternalRouteMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="WarehouseExternalRouteMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam"
            resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam"
            resultMap="WarehouseExternalRouteMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam"
            resultMap="WarehouseExternalRouteMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="queryDistinctWarehouseNo" resultType="java.lang.Long">
        select distinct warehouse_no
        from <include refid="table_name"/>
        where route_status = #{routeStatus}
        and external_app_key = #{externalAppKey}
    </select>
</mapper>
