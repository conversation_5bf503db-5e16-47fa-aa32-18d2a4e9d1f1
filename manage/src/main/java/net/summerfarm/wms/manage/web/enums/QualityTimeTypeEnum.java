package net.summerfarm.wms.manage.web.enums;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
public enum QualityTimeTypeEnum {

    DURATION(0, "固定时长"),
    EXPIRE(1, "到期时间"),
    ;

    private final Integer code;
    private final String desc;

    QualityTimeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(Integer code) {
        for (QualityTimeTypeEnum qualityTimeTypeEnum : QualityTimeTypeEnum.values()) {
            if (qualityTimeTypeEnum.getCode() == code) {
                return qualityTimeTypeEnum.getDesc();
            }
        }
        return "";
    }

}
