package net.summerfarm.wms.manage.common.util;

import com.google.common.base.Throwables;
import net.summerfarm.wms.common.constant.Global;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Component
@Async
public class MailUtil {

    private static final Logger logger = LoggerFactory.getLogger(MailUtil.class);

    public MailUtil(){

    }

    @Resource
    private JavaMailSenderImpl mailSender;

    public  void sendMail(String subject,String text){
        String[] tos=new String[]{"<EMAIL>","<EMAIL>","<EMAIL>"};
        sendMail(subject,text, tos, null);
    }

    public  void sendMail(String subject,String text, String[] to, String[] cc){
        // 构建简单邮件对象，见名知意
        SimpleMailMessage smm = new SimpleMailMessage();
        try {
            // 设定邮件参数
            smm.setFrom(mailSender.getUsername());
            smm.setTo(to);
            smm.setCc(cc);
            smm.setSubject(subject);
            smm.setText(text);
            mailSender.send(smm);
        } catch (Exception e) {
            logger.warn("邮件发送失败,SimpleMailMessage:{},cause:{}", smm, Throwables.getStackTraceAsString(e));
        }
    }

    /**
     *
     * @param subject 标题
     * @param text 文本内容
     * @param tos 接收人
     * @param ccs 抄送人
     * @param excelParts excel附件 文件名 -> excel
     * @throws Exception
     */
    public void sendMail(String subject, String text, String[] tos, String[] ccs, Map<String, Workbook> excelParts) throws Exception {
        logger.info("开始发送邮件");
        MimeMessage message = mailSender.createMimeMessage();
        message.setFrom(new InternetAddress(mailSender.getUsername()));
        for (String to: tos) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
        }
        if (ccs != null) {
            for (String cc: ccs) {
                message.addRecipients(Message.RecipientType.CC, cc);
            }
        }

        message.setSubject(subject);
        message.addHeader("charset", "UTF-8");

        Multipart multipart = new MimeMultipart();
        //文本内容
        logger.info("文本内容:{}",text);
        if (!StringUtils.isEmpty(text)) {
            MimeBodyPart contentPart = new MimeBodyPart();
            contentPart.setText(text, "UTF-8");
            contentPart.setHeader("Content-Type","text/html; charset=UTF-8");
            multipart.addBodyPart(contentPart);
        }
        // 附件内容 文件名 -> excel
        if (!CollectionUtils.isEmpty(excelParts)) {
            logger.info("处理附件内容开始");
            excelParts.forEach((fileName,workbook) -> {
                MimeBodyPart excelPart = new MimeBodyPart();
                ByteArrayOutputStream baos = null;
                ByteArrayInputStream bais = null;
                try {
                    baos = new ByteArrayOutputStream();
                    workbook.write(baos);
                    byte[] bt = baos.toByteArray();
                    bais = new ByteArrayInputStream(bt, 0, bt.length);
                    DataSource source = new ByteArrayDataSource(bais, "application/msexcel");
                    excelPart.setDataHandler(new DataHandler(source));
                    excelPart.setFileName(MimeUtility.encodeText(fileName));
                    multipart.addBodyPart(excelPart);
                } catch (Exception e) {
                    logger.error(Global.collectExceptionStackMsg(e));
                } finally {
                    try {
                        baos.close();
                        bais.close();
                    } catch (IOException e) {
                        logger.error(Global.collectExceptionStackMsg(e));
                    }
                }

            });
        }
        logger.info("执行发送邮件");
        message.setContent(multipart);
        message.setSentDate(new Date());
        message.saveChanges();
        mailSender.send(message);
    }

}
