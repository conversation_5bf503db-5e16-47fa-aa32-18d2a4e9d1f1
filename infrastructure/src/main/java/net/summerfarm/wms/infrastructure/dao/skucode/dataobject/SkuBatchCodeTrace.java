package net.summerfarm.wms.infrastructure.dao.skucode.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 批次唯一溯源码
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Getter
@Setter
@TableName("sku_batch_code_trace")
public class SkuBatchCodeTrace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * sku编码
     */
    @TableField("sku")
    private String sku;

    /**
     * 货品毛重
     */
    @TableField("good_gross_weight")
    private BigDecimal goodGrossWeight;

    /**
     * 货品名称
     */
    @TableField("pd_name")
    private String pdName;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 买手名称
     */
    @TableField("buyer_name")
    private String buyerName;

    /**
     * 状态 
10：订单待分配
20：路线待分配
30：待称重
40：已称重
     */
    @TableField("state")
    private Integer state;

    /**
     * sku子类型
     */
    @TableField("sku_sub_type")
    private Integer skuSubType;

    /**
     * 批次唯一码
     */
    @TableField("sku_batch_only_code")
    private String skuBatchOnlyCode;

    /**
     * 批次唯一溯源码
     */
    @TableField("sku_batch_trace_code")
    private String skuBatchTraceCode;

    /**
     * 入库任务id
     */
    @TableField("stock_task_storage_id")
    private Long stockTaskStorageId;

    /**
     * 批次日期
     */
    @TableField("batch_date")
    private LocalDate batchDate;

    /**
     * 入库任务次序编号
     */
    @TableField("stock_task_storage_seq")
    private Integer stockTaskStorageSeq;

    /**
     * 采购单号
     */
    @TableField("purchase_no")
    private String purchaseNo;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * OFC采购供应单编号
     */
    @TableField("pso_no")
    private String psoNo;

    /**
     * 订单单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 履约单号
     */
    @TableField("fulfillment_no")
    private Long fulfillmentNo;

    /**
     * 门店名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 线路编码
     */
    @TableField("path_code")
    private String pathCode;

    /**
     * 点位在路线上的顺序
     */
    @TableField("path_sequence")
    private Integer pathSequence;

    /**
     * SKU计划签收数量
     */
    @TableField("sku_plan_receipt_count")
    private Integer skuPlanReceiptCount;

    /**
     * SKU当前商品次序
     */
    @TableField("sku_current_seq")
    private Integer skuCurrentSeq;

    /**
     * 门店商品总数
     */
    @TableField("merchant_sku_total_num")
    private Integer merchantSkuTotalNum;

    /**
     * 重量
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 称重人
     */
    @TableField("weight_person")
    private String weightPerson;

    /**
     * 称重时间
     */
    @TableField("weight_time")
    private LocalDateTime weightTime;


    /**
     * 货品Id
     */
    @TableField("pd_id")
    private Long pdId;

    /**
     * 配送日期
     */
    @TableField("delivery_time")
    private LocalDate deliveryTime;

    /**
     * 城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 报价类型 0默认,1按斤报价,2按件报价
     */
    @TableField("quote_type")
    private Integer quoteType;

    /**
     * 联系人ID
     */
    @TableField("contact_id")
    private String contactId;

    /**
     * 门店ID
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 履约方式，0：干配，1：自提，2：干线，3：快递
     */
    @TableField("fulfillment_way")
    private Integer fulfillmentWay;

    /**
     * 标签类型0POP，1POPT2
     */
    @TableField("label_type")
    private Integer labelType;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
}
