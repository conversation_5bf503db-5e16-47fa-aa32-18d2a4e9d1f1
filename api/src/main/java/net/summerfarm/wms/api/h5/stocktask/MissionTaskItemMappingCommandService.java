package net.summerfarm.wms.api.h5.stocktask;

import net.summerfarm.wms.api.h5.stocktask.dto.req.MissionTaskItemMappingActualCommand;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/5
 */
public interface MissionTaskItemMappingCommandService {

    /**
     * 更新实出数量及状态
     * @param missionTaskItemMappingActualCommand
     */
    void updateActualQuantityAndStatus(MissionTaskItemMappingActualCommand missionTaskItemMappingActualCommand, List<Long> updateMappingIdList);

    /**
     * 更新确认出库异常状态
     * @param ids
     */
    void updateDiffConfirmedStatus(List<Long> ids);

    void updateDiffConfirmedOutStatus(List<Long> ids);
}
