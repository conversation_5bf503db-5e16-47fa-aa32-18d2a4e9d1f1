package net.summerfarm.wms.manage.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseInfoPmsDTO {
    /** 仓库编号 **/
    private Integer warehouseNo;
    /** 仓库名称 **/
    private String warehouseName;
    /** 仓库类型 **/
    private Integer warehouseType;
    /** 仓库类型名称 **/
    private String warehouseTypeName;
    /** 服务商 **/
    private String serviceProviderName;
    /** 仓库负责人 **/
    private Integer manageAdminId;
    /** 仓库负责人 **/
    private String personContact;
    /** 联系方式 **/
    private String phone;
    /**
     * 租户id
     */
    private Long tenantId;

}