package net.summerfarm.wms.domain.goods.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dongcheng
 * @date: 2023/9/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GoodsTaskWhiteList {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品sku编码
     */
    private String sku;

    /**
     * sku状态
     */
    private Integer pushStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;

    /**
     * 是否软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;


}
