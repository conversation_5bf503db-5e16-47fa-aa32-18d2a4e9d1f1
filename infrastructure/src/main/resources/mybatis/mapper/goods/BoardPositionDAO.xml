<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.goods.BoardPositionDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.goods.dataobject.BoardPositionDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="layer_total" jdbcType="BIGINT" property="layerTotal"/>
        <result column="storage_num" jdbcType="BIGINT" property="storageNum"/>
        <result column="layer_height" jdbcType="BIGINT" property="layerHeight"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="storage_type" jdbcType="BIGINT" property="storageType"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        , sku, layer_total, storage_num, layer_height, warehouse_no, storage_type
    </sql>

    <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into board_position_info(sku, layer_total, storage_num, layer_height, warehouse_no, storage_type)values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.sku},
            #{item.layerTotal},
            #{item.storageNum},
            #{item.layerHeight},
            #{item.warehouseNo},
            #{item.storageType}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        layer_total = VALUES(layer_total),
        storage_num = VALUES(storage_num),
        layer_height = VALUES(layer_height),
        storage_type = VALUES(storage_type)
    </insert>

    <update id="update">
        update board_position_info
        set layer_total  = #{layerTotal},
            storage_num  = #{storageNum},
            layer_height = #{layerHeight},
            storage_type = #{storageType}
        where sku = #{sku}
          and warehouse_no = #{warehouseNo}
    </update>

    <update id="updateSelective">
        update board_position_info
        <set>
            <if test="layerTotal != null">
                layer_total = #{layerTotal},
            </if>
            <if test="storageNum != null">
                storage_num = #{storageNum},
            </if>
            <if test="layerHeight != null">
                layer_height = #{layerHeight},
            </if>
            <if test="storageType != null">
                storage_type = #{storageType}
            </if>
        </set>
        where sku = #{sku}
        and warehouse_no = #{warehouseNo}
    </update>

    <select id="selectBySkuAndWarehouseNo" resultMap="baseResultMap">
        /*FORCE_MASTER*/  select
        <include refid="BASE_COLUMN"/>
        from board_position_info
        where sku = #{sku} and warehouse_no = #{warehouseNo}
    </select>

    <select id="selectBySkusAndWarehouseNo" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from board_position_info
        where warehouse_no = #{warehouseNo} and sku in
        <foreach collection="skus" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listBoardPositionWithProducts"
            resultType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.BoardPositionWithProductsDO">
        select
        ass.sku sku, bpi.layer_total layerTotal, bpi.storage_num storageNum, bpi.layer_height layerHeight,
        ass.area_no warehouseNo, bpi.storage_type storageType, p.pd_name pdName, ass.quantity quantity,
        p.storage_location storageLocation, i.weight specification, p.category_id categoryId,
        i.is_domestic isDomestic, i.unit
        from area_store as ass
        left join board_position_info as bpi on ass.sku = bpi.sku and ass.area_no = bpi.warehouse_no
        left join inventory as i on ass.sku = i.sku
        left join products as p on i.pd_id = p.pd_id
        <where>
            <if test="skus != null and skus.size() > 0">
                ass.sku in
                <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
            <if test="pdId != null">
                and p.pd_id = #{pdId}
            </if>
            <if test="warehouseNo != null">
                and ass.area_no = #{warehouseNo}
            </if>
            <if test="haveVolume == false">
                AND i.volume is null
            </if>
            <if test="haveVolume == true">
                AND i.volume is not null
            </if>
            <if test="haveWeight == false">
                AND i.weight_num is null
            </if>
            <if test="haveWeight  == true">
                AND i.weight_num is not null
            </if>
        </where>
    </select>

</mapper>