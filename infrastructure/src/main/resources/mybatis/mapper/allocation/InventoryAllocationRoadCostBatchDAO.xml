<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.allocation.InventoryAllocationRoadCostBatchDAO">
    <!-- 结果集映射 -->
    <resultMap id="inventoryAllocationRoadCostBatchResultMap" type="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.InventoryAllocationRoadCostBatchDO">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="list_no" property="listNo" jdbcType="VARCHAR"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
		<result column="production_date" property="productionDate" jdbcType="DATE"/>
		<result column="quality_date" property="qualityDate" jdbcType="DATE"/>
		<result column="road_quantity" property="roadQuantity" jdbcType="INTEGER"/>
		<result column="cost" property="cost" jdbcType="DOUBLE"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="inventoryAllocationRoadCostBatchColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.list_no,
          t.warehouse_no,
          t.sku,
          t.batch_no,
          t.production_date,
          t.quality_date,
          t.road_quantity,
          t.cost
    </sql>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_allocation_road_cost_batch (
          list_no, warehouse_no, sku, batch_no, production_date, quality_date, road_quantity, cost
        ) values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.listNo}, #{item.warehouseNo}, #{item.sku}, #{item.batchNo}, #{item.productionDate}, #{item.qualityDate}, #{item.roadQuantity}, #{item.cost}
            )
        </foreach>
    </insert>

    <!-- 根据主键ID获取数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="inventoryAllocationRoadCostBatchResultMap" >
        SELECT <include refid="inventoryAllocationRoadCostBatchColumns" />
        FROM inventory_allocation_road_cost_batch t
        WHERE t.id = #{id}
    </select>

    <!-- 根据主键ID列表获取数据 -->
    <select id="selectByIdList" resultMap="inventoryAllocationRoadCostBatchResultMap" >
        SELECT <include refid="inventoryAllocationRoadCostBatchColumns" />
        FROM inventory_allocation_road_cost_batch t
        WHERE t.id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.allocation.domainobject.query.InventoryAllocationRoadCostBatchQuery" resultMap="inventoryAllocationRoadCostBatchResultMap" >
        SELECT <include refid="inventoryAllocationRoadCostBatchColumns" />
        FROM inventory_allocation_road_cost_batch t
        <where>
            <if test="listNo != null and listNo !=''">
                AND t.list_no = #{listNo}
            </if>
            <if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
            <if test="batchNo != null and batchNo !=''">
                AND t.batch_no = #{batchNo}
            </if>
            <if test="productionDate != null">
                AND t.production_date = #{productionDate}
            </if>
            <if test="qualityDate != null">
                AND t.quality_date = #{qualityDate}
            </if>
        </where>
    </select>

    <!-- 根据仓库列表和sku列表查询聚合的调拨在途数据 -->
    <select id="queryAggregateAllocationRoadData" resultType="net.summerfarm.wms.domain.allocation.domainobject.AggregateAllocationRoadData" >
        SELECT
        t.`warehouse_no`,
        t.`sku`,
        SUM(IFNULL(t.`road_quantity`, 0)) roadQuantity,
        SUM(IFNULL(t.`road_quantity`, 0) * IFNULL(t.`cost`, 0)) goodsValue
        FROM inventory_allocation_road_cost_batch t
        <where>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                AND t.`warehouse_no` IN
                <foreach collection="warehouseNoList" item="warehouseNo" open="(" close=")" separator=",">
                    #{warehouseNo}
                </foreach>
            </if>
            <if test="skuList != null and skuList.size() > 0">
                AND t.`sku` IN
                <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
        </where>
        GROUP BY t.`warehouse_no`, t.`sku`
    </select>

    <!-- 根据库存仓查询当前存在调拨在途库存的数据 -->
    <select id="queryAllocationRoadQuantity" resultType="net.summerfarm.wms.domain.allocation.valueobject.AllocationRoadQuantityValueObject" >
        SELECT `warehouse_no` as warehouseNo, `list_no` as listNo, `sku`, SUM(`road_quantity`) as costBatchRoadQuantity
        FROM `inventory_allocation_road_cost_batch`
        WHERE warehouse_no = #{warehouseNo} AND `road_quantity` > 0
        GROUP BY `warehouse_no`, `list_no` , `sku`
    </select>

    <!-- 根据ID修改调拨在途数量 -->
  	<update id="updateRoadQuantityById">
        UPDATE inventory_allocation_road_cost_batch t
        SET t.road_quantity = t.road_quantity + #{changeRoadQuantity}
        WHERE t.id = #{id}
    </update>

    <!-- 根据主键ID列表删除记录 -->
    <delete id="removeByIdList">
        DELETE FROM inventory_allocation_road_cost_batch
        WHERE id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

</mapper>