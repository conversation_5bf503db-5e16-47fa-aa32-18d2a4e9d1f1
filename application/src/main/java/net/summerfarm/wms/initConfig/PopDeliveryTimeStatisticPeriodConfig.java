package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description pop配送统计口径
 * @date 2025/1/15
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(prefix = "pop.delivery.time.statistic", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class PopDeliveryTimeStatisticPeriodConfig {

    /**
     * 统计周期
     */
    private Integer period;

}
