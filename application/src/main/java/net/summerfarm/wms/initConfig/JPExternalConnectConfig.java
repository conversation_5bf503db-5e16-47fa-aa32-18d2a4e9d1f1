package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 绝配对接配置
 * @date 2023/11/15
 */
@NacosConfigurationProperties(prefix = "jp.external.connect", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Configuration
@Data
public class JPExternalConnectConfig {

    /**
     * appkey
     */
    private String appKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 用户id
     */
    private String customerId;

}
