package net.summerfarm.wms.manage.web.service.strategy.stockout.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8
 */
@Data
public class StockOutCabinetDetail implements Serializable {

    private static final long serialVersionUID = 5481093116284609451L;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 出库数量
     */
    private Integer quantity;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;


}
