package net.summerfarm.wms.scheduler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.api.h5.storealert.FreezeInvImbalanceService;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.initConfig.WarehouseGroupConfig;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/11/27
 */
@Slf4j
@Component
public class FreezeInvImbalanceAlarmJob extends XianMuJavaProcessorV2 {

    @Resource
    private FreezeInvImbalanceService freezeInvImbalanceService;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private WarehouseGroupConfig warehouseGroupConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 普通仓库 获取下鲜沐仓库列表
        List<WarehouseStorageCenter> storageCenters = warehouseStorageService.selectAllForSummerfarm(new WarehouseStorageCenter());
        if (CollectionUtils.isEmpty(storageCenters)) {
            log.info("未找到鲜沐仓库信息");
            return new ProcessResult(true);
        }
        // 存在不平对账的标志
        boolean hasImbalanceFlag = false;
        // 按照仓库遍历
        List<Integer> testWarehouseNoList = getTestWarehouseNoList();
        for (WarehouseStorageCenter storageCenter : storageCenters) {
            // 测试仓直接跳过
            if (testWarehouseNoList.contains(storageCenter.getWarehouseNo())) {
                continue;
            }
            boolean tmpFlag = freezeInvImbalanceService.alarm(storageCenter);
            hasImbalanceFlag = hasImbalanceFlag || tmpFlag;
        }
        // 消息通知
        String url = configRepository.queryValueByKey("freezeInv_imbalance_url");
        if (StringUtils.isEmpty(url)) {
            log.error("仓库冻结库存不准飞书消息通知地址没有配置key: freezeInv_imbalance_url \n");
            return new ProcessResult(true);
        }
        StringBuilder text = new StringBuilder();
        if (!hasImbalanceFlag) {
            text.append("本次全部仓库未查询到冻结库存不准的情况 \n")
                    .append("运行时间:").append(DateUtil.formatDate(LocalDateTime.now())).append("\n");
            // 发送飞书消息通知
            FeishuBotUtil.sendMarkdownMsgAndAtAll(url, text.toString());
        } else {
            text.append("本次全部仓库查询冻结库存不准全部查询完成 \n")
                    .append("运行时间:").append(DateUtil.formatDate(LocalDateTime.now())).append("\n");
            // 发送飞书消息通知
            FeishuBotUtil.sendMarkdownMsgAndAtAll(url, text.toString());
        }
        return new ProcessResult(true);
    }

    public List<Integer> getTestWarehouseNoList() {
        // 获取测试仓库列表
        return warehouseGroupConfig.getIntegerTestWarehouseList();
    }
}
