package net.summerfarm.wms.api.h5.stockdamage;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/24
 */
public interface StockDamageCommandService {

    /**
     * 异步处理货损任务下发
     * @param stockDamageId
     * @param warehouseNo
     * @param taskType
     */
    void handleExternalStockDamageNoticeAsync(Long stockDamageId, Integer warehouseNo, Integer taskType);

    /**
     * 下发外部货损通知
     * @param stockDamageId
     * @param warehouseNo
     */
    void exeExternalStockDamageNotice(Long stockDamageId, Integer warehouseNo);

    /**
     * 异步处理货损任务提交
     * @param stockDamageId
     * @param warehouseNo
     */
    void handleExternalStockDamageCommitAsync(Long stockDamageId, Integer warehouseNo);

    /**
     * 下发外部货损提交
     * @param stockDamageId
     * @param warehouseNo
     */
    void exeExternalStockDamageCommit(Long stockDamageId, Integer warehouseNo);

    /**
     * 异步处理货损任务取消
     * @param stockDamageId
     * @param warehouseNo
     */
    void handleExternalStockDamageCancelAsync(Long stockDamageId, Integer warehouseNo);

    /**
     * 下发外部货损取消
     * @param stockDamageId
     * @param warehouseNo
     */
    void exeExternalStockDamageCancel(Long stockDamageId, Integer warehouseNo);
}
