package net.summerfarm.wms.domain.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 波次任务配置sku选项
 * @author: xdc
 * @date: 2024/3/15
 **/
@Getter
@AllArgsConstructor
public enum StockTaskWaveConfigSkuOptionEnums {

    /**
     * 非代销不入仓的品
     */
    COMMON_IN_STORE_SKU(1, "非代销不入仓的品"),

    /**
     * 代销不入仓的品
     */
    DX_NOT_IN_STORE_SKU(2, "代销不入仓的品"),

    /**
     * 代销不入仓和非代销不入仓的品都有
     */
    MIX(3, "代销不入仓和非代销不入仓的品都有"),

    /**
     * POP品
     */
    POP_SKU(4, "POP品")
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
}
