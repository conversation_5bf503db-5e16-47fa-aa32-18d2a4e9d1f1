package net.summerfarm.wms.domain.inventory.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryLockDetail implements Serializable {

    /**
     * primary key
     */
    private Long id;
    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
    /**
     * 库存仓编号
     */
    private Long warehouseNo;
    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    private Long warehouseTenantId;
    /**
     * sku编码
     */
    private String skuCode;
    /**
     * 业务单号
     */
    private String orderNo;
    /**
     * 库存变动类型
     */
    private String orderTypeName;
    /**
     * 锁库数量
     */
    private Integer lockQuantity;
    /**
     * 剩余数量
     */
    private Integer remainQuantity;
    /**
     * 释放数量
     */
    private Integer releaseQuantity;
    /**
     * 扣减数量
     */
    private Integer reduceQuantity;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;
    /**
     * 业务子单号
     */
    private String orderSubNo;
}
