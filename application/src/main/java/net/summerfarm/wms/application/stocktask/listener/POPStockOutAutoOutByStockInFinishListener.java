package net.summerfarm.wms.application.stocktask.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.wms.enums.StockTaskTypeEnum;
import net.summerfarm.wms.application.stocktask.AutoStockOutService;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.enums.StockStoragePurchaseModeEnums;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.qms.enums.QmsInspectTaskSourceTypeEnum;
import net.summerfarm.wms.domain.stocktask.StockTaskNoticeOrderRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder;
import net.summerfarm.wms.domain.stocktask.enums.NoticeSkuFlagEnums;
import net.summerfarm.wms.domain.stocktask.enums.StockTaskOrderTypeEnum;
import net.summerfarm.wms.facade.ofc.PurchaseSupplyQueryFacade;
import net.summerfarm.wms.instore.dto.dto.StockStorageMsgDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * POP自提自动出库监听入库完成消息
 */
@Component
@MqListener(
        topic = POPStockOutAutoOutByStockInFinishListener.POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_TOPIC,
        tag = POPStockOutAutoOutByStockInFinishListener.POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_TAG,
        consumerGroup = POPStockOutAutoOutByStockInFinishListener.POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_GROUP,
        maxReconsumeTimes = 5,
        maxReconsumeTimesWarnLog = 5)
@Slf4j
public class POPStockOutAutoOutByStockInFinishListener extends AbstractMqListener<StockStorageMsgDTO> {

    public final static String POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_TOPIC = "topic_wms_stock_task";
    public final static String POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_TAG = "tag_wms_stock_task_storage_supply_finish";
    public final static String POP_STOCK_OUT_AUTO_OUT_BY_STOCK_IN_FINISH_GROUP = Global.MQ_GROUP;

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private StockTaskNoticeOrderRepository stockTaskNoticeOrderRepository;
    @Resource
    private PurchaseSupplyQueryFacade purchaseSupplyQueryFacade;
    @Resource
    private AutoStockOutService autoStockOutService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(StockStorageMsgDTO stockStorageMsgDTO) {
        log.info("POPStockOutAutoOutByStockInFinishListener process tag_wms_stock_task_storage_supply_finish: {}",
                JSONUtil.toJsonStr(stockStorageMsgDTO));

        if (stockStorageMsgDTO == null ||
                stockStorageMsgDTO.getState() == null ||
                stockStorageMsgDTO.getTaskId() == null ||
                stockStorageMsgDTO.getType() == null ||
                stockStorageMsgDTO.getInWarehouseNo() == null){
            return;
        }

        // 非采购返回
        if (!QmsInspectTaskSourceTypeEnum.PURCHASE_IN.equalsCode(stockStorageMsgDTO.getType())){
            return;
        }
        // 非完成返回
        if (!Integer.valueOf(StockTaskStorageStateEnum.FINISH.getId()).equals(stockStorageMsgDTO.getState())){
            return;
        }

        // 查询入库任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository
                .queryStockTaskStorageById(stockStorageMsgDTO.getTaskId());
        if (Objects.isNull(stockTaskStorage)) {
            return;
        }
        // 非POP返回
        if (!StockStoragePurchaseModeEnums.POP_PURCHASE.equalsCode(stockTaskStorage.getPurchaseMode())){
            return;
        }

        // 查询履约获取同批的入库任务是否全部完成
        List<String> psoNoList = purchaseSupplyQueryFacade.querySameBatchGoodsSupplyOrders(stockTaskStorage.getPsoNo());
        if (CollectionUtils.isEmpty(psoNoList)){
            log.error("\n查询到非POP的同批入库任务为空 {}\n", stockTaskStorage.getPsoNo());
            return;
        }
        List<StockTaskStorage> stockTaskStorageList = stockTaskStorageQueryRepository.queryStockTaskStorageByPsoNoList(
                stockTaskStorage.getInWarehouseNo(),
                Arrays.asList(OperationType.PURCHASE_IN.getId()),
                psoNoList
        );
        // 任一未完成则返回
        if (stockTaskStorageList.stream()
                .anyMatch(s -> !Integer.valueOf(StockTaskStorageStateEnum.FINISH.getId()).equals(s.getState()))){
            return;
        }
        // 存在非POP则告警返回
        if (stockTaskStorageList.stream()
                .anyMatch(s -> !StockStoragePurchaseModeEnums.POP_PURCHASE.equalsCode(s.getPurchaseMode()))){
            log.error("\n查询到非POP的同批入库任务非POP {} {}\n",
                    JSONObject.toJSONString(stockStorageMsgDTO), JSONObject.toJSONString(stockTaskStorageList));
            return;
        }

        // 采购供应单对应的货品供应单列表
        List<String> psoNoListTmp = stockTaskStorageList.stream()
                .map(StockTaskStorage::getPsoNo)
                .distinct()
                .collect(Collectors.toList());
        List<String> goodsSupplyNoList = purchaseSupplyQueryFacade.queryGoodsSupplyNoListByPsoNoList(
                stockTaskStorage.getInWarehouseNo().longValue(), psoNoListTmp
        );
        if (CollectionUtils.isEmpty(goodsSupplyNoList)){
            log.error("\n查询到采购供应单对应的货品供应单列表为空 {}\n", JSONObject.toJSONString(psoNoListTmp));
            return;
        }

        // 根据货品供应单查询出库通知单列表
        List<StockTaskNoticeOrder> stockTaskNoticeOrderList = stockTaskNoticeOrderRepository
                .findByGoodsSupplyNoList(goodsSupplyNoList);
        // 判断存在非POP则异常返回
        if (stockTaskNoticeOrderList.stream()
                .anyMatch(s -> !NoticeSkuFlagEnums.POP_SKU.equalsCode(s.getNoticeSkuFlag()))){
            log.error("\n查询到非POP的同批出库任务非POP {} {}\n",
                    JSONObject.toJSONString(stockStorageMsgDTO), JSONObject.toJSONString(stockTaskNoticeOrderList));
            return;
        }
        // 判断存在非自提则异常返回
        if (stockTaskNoticeOrderList.stream()
                .anyMatch(s -> !StockTaskOrderTypeEnum.SELF_PICK.equalsOutOrderCode(s.getOutOrderType()))){
            log.warn("查询到非POP的同批出库任务非POP {} {}",
                    JSONObject.toJSONString(stockStorageMsgDTO), JSONObject.toJSONString(stockTaskNoticeOrderList));
            return;
        }
        // 判断自提未生成出库任务
        if (stockTaskNoticeOrderList.stream()
                .anyMatch(s -> s.getStockTaskId() == null)){
            log.warn("自提未生成出库任务 {} {}",
                    JSONObject.toJSONString(stockStorageMsgDTO), JSONObject.toJSONString(stockTaskNoticeOrderList));
            throw new BizException("自动出库，自提未生成出库任务，等待重试");
        }

        try {
            // 收货过快，库存未加完，睡眠等待
            Thread.sleep(100L);

            AdminUtil.setDefaultUserBase();
            List<Long> stockTaskIdList = stockTaskNoticeOrderList.stream()
                    .map(StockTaskNoticeOrder::getStockTaskId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            // 自提自动出库
            for (Long stockTaskId : stockTaskIdList) {
                try {
                    String lockKey = "WMS:autoOutByStockOutIdAndTypeForNotCabinetManagement:" + stockTaskId;

                    redisUtil.executeCallableByJustTryOneTime(lockKey, 2L, TimeUnit.HOURS, () -> {
                        autoStockOutService.autoOutByStockOutIdAndTypeForNotCabinetManagement(
                                stockTaskId, StockTaskTypeEnum.OWN_SALE_OUT_TASK);
                        return 1;
                    });
                } catch (Exception ex){
                    throw ex;
                }
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            AdminUtil.USER_BASE.remove();
        }


    }
}
