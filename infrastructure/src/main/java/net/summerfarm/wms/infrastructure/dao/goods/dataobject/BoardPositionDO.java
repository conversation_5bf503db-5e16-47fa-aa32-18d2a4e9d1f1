package net.summerfarm.wms.infrastructure.dao.goods.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * 板位信息数据对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BoardPositionDO {
    Long id;

    /**
     * sku
     */
    String sku;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 储存类别
     */
    Integer storageType;

    /**
     * 层高
     */
    Integer layerHeight;

    /**
     * 箱入数
     */
    Integer storageNum;

    /**
     * 层码放数量
     */
    Integer layerTotal;
}
