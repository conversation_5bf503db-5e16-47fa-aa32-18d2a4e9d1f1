package net.summerfarm.wms.manage.dao.stockTransfer;

import net.summerfarm.wms.manage.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.wms.manage.model.domain.StockTransferItemOpDO;
import net.summerfarm.wms.manage.model.domain.StockTransferItemOpDetailDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Repository
public interface StockTransferItemV1DAO {

    /**
     * 根据主键列表查询
     *
     * @param ids
     * @return
     */
    List<StockTransferItemDO> listByIds(@Param("ids") List<Long> ids);

    /**
     * 根据skus查询实例id列表
     */
    List<Long> listBySku(@Param("skus") List<String> skus);

    /**
     * 根据stockTransferId查询
     */
    List<StockTransferItemDO> listByStockTransferId(@Param("transferId") Long transferId);

    /**
     * 根据stockTransferIds和sku查询
     */
    List<Long> listByStockTransferIdsAndSku(@Param("transferIds") List<Long> transferIds,
                                            @Param("sku") String sku);

    /**
     * 批量插入
     *
     * @param items 对象列表
     */
    void batchInsert(@Param("items") List<StockTransferItemDO> items);

    /**
     * 根据主键id查询
     */
    StockTransferItemDO selectById(@Param("id") Long id);


    int insert(StockTransferItemOpDetailDO stockTransferItemOpDetailDO);

}
