package net.summerfarm.wms.decorator;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/3 18:42
 */
public class MdcDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable task) {
        Map<String, String> threadContext = MDC.getCopyOfContextMap();
        return () -> {
            try {
                if (Objects.nonNull(threadContext)) {
                    // 将父线程的context set到子线程
                    MDC.setContextMap(threadContext);
                    task.run();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                MDC.clear();
            }
        };
    }
}
