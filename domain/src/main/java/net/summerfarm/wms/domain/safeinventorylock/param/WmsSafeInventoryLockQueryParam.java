package net.summerfarm.wms.domain.safeinventorylock.param;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全库存锁定表查询参数
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
public class WmsSafeInventoryLockQueryParam extends BasePageInput {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * 仓库编码列表
     */
    private List<Integer> warehouseNos;

    /**
     * sku
     */
    private String sku;

    /**
     * sku列表
     */
    private List<String> skus;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 锁定编号列表
     */
    private List<String> lockNos;

    /**
     * 初始化数量
     */
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定类型列表
     */
    private List<Integer> lockTypes;

    /**
     * 锁定状态，1：锁定 0：释放
     */
    private Integer lockStatus;

    /**
     * 锁定状态列表
     */
    private List<Integer> lockStatuses;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    private LocalDateTime updateTimeEnd;

    /**
     * 创建人ID
     */
    private Long createOperatorId;

    /**
     * 更新人ID
     */
    private Long updateOperatorId;

    /**
     * 仓库租户ID 1-鲜沐
     */
    private Long warehouseTenantId;
}
