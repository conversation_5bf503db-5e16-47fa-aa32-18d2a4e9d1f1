package net.summerfarm.wms.application.qms.inbound.controller.qms;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.qms.inbound.controller.qms.assembler.QmsInspectionRuleAssembler;
import net.summerfarm.wms.application.qms.inbound.controller.qms.assembler.QmsInspectionTaskAssembler;
import net.summerfarm.wms.application.qms.inbound.controller.qms.assembler.QmsInspectionTaskVOAssembler;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.command.*;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.query.QmsInspectionTaskQueryInput;
import net.summerfarm.wms.application.qms.inbound.controller.qms.vo.QmsInspectionTaskCommitVO;
import net.summerfarm.wms.application.qms.inbound.controller.qms.vo.QmsInspectionTaskVO;
import net.summerfarm.wms.application.qms.service.qms.QmsInspectionTaskCommandService;
import net.summerfarm.wms.application.qms.service.qms.QmsInspectionTaskQueryService;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.JSONUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.mission.domainobject.Mission;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionScaleEntity;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionTaskEntity;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionTaskSnapshotEntity;
import net.summerfarm.wms.domain.qms.enums.QmsInspectScaleProcessTypeEnum;
import net.summerfarm.wms.domain.qms.enums.QmsIsQualifiedEnum;
import net.summerfarm.wms.domain.qms.param.command.QmsInspContentValueObject;
import net.summerfarm.wms.domain.qms.repository.QmsInspectionScaleQueryRepository;
import net.summerfarm.wms.domain.qms.repository.QmsInspectionTaskSnapshotQueryRepository;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.warehouse.WarehouseWmsFacade;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * qms货检单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-04-02 09:34:21
 */
@RestController
@RequestMapping(value = "/qms/inspectionTask")
public class QmsInspectionTaskController {

    @Autowired
    private QmsInspectionTaskCommandService qmsInspectionTaskCommandService;
    @Autowired
    private QmsInspectionTaskQueryService qmsInspectionTaskQueryService;
    @Autowired
    private QmsInspectionTaskSnapshotQueryRepository snapshotQueryRepository;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private GoodsReadFacade goodsReadFacade;
    @Autowired
    private WarehouseWmsFacade warehouseWmsFacade;
    @Autowired
    private MissionRepository missionRepository;
    @Autowired
    private QmsInspectionScaleQueryRepository scaleQueryRepository;
    @Autowired
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;

    /**
     * 获取要求货检数量
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/task/requireInQuantityDesc")
    public CommonResult<String> requireInQuantityDesc(@RequestBody @Validated QmsInspectionTaskRequireQuantityCommandInput input) {
        Mission mission = missionRepository.findMission(input.getMissionNo());
        if (mission == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求任务不存在,请刷新后重试");
        }

        String sourceOrderNo = mission.getSourceOrderNo();
        if (sourceOrderNo == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求任务来源货检单不存在,请刷新后重试");
        }
        QmsInspectionTaskEntity qmsTaskEntity = qmsInspectionTaskQueryService.getDetail(Long.valueOf(sourceOrderNo));
        if (qmsTaskEntity == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求货检单不存在,请刷新后重试");
        }

        GoodsInfoDTO goodsInfoDTO = goodsReadFacade.findGoodsInfoBySkuList(
                LoginInfoThreadLocal.getTenantId(), input.getSkuCode());
        if (goodsInfoDTO == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求SKU不存在,请刷新后重试");
        }
        Integer goodsProcessType = goodsInfoDTO.getMaterialType() == null ?
                QmsInspectScaleProcessTypeEnum.PRODUCT_INSP.getCode() : goodsInfoDTO.getMaterialType();

        // 原料乘以系数
        BigDecimal actualStoreQuantity = BigDecimal.valueOf(input.getActualStoreQuantity());
        if (QmsInspectScaleProcessTypeEnum.MATERIAL_INSP.getCode().equals(goodsProcessType) &&
                goodsInfoDTO.getWeight() != null){
            actualStoreQuantity = actualStoreQuantity.multiply(goodsInfoDTO.getWeight()).setScale(2, RoundingMode.HALF_DOWN);
        }
        QmsInspectionScaleEntity scaleEntity = scaleQueryRepository.selectByGoodsProcessTypeAndQuantity(
                goodsProcessType, actualStoreQuantity);
        if (scaleEntity == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求库存数量对应的比例配置不存在,请检查配置后重试");
        }

        //要求货检数量
        String requireInQuantityDesc = QmsInspectionRuleAssembler.getQuantityDescByScale(
                scaleEntity, qmsTaskEntity, input.getActualStoreQuantity(), goodsInfoDTO);
        return CommonResult.ok(requireInQuantityDesc);
    }

	/**
	 * 任务暂存
	 *
	 * @return QmsInspectionTaskVO
	 */
	@PostMapping(value = "/task/tempStorage")
	public CommonResult<String> tempStorage(@RequestBody QmsInspectionTaskCommitCommandInput input) {
        if (input == null || input.getMissionNo() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }

        String key  = input.getMissionNo();
        String value = JSONUtil.toJSONString(input);
        redisUtil.setExpire(key, value, 7L, TimeUnit.DAYS);
        return CommonResult.ok("暂存成功");
	}

    /**
     * 任务暂存读取
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/task/tempStorage/get")
    public CommonResult<QmsInspectionTaskCommitCommandInput> tempStorageGet(
            @RequestBody QmsInspectionTaskCommitCommandInput input) {
        if (input == null || input.getMissionNo() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }

        String key  = input.getMissionNo();
        String value = redisUtil.get(key);
        if (StringUtils.isEmpty(value)){
            return CommonResult.ok(null);
        }

        QmsInspectionTaskCommitCommandInput object = JSONObject.parseObject(
                value, QmsInspectionTaskCommitCommandInput.class);

        // 历史数据刷是否合格数据
        if (object.getIsQualified() == null && object.getUnqualifiedQuantity() != null){
            object.setIsQualified(object.getUnqualifiedQuantity().compareTo(BigDecimal.ZERO) == 0 ?
                    QmsIsQualifiedEnum.QUALIFIED.getCode() : QmsIsQualifiedEnum.NOT_QUALIFIED.getCode());
        }
        if (!CollectionUtils.isEmpty(object.getInspContentValueObjects())) {
            for (QmsInspContentValueObject inspContentValueObject : object.getInspContentValueObjects()) {
                if (inspContentValueObject == null || inspContentValueObject.getCommitInfo() == null){
                    continue;
                }
                if (inspContentValueObject.getCommitInfo().getIsQualified() == null &&
                        inspContentValueObject.getCommitInfo().getUnqualifiedQuantity() != null){
                    inspContentValueObject.getCommitInfo().setIsQualified(
                            inspContentValueObject.getCommitInfo().getUnqualifiedQuantity().compareTo(BigDecimal.ZERO) == 0 ?
                                    QmsIsQualifiedEnum.QUALIFIED.getCode() : QmsIsQualifiedEnum.NOT_QUALIFIED.getCode());
                }
            }
        }
        return CommonResult.ok(object);
    }


    /**
	 * 任务提交pc
	 *
	 * @return QmsInspectionTaskVO
	 */
	@PostMapping(value = "/task/commit_pc")
	public CommonResult<QmsInspectionTaskCommitVO> commitPc(@RequestBody QmsInspectionTaskCommitCommandInput input) {
        return qmsInspectionTaskCommandService.commitPc(input);
	}

    /**
     * 任务提交pda
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/task/commit_pda")
    public CommonResult<QmsInspectionTaskCommitVO> commitPda(@RequestBody QmsInspectionTaskCommitCommandInput input) {
        return qmsInspectionTaskCommandService.commitPda(input);
    }

    /**
     * qms货检单表列表
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<QmsInspectionTaskVO>> getPage(@RequestBody QmsInspectionTaskQueryInput input) {
        PageInfo<QmsInspectionTaskEntity> page = qmsInspectionTaskQueryService.getPage(input);
        PageInfo<QmsInspectionTaskVO> result = PageInfoConverter.toPageResp(
                page, QmsInspectionTaskAssembler::toQmsInspectionTaskVO);
        if (!CollectionUtils.isEmpty(result.getList())){
            List<String> skuList = result.getList().stream()
                    .map(QmsInspectionTaskVO::getSku)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                    LoginInfoThreadLocal.getTenantId(), skuList);

            List<Integer> warehouseNoList = result.getList().stream()
                    .filter(s -> s.getWarehouseNo() != null)
                    .map(s -> s.getWarehouseNo().intValue())
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> warehouseNameMap = warehouseWmsFacade
                    .queryWarehouseNameMap(warehouseNoList);

            for (QmsInspectionTaskVO vo : result.getList()) {
                String warehouseName = warehouseNameMap
                        .getOrDefault(vo.getWarehouseNo().intValue(), null);

                QmsInspectionTaskVOAssembler.aseemble(vo, goodsInfoDTOMap, null, warehouseName, null);
            }
        }

        return CommonResult.ok(result);
    }

    /**
     * 货检单获取详情
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/query/detail/{id}")
    public CommonResult<QmsInspectionTaskVO> detail(@PathVariable Long id) {
        QmsInspectionTaskEntity entity = qmsInspectionTaskQueryService.getDetail(id);
        if (entity == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "数据不存在");
        }

        QmsInspectionTaskVO qmsInspectionTaskVO = QmsInspectionTaskAssembler.toQmsInspectionTaskVO(entity);

        QmsInspectionTaskSnapshotEntity snapshotEntity = snapshotQueryRepository.selectByQmsInspectionTaskId(entity.getId());

        GoodsInfoDTO goodsInfoDTO = goodsReadFacade.findGoodsInfoBySkuList(
                LoginInfoThreadLocal.getTenantId(), entity.getSku());
        Integer goodsProcessType = goodsInfoDTO == null || goodsInfoDTO.getMaterialType() == null ?
                QmsInspectScaleProcessTypeEnum.PRODUCT_INSP.getCode() : goodsInfoDTO.getMaterialType();
        List<QmsInspectionScaleEntity> scaleEntityListProduct = scaleQueryRepository.selectByGoodsProcessType(
                goodsProcessType);

        String warehouseName = warehouseWmsFacade.queryWarehouseName(
                entity.getWarehouseNo() == null ? null : entity.getWarehouseNo().intValue());

        Map<String, GoodsInfoDTO> goodsInfoDTOMap = new HashMap<>();
        if (goodsInfoDTO != null) {
            goodsInfoDTOMap.put(goodsInfoDTO.getSku(), goodsInfoDTO);
        }

        QmsInspectionTaskVOAssembler.aseemble(qmsInspectionTaskVO, goodsInfoDTOMap, snapshotEntity, warehouseName,
                scaleEntityListProduct);
        return CommonResult.ok(qmsInspectionTaskVO);
    }


    /**
     * 新增货检单
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/upsert/insert")
    public CommonResult<QmsInspectionTaskVO> insert(@RequestBody QmsInspectionTaskCreateCommandInput input) {
        return qmsInspectionTaskCommandService.insert(input);
    }


    /**
     * 指定日期入库任务新增
     *
     * @return QmsInspectionTaskVO
     */
    @PostMapping(value = "/upsert/insertBySomeDayStockInTask/{date}")
    public CommonResult<QmsInspectionTaskVO> insertBySomeDayStockInTask(@PathVariable String date) {
        if (StringUtils.isEmpty(date)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        qmsInspectionTaskCommandService.insertBySomeDayStockInTask(DateUtil.parseLocalDate(date));
        return CommonResult.ok(null);
    }

    @PostMapping(value = "/upsert/insertBySomeDayStockInTask/byId/{id}")
    public CommonResult<QmsInspectionTaskVO> insertBySomeDayStockInTask(@PathVariable Long id) {
        if (StringUtils.isEmpty(id)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(id);
        if (stockTaskStorage == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "数据不存在");
        }
        qmsInspectionTaskCommandService.insertBySomeDayStockInTask(stockTaskStorage);
        return CommonResult.ok(null);
    }

    /**
     * 复检货检单
     *
     * @return
     */
    @PostMapping(value = "/upsert/reInsp/{id}")
    public CommonResult<Integer> reInsp(@PathVariable Long id) {
        return qmsInspectionTaskCommandService.reInsp(id);
    }

    /**
     * 重开货检单
     *
     * @return
     */
    @PostMapping(value = "/upsert/reOpen/{id}")
    @RequiresPermissions(value = {"qms-inspection-task:reopen"}, logical = Logical.OR)
    public CommonResult<Integer> reOpen(@PathVariable Long id) {
        return qmsInspectionTaskCommandService.reOpen(id);
    }


    /**
     * 关闭货检单
     *
     * @return
     */
    @PostMapping(value = "/upsert/close/{id}")
    public CommonResult<Integer> close(@PathVariable Long id) {
        return qmsInspectionTaskCommandService.close(id);
    }


    /**
     * 拒收关闭货检单
     *
     * @return
     */
    @PostMapping(value = "/upsert/rejectClose/{id}")
    public CommonResult<Integer> rejectClose(@PathVariable Long id,
                                             @RequestBody QmsInspectionTaskRejectCloseCommandInput commandInput) {
        return qmsInspectionTaskCommandService.rejectClose(id, commandInput.getRemark());
    }

    /**
     * 拒收变更收货方式
     *
     * - 收货方式为拒收时，可以操作切换收货方式“让步接收、特批入库”，切换收货方式为“让步接收、特批入库”时 给出审批证明的上传入口，
     * 操作按钮变更为“提交、返回”，切换为“拒收”时操作按钮为“无需复检、复检、返回”
     *
     * @return
     */
    @PostMapping(value = "/upsert/rejectChange/{id}")
    public CommonResult<Integer> rejectChange(@PathVariable Long id,
                                             @RequestBody QmsInspectionTaskRejectChangeCommandInput commandInput) {
        return qmsInspectionTaskCommandService.rejectChange(id, commandInput);
    }
}

