package net.summerfarm.wms.api.h5.instore;

import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderBatchCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderCommand;

/**
 * 入库单服务
 *
 * <AUTHOR>
 */
public interface InBoundOrderCommandService {
    /**
     * 创建入库单
     * @param command c
     */
    void createInBoundOrder(InBoundOrderCommand command);

    /**
     * 批量创建入库单
     * @param inBoundOrderBatchCommand c
     */
    void batchCreateInBoundOrder(InBoundOrderBatchCommand inBoundOrderBatchCommand);
}
