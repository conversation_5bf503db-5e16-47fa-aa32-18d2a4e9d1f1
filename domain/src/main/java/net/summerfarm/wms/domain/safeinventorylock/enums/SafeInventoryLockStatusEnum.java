package net.summerfarm.wms.domain.safeinventorylock.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 安全库存锁定状态枚举
 * <AUTHOR>
 * @date 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum SafeInventoryLockStatusEnum {

    RELEASED(0, "已释放"),
    LOCKED(1, "已锁定"),
    ;

    private final Integer code;
    private final String desc;

    public static SafeInventoryLockStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SafeInventoryLockStatusEnum lockStatus : values()) {
            if (lockStatus.getCode().equals(code)) {
                return lockStatus;
            }
        }
        return null;
    }
}
