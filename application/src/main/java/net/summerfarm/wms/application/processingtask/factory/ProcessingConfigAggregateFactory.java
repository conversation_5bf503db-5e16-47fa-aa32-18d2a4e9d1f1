package net.summerfarm.wms.application.processingtask.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingMaterialConfigUpsetReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigProductSkuSpecDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigUpsetReqDTO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingMaterialModelTypeEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigCommandParam;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.common.util.StringUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingConfigInvalidEnum;
import net.summerfarm.wms.domain.products.domainobject.Product;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class ProcessingConfigAggregateFactory {

    public static ProcessingConfigCreateAggregate newCreateInstance(
            Long tenantId,
            ProcessingConfigUpsetReqDTO reqDTO,
            Map<String, Product> materialSpuMap,
            Map<Long, Product> materialSpuIdMap,
            Product productSpu
    ) {
        ProcessingConfig processingConfig = newProcessingConfig(
                reqDTO, productSpu
        );

        List<SkuSpec> skuSpecList = newSkuSpecList(
                reqDTO, productSpu
        );

        List<WmsProcessingMaterialConfigCommandParam> materialConfigEntityList = newMaterialConfigEntityList(
                reqDTO, tenantId,materialSpuMap,materialSpuIdMap,productSpu
        );

        ProcessingConfigCreateAggregate aggregate = new ProcessingConfigCreateAggregate();
        aggregate.setProcessingConfig(processingConfig);
        aggregate.setSkuSpecList(skuSpecList);
        aggregate.setProcessingMaterialConfigEntityList(materialConfigEntityList);

        return aggregate;
    }

    private static List<WmsProcessingMaterialConfigCommandParam> newMaterialConfigEntityList(
            ProcessingConfigUpsetReqDTO reqDTO, Long tenantId,
            Map<String, Product> materialSpuMap, Map<Long, Product> materialSpuIdMap,
            Product productSpu) {
        List<WmsProcessingMaterialConfigCommandParam> materialConfigEntityList = new ArrayList<>();
        for (ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO : reqDTO.getProcessingMaterialConfigList()) {
            Product materialSpu = WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    materialSpuMap.get(materialConfigUpsetReqDTO.getMaterialSkuCode()) :
                    materialSpuIdMap.get(materialConfigUpsetReqDTO.getMaterialSkuSaasId());
            if (materialSpu == null) {
                log.error("物料不存在 {} {}",
                        materialConfigUpsetReqDTO.getMaterialSkuCode(),
                        materialConfigUpsetReqDTO.getMaterialSkuSaasId());
                throw new BizException("物料不存在", ErrorCodeNew.PARAM_ERROR);
            }

            WmsProcessingMaterialConfigCommandParam entity = new WmsProcessingMaterialConfigCommandParam();

//            entity.setProcessingConfigId();
            entity.setMaterialSkuCode(materialSpu.getSku());
            entity.setMaterialSkuName(materialSpu.getPdName());
            entity.setMaterialSkuWeight(materialConfigUpsetReqDTO.getMaterialSkuWeight());
            entity.setMaterialSkuUnit(materialConfigUpsetReqDTO.getMaterialSkuUnit());
            entity.setMaterialSkuUnitDesc(materialSpu.getSpecification());
            entity.setMaterialSkuRatioNum(materialConfigUpsetReqDTO.getMaterialSkuRatioNum());

            entity.setProductSkuCode(productSpu.getSku());
            entity.setProductSkuName(productSpu.getPdName());
            entity.setProductSkuWeight(reqDTO.getProductSkuWeight());
            entity.setProductSkuUnit(reqDTO.getProductSkuUnit());
            entity.setProductSkuUnitDesc(productSpu.getSpecification());

            entity.setCreator(LoginInfoThreadLocal.getCurrentUserName());
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
            entity.setUpdateTime(LocalDateTime.now());
            entity.setDeleteFlag(DeleteFlagEnum.NO.getValue());

            materialConfigEntityList.add(entity);
        }

        return materialConfigEntityList;
    }


    public static ProcessingConfigUpdateAggregate newUpdateInstance(
            Long tenantId,
            ProcessingConfig queryProcessingConfig,
            ProcessingConfigUpsetReqDTO reqDTO,
            Map<String, Product> materialSpuMap,
            Map<Long, Product> materialSpuIdMap,
            Product productSpu
    ) {
        ProcessingConfig updateProcessingConfig = updateProcessingConfig(
                queryProcessingConfig,
                reqDTO
        );

        List<SkuSpec> skuSpecList = newSkuSpecList(
                reqDTO, productSpu
        );

        List<WmsProcessingMaterialConfigCommandParam> materialConfigEntityList = newMaterialConfigEntityList(
                reqDTO, tenantId,materialSpuMap,materialSpuIdMap,productSpu
        );

        ProcessingConfigUpdateAggregate aggregate = new ProcessingConfigUpdateAggregate();
        aggregate.setProcessingConfig(updateProcessingConfig);
        aggregate.setSkuSpecList(skuSpecList);
        aggregate.setProcessingMaterialConfigEntityList(materialConfigEntityList);


        return aggregate;
    }


    protected static ProcessingConfig newProcessingConfig(
            ProcessingConfigUpsetReqDTO reqDTO,
            Product productSpu) {
        ProcessingConfig createProcessingConfig = new ProcessingConfig();
        createProcessingConfig.setWarehouseNo(reqDTO.getWarehouseNo());
        createProcessingConfig.setType(reqDTO.getType());
        createProcessingConfig.setInvalid(ProcessingConfigInvalidEnum.NO.getValue());

//        createProcessingConfig.setMaterialSkuCode(materialSpu.getSku());
//        createProcessingConfig.setMaterialSkuName(materialSpu.getPdName());
//        createProcessingConfig.setMaterialSkuWeight(reqDTO.getMaterialSkuWeight());
//        createProcessingConfig.setMaterialSkuUnit(reqDTO.getMaterialSkuUnit());
//        createProcessingConfig.setMaterialSkuUnitDesc(materialSpu.getSpecification());
        createProcessingConfig.setMaterialModel(ProcessingMaterialModelTypeEnum.MULTI_MATERIAL.getValue());

        createProcessingConfig.setProductSkuCode(productSpu.getSku());
        createProcessingConfig.setProductSkuName(productSpu.getPdName());
        createProcessingConfig.setProductSkuWeight(reqDTO.getProductSkuWeight());
        createProcessingConfig.setProductSkuUnit(reqDTO.getProductSkuUnit());
        createProcessingConfig.setProductSkuUnitDesc(productSpu.getSpecification());

        createProcessingConfig.setCreator(LoginInfoThreadLocal.getCurrentUserName());
        createProcessingConfig.setCreateTime(new Date());
        createProcessingConfig.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
        createProcessingConfig.setUpdateTime(new Date());
        createProcessingConfig.setDeleteFlag(DeleteFlagEnum.NO.getValue());

        createProcessingConfig.setProductSkuRatioNum(reqDTO.getProductSkuRatioNum());

        return createProcessingConfig;
    }

    protected static ProcessingConfig updateProcessingConfig(
            ProcessingConfig processingConfig,
            ProcessingConfigUpsetReqDTO reqDTO) {

        ProcessingConfig updateProcessingConfig = new ProcessingConfig();
        updateProcessingConfig.setWarehouseNo(processingConfig.getWarehouseNo());
        updateProcessingConfig.setId(processingConfig.getId());
        updateProcessingConfig.setType(reqDTO.getType());

//        updateProcessingConfig.setMaterialSkuWeight(reqDTO.getMaterialSkuWeight());
//        updateProcessingConfig.setMaterialSkuUnit(reqDTO.getMaterialSkuUnit());
        updateProcessingConfig.setMaterialModel(ProcessingMaterialModelTypeEnum.MULTI_MATERIAL.getValue());

        updateProcessingConfig.setProductSkuWeight(reqDTO.getProductSkuWeight());
        updateProcessingConfig.setProductSkuUnit(reqDTO.getProductSkuUnit());
        updateProcessingConfig.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
        updateProcessingConfig.setUpdateTime(new Date());

        updateProcessingConfig.setProductSkuRatioNum(reqDTO.getProductSkuRatioNum());

        return updateProcessingConfig;
    }

    protected static List<SkuSpec> newSkuSpecList(
            ProcessingConfigUpsetReqDTO reqDTO,
            Product productSpu
    ) {
        List<SkuSpec> skuSpecList = new ArrayList<>();

        for (ProcessingConfigProductSkuSpecDTO productSkuSpec : reqDTO.getProductSkuSpecList()) {
            BigDecimal productSkuSpecWeight = productSkuSpec.getProductSkuSpecWeight();
            if (productSkuSpecWeight == null ||
                    productSkuSpecWeight.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException(ErrorCodeNew.PARAM_ERROR);
            }
            if (reqDTO.getProductSkuWeight() == null ||
                    reqDTO.getProductSkuWeight().compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException(ErrorCodeNew.PARAM_ERROR);
            }

            // 整除判断
            BigDecimal number = productSkuSpecWeight.divide(
                    reqDTO.getProductSkuWeight(), 10, BigDecimal.ROUND_HALF_DOWN);
            if (new BigDecimal(number.intValue()).compareTo(number) != 0) {
                log.error("请求规格需要是sku规格的倍数 {} {} {}",
                        productSpu.getSku(), productSkuSpecWeight, reqDTO.getProductSkuWeight());
                throw new BizException("请求规格需要是sku规格的倍数", ErrorCodeNew.PARAM_ERROR);
            }
            // 倍数
            Integer multiple = number.intValue();

            // region 组装
            SkuSpec skuSpec = new SkuSpec();
            skuSpec.setWarehouseNo(reqDTO.getWarehouseNo());
            skuSpec.setType(reqDTO.getType());
            skuSpec.setInvalid(ProcessingConfigInvalidEnum.NO.getValue());

//            skuSpec.getProcessingConfigId()

//            skuSpec.setMaterialSkuCode(materialSpu.getSku());
//            skuSpec.setMaterialSkuName(materialSpu.getPdName());

            skuSpec.setProductSkuCode(productSpu.getSku());
            skuSpec.setProductSkuName(productSpu.getPdName());
            skuSpec.setProductSkuWeight(reqDTO.getProductSkuWeight());
            skuSpec.setProductSkuUnit(reqDTO.getProductSkuUnit());
            skuSpec.setProductSkuUnitDesc(productSpu.getSpecification());

            skuSpec.setProductSkuSpecWeight(productSkuSpec.getProductSkuSpecWeight());
            skuSpec.setProductSkuSpecUnit(productSkuSpec.getProductSkuSpecUnit());
            skuSpec.setProductSkuSpecUnitDesc(
                    multiple == 1 ? productSpu.getSpecification() :
                            StringUtil.replaceFirstNumberWithMultiple(
                                    productSpu.getSpecification(),
                                    multiple,
                                    String.format("请求SKU：%s 参数规格不包含数字，无法建立多规格", productSpu.getSku()))
            );

            skuSpec.setCreator(LoginInfoThreadLocal.getCurrentUserName());
            skuSpec.setCreateTime(new Date());
            skuSpec.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
            skuSpec.setUpdateTime(new Date());
            skuSpec.setDeleteFlag(DeleteFlagEnum.NO.getValue());

            // endregion

            skuSpecList.add(skuSpec);
        }

        return skuSpecList;
    }

}
