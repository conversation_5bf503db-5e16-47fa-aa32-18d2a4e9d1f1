package net.summerfarm.wms.application.instore.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.api.h5.crosswarehouse.CrossWarehouseCommandService;
import net.summerfarm.wms.api.h5.goods.SkuConvertCommandService;
import net.summerfarm.wms.api.h5.instore.InBoundOrderCommandService;
import net.summerfarm.wms.api.h5.instore.StockStorageCommandService;
import net.summerfarm.wms.api.h5.instore.dto.req.*;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStorageArrivalCargoLimitVO;
import net.summerfarm.wms.api.inner.instore.StockStorageInnerService;
import net.summerfarm.wms.application.acl.mq.bo.StockStorageDataBO;
import net.summerfarm.wms.application.common.enums.SaaSFileDownloadTypeEnum;
import net.summerfarm.wms.application.external.service.StockInExternalService;
import net.summerfarm.wms.application.instore.dto.CheckDTO;
import net.summerfarm.wms.application.instore.enums.StockTaskLogOperateType;
import net.summerfarm.wms.application.instore.factory.InBoundOrderFactory;
import net.summerfarm.wms.application.instore.factory.StockStorageCommandFactory;
import net.summerfarm.wms.application.inventory.factory.SaleInventoryCenterCommandReqFactory;
import net.summerfarm.wms.application.sku.enums.StorageLocation;
import net.summerfarm.wms.application.stocktask.enums.StockTaskStateEnum;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.application.stocktask.listener.event.CrossStockTaskGenerateEvent;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.enums.StockTaskStorageOptionFlagTypeEnum;
import net.summerfarm.wms.common.enums.SystemSourceEnum;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.mqUtil.MqUtil;
import net.summerfarm.wms.common.mqUtil.base.enums.MType;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.*;
import net.summerfarm.wms.domain.StoreRecord.enums.StoreRecordType;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.category.domainobject.Category;
import net.summerfarm.wms.domain.common.domainobject.Container;
import net.summerfarm.wms.domain.common.domainobject.ContainerQuery;
import net.summerfarm.wms.domain.common.domainobject.StoreTaskLog;
import net.summerfarm.wms.domain.common.repository.ContainerRepository;
import net.summerfarm.wms.domain.common.repository.StoreTaskLogRepository;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.crosswarehouse.domainobject.CrossWarehouseSortInfo;
import net.summerfarm.wms.domain.crosswarehouse.repository.CrossWarehouseSortInfoRepository;
import net.summerfarm.wms.domain.instore.domainobject.*;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.entity.WmsStockTaskStorageNoticeOrderEntity;
import net.summerfarm.wms.domain.instore.enums.*;
import net.summerfarm.wms.domain.instore.repository.*;
import net.summerfarm.wms.domain.products.BoardPositionQueryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.BoardPosition;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.stocktask.OutNoticeAbnormalDetailCommandRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItem;
import net.summerfarm.wms.domain.stocktask.enums.NoticeOrderStatusEnum;
import net.summerfarm.wms.facade.inventory.SaleInventoryCenterCommandFacade;
import net.summerfarm.wms.facade.inventory.dto.OrderReleaseBySpecifySkuReqDTO;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.summerfarm.wms.facade.saas.SaasDownloadFacade;
import net.summerfarm.wms.initConfig.CrossWarehouseArrivalCargoConfig;
import net.summerfarm.wms.manage.web.enums.StockTaskState;
import net.summerfarm.wms.manage.web.utils.OptionFlagUtil;
import net.summerfarm.wms.mq.inStore.StockTaskInStoreFinishDetail;
import net.summerfarm.wms.mq.inStore.StockTaskInStoreFinishEvent;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> ct
 * create at:  2022/11/21  11:07
 */
@Slf4j
@Service
public class StockStorageCommandServiceImpl implements StockStorageCommandService {

    @Resource
    StockTaskStorageRepository stockTaskStorageRepository;

    @Resource
    StockStorageItemRepository stockStorageItemRepository;

    @Resource
    StockStorageItemDetailRepository stockStorageItemDetailRepository;

    @Resource
    WarehouseStorageService warehouseStorageService;

    @Resource
    StockTaskStorageQueryRepository stockTaskStorageQueryRepository;

    @Resource
    InBoundOrderQueryRepository inBoundOrderQueryRepository;
    @Resource
    ProductRepository productRepository;
    @Resource
    StockTaskRepository stockTaskRepository;
    @Resource
    SaasDownloadFacade saasDownloadFacade;
    @Resource
    SkuConvertCommandService skuConvertCommandService;

    @Resource
    MqUtil mqUtil;
    @Resource
    AdminUtil adminUtil;
    @Resource
    MqProducer mqProducer;

    @Resource
    StoreTaskLogRepository storeTaskLogRepository;

    @Resource
    private BoardPositionQueryRepository boardPositionQueryRepository;
    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;
    @Resource
    @Lazy
    private InBoundOrderCommandService inBoundOrderCommandService;
    @Resource
    private StockStorageCommandFactory stockStorageCommandFactory;
    @Resource
    private StockStorageInnerService stockStorageInnerService;
    @Resource
    private InBoundOrderFactory inBoundOrderFactory;
    @Resource
    private ContainerRepository containerRepository;

    @Resource
    private InBoundOrderMsgService inBoundOrderMsgService;
    @Resource
    private CrossWarehouseCommandService crossWarehouseCommandService;
    @Resource
    private OutNoticeAbnormalDetailCommandRepository outNoticeAbnormalDetailCommandRepository;
    @Resource
    private SaleInventoryCenterCommandReqFactory saleInventoryCenterCommandReqFactory;
    @Resource
    private SaleInventoryCenterCommandFacade saleInventoryCenterCommandFacade;
    @Resource
    private CrossWarehouseSortInfoRepository crossWarehouseSortInfoRepository;
    @Resource
    private CrossWarehouseArrivalCargoConfig crossWarehouseArrivalCargoConfig;
    @Resource
    private StockTaskStorageMsgService stockTaskStorageMsgService;

    @Resource
    private StockInExternalService stockInExternalService;
    @Resource
    private WmsStockTaskStorageNoticeOrderCommandRepository wmsStockTaskStorageNoticeOrderCommandRepository;
    @Resource
    private WmsStockTaskStorageNoticeOrderQueryRepository wmsStockTaskStorageNoticeOrderQueryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperatorAnnotation
    public Long createStockStorage(StockStorageCommandDTO stockStorageCommandDTO) {
        List<StockStorageItemCommandDTO> itemCommandDTOList = stockStorageCommandDTO.getStockStorageItemCommandDTOList();
        //生成入库任务
        Set<Integer> categoryTypeSet = itemCommandDTOList.stream().filter(x -> !Objects.isNull(x.getCategoryType()))
                .map(StockStorageItemCommandDTO::getCategoryType).collect(Collectors.toSet());
        Integer category = getCategory(categoryTypeSet);
        Integer inWarehouseNo = stockStorageCommandDTO.getInWarehouseNo();
        // 租户id
        Long tenantId = stockStorageCommandDTO.getTenantId();
        //旧任务
        this.handleIfExternal(stockStorageCommandDTO, category);
        Long taskId = stockStorageCommandDTO.getTaskId();
        if (Objects.isNull(taskId)) {
            Integer adminId = Objects.isNull(stockStorageCommandDTO.getAdminId()) ? 0 : stockStorageCommandDTO.getAdminId().intValue();
            StockTask stockTask = StockTask.builder()
                    .adminId(adminId)
                    .areaNo(inWarehouseNo)
                    .type(stockStorageCommandDTO.getType())
                    .taskNo(stockStorageCommandDTO.getSourceNo())
                    .state(StockTaskStateEnum.WAIT_IN_OUT.getId())
                    .tenantId(tenantId)
                    .systemSource(stockStorageCommandDTO.getSystemSource())
                    .expectTime(stockStorageCommandDTO.getExpectTime())
                    .build();
            Long stockTaskId = stockTaskRepository.saveStockTask(stockTask);
            stockStorageCommandDTO.setTaskId(stockTaskId);
            List<StockStorageItemCommandDTO> stockStorageItemCommandDTOList = stockStorageCommandDTO.getStockStorageItemCommandDTOList();
            stockStorageItemCommandDTOList.forEach(item -> {
                StockTaskItem stockTaskItem = new StockTaskItem();
                stockTaskItem.setStockTaskId(stockTaskId.intValue());
                stockTaskItem.setSku(item.getSku());
                stockTaskItem.setQuantity(item.getQuantity());
                stockTaskItem.setActualQuantity(NumberUtils.INTEGER_ZERO);
                stockTaskRepository.createStockTaskItem(stockTaskItem);
            });
            log.info("stockTaskId={}", stockTaskId);
        }

        // 查询入库通知单打标
        Long optionFlag = StockTaskStorageOptionFlagTypeEnum.DEFAULT.getCode();
        if (!CollectionUtils.isEmpty(stockStorageCommandDTO.getGoodsRecycleOrderNos())) {
            List<WmsStockTaskStorageNoticeOrderEntity> wmsStockTaskStorageNoticeOrderEntityList = wmsStockTaskStorageNoticeOrderQueryRepository.selectByGoodsRecycleOrderNos(stockStorageCommandDTO.getGoodsRecycleOrderNos());
            if (!CollectionUtils.isEmpty(wmsStockTaskStorageNoticeOrderEntityList)
                    && NoticeStockTaskStorageOrderTypeEnum.INTERCEPT_STORE_IN_DELAY.getAfterSaleOrderType().equals(wmsStockTaskStorageNoticeOrderEntityList.get(0).getAferSaleOrderType())) {
                optionFlag = OptionFlagUtil.setValueEffective(optionFlag, StockTaskStorageOptionFlagTypeEnum.INTERCEPT_DELAY.getCode());
            }
        }

        WarehouseStorageCenter storageCenter = warehouseStorageService.selectByWarehouseNo(inWarehouseNo);
        StockTaskStorage save = StockTaskStorage.builder()
                .reason(stockStorageCommandDTO.getReason())
                .state(StockTaskStorageStateEnum.NORMAL.getId())
                .adminId(stockStorageCommandDTO.getAdminId())
                .expectTime(stockStorageCommandDTO.getExpectTime())
                .remark(stockStorageCommandDTO.getRemark())
                .outWarehouseNo(stockStorageCommandDTO.getOutWarehouseNo())
                .inWarehouseNo(stockStorageCommandDTO.getInWarehouseNo())
                .inWarehouseName(storageCenter.getWarehouseName())
                .outWarehouseName(stockStorageCommandDTO.getOutWarehouseName())
                .processState(StockTaskStorageStateEnum.NORMAL.getId())
                .type(stockStorageCommandDTO.getType())
                .category(category)
                .sourceId(stockStorageCommandDTO.getSourceNo())
                .stockTaskId(stockStorageCommandDTO.getTaskId())
                .ownership(stockStorageCommandDTO.getOwnership())
                .remark(stockStorageCommandDTO.getRemark())
                .operatorName(stockStorageCommandDTO.getUserOperatorName())
                .afterSaleOrder(stockStorageCommandDTO.getAfterSaleOrderNo())
                .tenantId(stockStorageCommandDTO.getTenantId())
                .fulfillmentNo(stockStorageCommandDTO.getFulfillmentNo())
                .receiptUrl(stockStorageCommandDTO.getReceiptUrl())
                .psoNo(stockStorageCommandDTO.getPsoNo())
                .uniqueKey(stockStorageCommandDTO.getUniqueKey())
                .systemSource(stockStorageCommandDTO.getSystemSource())
                .thirdOrderNo(stockStorageCommandDTO.getThirdOrderNo())
                .purchaseMode(stockStorageCommandDTO.getPurchaseMode())
                .optionFlag(optionFlag)
                .sourceOrderNo(stockStorageCommandDTO.getSourceOrderNo())
                .build();
        Long stockTaskStorageId = stockTaskStorageRepository.saveStockTaskStorage(save);

        //双写
        Long stockTaskId = Objects.isNull(stockStorageCommandDTO.getTaskId()) ? stockTaskStorageId : stockStorageCommandDTO.getTaskId();
        //插入入库信息
        List<String> skus = itemCommandDTOList.stream().map(StockStorageItemCommandDTO::getSku).collect(Collectors.toList());

        List<BoardPosition> boardPositions = boardPositionQueryRepository.findBoardPositions(skus, inWarehouseNo.longValue());
        Map<String, BoardPosition> bpMap = boardPositions.stream().collect(Collectors.toMap(BoardPosition::getSku, Function.identity(), (o1, o2) -> o1));

        Lists.partition(skus, WmsConstant.PARTITION_SIZE).forEach(list -> {
            HashSet<String> skuSet = new HashSet<>(list);
            List<Product> products = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                    .warehouseNo(inWarehouseNo.longValue()).skus(list).tenantId(tenantId).build());
            Map<String, Product> productMap = products.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));

            itemCommandDTOList.stream()
                    .filter(item -> skuSet.contains(item.getSku()))
                    .forEach(item -> {
                        Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                        BoardPosition bp = bpMap.get(item.getSku());
                        String stackRule = Objects.nonNull(bp) && Objects.nonNull(bp.getLayerTotal()) &&
                                Objects.nonNull(bp.getLayerHeight()) ? bp.getLayerHeight() + "*" + bp.getLayerTotal() : null;
                        StockStorageItem saveStorageItem = StockStorageItem.builder()
                                .sku(item.getSku())
                                .pdName(item.getPdName())
                                .stockTaskStorageId(stockTaskStorageId)
                                .stockTaskId(stockTaskId)
                                .quantity(item.getQuantity())
                                .supplier(item.getSupplier())
                                .packaging(item.getPackaging())
                                .specification(item.getSpecification())
                                .categoryType(item.getCategoryType())
                                .temperature(product.getTemperature())
                                .skuType(item.getSkuType())
                                .supplierId(item.getSupplierId())
                                .tenantId(item.getTenantId())
                                .afterSaleOrderNo(item.getAfterSaleOrderNo())
                                .fulfillmentNo(item.getFulfillmentNo())
                                .afterSaleReason(item.getAfterSaleReason())
                                .category(product.getCategory())
                                .stackRule(stackRule)
                                .customerSkuCode(product.getCustomSkuCode())
                                .build();
                        saveStorageItem.initCategoryType();
                        List<StockStorageItemDetailCommandDTO> detailCommandDTOList = item.getItemDetailCommandDTOList();
                        Long id = stockStorageItemRepository.saveStockStorageItem(saveStorageItem);
                        if (!CollectionUtils.isEmpty(detailCommandDTOList)) {
                            List<StockStorageItemDetail> itemDetailList = detailCommandDTOList.stream().map(x -> StockStorageItemDetail.builder()
                                    .shouldQuantity(x.getQuantity())
                                    .productionDate(x.getProductionDate())
                                    .qualityDate(x.getQualityDate())
                                    .purchaseNo(x.getPurchaseNo())
                                    .actualInQuantity(0)
                                    .specify(0)
                                    .tenantId(x.getTenantId())
                                    .stockStorageItemId(id).build()).collect(Collectors.toList());
                            stockStorageItemDetailRepository.saveBatchStockStorageItemDetail(itemDetailList);
                        }
                    });
        });


        if (!Objects.equals(stockStorageCommandDTO.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            StockStorageDataBO stockStorageDataBO = StockStorageDataBO.builder().type(stockStorageCommandDTO.getType())
                    .taskId(taskId)
                    .stockTaskId(stockStorageCommandDTO.getTaskId())
                    .tenantId(stockStorageCommandDTO.getTenantId())
                    .afterSaleOrderNo(stockStorageCommandDTO.getAfterSaleOrderNo())
                    .build();
            //任务完成
            mqUtil.sendMqData(RocketMqMessageConstant.MALL_LIST, stockStorageDataBO, MType.CREATE_IN_TASK.name());
            // 消息通知
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    stockTaskStorageMsgService.sendStockStorageCreatedMsg(stockTaskStorageId);
                }
            });

        }
        if (stockStorageCommandDTO.getType().equals(OperationType.OUT_MORE_IN.getId())) {
            List<Long> abnormalNoticeIds = stockStorageCommandDTO.getStockStorageItemCommandDTOList().stream()
                    .map(StockStorageItemCommandDTO::getAbnormalNoticeId)
                    .collect(Collectors.toList());
            abnormalNoticeIds = abnormalNoticeIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            outNoticeAbnormalDetailCommandRepository.finishAbnormalDetail(abnormalNoticeIds);
        }
        // 更新入库通知单
        if (!CollectionUtils.isEmpty(stockStorageCommandDTO.getGoodsRecycleOrderNos())) {
            List<WmsStockTaskStorageNoticeOrderEntity> wmsStockTaskStorageNoticeOrderEntityList = wmsStockTaskStorageNoticeOrderQueryRepository.selectByGoodsRecycleOrderNos(stockStorageCommandDTO.getGoodsRecycleOrderNos());
            if (!CollectionUtils.isEmpty(wmsStockTaskStorageNoticeOrderEntityList)) {
                for (WmsStockTaskStorageNoticeOrderEntity wmsStockTaskStorageNoticeOrderEntity : wmsStockTaskStorageNoticeOrderEntityList) {
                    wmsStockTaskStorageNoticeOrderCommandRepository.updateStorageIdAndStatusById(wmsStockTaskStorageNoticeOrderEntity.getId(), stockTaskStorageId, NoticeOrderStatusEnum.FINISHED.getStatus());
                }
            }
        }

        return stockTaskStorageId;
    }

    private void handleIfExternal(StockStorageCommandDTO stockStorageCommandDTO, Integer category) {
        if (!Objects.equals(stockStorageCommandDTO.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            return;
        }
        StockTask stockTask = StockTask.builder()
                .taskNo(stockStorageCommandDTO.getSourceNo())
                .category(category.toString())
                .areaNo(stockStorageCommandDTO.getInWarehouseNo())
                .type(stockStorageCommandDTO.getType())
                .expectTime(stockStorageCommandDTO.getExpectTime())
                .state(StockTaskState.WAIT_IN_OUT.getId())
                .addtime(LocalDateTime.now())
                .tenantId(stockStorageCommandDTO.getTenantId())
                .systemSource(stockStorageCommandDTO.getSystemSource())
                .build();
        Long taskId = stockTaskRepository.insertStockTask(stockTask);
        List<StockStorageItemCommandDTO> stockStorageItemCommandDTOList = stockStorageCommandDTO.getStockStorageItemCommandDTOList();
        stockStorageItemCommandDTOList.forEach(item -> {
            StockTaskItem stockTaskItem = new StockTaskItem();
            stockTaskItem.setStockTaskId(taskId.intValue());
            stockTaskItem.setSku(item.getSku());
            stockTaskItem.setQuantity(item.getQuantity());
            stockTaskItem.setActualQuantity(NumberUtils.INTEGER_ZERO);
            stockTaskRepository.createStockTaskItem(stockTaskItem);
        });
        stockStorageCommandDTO.setTaskId(taskId);
    }

    @Override
    @OperatorAnnotation
    @Transactional(rollbackFor = Exception.class)
    public void closeStockStorage(StockStorageCompleteReqDTO completeReqDTO) {

        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(completeReqDTO.getStockStorageId());
        //校验任务状态
        if (!Objects.equals(stockTaskStorage.getState(), StockTaskStorageStateEnum.NORMAL.getId())) {
            throw new BizException(ErrorCode.CLOSE_STATE_COMPLETE);
        }
        if (CheckDTO.cantCloseTask.contains(stockTaskStorage.getType())) {
            throw new net.xianmu.common.exception.BizException("该入库类型不允许关闭任务");
        }
        if (!Objects.equals(stockTaskStorage.getProcessState(), StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId())) {
            if (CheckDTO.cantCloseStockStorageType.contains(stockTaskStorage.getType())) {
                if (Objects.equals(stockTaskStorage.getProcessState(), StockTaskStorageCabinetDelicacyProcessStateEnum.RECEIVED.getId())) {
                    List<InBoundOrderDetail> inBoundOrderDetailByTaskId = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder()
                            .taskId(stockTaskStorage.getId())
                            .build());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inBoundOrderDetailByTaskId)) {
                        throw new BizException(ErrorCode.CLOSE_STATE_ERR.getMessage());
                    }
                }
            }
        }

        StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder()
                .id(completeReqDTO.getStockStorageId())
                .adminId(completeReqDTO.getAdminId())
                .operatorName(completeReqDTO.getUserOperatorName())
                .state(StockTaskStorageStateEnum.CLOSE.getId())
                .closeReason(completeReqDTO.getReason())
                .fileAddress(CollectionUtils.isEmpty(completeReqDTO.getFileAddress()) ? Lists.newArrayList() : completeReqDTO.getFileAddress())
                .build();
        stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);

        // 发送完成入库消息
        sendInStoreTaskFinishWithState(stockTaskStorage.getId(), StockTaskStorageStateEnum.CLOSE.getId());

        StockStorageDataBO stockStorageDataBO = StockStorageDataBO.builder().type(stockTaskStorage.getType())
                .taskId(stockTaskStorage.getId())
                .stockTaskId(stockTaskStorage.getStockTaskId())
//                .sourceId(stockTaskStorage.getSourceId())
                .tenantId(stockTaskStorage.getTenantId())
                .afterSaleOrderNo(stockTaskStorage.getAfterSaleOrder())
                .build();
        mqUtil.sendMqData(RocketMqMessageConstant.MALL_LIST, stockStorageDataBO, MType.CLOSE_IN_STORE.name());
        inBoundOrderMsgService.sendStockStorageFinishNotice(completeReqDTO.getStockStorageId());

        // 关闭任务 - 对外通知取消入库任务
        stockInExternalService.cancelStockInTask(stockTaskStorage);
    }


    @Override
    @OperatorAnnotation
    @Transactional(rollbackFor = Exception.class)
    public void completeStockStorage(StockStorageCompleteReqDTO completeReqDTO) {
        Long stockStorageId = completeReqDTO.getStockStorageId();
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        Boolean open = warehouseConfigRepository.openCabinetManagement(stockTaskStorage.getInWarehouseNo());
        StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder()
                .id(completeReqDTO.getStockStorageId())
                .adminId(completeReqDTO.getAdminId())
                .state(StockTaskStorageStateEnum.FINISH.getId())
                .processState(StockTaskStorageProcessStateEnums.ALL_IN_STORE.getId())
                .mismatchReason(completeReqDTO.getReason())
                .receivingState(Boolean.TRUE.equals(open) ? StockTaskStorageReceivingStateEnum.FINISHED.getId() : null)
                .build();
        stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);
        StockStorageDataBO stockStorageDataBO = StockStorageDataBO.builder().type(stockTaskStorage.getType())
                .adminId(stockTaskStorage.getAdminId())
                .operatorName(stockTaskStorage.getOperatorName())
                .taskId(stockTaskStorage.getId())
                .stockTaskId(stockTaskStorage.getStockTaskId())
//                .sourceId(stockTaskStorage.getSourceId())
                .tenantId(stockTaskStorage.getTenantId())
                .afterSaleOrderNo(stockTaskStorage.getAfterSaleOrder())
                .build();
        //
        if (Objects.equals(stockTaskStorage.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            return;
        } else {
            mqUtil.sendMqData(RocketMqMessageConstant.MALL_LIST, stockStorageDataBO, MType.COMPLETE_IN_STORE.name());
            inBoundOrderMsgService.sendStockStorageFinishNotice(stockTaskStorage.getId());
        }
        if (Objects.nonNull(stockTaskStorage.getFulfillmentNo())) {
            // 入库完成发送消息
            sendInStoreTaskFinishWithState(stockTaskStorage.getId(), StockTaskStorageStateEnum.FINISH.getId());
        }
    }

    /**
     * 发送完成入库消息（越库入库异常完成）
     * @param stockStorageId
     * @param state
     * @param inboundQuantity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendInStoreTaskFinishForCrossAbnormal(Long stockStorageId, Integer state, Integer inboundQuantity) {
        log.info("越库入库异常完成，根据状态发送完成入库消息，入库任务：{}，状态：{}，已入库数量：{}", stockStorageId, state, inboundQuantity);
        // 入库任务为空 或者 入库任务未完成不发完成入库消息 任务状态不是已完成和已关闭的状态
        if (null == stockStorageId ||
                (!Objects.equals(StockTaskStorageStateEnum.FINISH.getId(), state) &&
                        !Objects.equals(StockTaskStorageStateEnum.CLOSE.getId(), state))) {
            log.info("越库入库异常完成，根据状态入库任务未完成，任务id：{}", stockStorageId);
            return;
        }
        // 越库入库投线任务未生成完成就已进行异常操作，由投线任务生成完成触发入库完成消息
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        log.info("越库入库异常完成，查询入库任务：{}，任务id：{}", JSON.toJSONString(stockTaskStorage), stockStorageId);
        if (StockTaskTypeEnum.CROSS_WAREHOUSE_IN.getId() != stockTaskStorage.getType()) {
            log.info("越库入库异常完成，非越库入库类型，任务id：{}", stockStorageId);
            return;
        }
        if (!StringUtils.isEmpty(stockTaskStorage.getPsoNo())) {
            // 查询分拣分配明细
            List<CrossWarehouseSortInfo> warehouseSortInfos = crossWarehouseSortInfoRepository.findByPsoNo(stockTaskStorage.getPsoNo());
            if (inboundQuantity > 0 && CollectionUtils.isEmpty(warehouseSortInfos)) {
                return;
            }
            Integer crossQuantitySum = warehouseSortInfos.stream().map(CrossWarehouseSortInfo::getGenerateQuantity).reduce(Integer::sum).orElse(0);
            Integer shelveQuantitySum = warehouseSortInfos.stream().map(CrossWarehouseSortInfo::getShelveQuantity).reduce(Integer::sum).orElse(0);
            log.info("越库入库异常完成，已入库数量：{}，已投线数量：{}，已上架数量：{}，入库任务：{}", inboundQuantity, crossQuantitySum, shelveQuantitySum, stockStorageId);
            if (inboundQuantity == crossQuantitySum + shelveQuantitySum) {
                exeStockTaskStorageFinishedMsg(stockStorageId);
            }
        }

    }

    /**
     * 发送完成入库消息（状态控制）
     * @param stockStorageId
     * @param state
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendInStoreTaskFinishWithState(Long stockStorageId, Integer state) {
        log.info("根据状态发送完成入库消息，入库任务：{}，状态：{}", stockStorageId, state);
        // 入库任务为空 或者 入库任务未完成不发完成入库消息 任务状态不是已完成和已关闭的状态
        if (null == stockStorageId ||
                (!Objects.equals(StockTaskStorageStateEnum.FINISH.getId(), state) &&
                        !Objects.equals(StockTaskStorageStateEnum.CLOSE.getId(), state))) {
            log.info("根据状态入库任务未完成，任务id：{}", stockStorageId);
            return;
        }
        exeStockTaskStorageFinishedMsg(stockStorageId);
    }

    /**
     * 发送完成入库消息
     *
     * @param stockStorageId 入库任务id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendInStoreTaskFinish(Long stockStorageId) {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        log.info("发送完成入库消息，入库任务：{}，：{}", stockStorageId, JSON.toJSONString(stockTaskStorage));
        // 入库任务为空 或者 入库任务未完成不发完成入库消息 任务状态不是已完成和已关闭的状态
        if (stockTaskStorage == null ||
                (!Objects.equals(StockTaskStorageStateEnum.FINISH.getId(), stockTaskStorage.getState()) &&
                        !Objects.equals(StockTaskStorageStateEnum.CLOSE.getId(), stockTaskStorage.getState()))) {
            log.info("入库任务未完成，任务id：{}", stockStorageId);
            return;
        }
        exeStockTaskStorageFinishedMsg(stockStorageId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void exeStockTaskStorageFinishedMsg(Long stockStorageId) {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        log.info("执行发送完成入库消息，入库任务：{}，：{}", stockStorageId, JSON.toJSONString(stockTaskStorage));
        List<StockTaskInStoreFinishDetail> inboundInfoList = Lists.newArrayList();
        String externalNo = stockTaskStorage.getSourceId();
        // 入库任务是否有采购供应单号
        if (!StringUtils.isEmpty(stockTaskStorage.getPsoNo())) {
            // 查询分拣分配明细
            List<CrossWarehouseSortInfo> warehouseSortInfos = crossWarehouseSortInfoRepository.findByPsoNo(stockTaskStorage.getPsoNo());
            if (!CollectionUtils.isEmpty(warehouseSortInfos)) {
                // 转成回告入库详情
                inboundInfoList = warehouseSortInfos.stream().map(it ->
                                StockTaskInStoreFinishDetail.builder()
                                        .warehouseNo(it.getWarehouseNo())
                                        .fulfillmentNo(it.getFulfillmentNo())
                                        .skuCode(it.getSku())
                                        .initQuantity(it.getInitQuantity())
                                        .shouldQuantity(it.getTotalQuantity())
                                        .actualQuantity(it.getGenerateQuantity())
                                        .shelveQuantity(it.getShelveQuantity())
                                        .tenantId(it.getTenantId())
                                        .build())
                        .collect(Collectors.toList());
            }
            externalNo = stockTaskStorage.getPsoNo();
        }

        // 入库完成发送消息
        StockTaskInStoreFinishEvent event = StockTaskInStoreFinishEvent.builder()
                .inStoreTaskId(stockTaskStorage.getId())
                .tenantId(stockTaskStorage.getTenantId())
                .externalNo(externalNo)
                .fulfillmentNo(stockTaskStorage.getFulfillmentNo())
                .type(stockTaskStorage.getType())
                .mismatchReason(stockTaskStorage.getCloseReason())
                .purchaseMode(stockTaskStorage.getPurchaseMode())
                .fileAddress(stockTaskStorage.getFileAddress())
                .operateTime(stockTaskStorage.getUpdateTime())
                .inboundInfoList(inboundInfoList)
                .build();

        // 事务后发送消息内容
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                mqProducer.send(Global.STOCK_TASK, "tag_wms_in_store_task_finish", event);
                log.info("入库任务完成回告通知 taskId={} data={}", event.getInStoreTaskId(), JSON.toJSONString(event));
            }
        });
    }

    @Override
    @OperatorAnnotation
    public void downStockStorage(StockStorageDownReqDTO storageDownReqDTO) {
        log.info("入库任务开始导出:{}", JSON.toJSONString(storageDownReqDTO));
        Long stockStorageId = storageDownReqDTO.getStockStorageId();
        Long saasDownloadId = null;
        /*if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
            HashMap<String, LocalDate> dateHashMap = downloadTime();
            saasDownloadId = saasDownloadFacade.saasStartDownloadTask(storageDownReqDTO.getTenantId(), SaasFileUploadType.IN_STORE_TASK_SAAS_UPLOAD_TYPE, JSON.toJSONString(dateHashMap));
        }*/
        Long finalSaasDownloadId = saasDownloadId;
        // 子线程共享
        ServletRequestAttributes servletRequestAttributes = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes());
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            try {
                StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
                // 校验是否开启库位精细化管理
                Boolean flag = warehouseConfigRepository.openCabinetManagement(stockTaskStorage.getInWarehouseNo());
                if (Objects.equals(stockTaskStorage.getType(), OperationType.STORE_ALLOCATION_IN.getId())) {
                    if (Boolean.TRUE.equals(flag)) {
                        allocationInTaskDownV2(stockStorageId, storageDownReqDTO.getTenantId(), finalSaasDownloadId);
                    } else {
                        allocationInTaskDown(stockStorageId, storageDownReqDTO.getTenantId(), finalSaasDownloadId);
                    }
                } else if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId()) && Objects.equals(stockTaskStorage.getType(), OperationType.ALLOCATION_ABNORMAL_IN.getId())) {
                    allocationBackTaskDown(stockStorageId, storageDownReqDTO.getTenantId(), finalSaasDownloadId);
                } else {
                    //任务查询
                    Workbook workbook = new HSSFWorkbook();
                    CellStyle baseCellStyle = workbook.createCellStyle();
                    // 水平居中
                    baseCellStyle.setAlignment(HorizontalAlignment.CENTER);
                    // 垂直居中
                    baseCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    Sheet sheet = workbook.createSheet();
                    int index;
                    if (Boolean.TRUE.equals(flag)) {
                        index = excelHeadV2(stockTaskStorage, sheet);
                        packContentV2(index, sheet, baseCellStyle, stockTaskStorage.getId(), Boolean.FALSE);
                    } else {
                        index = excelHead(stockTaskStorage, sheet);
                        packContent(index, sheet, baseCellStyle, stockTaskStorage.getId(), Boolean.FALSE);
                    }
                    if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
                        String fileName = stockTaskStorage.getSourceId();
                        if (Objects.equals(stockTaskStorage.getType(), StoreRecordType.OTHER_IN.getId())) {
                            fileName = StoreRecordType.OTHER_IN.getName();
                        }
                        if (Objects.equals(stockTaskStorage.getType(), StoreRecordType.INIT_IN.getId())) {
                            fileName = StoreRecordType.INIT_IN.getName();
                        }
                        ByteArrayInputStream inputStream = convertStream(workbook);
                        // OssUploadResult upload = OssUploadUtil.upload(fileName + ".xls", inputStream, OSSExpiredLabelEnum.MONTH);
                        // saasDownloadFacade.downloadTaskSuccess(finalSaasDownloadId, upload.getUrl());
                        // 替换成鲜沐导出组件
                        fileName = fileName + ".xls";
                        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
                        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
                        recordDTO.setBizType(SaaSFileDownloadTypeEnum.STOCK_TASK_STORAGE.getType());
                        recordDTO.setTenantId(storageDownReqDTO.getTenantId());
                        recordDTO.setFileName(fileName);
                        recordDTO.setParams(JSONUtil.toJsonStr(downloadTime()));
                        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
                        DownloadCenterHelper.build(ExecutorUtil.downloadExecutor, recordDTO).asyncWriteWithOssResp(null, (x) -> {
                            // 1、文件上传至OSS
                            OssUploadResult uploadResult;
                            try {
                                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), inputStream, recordDTO.getExpiredDayLabel());
                            } catch (Exception e) {
                                log.error("上传文件到OSS失败，recordDTO:{}", JSON.toJSONString(recordDTO), e);
                                throw new net.xianmu.common.exception.BizException("上传文件到OSS失败");
                            }

                            // 2、返回OSS文件地址
                            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
                            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
                            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
                            return downloadCenterOssRespDTO;
                        });


                    } else {
                        ExcelUtils.outputExcel(workbook, stockTaskStorage.getSourceId() + ".xls", servletRequestAttributes.getResponse());
                    }
                }
            } catch (Exception e) {
                log.warn("导出异常", e);
                /*if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
                    saasDownloadFacade.downloadTaskFail(finalSaasDownloadId);
                }*/
                throw new net.xianmu.common.exception.BizException("导出异常", e);
            }
            return Boolean.TRUE;
        }, ExecutorUtil.downloadExecutor);
        if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
            log.info("saas入库任务导出结束");
            return;
        }
        // 鲜沐同步返回流
        try {
            future.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("异步执行异常", e);
            throw new net.xianmu.common.exception.BizException("导出异常", e);
        }
        log.info("入库任务导出结束");
    }

    @NotNull
    private HashMap<String, LocalDate> downloadTime() {
        HashMap<String, LocalDate> dateHashMap = Maps.newHashMap();
        dateHashMap.put("开始导出时间", LocalDate.now());
        return dateHashMap;
    }

    @NotNull
    private ByteArrayInputStream convertStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.info("读流异常", e);
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        return inputStream;
    }

    @Override
    @OperatorAnnotation
    public void downBatchStockStorage(StockStorageDownReqDTO storageDownReqDTO) {
        log.info("入库任务开始导出:{}", JSON.toJSONString(storageDownReqDTO));
        List<Long> stockStorageIdList = storageDownReqDTO.getStockStorageIdList();
        List<StockTaskStorage> stockTaskStorageList = stockTaskStorageQueryRepository.selectStockStorageBatch(stockStorageIdList);
        stockTaskStorageList.forEach(x -> {
            Integer type = x.getType();
            if (!Objects.equals(type, OperationType.PURCHASE_IN.getId())) {
                throw new BizException(ErrorCode.PURCHASE_NO_ERR.getCode(), ErrorCode.PURCHASE_NO_ERR.getMessage());
            }
        });
        Set<LocalDateTime> collect = stockTaskStorageList.stream().map(StockTaskStorage::getExpectTime).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect) || collect.size() > 1) {
            throw new BizException(ErrorCode.EXPECT_TIME_ERR.getCode(), ErrorCode.EXPECT_TIME_ERR.getMessage());
        }
        Map<Integer, List<StockTaskStorage>> inWarehouseListMap = stockTaskStorageList.stream().collect(Collectors.groupingBy(StockTaskStorage::getInWarehouseNo));
        if (inWarehouseListMap.keySet().size() > 1) {
            throw new BizException(ErrorCode.WAREHOUSE_DOWN_ERR.getCode(), ErrorCode.WAREHOUSE_DOWN_ERR.getMessage());
        }

        // 检验是否开启精细化
        StockTaskStorage storage = stockTaskStorageList.stream().findFirst().orElse(new StockTaskStorage());
        Boolean flag = warehouseConfigRepository.openCabinetManagement(storage.getInWarehouseNo());

        Long saasDownloadId = null;
        /*if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
            HashMap<String, LocalDate> dateHashMap = downloadTime();
            saasDownloadId = saasDownloadFacade.saasStartDownloadTask(storageDownReqDTO.getTenantId(), SaasFileUploadType.IN_STORE_TASK_SAAS_UPLOAD_TYPE, JSON.toJSONString(dateHashMap));
        }*/
        Long finalSaasDownloadId = saasDownloadId;
        // 子线程共享
        ServletRequestAttributes servletRequestAttributes = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes());
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            try {
                StockTaskStorage stockTaskStorage = stockTaskStorageList.get(NumberUtils.INTEGER_ZERO);
                Workbook workbook = new HSSFWorkbook();

                CellStyle baseCellStyle = workbook.createCellStyle();
                // 水平居中
                baseCellStyle.setAlignment(HorizontalAlignment.CENTER);
                // 垂直居中
                baseCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //创建sheet
                Sheet sheet = workbook.createSheet();

                int index;
                if (Boolean.TRUE.equals(flag)) {
                    index = excelDownHeadV2(stockTaskStorage, sheet);
                    //拼装数据
                    for (StockTaskStorage task : stockTaskStorageList) {
                        index = packContentV2(index, sheet, baseCellStyle, task.getId(), Boolean.TRUE);
                        index++;
                    }
                } else {
                    index = excelDownHead(stockTaskStorage, sheet);
                    //拼装数据
                    for (StockTaskStorage task : stockTaskStorageList) {
                        index = packContent(index, sheet, baseCellStyle, task.getId(), Boolean.TRUE);
                        index++;
                    }
                }
                String warehouseName = stockTaskStorage.getInWarehouseName();
                LocalDateTime expectTime = stockTaskStorage.getExpectTime();
                //数据导出
                String expectDate = DateUtil.formatYmdWithOutSplitDate(expectTime.toLocalDate());
                try {
                    if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
                        ByteArrayInputStream inputStream = convertStream(workbook);
                        // OssUploadResult upload = OssUploadUtil.upload(warehouseName + expectDate + "入库汇总数据.xls", inputStream, OSSExpiredLabelEnum.MONTH);
                        // saasDownloadFacade.downloadTaskSuccess(finalSaasDownloadId, upload.getUrl());
                        // 替换成鲜沐导出组件
                        String fileName = warehouseName + expectDate + "入库汇总数据.xls";
                        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
                        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
                        recordDTO.setBizType(SaaSFileDownloadTypeEnum.STOCK_TASK_STORAGE.getType());
                        recordDTO.setTenantId(storageDownReqDTO.getTenantId());
                        recordDTO.setFileName(fileName);
                        recordDTO.setParams(JSONUtil.toJsonStr(downloadTime()));
                        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
                        DownloadCenterHelper.build(ExecutorUtil.downloadExecutor, recordDTO).asyncWriteWithOssResp(null, (x) -> {
                            // 1、文件上传至OSS
                            OssUploadResult uploadResult;
                            try {
                                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), inputStream, recordDTO.getExpiredDayLabel());
                            } catch (Exception e) {
                                log.error("上传文件到OSS失败，recordDTO:{}", JSON.toJSONString(recordDTO), e);
                                throw new net.xianmu.common.exception.BizException("上传文件到OSS失败");
                            }

                            // 2、返回OSS文件地址
                            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
                            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
                            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
                            return downloadCenterOssRespDTO;
                        });

                    } else {
                        ExcelUtils.outputExcel(workbook, warehouseName + expectDate + "入库汇总数据.xls", servletRequestAttributes.getResponse());
                    }
                } catch (IOException e) {
                    throw new DefaultServiceException("导出异常");
                }
            } catch (Exception e) {
                log.warn("导出异常", e);
                /*if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
                    saasDownloadFacade.downloadTaskFail(finalSaasDownloadId);
                }*/
                throw new net.xianmu.common.exception.BizException("导出异常", e);
            }
            return Boolean.TRUE;
        }, ExecutorUtil.downloadExecutor);
        if (!TenantIdCheckUtil.isXm(storageDownReqDTO.getTenantId())) {
            log.info("saas入库任务导出结束");
            return;
        }
        // 鲜沐同步返回流
        try {
            future.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("异步执行异常", e);
            throw new net.xianmu.common.exception.BizException("导出异常", e);
        }
        log.info("入库任务导出结束");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelStockStorage(StockStorageCompleteReqDTO completeReqDTO) {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(completeReqDTO.getStockStorageId());
        //校验任务状态
        if (!Objects.equals(stockTaskStorage.getState(), StockTaskStorageStateEnum.NORMAL.getId())) {
            throw new BizException(ErrorCode.CLOSE_STATE_COMPLETE);
        }
        if (!Objects.equals(stockTaskStorage.getProcessState(), StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId())) {
            if (CheckDTO.cantCloseStockStorageType.contains(stockTaskStorage.getType())) {
                if (Objects.equals(stockTaskStorage.getProcessState(), StockTaskStorageCabinetDelicacyProcessStateEnum.RECEIVED.getId())) {
                    List<InBoundOrderDetail> inBoundOrderDetailByTaskId = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder()
                            .taskId(stockTaskStorage.getId())
                            .build());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inBoundOrderDetailByTaskId)) {
                        throw new BizException(ErrorCode.CLOSE_STATE_ERR.getMessage());
                    }
                }
            }
        }

        StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder()
                .id(completeReqDTO.getStockStorageId())
                .adminId(completeReqDTO.getAdminId())
                .state(StockTaskStorageStateEnum.CANCEL.getId())
                .closeReason(completeReqDTO.getReason())
                .build();
        stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);

        // 同步取消老模型任务状态
        StockTask cancelStockTask = StockTask.builder()
                .id(stockTaskStorage.getStockTaskId())
                .state(StockTaskState.CANCEL.getId())
                .build();
        stockTaskRepository.cancel(cancelStockTask);

        StockStorageDataBO stockStorageDataBO = StockStorageDataBO.builder().type(stockTaskStorage.getType())
                .taskId(stockTaskStorage.getId())
                .stockTaskId(stockTaskStorage.getStockTaskId())
//                .sourceId(stockTaskStorage.getSourceId())
                .tenantId(stockTaskStorage.getTenantId())
                .afterSaleOrderNo(stockTaskStorage.getAfterSaleOrder())
                .build();
        if (!Objects.equals(stockTaskStorage.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            mqUtil.sendMqData(RocketMqMessageConstant.MALL_LIST, stockStorageDataBO, MType.CLOSE_IN_STORE.name());
            // 取消通知
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    inBoundOrderMsgService.sendStockStorageFinishNotice(stockTaskStorage.getId());
                }
            });
        }
        // 取消入库任务 - 对外通知取消入库任务
        stockInExternalService.cancelStockInTask(stockTaskStorage);
    }

    @Override
    public void updateExpectTimeStockStorage(StockStorageCommandDTO commandDTO) {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageByOldId(commandDTO.getTaskId());
        StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder()
                .id(stockTaskStorage.getId())
                .expectTime(commandDTO.getExpectTime())
                .build();
        stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);
    }

    /**
     * 拼装表头信息
     */
    private Integer excelDownHead(StockTaskStorage stockTaskStorage, Sheet sheet) {
        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        int rowIndex = 0;

        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("预计到货时间");
        LocalDate expectTime = null == stockTaskStorage || Objects.isNull(stockTaskStorage.getExpectTime().toLocalDate()) ? LocalDateTime.now().toLocalDate() : stockTaskStorage.getExpectTime().toLocalDate();
        String time = DateUtil.formatYmdWithOutSplitDate(expectTime);
        first.createCell(1).setCellValue(time);

        Row third = sheet.createRow(rowIndex++);
        third.createCell(0).setCellValue("入库仓库");
        third.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getInWarehouseName());

        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue("任务编码");
        title.createCell(1).setCellValue("类目类型");
        title.createCell(2).setCellValue("类目名称");
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title.createCell(3).setCellValue("商品名称");
            title.createCell(4).setCellValue("sku");
            title.createCell(5).setCellValue("规格");
            title.createCell(6).setCellValue("商品归属");
            title.createCell(7).setCellValue("储存区域");
            title.createCell(8).setCellValue("包装");
            title.createCell(9).setCellValue("进口/国产");
            title.createCell(10).setCellValue("供应商");
            title.createCell(11).setCellValue("采购数量");
            title.createCell(12).setCellValue("预约数量");
            title.createCell(13).setCellValue("入库数量");
            title.createCell(14).setCellValue("生产日期");
            title.createCell(15).setCellValue("保质期");
        } else {
            title.createCell(3).setCellValue("货品名称");
            title.createCell(4).setCellValue("货品ID");
            title.createCell(5).setCellValue("自有编码");
            title.createCell(6).setCellValue("规格");
            title.createCell(7).setCellValue("储存区域");
            title.createCell(8).setCellValue("规格单位");
            title.createCell(9).setCellValue("供应商");
            title.createCell(10).setCellValue("采购数量");
            title.createCell(11).setCellValue("预约数量");
            title.createCell(12).setCellValue("入库数量");
            title.createCell(13).setCellValue("生产日期");
            title.createCell(14).setCellValue("保质期");
        }
        return rowIndex;

    }

    /**
     * 拼装表头信息
     */
    private Integer excelDownHeadV2(StockTaskStorage stockTaskStorage, Sheet sheet) {
        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        int rowIndex = 0;

        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("预计到货时间");
        LocalDate expectTime = null == stockTaskStorage || Objects.isNull(stockTaskStorage.getExpectTime().toLocalDate()) ? LocalDateTime.now().toLocalDate() : stockTaskStorage.getExpectTime().toLocalDate();
        String time = DateUtil.formatYmdWithOutSplitDate(expectTime);
        first.createCell(1).setCellValue(time);

        Row third = sheet.createRow(rowIndex++);
        third.createCell(0).setCellValue("入库仓库");
        third.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getInWarehouseName());

        Row fourth = sheet.createRow(rowIndex++);
//        fourth.createCell(0).setCellValue("货主");
//        fourth.createCell(1).setCellValue(StringUtils.isNotBlank(stockTaskStorage.getCargoOwner()) ? stockTaskStorage.getCargoOwner() : "");
        fourth.createCell(0).setCellValue("确认到货时间");
        fourth.createCell(1).setCellValue(null != stockTaskStorage && Objects.nonNull(stockTaskStorage.getArrivalTime()) ? DateUtil.formatDate(stockTaskStorage.getArrivalTime()) : "");

        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue("任务编码");
        title.createCell(1).setCellValue("类目类型");
        title.createCell(2).setCellValue("类目名称");
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title.createCell(3).setCellValue("商品名称");
            title.createCell(4).setCellValue("sku");
            title.createCell(5).setCellValue("规格");
            title.createCell(6).setCellValue("商品归属");
            title.createCell(7).setCellValue("储存区域");
            title.createCell(8).setCellValue("包装");
            title.createCell(9).setCellValue("进口/国产");
            title.createCell(10).setCellValue("供应商");
            title.createCell(11).setCellValue("采购数量");
            title.createCell(12).setCellValue("预约数量");
            title.createCell(13).setCellValue("入库数量");
            title.createCell(14).setCellValue("生产日期");
            title.createCell(15).setCellValue("保质期");
        } else {
            title.createCell(3).setCellValue("货品名称");
            title.createCell(4).setCellValue("货品ID");
            title.createCell(5).setCellValue("规格");
            title.createCell(6).setCellValue("储存区域");
            title.createCell(7).setCellValue("规格单位");
            title.createCell(8).setCellValue("供应商");
            title.createCell(9).setCellValue("采购数量");
            title.createCell(10).setCellValue("预约数量");
            title.createCell(11).setCellValue("入库数量");
            title.createCell(12).setCellValue("生产日期");
            title.createCell(13).setCellValue("保质期");
        }
        return rowIndex;

    }

    /**
     * 拼装表内容
     */
    private Integer packContent(Integer index, Sheet sheet, CellStyle baseCellStyle, Long taskId, Boolean flag) {

        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(taskId);
        if (null == stockTaskStorage) {
            throw new net.xianmu.common.exception.BizException("未查询到入库任务记录");
        }
        Long id = stockTaskStorage.getId();
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(taskId);
        // saas sku id
        if (!TenantIdCheckUtil.isXm(stockTaskStorage.getTenantId())) {
            if (!CollectionUtils.isEmpty(stockStorageItems)) {
                skuConvertCommandService.setSaasSkuIdForList(stockStorageItems);
            }
        }

        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        List<InBoundOrderDetail> details = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(id).build());
        // 根据一级类目排序 鲜果在前 非鲜果在后
        Map<String, List<InBoundOrderDetail>> inBoundMap = details.stream().collect(Collectors.groupingBy(InBoundOrderDetail::getSku));
        List<String> skus = stockStorageItems.stream().map(StockStorageItem::getSku).distinct().collect(Collectors.toList());
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockTaskStorage.getInWarehouseNo().longValue(), skus);

        for (StockStorageItem storageItem : stockStorageItems) {
            List<InBoundOrderDetail> skuItemDetailList = inBoundMap.get(storageItem.getSku());

            Product product = productMap.getOrDefault(storageItem.getSku(), Product.builder().build());
            String temperature = StorageLocation.getTypeById(product.getTemperature());

            Map<Long, List<InBoundOrderDetail>> purchaseNoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(skuItemDetailList)) {
                purchaseNoMap = skuItemDetailList.stream().collect(Collectors.groupingBy(InBoundOrderDetail::getShelfLife));
            }

            Long adminId = product.getInventoryAdminId();
            AdminFacadeDTO admin = adminUtil.getAdmin(adminId);
            String remake = Objects.isNull(admin) ? null : admin.getRealName();
            if (CollectionUtils.isEmpty(purchaseNoMap)) {
                int cellIndex = NumberUtils.INTEGER_ZERO;
                Row row = sheet.createRow(index);
                if (flag) {
                    Cell fristCategory = row.createCell(cellIndex++);
                    fristCategory.setCellValue(taskId);
                    fristCategory.setCellStyle(baseCellStyle);
                }
                Cell fristCategory = row.createCell(cellIndex++);
                fristCategory.setCellValue(CategoryEnum.getValue(storageItem.getCategoryType()));
                fristCategory.setCellStyle(baseCellStyle);

                Cell secondCategory = row.createCell(cellIndex++);
                secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                secondCategory.setCellStyle(baseCellStyle);

                Cell pdName = row.createCell(cellIndex++);
                //活动信息
                pdName.setCellValue(storageItem.getPdName());
                pdName.setCellStyle(baseCellStyle);

                Cell sku = row.createCell(cellIndex++);
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    sku.setCellValue(storageItem.getSku());
                } else {
                    sku.setCellValue(Objects.nonNull(storageItem.getSaasSkuId()) ? String.valueOf(storageItem.getSaasSkuId()) : "");
                }
                sku.setCellStyle(baseCellStyle);

                if (!TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell saasCustomSkuCode = row.createCell(cellIndex++);
                    saasCustomSkuCode.setCellValue(Objects.nonNull(storageItem.getSaasCustomSkuCode()) ? String.valueOf(storageItem.getSaasCustomSkuCode()) : "");
                    saasCustomSkuCode.setCellStyle(baseCellStyle);
                }

                Cell weight = row.createCell(cellIndex++);
                weight.setCellValue(storageItem.getSpecification());
                weight.setCellStyle(baseCellStyle);
                String productType = StringUtils.productType(storageItem.getSkuType(), remake);

                // 商品归属自营仓处理
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell productsType = row.createCell(cellIndex++);
                    productsType.setCellValue(productType);
                    productsType.setCellStyle(baseCellStyle);
                }

                Cell storageLocation = row.createCell(cellIndex++);
                storageLocation.setCellValue(temperature);
                storageLocation.setCellStyle(baseCellStyle);

                Cell packing = row.createCell(cellIndex++);
                packing.setCellValue(storageItem.getPackaging());
                packing.setCellStyle(baseCellStyle);

                // 国产/进口
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell origin = row.createCell(cellIndex++);
                    String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, product.getIsDomestic()) ? "进口" : "国产";
                    origin.setCellValue(originStr);
                    origin.setCellStyle(baseCellStyle);
                }

                Cell supplier = row.createCell(cellIndex++);
                supplier.setCellValue(storageItem.getSupplier());
                supplier.setCellStyle(baseCellStyle);

                Cell quantity = row.createCell(cellIndex++);
                quantity.setCellValue(storageItem.getQuantity());
                quantity.setCellStyle(baseCellStyle);

                Cell arrQuantity = row.createCell(cellIndex++);
                arrQuantity.setCellValue(storageItem.getQuantity());
                arrQuantity.setCellStyle(baseCellStyle);
                row.createCell(cellIndex++).setCellValue(0);
                row.createCell(cellIndex++).setCellValue("无");
                row.createCell(cellIndex++).setCellValue("无");
                index++;
            } else {
                //多数据合并
                if (purchaseNoMap.keySet().size() > 1) {
                    mergedRegion(sheet, index, purchaseNoMap.keySet().size());
                }
                for (Long shelfLifeKey : purchaseNoMap.keySet()) {
                    //信息拼装
                    Row row = sheet.createRow(index);
                    int cellIndex = NumberUtils.INTEGER_ZERO;
                    if (flag) {
                        Cell fristCategory = row.createCell(cellIndex++);
                        fristCategory.setCellValue(taskId);
                        fristCategory.setCellStyle(baseCellStyle);
                    }
                    Cell fristCategory = row.createCell(cellIndex++);
                    fristCategory.setCellValue(CategoryEnum.getValue(storageItem.getCategoryType()));
                    fristCategory.setCellStyle(baseCellStyle);

                    Cell secondCategory = row.createCell(cellIndex++);
                    secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                    secondCategory.setCellStyle(baseCellStyle);

                    Cell pdName = row.createCell(cellIndex++);
                    //活动信息
                    pdName.setCellValue(storageItem.getPdName());
                    pdName.setCellStyle(baseCellStyle);

                    Cell sku = row.createCell(cellIndex++);
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        sku.setCellValue(storageItem.getSku());
                    } else {
                        sku.setCellValue(Objects.nonNull(storageItem.getSaasSkuId()) ? String.valueOf(storageItem.getSaasSkuId()) : "");
                    }
                    sku.setCellStyle(baseCellStyle);

                    if (!TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        Cell saasCustomSkuCode = row.createCell(cellIndex++);
                        saasCustomSkuCode.setCellValue(Objects.nonNull(storageItem.getSaasCustomSkuCode()) ? String.valueOf(storageItem.getSaasCustomSkuCode()) : "");
                        saasCustomSkuCode.setCellStyle(baseCellStyle);
                    }

                    Cell weight = row.createCell(cellIndex++);
                    weight.setCellValue(storageItem.getSpecification());
                    weight.setCellStyle(baseCellStyle);

                    // 商品归属特殊处理
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        String productType = StringUtils.productType(storageItem.getSkuType(), remake);
                        Cell productsType = row.createCell(cellIndex++);
                        productsType.setCellValue(productType);
                        productsType.setCellStyle(baseCellStyle);
                    }

                    Cell storageLocation = row.createCell(cellIndex++);
                    storageLocation.setCellValue(temperature);
                    storageLocation.setCellStyle(baseCellStyle);

                    Cell packing = row.createCell(cellIndex++);
                    packing.setCellValue(storageItem.getPackaging());
                    packing.setCellStyle(baseCellStyle);

                    // 国产/进口
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        Cell origin = row.createCell(cellIndex++);
                        String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, product.getIsDomestic()) ? "进口" : "国产";
                        origin.setCellValue(originStr);
                        origin.setCellStyle(baseCellStyle);
                    }

                    Cell supplier = row.createCell(cellIndex++);
                    supplier.setCellValue(storageItem.getSupplier());
                    supplier.setCellStyle(baseCellStyle);

                    Cell quantity = row.createCell(cellIndex++);
                    quantity.setCellValue(storageItem.getQuantity());
                    quantity.setCellStyle(baseCellStyle);

                    Cell arrQuantity = row.createCell(cellIndex++);
                    arrQuantity.setCellValue(storageItem.getQuantity());
                    arrQuantity.setCellStyle(baseCellStyle);
                    //无入库信息
                    List<InBoundOrderDetail> deMsg = purchaseNoMap.get(shelfLifeKey);
                    InBoundOrderDetail detail = deMsg.stream().findFirst().orElse(new InBoundOrderDetail());
                    int sum = deMsg.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                    row.createCell(cellIndex++).setCellValue(sum);
                    String shelfLife = DateUtil.formatYmd(detail.getShelfLife());
                    String produceAt = DateUtil.formatYmd(detail.getProduceAt());
                    row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(produceAt) ? "无" : produceAt);
                    row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(shelfLife) ? "无" : shelfLife);
                    index++;

                }
            }

        }
        return index;
    }

    /**
     * 拼装表内容
     */
    private Integer packContentV2(Integer index, Sheet sheet, CellStyle baseCellStyle, Long taskId, Boolean flag) {

        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(taskId);
        if (null == stockTaskStorage) {
            throw new net.xianmu.common.exception.BizException("未查询到入库任务记录");
        }
        Long id = stockTaskStorage.getId();
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(taskId);
        // 查询StockTaskStorageDetail
        List<Long> itemIdList = stockStorageItems.stream().map(StockStorageItem::getId).collect(Collectors.toList());
        List<StockStorageItemDetail> stockStorageItemDetails = stockStorageItemDetailRepository.selectItemDetailBatch(itemIdList);
        Map<Long, List<StockStorageItemDetail>> stockStorageItemDetailMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(stockStorageItemDetails)) {
            stockStorageItemDetailMap = stockStorageItemDetails.stream()
                    .collect(Collectors.groupingBy(StockStorageItemDetail::getStockStorageItemId));
        }
        // saas sku id
        if (!TenantIdCheckUtil.isXm(stockTaskStorage.getTenantId())) {
            if (!CollectionUtils.isEmpty(stockStorageItems)) {
                skuConvertCommandService.setSaasSkuIdForList(stockStorageItems);
            }
        }

        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        List<InBoundOrderDetail> details = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(id).build());
        List<String> skus = stockStorageItems.stream().map(StockStorageItem::getSku).distinct().collect(Collectors.toList());
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockTaskStorage.getInWarehouseNo().longValue(), skus);

        // 根据一级类目排序 鲜果在前 非鲜果在后
        Map<String, List<InBoundOrderDetail>> inBoundMap = details.stream().collect(Collectors.groupingBy(InBoundOrderDetail::getSku));
        for (StockStorageItem storageItem : stockStorageItems) {
            List<InBoundOrderDetail> skuItemDetailList = inBoundMap.get(storageItem.getSku());

            Product product = productMap.getOrDefault(storageItem.getSku(), Product.builder().build());
            String temperature = StorageLocation.getTypeById(product.getTemperature());
            Map<Long, List<InBoundOrderDetail>> purchaseNoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(skuItemDetailList)) {
                purchaseNoMap = skuItemDetailList.stream().collect(Collectors.groupingBy(InBoundOrderDetail::getShelfLife));
            }

            Long adminId = product.getInventoryAdminId();
            AdminFacadeDTO admin = adminUtil.getAdmin(adminId);
            String remake = Objects.isNull(admin) ? null : admin.getRealName();
            if (CollectionUtils.isEmpty(purchaseNoMap)) {
                int cellIndex = NumberUtils.INTEGER_ZERO;
                Row row = sheet.createRow(index);
                if (flag) {
                    Cell fristCategory = row.createCell(cellIndex++);
                    fristCategory.setCellValue(taskId);
                    fristCategory.setCellStyle(baseCellStyle);
                }
                Cell fristCategory = row.createCell(cellIndex++);
                fristCategory.setCellValue(CategoryEnum.getValue(storageItem.getCategoryType()));
                fristCategory.setCellStyle(baseCellStyle);

                Cell secondCategory = row.createCell(cellIndex++);
                secondCategory.setCellValue(product.getCategory());
                secondCategory.setCellStyle(baseCellStyle);

                Cell pdName = row.createCell(cellIndex++);
                //活动信息
                pdName.setCellValue(storageItem.getPdName());
                pdName.setCellStyle(baseCellStyle);

                Cell sku = row.createCell(cellIndex++);
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    sku.setCellValue(storageItem.getSku());
                } else {
                    sku.setCellValue(Objects.nonNull(storageItem.getSaasSkuId()) ? String.valueOf(storageItem.getSaasSkuId()) : "");
                }
                sku.setCellStyle(baseCellStyle);

                Cell weight = row.createCell(cellIndex++);
                weight.setCellValue(storageItem.getSpecification());
                weight.setCellStyle(baseCellStyle);
                String productType = StringUtils.productType(storageItem.getSkuType(), remake);

                // 商品归属自营仓处理
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell productsType = row.createCell(cellIndex++);
                    productsType.setCellValue(productType);
                    productsType.setCellStyle(baseCellStyle);
                }

                Cell storageLocation = row.createCell(cellIndex++);
                storageLocation.setCellValue(temperature);
                storageLocation.setCellStyle(baseCellStyle);

                Cell packing = row.createCell(cellIndex++);
                packing.setCellValue(storageItem.getPackaging());
                packing.setCellStyle(baseCellStyle);

                // 国产/进口
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell origin = row.createCell(cellIndex++);
                    String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, product.getIsDomestic()) ? "进口" : "国产";
                    origin.setCellValue(originStr);
                    origin.setCellStyle(baseCellStyle);
                }

                Cell supplier = row.createCell(cellIndex++);
                supplier.setCellValue(storageItem.getSupplier());
                supplier.setCellStyle(baseCellStyle);

                Cell quantity = row.createCell(cellIndex++);
                quantity.setCellValue(storageItem.getQuantity());
                quantity.setCellStyle(baseCellStyle);

                Cell arrQuantity = row.createCell(cellIndex++);
                arrQuantity.setCellValue(storageItem.getQuantity());
                arrQuantity.setCellStyle(baseCellStyle);
                row.createCell(cellIndex++).setCellValue(0);
                row.createCell(cellIndex++).setCellValue("无");
                row.createCell(cellIndex++).setCellValue("无");
                index++;
            } else {
                //多数据合并
                if (purchaseNoMap.keySet().size() > 1) {
                    mergedRegion(sheet, index, purchaseNoMap.keySet().size());
                }
                List<StockStorageItemDetail> itemDetails = stockStorageItemDetailMap.get(storageItem.getId());
                if (CollectionUtils.isEmpty(itemDetails)) {
                    for (Long shelfLifeKey : purchaseNoMap.keySet()) {
                        //信息拼装
                        Row row = sheet.createRow(index);
                        int cellIndex = NumberUtils.INTEGER_ZERO;
                        if (flag) {
                            Cell fristCategory = row.createCell(cellIndex++);
                            fristCategory.setCellValue(taskId);
                            fristCategory.setCellStyle(baseCellStyle);
                        }
                        Cell fristCategory = row.createCell(cellIndex++);
                        fristCategory.setCellValue(CategoryEnum.getValue(storageItem.getCategoryType()));
                        fristCategory.setCellStyle(baseCellStyle);

                        Cell secondCategory = row.createCell(cellIndex++);
                        secondCategory.setCellValue(product.getCategory());
                        secondCategory.setCellStyle(baseCellStyle);

                        Cell pdName = row.createCell(cellIndex++);
                        //活动信息
                        pdName.setCellValue(storageItem.getPdName());
                        pdName.setCellStyle(baseCellStyle);

                        Cell sku = row.createCell(cellIndex++);
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            sku.setCellValue(storageItem.getSku());
                        } else {
                            sku.setCellValue(Objects.nonNull(storageItem.getSaasSkuId()) ? String.valueOf(storageItem.getSaasSkuId()) : "");
                        }
                        sku.setCellStyle(baseCellStyle);

                        Cell weight = row.createCell(cellIndex++);
                        weight.setCellValue(storageItem.getSpecification());
                        weight.setCellStyle(baseCellStyle);

                        // 商品归属特殊处理
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            String productType = StringUtils.productType(storageItem.getSkuType(), remake);
                            Cell productsType = row.createCell(cellIndex++);
                            productsType.setCellValue(productType);
                            productsType.setCellStyle(baseCellStyle);
                        }

                        Cell storageLocation = row.createCell(cellIndex++);
                        storageLocation.setCellValue(temperature);
                        storageLocation.setCellStyle(baseCellStyle);

                        Cell packing = row.createCell(cellIndex++);
                        packing.setCellValue(storageItem.getPackaging());
                        packing.setCellStyle(baseCellStyle);

                        // 国产/进口
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            Cell origin = row.createCell(cellIndex++);
                            String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, product.getIsDomestic()) ? "进口" : "国产";
                            origin.setCellValue(originStr);
                            origin.setCellStyle(baseCellStyle);
                        }

                        Cell supplier = row.createCell(cellIndex++);
                        supplier.setCellValue(storageItem.getSupplier());
                        supplier.setCellStyle(baseCellStyle);

                        Cell quantity = row.createCell(cellIndex++);
                        quantity.setCellValue(storageItem.getQuantity());
                        quantity.setCellStyle(baseCellStyle);

                        Cell arrQuantity = row.createCell(cellIndex++);
                        arrQuantity.setCellValue(storageItem.getQuantity());
                        arrQuantity.setCellStyle(baseCellStyle);

                        //无入库信息
                        List<InBoundOrderDetail> deMsg = purchaseNoMap.get(shelfLifeKey);
                        InBoundOrderDetail detail = deMsg.stream().findFirst().orElse(new InBoundOrderDetail());
                        int sum = deMsg.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                        row.createCell(cellIndex++).setCellValue(sum);

                        Cell receivingContainer = row.createCell(cellIndex++);
                        receivingContainer.setCellValue("");
                        receivingContainer.setCellStyle(baseCellStyle);

                        String shelfLife = DateUtil.formatYmd(detail.getShelfLife());
                        String produceAt = DateUtil.formatYmd(detail.getProduceAt());
                        row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(produceAt) ? "无" : produceAt);
                        row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(shelfLife) ? "无" : shelfLife);
                        index++;
                    }
                } else {
                    for (StockStorageItemDetail itemDetail : itemDetails) {
                        //信息拼装
                        Row row = sheet.createRow(index);
                        int cellIndex = NumberUtils.INTEGER_ZERO;
                        if (flag) {
                            Cell fristCategory = row.createCell(cellIndex++);
                            fristCategory.setCellValue(taskId);
                            fristCategory.setCellStyle(baseCellStyle);
                        }
                        Cell fristCategory = row.createCell(cellIndex++);
                        fristCategory.setCellValue(CategoryEnum.getValue(storageItem.getCategoryType()));
                        fristCategory.setCellStyle(baseCellStyle);

                        Cell secondCategory = row.createCell(cellIndex++);
                        secondCategory.setCellValue(product.getCategory());
                        secondCategory.setCellStyle(baseCellStyle);

                        Cell pdName = row.createCell(cellIndex++);
                        //活动信息
                        pdName.setCellValue(storageItem.getPdName());
                        pdName.setCellStyle(baseCellStyle);

                        Cell sku = row.createCell(cellIndex++);
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            sku.setCellValue(storageItem.getSku());
                        } else {
                            sku.setCellValue(Objects.nonNull(storageItem.getSaasSkuId()) ? String.valueOf(storageItem.getSaasSkuId()) : "");
                        }
                        sku.setCellStyle(baseCellStyle);

                        Cell weight = row.createCell(cellIndex++);
                        weight.setCellValue(storageItem.getSpecification());
                        weight.setCellStyle(baseCellStyle);

                        // 商品归属特殊处理
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            String productType = StringUtils.productType(storageItem.getSkuType(), remake);
                            Cell productsType = row.createCell(cellIndex++);
                            productsType.setCellValue(productType);
                            productsType.setCellStyle(baseCellStyle);
                        }

                        Cell storageLocation = row.createCell(cellIndex++);
                        storageLocation.setCellValue(temperature);
                        storageLocation.setCellStyle(baseCellStyle);

                        Cell packing = row.createCell(cellIndex++);
                        packing.setCellValue(storageItem.getPackaging());
                        packing.setCellStyle(baseCellStyle);

                        // 国产/进口
                        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                            Cell origin = row.createCell(cellIndex++);
                            String originStr = Objects.equals(NumberUtils.INTEGER_ZERO, product.getIsDomestic()) ? "进口" : "国产";
                            origin.setCellValue(originStr);
                            origin.setCellStyle(baseCellStyle);
                        }

                        Cell supplier = row.createCell(cellIndex++);
                        supplier.setCellValue(storageItem.getSupplier());
                        supplier.setCellStyle(baseCellStyle);

                        Cell quantity = row.createCell(cellIndex++);
                        quantity.setCellValue(storageItem.getQuantity());
                        quantity.setCellStyle(baseCellStyle);

                        Cell arrQuantity = row.createCell(cellIndex++);
                        arrQuantity.setCellValue(storageItem.getQuantity());
                        arrQuantity.setCellStyle(baseCellStyle);

                        row.createCell(cellIndex++).setCellValue(itemDetail.getActualInQuantity());

                        Cell receivingContainer = row.createCell(cellIndex++);
                        receivingContainer.setCellValue(StringUtils.isNotBlank(itemDetail.getReceivingContainer()) ? itemDetail.getReceivingContainer() : "");
                        receivingContainer.setCellStyle(baseCellStyle);

                        String shelfLife = DateUtil.formatYmdDate(itemDetail.getQualityDate());
                        String produceAt = DateUtil.formatYmdDate(itemDetail.getProductionDate());
                        row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(produceAt) ? "无" : produceAt);
                        row.createCell(cellIndex++).setCellValue(StringUtils.isEmpty(shelfLife) ? "无" : shelfLife);
                        index++;
                    }
                }
            }

        }
        return index;
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param index
     * @param size
     */
    private void mergedRegion(Sheet sheet, Integer index, Integer size) {
        for (int i = 0; i <= 10; i++) {
            sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, i, i));
        }
    }

    /**
     * 类目类型
     *
     * @param category
     * @return
     */
    private Integer getCategory(Set<Integer> category) {

        if (category.stream().allMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return CategoryEnum.FRUIT.getType();
        } else if (category.stream().noneMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return CategoryEnum.NOT_FRUIT.getType();
        } else {
            return CategoryEnum.OTHER.getType();
        }
    }

    /**
     * 拼装表头信息
     */
    private Integer excelHead(StockTaskStorage stockTaskStorage, Sheet sheet) {
        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        int rowIndex = 0;

        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("任务编号");
        first.createCell(1).setCellValue(null == stockTaskStorage ? 0 : stockTaskStorage.getId());

        Row second = sheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("采购批次");
        second.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getSourceId());

        Row third = sheet.createRow(rowIndex++);
        third.createCell(0).setCellValue("入库仓库");
        third.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getInWarehouseName());

        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue("类目类型");
        title.createCell(1).setCellValue("类目名称");
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title.createCell(2).setCellValue("商品名称");
            title.createCell(3).setCellValue("sku");
            title.createCell(4).setCellValue("规格");
            title.createCell(5).setCellValue("商品归属");
            title.createCell(6).setCellValue("储存区域");
            title.createCell(7).setCellValue("包装");
            title.createCell(8).setCellValue("进口/国产");
            title.createCell(9).setCellValue("供应商");
            title.createCell(10).setCellValue("采购数量");
            title.createCell(11).setCellValue("预约数量");
            title.createCell(12).setCellValue("入库数量");
            title.createCell(13).setCellValue("生产日期");
            title.createCell(14).setCellValue("保质期");
        } else {
            title.createCell(2).setCellValue("货品名称");
            title.createCell(3).setCellValue("货品ID");
            title.createCell(4).setCellValue("自有编码");
            title.createCell(5).setCellValue("规格");
            title.createCell(6).setCellValue("储存区域");
            title.createCell(7).setCellValue("规格单位");
            title.createCell(8).setCellValue("供应商");
            title.createCell(9).setCellValue("采购数量");
            title.createCell(10).setCellValue("预约数量");
            title.createCell(11).setCellValue("入库数量");
            title.createCell(12).setCellValue("生产日期");
            title.createCell(13).setCellValue("保质期");
        }
        return rowIndex;

    }

    /**
     * 拼装表头信息
     */
    private Integer excelHeadV2(StockTaskStorage stockTaskStorage, Sheet sheet) {
        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        int rowIndex = 0;

        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("任务编号");
        first.createCell(1).setCellValue(null == stockTaskStorage ? 0 : stockTaskStorage.getId());

        Row second = sheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("采购批次");
        second.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getSourceId());

        Row third = sheet.createRow(rowIndex++);
        third.createCell(0).setCellValue("入库仓库");
        third.createCell(1).setCellValue(null == stockTaskStorage ? "" : stockTaskStorage.getInWarehouseName());

        Row fourth = sheet.createRow(rowIndex++);
//        fourth.createCell(0).setCellValue("货主");
//        fourth.createCell(1).setCellValue(StringUtils.isNotBlank(stockTaskStorage.getCargoOwner()) ? stockTaskStorage.getCargoOwner() : "");
        fourth.createCell(0).setCellValue("确认到货时间");
        fourth.createCell(1).setCellValue(null != stockTaskStorage && Objects.nonNull(stockTaskStorage.getArrivalTime()) ? DateUtil.formatDate(stockTaskStorage.getArrivalTime()) : "");

        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue("类目类型");
        title.createCell(1).setCellValue("类目名称");
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title.createCell(2).setCellValue("商品名称");
            title.createCell(3).setCellValue("sku");
            title.createCell(4).setCellValue("规格");
            title.createCell(5).setCellValue("商品归属");
            title.createCell(6).setCellValue("储存区域");
            title.createCell(7).setCellValue("包装");
            title.createCell(8).setCellValue("进口/国产");
            title.createCell(9).setCellValue("供应商");
            title.createCell(10).setCellValue("采购数量");
            title.createCell(11).setCellValue("预约数量");
            title.createCell(12).setCellValue("入库数量");
            title.createCell(13).setCellValue("收货容器");
            title.createCell(14).setCellValue("生产日期");
            title.createCell(15).setCellValue("保质期");
        } else {
            title.createCell(2).setCellValue("货品名称");
            title.createCell(3).setCellValue("货品ID");
            title.createCell(4).setCellValue("规格");
            title.createCell(5).setCellValue("储存区域");
            title.createCell(6).setCellValue("规格单位");
            title.createCell(7).setCellValue("供应商");
            title.createCell(8).setCellValue("采购数量");
            title.createCell(9).setCellValue("预约数量");
            title.createCell(10).setCellValue("入库数量");
            title.createCell(11).setCellValue("生产日期");
            title.createCell(12).setCellValue("保质期");
        }
        return rowIndex;
    }

    /**
     * 调拨入库 任务下载 copy
     *
     * @param stockStorageId
     */
    private void allocationInTaskDown(Long stockStorageId, Long tenantId, Long saasDownloadId) throws IOException {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        if (null == stockTaskStorage) {
            throw new net.xianmu.common.exception.BizException("未查询到入库任务记录");
        }
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockTaskStorage.getId());

        // region saas sku id
        Long inWarehouseTenantId = this.getInWarehouseTenantId(stockTaskStorage.getInWarehouseNo());
        if (!TenantIdCheckUtil.isXm(inWarehouseTenantId)) {
            if (!CollectionUtils.isEmpty(stockStorageItems)) {
                skuConvertCommandService.setSaasSkuIdForList(stockStorageItems);
            }
        }
        // end region

        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        List<Long> stockStorageIdList = stockStorageItems.stream().map(StockStorageItem::getId).collect(Collectors.toList());
        List<StockStorageItemDetail> itemDetailList = stockStorageItemDetailRepository.selectItemDetailBatch(stockStorageIdList);
        List<InBoundOrderDetail> inBoundOrderDetail =
                inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(stockStorageId).build());
        Map<String, List<InBoundOrderDetail>> InBoundOrderDetailMap = inBoundOrderDetail.stream().collect(Collectors.groupingBy(o -> o.getPurchaseNo() + o.getSku() + o.getShelfLife()));
        Map<Long, List<StockStorageItemDetail>> itemDetail = itemDetailList.stream().collect(Collectors.groupingBy(StockStorageItemDetail::getStockStorageItemId));
        String outStoreName = stockTaskStorage.getOutWarehouseName();
        String inStoreName = stockTaskStorage.getInWarehouseName();
        String fileName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + outStoreName + "至" + inStoreName;
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;

        Row row1 = sheet.createRow(rowIndex++);
        row1.createCell(0).setCellValue("调拨入库");
        row1.createCell(1).setCellValue(stockTaskStorage.getId());

        Row row2 = sheet.createRow(rowIndex++);
        row2.createCell(0).setCellValue("调拨单号");
        row2.createCell(1).setCellValue(stockTaskStorage.getSourceId());

        Row row3 = sheet.createRow(rowIndex++);
        row3.createCell(0).setCellValue("调出仓");
        row3.createCell(1).setCellValue(outStoreName);
        row3.createCell(2).setCellValue("调入仓");

        row3.createCell(3).setCellValue(inStoreName);

        Row row4 = sheet.createRow(rowIndex++);
        row4.createCell(0).setCellValue("实际入库时间");
        row4.createCell(1).setCellValue(stockTaskStorage.getUpdateTime() == null ? "" : stockTaskStorage.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        CellStyle baseCellyStyle = workbook.createCellStyle();
        // 水平居中
        baseCellyStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        baseCellyStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 空一行
        rowIndex++;

        List<String> title;
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title = Lists.newArrayList("类目类型", "类目名称", "sku", "商品名称", "规格", "商品归属", "储存区域", "包装", "应入数量", "调拨说明", "备注", "批次", "生产日期", "保质期", "实发数量", "实收数量");
        } else {
            title = Lists.newArrayList("类目类型", "类目名称", "货品ID", "自有编码", "货品名称", "规格", "储存区域", "规格单位", "应入数量", "调拨说明", "备注", "批次", "生产日期", "保质期", "实发数量", "实收数量");
        }

        Row titleRow = sheet.createRow(rowIndex++);
        for (int i = 0; i < title.size(); i++) {
            Cell titleCell = titleRow.createCell(i);
            titleCell.setCellValue(title.get(i));
            titleCell.setCellStyle(baseCellyStyle);
        }
        List<String> skus = stockStorageItems.stream().map(StockStorageItem::getSku).distinct().collect(Collectors.toList());
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockTaskStorage.getInWarehouseNo().longValue(), skus);

        for (StockStorageItem stockItem : stockStorageItems) {
            List<StockStorageItemDetail> itemDetails = itemDetail.get(stockItem.getId());

            Product product = productMap.getOrDefault(stockItem.getSku(), Product.builder().build());
            AdminFacadeDTO admin = adminUtil.getAdmin(product.getInventoryAdminId());
            String remake = Objects.isNull(admin) ? null : admin.getRealName();

            if (CollectionUtils.isEmpty(itemDetails)) {

                Row row = sheet.createRow(rowIndex);
                Cell fristCategory = row.createCell(0);
                fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                fristCategory.setCellStyle(baseCellyStyle);

                Cell secondCategory = row.createCell(1);
                secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                secondCategory.setCellStyle(baseCellyStyle);

                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    Cell sku = row.createCell(2);
                    sku.setCellValue(stockItem.getSku());
                    sku.setCellStyle(baseCellyStyle);

                    Cell pdName = row.createCell(3);
                    pdName.setCellValue(stockItem.getPdName());
                    pdName.setCellStyle(baseCellyStyle);

                    Cell weight = row.createCell(4);
                    weight.setCellValue(stockItem.getSpecification());
                    weight.setCellStyle(baseCellyStyle);

                    String productType = StringUtils.productType(stockItem.getSkuType(), remake);
                    Cell productsType = row.createCell(5);
                    productsType.setCellValue(productType);
                    productsType.setCellStyle(baseCellyStyle);

                    Cell storageArea = row.createCell(6);
                    storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                    storageArea.setCellStyle(baseCellyStyle);

                    Cell packing = row.createCell(7);
                    packing.setCellValue(stockItem.getPackaging());
                    packing.setCellStyle(baseCellyStyle);

                    Cell outQuantity = row.createCell(8);
                    outQuantity.setCellValue(stockItem.getQuantity());
                    outQuantity.setCellStyle(baseCellyStyle);

                    Cell reason = row.createCell(9);
                    reason.setCellValue("");
                    reason.setCellStyle(baseCellyStyle);

                    Cell remark = row.createCell(10);
                    remark.setCellValue(stockItem.getRemark());
                    remark.setCellStyle(baseCellyStyle);
                } else {
                    Cell sku = row.createCell(2);
                    sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                    sku.setCellStyle(baseCellyStyle);

                    Cell saasCustomSkuCode = row.createCell(3);
                    saasCustomSkuCode.setCellValue(Objects.nonNull(stockItem.getSaasCustomSkuCode()) ? String.valueOf(stockItem.getSaasCustomSkuCode()) : "");
                    saasCustomSkuCode.setCellStyle(baseCellyStyle);

                    Cell pdName = row.createCell(4);
                    pdName.setCellValue(stockItem.getPdName());
                    pdName.setCellStyle(baseCellyStyle);

                    Cell weight = row.createCell(5);
                    weight.setCellValue(stockItem.getSpecification());
                    weight.setCellStyle(baseCellyStyle);

                    Cell storageArea = row.createCell(6);
                    storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                    storageArea.setCellStyle(baseCellyStyle);

                    Cell packing = row.createCell(7);
                    packing.setCellValue(stockItem.getPackaging());
                    packing.setCellStyle(baseCellyStyle);

                    Cell outQuantity = row.createCell(8);
                    outQuantity.setCellValue(stockItem.getQuantity());
                    outQuantity.setCellStyle(baseCellyStyle);

                    Cell reason = row.createCell(9);
                    reason.setCellValue("");
                    reason.setCellStyle(baseCellyStyle);

                    Cell remark = row.createCell(10);
                    remark.setCellValue(stockItem.getRemark());
                    remark.setCellStyle(baseCellyStyle);
                }

                rowIndex++;
            } else {
                if (itemDetails.size() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 4, 4));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 5, 5));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 6, 6));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 7, 7));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 8, 8));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 9, 9));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 10, 10));
                }
                for (StockStorageItemDetail itemDetailMsg : itemDetails) {
                    String key = itemDetailMsg.getPurchaseNo() + stockItem.getSku() + DateUtil.toMill(itemDetailMsg.getQualityDate());
                    List<InBoundOrderDetail> details = InBoundOrderDetailMap.get(key);
                    int sum = CollectionUtils.isEmpty(details) ? 0 : details.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                    Row row = sheet.createRow(rowIndex);

                    Cell fristCategory = row.createCell(0);
                    fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                    fristCategory.setCellStyle(baseCellyStyle);

                    Cell secondCategory = row.createCell(1);
                    secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                    secondCategory.setCellStyle(baseCellyStyle);


                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        Cell sku = row.createCell(2);
                        sku.setCellValue(stockItem.getSku());
                        sku.setCellStyle(baseCellyStyle);

                        Cell pdName = row.createCell(3);
                        pdName.setCellValue(stockItem.getPdName());
                        pdName.setCellStyle(baseCellyStyle);

                        Cell weight = row.createCell(4);
                        weight.setCellValue(stockItem.getSpecification());
                        weight.setCellStyle(baseCellyStyle);

                        String productType = StringUtils.productType(stockItem.getSkuType(), remake);
                        Cell productsType = row.createCell(5);
                        productsType.setCellValue(productType);
                        productsType.setCellStyle(baseCellyStyle);

                        Cell storageArea = row.createCell(6);
                        storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                        storageArea.setCellStyle(baseCellyStyle);

                        Cell packing = row.createCell(7);
                        packing.setCellValue(stockItem.getPackaging());
                        packing.setCellStyle(baseCellyStyle);

                        Cell outQuantity = row.createCell(8);
                        outQuantity.setCellValue(stockItem.getQuantity());
                        outQuantity.setCellStyle(baseCellyStyle);

                        Cell reason = row.createCell(9);
                        reason.setCellValue("");
                        reason.setCellStyle(baseCellyStyle);

                        Cell remark = row.createCell(10);
                        remark.setCellValue(stockItem.getRemark());
                        remark.setCellStyle(baseCellyStyle);

                        Cell purchaseNo = row.createCell(11);
                        purchaseNo.setCellValue(itemDetailMsg.getPurchaseNo());
                        purchaseNo.setCellStyle(baseCellyStyle);

                        Cell productionDate = row.createCell(12);
                        productionDate.setCellValue(itemDetailMsg.getProductionDate() == null ? "" : itemDetailMsg.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        productionDate.setCellStyle(baseCellyStyle);

                        Cell qualityDate = row.createCell(13);
                        qualityDate.setCellValue(itemDetailMsg.getQualityDate() == null ? "" : itemDetailMsg.getQualityDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        qualityDate.setCellStyle(baseCellyStyle);

                        Cell shouldQuantity = row.createCell(14);
                        shouldQuantity.setCellValue(itemDetailMsg.getShouldQuantity());
                        shouldQuantity.setCellStyle(baseCellyStyle);

                        Cell inQuantity = row.createCell(15);
                        inQuantity.setCellValue(sum);
                        inQuantity.setCellStyle(baseCellyStyle);
                    } else {
                        Cell sku = row.createCell(2);
                        sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                        sku.setCellStyle(baseCellyStyle);

                        Cell saasCustomSkuCode = row.createCell(3);
                        saasCustomSkuCode.setCellValue(Objects.nonNull(stockItem.getSaasCustomSkuCode()) ? String.valueOf(stockItem.getSaasCustomSkuCode()) : "");
                        saasCustomSkuCode.setCellStyle(baseCellyStyle);

                        Cell pdName = row.createCell(4);
                        pdName.setCellValue(stockItem.getPdName());
                        pdName.setCellStyle(baseCellyStyle);

                        Cell weight = row.createCell(5);
                        weight.setCellValue(stockItem.getSpecification());
                        weight.setCellStyle(baseCellyStyle);

                        Cell storageArea = row.createCell(6);
                        storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                        storageArea.setCellStyle(baseCellyStyle);

                        Cell packing = row.createCell(7);
                        packing.setCellValue(stockItem.getPackaging());
                        packing.setCellStyle(baseCellyStyle);

                        Cell outQuantity = row.createCell(8);
                        outQuantity.setCellValue(stockItem.getQuantity());
                        outQuantity.setCellStyle(baseCellyStyle);

                        Cell reason = row.createCell(9);
                        reason.setCellValue("");
                        reason.setCellStyle(baseCellyStyle);

                        Cell remark = row.createCell(10);
                        remark.setCellValue(stockItem.getRemark());
                        remark.setCellStyle(baseCellyStyle);

                        Cell purchaseNo = row.createCell(11);
                        purchaseNo.setCellValue(itemDetailMsg.getPurchaseNo());
                        purchaseNo.setCellStyle(baseCellyStyle);

                        Cell productionDate = row.createCell(12);
                        productionDate.setCellValue(itemDetailMsg.getProductionDate() == null ? "" : itemDetailMsg.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        productionDate.setCellStyle(baseCellyStyle);

                        Cell qualityDate = row.createCell(13);
                        qualityDate.setCellValue(itemDetailMsg.getQualityDate() == null ? "" : itemDetailMsg.getQualityDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        qualityDate.setCellStyle(baseCellyStyle);

                        Cell shouldQuantity = row.createCell(14);
                        shouldQuantity.setCellValue(itemDetailMsg.getShouldQuantity());
                        shouldQuantity.setCellStyle(baseCellyStyle);

                        Cell inQuantity = row.createCell(15);
                        inQuantity.setCellValue(sum);
                        inQuantity.setCellStyle(baseCellyStyle);
                    }

                    rowIndex++;
                }
            }
        }
        try {
            if (!TenantIdCheckUtil.isXm(tenantId)) {
                ByteArrayInputStream inputStream = convertStream(workbook);
                OssUploadResult upload = OssUploadUtil.upload(fileName + ".xls", inputStream, OSSExpiredLabelEnum.MONTH);
                saasDownloadFacade.downloadTaskSuccess(saasDownloadId, upload.getUrl());
            } else {
                ExcelUtils.outputExcel(workbook, fileName + ".xls", RequestHolder.getResponse());
            }
        } catch (IOException e) {
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 调拨入库 任务下载 精细化管理
     *
     * @param stockStorageId 任务id
     */
    private void allocationInTaskDownV2(Long stockStorageId, Long tenantId, Long saasDownloadId) throws IOException {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        if (null == stockTaskStorage) {
            throw new net.xianmu.common.exception.BizException("未查询到入库任务记录");
        }
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockTaskStorage.getId());

        // region saas sku id
        Long inWarehouseTenantId = this.getInWarehouseTenantId(stockTaskStorage.getInWarehouseNo());
        if (!TenantIdCheckUtil.isXm(inWarehouseTenantId)) {
            if (!CollectionUtils.isEmpty(stockStorageItems)) {
                skuConvertCommandService.setSaasSkuIdForList(stockStorageItems);
            }
        }
        // end region

        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        List<Long> stockStorageIdList = stockStorageItems.stream().map(StockStorageItem::getId).collect(Collectors.toList());
        List<StockStorageItemDetail> itemDetailList = stockStorageItemDetailRepository.selectItemDetailBatch(stockStorageIdList);
        List<InBoundOrderDetail> inBoundOrderDetail =
                inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(stockStorageId).build());
        Map<String, List<InBoundOrderDetail>> InBoundOrderDetailMap = inBoundOrderDetail.stream().collect(Collectors.groupingBy(o -> o.getPurchaseNo() + o.getSku() + o.getShelfLife()));
        Map<Long, List<StockStorageItemDetail>> itemDetail = itemDetailList.stream().collect(Collectors.groupingBy(StockStorageItemDetail::getStockStorageItemId));
        String outStoreName = stockTaskStorage.getOutWarehouseName();
        String inStoreName = stockTaskStorage.getInWarehouseName();
        String fileName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + outStoreName + "至" + inStoreName;
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;

        Row row1 = sheet.createRow(rowIndex++);
        row1.createCell(0).setCellValue("调拨入库");
        row1.createCell(1).setCellValue(stockTaskStorage.getId());

        Row row2 = sheet.createRow(rowIndex++);
        row2.createCell(0).setCellValue("调拨单号");
        row2.createCell(1).setCellValue(stockTaskStorage.getSourceId());

        Row row3 = sheet.createRow(rowIndex++);
        row3.createCell(0).setCellValue("调出仓");
        row3.createCell(1).setCellValue(outStoreName);
        row3.createCell(2).setCellValue("调入仓");

        row3.createCell(3).setCellValue(inStoreName);

        Row row4 = sheet.createRow(rowIndex++);
        row4.createCell(0).setCellValue("实际入库时间");
        row4.createCell(1).setCellValue(stockTaskStorage.getUpdateTime() == null ? "" : stockTaskStorage.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Row row5 = sheet.createRow(rowIndex++);
//        row5.createCell(0).setCellValue("货主");
//        row5.createCell(1).setCellValue(StringUtils.isBlank(stockTaskStorage.getCargoOwner()) ? "" : stockTaskStorage.getCargoOwner());
        row5.createCell(0).setCellValue("确认到货时间");
        row5.createCell(1).setCellValue(stockTaskStorage.getArrivalTime() == null ? "" : stockTaskStorage.getArrivalTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        CellStyle baseCellyStyle = workbook.createCellStyle();
        // 水平居中
        baseCellyStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        baseCellyStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 空一行
        rowIndex++;

        List<String> title;
        if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
            title = Lists.newArrayList("类目类型", "类目名称", "sku", "商品名称", "规格", "商品归属", "储存区域", "包装", "应入数量", "调拨说明", "备注", "批次", "收货容器", "生产日期", "保质期", "实发数量", "实收数量");
        } else {
            title = Lists.newArrayList("类目类型", "类目名称", "货品ID", "货品名称", "规格", "储存区域", "规格单位", "应入数量", "调拨说明", "备注", "批次", "生产日期", "保质期", "实发数量", "实收数量");
        }

        Row titleRow = sheet.createRow(rowIndex++);
        for (int i = 0; i < title.size(); i++) {
            Cell titleCell = titleRow.createCell(i);
            titleCell.setCellValue(title.get(i));
            titleCell.setCellStyle(baseCellyStyle);
        }
        List<String> skus = stockStorageItems.stream().map(StockStorageItem::getSku).distinct().collect(Collectors.toList());
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockTaskStorage.getInWarehouseNo().longValue(), skus);

        for (StockStorageItem stockItem : stockStorageItems) {

            Product product = productMap.getOrDefault(stockItem.getSku(), Product.builder().build());
            AdminFacadeDTO admin = adminUtil.getAdmin(product.getInventoryAdminId());
            String remake = Objects.isNull(admin) ? null : admin.getRealName();

            List<StockStorageItemDetail> itemDetails = itemDetail.get(stockItem.getId());
            if (CollectionUtils.isEmpty(itemDetails)) {

                Row row = sheet.createRow(rowIndex);
                Cell fristCategory = row.createCell(0);
                fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                fristCategory.setCellStyle(baseCellyStyle);

                Cell secondCategory = row.createCell(1);
                secondCategory.setCellValue(product.getCategory());
                secondCategory.setCellStyle(baseCellyStyle);

                Cell sku = row.createCell(2);
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    sku.setCellValue(stockItem.getSku());
                } else {
                    sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                }
                sku.setCellStyle(baseCellyStyle);

                Cell pdName = row.createCell(3);
                pdName.setCellValue(stockItem.getPdName());
                pdName.setCellStyle(baseCellyStyle);

                Cell weight = row.createCell(4);
                weight.setCellValue(stockItem.getSpecification());
                weight.setCellStyle(baseCellyStyle);

                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    String productType = StringUtils.productType(stockItem.getSkuType(), remake);
                    Cell productsType = row.createCell(5);
                    productsType.setCellValue(productType);
                    productsType.setCellStyle(baseCellyStyle);

                    Cell storageArea = row.createCell(6);
                    storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                    storageArea.setCellStyle(baseCellyStyle);

                    Cell packing = row.createCell(7);
                    packing.setCellValue(stockItem.getPackaging());
                    packing.setCellStyle(baseCellyStyle);

                    Cell outQuantity = row.createCell(8);
                    outQuantity.setCellValue(stockItem.getQuantity());
                    outQuantity.setCellStyle(baseCellyStyle);

                    Cell reason = row.createCell(9);
                    reason.setCellValue("");
                    reason.setCellStyle(baseCellyStyle);

                    Cell remark = row.createCell(10);
                    remark.setCellValue(stockItem.getRemark());
                    remark.setCellStyle(baseCellyStyle);
                } else {
                    Cell storageArea = row.createCell(5);
                    storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                    storageArea.setCellStyle(baseCellyStyle);

                    Cell packing = row.createCell(6);
                    packing.setCellValue(stockItem.getPackaging());
                    packing.setCellStyle(baseCellyStyle);

                    Cell outQuantity = row.createCell(7);
                    outQuantity.setCellValue(stockItem.getQuantity());
                    outQuantity.setCellStyle(baseCellyStyle);

                    Cell reason = row.createCell(8);
                    reason.setCellValue("");
                    reason.setCellStyle(baseCellyStyle);

                    Cell remark = row.createCell(9);
                    remark.setCellValue(stockItem.getRemark());
                    remark.setCellStyle(baseCellyStyle);
                }
                rowIndex++;
            } else {
                if (itemDetails.size() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 4, 4));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 5, 5));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 6, 6));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 7, 7));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 8, 8));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 9, 9));
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 10, 10));
                    }
                }
                for (StockStorageItemDetail itemDetailMsg : itemDetails) {
                    String key = itemDetailMsg.getPurchaseNo() + stockItem.getSku() + DateUtil.toMill(itemDetailMsg.getQualityDate());
                    List<InBoundOrderDetail> details = InBoundOrderDetailMap.get(key);
                    int sum = CollectionUtils.isEmpty(details) ? 0 : details.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                    Row row = sheet.createRow(rowIndex);

                    Cell fristCategory = row.createCell(0);
                    fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                    fristCategory.setCellStyle(baseCellyStyle);

                    Cell secondCategory = row.createCell(1);
                    secondCategory.setCellValue(product.getCategory());
                    secondCategory.setCellStyle(baseCellyStyle);

                    Cell sku = row.createCell(2);
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        sku.setCellValue(stockItem.getSku());
                    } else {
                        sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                    }
                    sku.setCellStyle(baseCellyStyle);

                    Cell pdName = row.createCell(3);
                    pdName.setCellValue(stockItem.getPdName());
                    pdName.setCellStyle(baseCellyStyle);

                    Cell weight = row.createCell(4);
                    weight.setCellValue(stockItem.getSpecification());
                    weight.setCellStyle(baseCellyStyle);

                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        String productType = StringUtils.productType(stockItem.getSkuType(), remake);
                        Cell productsType = row.createCell(5);
                        productsType.setCellValue(productType);
                        productsType.setCellStyle(baseCellyStyle);

                        Cell storageArea = row.createCell(6);
                        storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                        storageArea.setCellStyle(baseCellyStyle);

                        Cell packing = row.createCell(7);
                        packing.setCellValue(stockItem.getPackaging());
                        packing.setCellStyle(baseCellyStyle);

                        Cell outQuantity = row.createCell(8);
                        outQuantity.setCellValue(stockItem.getQuantity());
                        outQuantity.setCellStyle(baseCellyStyle);

                        Cell reason = row.createCell(9);
                        reason.setCellValue("");
                        reason.setCellStyle(baseCellyStyle);

                        Cell remark = row.createCell(10);
                        remark.setCellValue(stockItem.getRemark());
                        remark.setCellStyle(baseCellyStyle);

                        Cell purchaseNo = row.createCell(11);
                        purchaseNo.setCellValue(itemDetailMsg.getPurchaseNo());
                        purchaseNo.setCellStyle(baseCellyStyle);

                        Cell receivingContainer = row.createCell(12);
                        receivingContainer.setCellValue(itemDetailMsg.getReceivingContainer() == null ? "" : itemDetailMsg.getReceivingContainer());
                        receivingContainer.setCellStyle(baseCellyStyle);

                        Cell productionDate = row.createCell(13);
                        productionDate.setCellValue(itemDetailMsg.getProductionDate() == null ? "" : itemDetailMsg.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        productionDate.setCellStyle(baseCellyStyle);

                        Cell qualityDate = row.createCell(14);
                        qualityDate.setCellValue(itemDetailMsg.getQualityDate() == null ? "" : itemDetailMsg.getQualityDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        qualityDate.setCellStyle(baseCellyStyle);

                        Cell shouldQuantity = row.createCell(15);
                        shouldQuantity.setCellValue(itemDetailMsg.getShouldQuantity() == 0 && itemDetailMsg.getActualInQuantity() > 0 ?
                                itemDetailMsg.getActualInQuantity() : itemDetailMsg.getShouldQuantity());
                        shouldQuantity.setCellStyle(baseCellyStyle);

                        Cell inQuantity = row.createCell(16);
                        inQuantity.setCellValue(itemDetailMsg.getActualInQuantity());
                        inQuantity.setCellStyle(baseCellyStyle);
                    } else {
                        Cell storageArea = row.createCell(5);
                        storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                        storageArea.setCellStyle(baseCellyStyle);

                        Cell packing = row.createCell(6);
                        packing.setCellValue(stockItem.getPackaging());
                        packing.setCellStyle(baseCellyStyle);

                        Cell outQuantity = row.createCell(7);
                        outQuantity.setCellValue(stockItem.getQuantity());
                        outQuantity.setCellStyle(baseCellyStyle);

                        Cell reason = row.createCell(8);
                        reason.setCellValue("");
                        reason.setCellStyle(baseCellyStyle);

                        Cell remark = row.createCell(9);
                        remark.setCellValue(stockItem.getRemark());
                        remark.setCellStyle(baseCellyStyle);

                        Cell purchaseNo = row.createCell(10);
                        purchaseNo.setCellValue(itemDetailMsg.getPurchaseNo());
                        purchaseNo.setCellStyle(baseCellyStyle);

                        Cell productionDate = row.createCell(11);
                        productionDate.setCellValue(itemDetailMsg.getProductionDate() == null ? "" : itemDetailMsg.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        productionDate.setCellStyle(baseCellyStyle);

                        Cell qualityDate = row.createCell(12);
                        qualityDate.setCellValue(itemDetailMsg.getQualityDate() == null ? "" : itemDetailMsg.getQualityDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        qualityDate.setCellStyle(baseCellyStyle);

                        Cell shouldQuantity = row.createCell(13);
                        shouldQuantity.setCellValue(itemDetailMsg.getShouldQuantity() == 0 && itemDetailMsg.getActualInQuantity() > 0 ?
                                itemDetailMsg.getActualInQuantity() : itemDetailMsg.getShouldQuantity());
                        shouldQuantity.setCellStyle(baseCellyStyle);

                        Cell inQuantity = row.createCell(14);
                        inQuantity.setCellValue(itemDetailMsg.getActualInQuantity());
                        inQuantity.setCellStyle(baseCellyStyle);
                    }

                    rowIndex++;
                }
            }
        }
        try {
            if (!TenantIdCheckUtil.isXm(tenantId)) {
                ByteArrayInputStream inputStream = convertStream(workbook);
                OssUploadResult upload = OssUploadUtil.upload(fileName + ".xls", inputStream, OSSExpiredLabelEnum.MONTH);
                saasDownloadFacade.downloadTaskSuccess(saasDownloadId, upload.getUrl());
            } else {
                ExcelUtils.outputExcel(workbook, fileName + ".xls", RequestHolder.getResponse());
            }
        } catch (IOException e) {
            throw new DefaultServiceException("导出异常");
        }
    }


    /**
     * 调拨回库 任务下载
     */
    private void allocationBackTaskDown(Long stockStorageId, Long tenantId, Long saasDownloadId) throws IOException {
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageId);
        if (null == stockTaskStorage) {
            throw new net.xianmu.common.exception.BizException("未查询到入库任务记录");
        }
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockTaskStorage.getId());

        // region saas sku id
        Long inWarehouseTenantId = this.getInWarehouseTenantId(stockTaskStorage.getInWarehouseNo());
        if (!TenantIdCheckUtil.isXm(inWarehouseTenantId)) {
            if (!CollectionUtils.isEmpty(stockStorageItems)) {
                skuConvertCommandService.setSaasSkuIdForList(stockStorageItems);
            }
        }
        // end region

        // 仓库租户ID
        Long warehouseTenantId = null;
        if (stockTaskStorage != null && stockTaskStorage.getInWarehouseNo() != null) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService
                    .selectByWarehouseNo(stockTaskStorage.getInWarehouseNo());
            warehouseTenantId = warehouseStorageCenter == null ? null :
                    warehouseStorageCenter.getTenantId();
        }

        List<Long> stockStorageIdList = stockStorageItems.stream().map(StockStorageItem::getId).collect(Collectors.toList());
        List<StockStorageItemDetail> itemDetailList = stockStorageItemDetailRepository.selectItemDetailBatch(stockStorageIdList);
        List<InBoundOrderDetail> inBoundOrderDetail =
                inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(stockStorageId).build());
        Map<String, List<InBoundOrderDetail>> InBoundOrderDetailMap = inBoundOrderDetail.stream().collect(Collectors.groupingBy(o -> o.getPurchaseNo() + o.getSku() + o.getShelfLife()));
        Map<Long, List<StockStorageItemDetail>> itemDetail = itemDetailList.stream().collect(Collectors.groupingBy(StockStorageItemDetail::getStockStorageItemId));
        String inStoreName = stockTaskStorage.getInWarehouseName();
        String fileName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + inStoreName;
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;

        Row row1 = sheet.createRow(rowIndex++);
        row1.createCell(0).setCellValue("调拨回库");
        row1.createCell(1).setCellValue(stockTaskStorage.getId());

        Row row2 = sheet.createRow(rowIndex++);
        row2.createCell(0).setCellValue("调拨单号");
        row2.createCell(1).setCellValue(stockTaskStorage.getSourceId());

        Row row3 = sheet.createRow(rowIndex++);
        row3.createCell(0).setCellValue("调出仓");
        row3.createCell(1).setCellValue("");
        row3.createCell(2).setCellValue("调入仓");

        row3.createCell(3).setCellValue(inStoreName);

        Row row4 = sheet.createRow(rowIndex++);
        row4.createCell(0).setCellValue("实际入库时间");
        row4.createCell(1).setCellValue(stockTaskStorage.getUpdateTime() == null ? "" : stockTaskStorage.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        CellStyle baseCellyStyle = workbook.createCellStyle();
        // 水平居中
        baseCellyStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        baseCellyStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 空一行
        rowIndex++;
        List<String> title;
        title = Lists.newArrayList("类目类型", "类目名称", "货品ID", "自有编码", "货品名称", "规格", "储存区域", "规格单位", "应入数量", "调拨说明", "备注", "批次", "生产日期", "有效期至", "实发数量", "实收数量");
        Row titleRow = sheet.createRow(rowIndex++);
        for (int i = 0; i < title.size(); i++) {
            Cell titleCell = titleRow.createCell(i);
            titleCell.setCellValue(title.get(i));
            titleCell.setCellStyle(baseCellyStyle);
        }
        List<String> skus = stockStorageItems.stream().map(StockStorageItem::getSku).distinct().collect(Collectors.toList());
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(stockTaskStorage.getInWarehouseNo().longValue(), skus);

        for (StockStorageItem stockItem : stockStorageItems) {
            Product product = productMap.getOrDefault(stockItem.getSku(), Product.builder().build());

            List<StockStorageItemDetail> itemDetails = itemDetail.get(stockItem.getId());


            if (CollectionUtils.isEmpty(itemDetails)) {

                Row row = sheet.createRow(rowIndex);
                Cell fristCategory = row.createCell(0);
                fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                fristCategory.setCellStyle(baseCellyStyle);

                Cell secondCategory = row.createCell(1);
                secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                secondCategory.setCellStyle(baseCellyStyle);

                Cell sku = row.createCell(2);
                if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                    sku.setCellValue(stockItem.getSku());
                } else {
                    sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                }
                sku.setCellStyle(baseCellyStyle);

                Cell saasCustomSkuCode = row.createCell(3);
                saasCustomSkuCode.setCellValue(stockItem.getSaasCustomSkuCode() != null ? stockItem.getSaasCustomSkuCode() : "");
                saasCustomSkuCode.setCellStyle(baseCellyStyle);

                Cell pdName = row.createCell(4);
                pdName.setCellValue(stockItem.getPdName());
                pdName.setCellStyle(baseCellyStyle);

                Cell weight = row.createCell(5);
                weight.setCellValue(stockItem.getSpecification());
                weight.setCellStyle(baseCellyStyle);

                Cell storageArea = row.createCell(6);
                storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                storageArea.setCellStyle(baseCellyStyle);

                Cell packing = row.createCell(7);
                packing.setCellValue(stockItem.getPackaging());
                packing.setCellStyle(baseCellyStyle);

                Cell outQuantity = row.createCell(8);
                outQuantity.setCellValue(stockItem.getQuantity());
                outQuantity.setCellStyle(baseCellyStyle);

                Cell reason = row.createCell(9);
                reason.setCellValue("");
                reason.setCellStyle(baseCellyStyle);

                Cell remark = row.createCell(10);
                remark.setCellValue(stockItem.getRemark());
                remark.setCellStyle(baseCellyStyle);
                rowIndex++;
            } else {
                if (itemDetails.size() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 4, 4));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 5, 5));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 6, 6));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 7, 7));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 8, 8));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 9, 9));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetails.size() - 1, 10, 10));
                }
                for (StockStorageItemDetail itemDetailMsg : itemDetails) {
                    String key = itemDetailMsg.getPurchaseNo() + stockItem.getSku() + DateUtil.toMill(itemDetailMsg.getQualityDate());
                    List<InBoundOrderDetail> details = InBoundOrderDetailMap.get(key);
                    int sum = CollectionUtils.isEmpty(details) ? 0 : details.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                    Row row = sheet.createRow(rowIndex);

                    Cell fristCategory = row.createCell(0);
                    fristCategory.setCellValue(CategoryEnum.getValue(stockItem.getCategoryType()));
                    fristCategory.setCellStyle(baseCellyStyle);

                    Cell secondCategory = row.createCell(1);
                    secondCategory.setCellValue(TenantIdCheckUtil.isXm(warehouseTenantId) ? product.getCategory() : product.getSecondCategory());
                    secondCategory.setCellStyle(baseCellyStyle);

                    Cell sku = row.createCell(2);
                    if (TenantIdCheckUtil.isXm(warehouseTenantId)) {
                        sku.setCellValue(stockItem.getSku());
                    } else {
                        sku.setCellValue(Objects.nonNull(stockItem.getSaasSkuId()) ? String.valueOf(stockItem.getSaasSkuId()) : "");
                    }
                    sku.setCellStyle(baseCellyStyle);

                    Cell saasCustomSkuCode = row.createCell(3);
                    saasCustomSkuCode.setCellValue(stockItem.getSaasCustomSkuCode() != null ? stockItem.getSaasCustomSkuCode() : "");
                    saasCustomSkuCode.setCellStyle(baseCellyStyle);


                    Cell pdName = row.createCell(4);
                    pdName.setCellValue(stockItem.getPdName());
                    pdName.setCellStyle(baseCellyStyle);

                    Cell weight = row.createCell(5);
                    weight.setCellValue(stockItem.getSpecification());
                    weight.setCellStyle(baseCellyStyle);

                    Cell storageArea = row.createCell(6);
                    storageArea.setCellValue(StorageLocation.getTypeById(product.getTemperature()));
                    storageArea.setCellStyle(baseCellyStyle);

                    Cell packing = row.createCell(7);
                    packing.setCellValue(stockItem.getPackaging());
                    packing.setCellStyle(baseCellyStyle);

                    Cell outQuantity = row.createCell(8);
                    outQuantity.setCellValue(stockItem.getQuantity());
                    outQuantity.setCellStyle(baseCellyStyle);

                    Cell reason = row.createCell(9);
                    reason.setCellValue("");
                    reason.setCellStyle(baseCellyStyle);

                    Cell remark = row.createCell(10);
                    remark.setCellValue(stockItem.getRemark());
                    remark.setCellStyle(baseCellyStyle);

                    Cell purchaseNo = row.createCell(11);
                    purchaseNo.setCellValue(itemDetailMsg.getPurchaseNo());
                    purchaseNo.setCellStyle(baseCellyStyle);

                    Cell productionDate = row.createCell(12);
                    productionDate.setCellValue(itemDetailMsg.getProductionDate() == null ? "" : itemDetailMsg.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    productionDate.setCellStyle(baseCellyStyle);

                    Cell qualityDate = row.createCell(13);
                    qualityDate.setCellValue(itemDetailMsg.getQualityDate() == null ? "" : itemDetailMsg.getQualityDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    qualityDate.setCellStyle(baseCellyStyle);

                    Cell shouldQuantity = row.createCell(14);
                    shouldQuantity.setCellValue(itemDetailMsg.getActualInQuantity());
                    shouldQuantity.setCellStyle(baseCellyStyle);

                    Cell inQuantity = row.createCell(15);
                    inQuantity.setCellValue(sum);
                    inQuantity.setCellStyle(baseCellyStyle);

                    rowIndex++;
                }
            }
        }
        try {
            if (!TenantIdCheckUtil.isXm(tenantId)) {
                ByteArrayInputStream inputStream = convertStream(workbook);
                OssUploadResult upload = OssUploadUtil.upload(fileName + ".xls", inputStream, OSSExpiredLabelEnum.MONTH);
                saasDownloadFacade.downloadTaskSuccess(saasDownloadId, upload.getUrl());
            } else {
                ExcelUtils.outputExcel(workbook, fileName + ".xls", RequestHolder.getResponse());
            }
        } catch (IOException e) {
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 确认到货
     *
     * @param command c
     * @return r
     */
    @Override
    public void arrivalCargo(CargoArrivalCommand command) {
        // 查询任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());

        // 校验任务
        checkArrivalCargo(stockTaskStorage);

        changeReceiveState(stockTaskStorage, StockTaskLogOperateType.ARR_CARGO);

        // 越库入库任务收货触发生成越库出库任务
        if (Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())) {
            // 查询任务 获取最新的收货状态
            stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());
            if (StockTaskStorageCabinetDelicacyProcessStateEnum.RECEIVED.getId().equals(stockTaskStorage.getProcessState())) {
                // 发送生成越库出库任务消息
                sendGenerateCrossStockTaskEvent(stockTaskStorage.getInWarehouseNo(), stockTaskStorage.getExpectTime());
            }
        }

    }

    /**
     * 初始化越库任务
     * @param warehouseNo
     * @param expectTime
     */
    @Override
    public void initCrossStockTask(Integer warehouseNo, LocalDateTime expectTime) {
        sendGenerateCrossStockTaskEvent(warehouseNo, expectTime);
    }

    /**
     * 校验收货
     * @param stockTaskStorage 入库任务信息
     */
    private void checkArrivalCargo(StockTaskStorage stockTaskStorage) {
        // 校验任务状态
        if (!Objects.equals(stockTaskStorage.getState(), StockTaskStorageStateEnum.NORMAL.getId()) ||
                !StockTaskStorageReceivingStateEnum.toArr.contains(stockTaskStorage.getReceivingState())) {
            throw new net.xianmu.common.exception.BizException("当前任务状态不允许操作确认收货");
        }
        // 配置不生效
        if (crossWarehouseArrivalCargoConfig.getEffect() == null || !crossWarehouseArrivalCargoConfig.getEffect()) {
            log.info("确认到货时间限制:{}", JSON.toJSONString(crossWarehouseArrivalCargoConfig));
            return;
        }
        // 越库入库类型收货时间校验
        if (Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())) {
            LocalDateTime expectTime = stockTaskStorage.getExpectTime();
            //   读取nacos 动态配置 时间限制
            LocalDateTime startTime =  expectTime.withHour(crossWarehouseArrivalCargoConfig.getHour())
                    .withMinute(crossWarehouseArrivalCargoConfig.getMinute())
                    .withSecond(crossWarehouseArrivalCargoConfig.getSecond());
            // 期望入库时间当前的时间 > 限制时间不做限制
            if (LocalDateTime.now().isBefore(startTime)) {
                String warn = "请在 " + DateUtil.formatDate(startTime) + " 时间后再操作确认到货";
                throw new net.xianmu.common.exception.BizException(warn);
            }
        }
    }

    /**
     * 发送生成越库出库任务事件
     *
     * @param warehouseNo 仓库编号
     * @param expectTime  期望送达时间
     */
    private void sendGenerateCrossStockTaskEvent(Integer warehouseNo, LocalDateTime expectTime) {
        log.info("发送生成越库出库任务事件 仓库编号:{}, 期望送达时间:{}", warehouseNo, expectTime);
        if (warehouseNo == null || expectTime == null) {
            log.warn("发送生成越库出库任务事件 仓库编号/期望到货时间为空");
            return;
        }
        // 越库出库是时间为越库入库时间 +1 天
        CrossStockTaskGenerateEvent taskGenerateEvent =
                CrossStockTaskGenerateEvent.builder()
                        .warehouseNo(warehouseNo)
                        .exceptTime(expectTime.plusDays(1))
                        .build();
        // 发送
        mqProducer.send("topic_wms_stock_task", "tag_wms_gen_cross_stock_task", taskGenerateEvent);
    }

    /**
     * 完成本次到货
     *
     * @param command c
     * @return r
     */
    @Override
    public void arrivalCargoFinish(CargoArrivalCommand command) {
        // 查询任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());
        // 校验任务状态
        if (!Objects.equals(stockTaskStorage.getReceivingState(), StockTaskStorageReceivingStateEnum.CONFIRMED.getId())) {
            throw new net.xianmu.common.exception.BizException("请先确认到货,再完成本次收货");
        }
        changeReceiveState(stockTaskStorage, StockTaskLogOperateType.FINISH_ARR);
        // 完成收货生成越库投线任务
        if (OperationType.CROSS_WAREHOUSE_IN.getId() == stockTaskStorage.getType() && Boolean.TRUE.equals(command.getWebExe())) {
            crossWarehouseCommandService.triggerGenerateMission(stockTaskStorage.getId(), null, false, command.getState());
        }
    }

    @Override
    @OperatorAnnotation
    @Transactional(rollbackFor = Exception.class)
    public Long saveAndCompeteStockStorage(SaveCompeteStockStorageCommandDTO command) {
        // 对象填充
        StockStorageCommandDTO stockStorageCommand = stockStorageCommandFactory.fillStockStorageCommandDTO(command);
        // 创建任务
        Long taskId = this.createStockStorage(stockStorageCommand);
        stockStorageCommand.setTaskId(taskId);
        Boolean open = warehouseConfigRepository.openCabinetManagement(command.getInWarehouseNo());

        // 库位管理仓自动收货操作，获取容器
        String containerNo = null;
        if (Boolean.TRUE.equals(open)) {
            CargoArrivalCommand cargoArrivalCommand = CargoArrivalCommand.builder()
                    .stockStorageTaskId(taskId)
                    .operator(command.getUserOperatorName())
                    .warehouseNo(command.getInWarehouseNo().longValue())
                    .build();
            this.arrivalCargo(cargoArrivalCommand);

            containerNo = containerRepository.getAbleContainer(command.getInWarehouseNo(), command.getTenantId());
        }

        // 自动完成入库
        InBoundOrderCommand inBoundOrderCommand = inBoundOrderFactory.buildInBoundOrderCommand(
                stockStorageCommand, containerNo, open);
        inBoundOrderCommandService.createInBoundOrder(inBoundOrderCommand);
        return taskId;
    }

    @Override
    public void updateStockStorageExpectTime(StockStorageExpectTimeUpdateCommand updateCommand) {
        // 状态校验
        StockTaskStorage stockTaskStorage = StockTaskStorage.builder()
                .sourceId(updateCommand.getOutOrderNo())
                .type(updateCommand.getType())
                .build();
        List<StockTaskStorage> stockTaskStorageList = stockTaskStorageQueryRepository.selectListBySourceIdAndType(stockTaskStorage);
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(stockTaskStorageList), "未查询到对应入库任务信息");
        List<Integer> processStateList = stockTaskStorageList.stream().map(StockTaskStorage::getProcessState).distinct().collect(Collectors.toList());
        ExceptionUtil.checkAndThrow(processStateList.size() == 1 &&
                processStateList.contains(StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId()), "已有部分入库，不能再修改预约入库时间了");
        StockTaskStorage updateStockStorage = StockTaskStorage.builder()
                .sourceId(updateCommand.getOutOrderNo())
                .type(updateCommand.getType())
                .expectTime(updateCommand.getExpectTime())
                .operatorName(updateCommand.getOperator())
                .build();
        stockTaskStorageRepository.updateExpectTimeBySourceId(updateStockStorage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    // @IdempotentAnnotation(expireTime = 10, message = "采购单正在预约入库中，请勿重复提交")
    public Long arrangePurchaseIn(StockStorageCommandDTO stockStorageCommandDTO) {
        // 生成入库任务
        return this.createStockStorage(stockStorageCommandDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPurchaseIn(StockStorageCompleteReqDTO completeReqDTO) {
        this.cancelStockStorage(completeReqDTO);
    }

    private void changeReceiveState(StockTaskStorage stockTaskStorage, String operateType) {
        // 记录日志
        StoreTaskLog storeTaskLog = StoreTaskLog.builder()
                .bizId(String.valueOf(stockTaskStorage.getId()))
                .bizType(stockTaskStorage.getType())
                .operator(AdminUtil.getCurrentLoginAdminIdV2().toString())
                .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                .operateType(operateType)
                .build();
        storeTaskLogRepository.create(storeTaskLog);

        // 更新收货状态和到货时间
        StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder()
                .id(stockTaskStorage.getId())
                .receivingState(StockTaskStorageReceivingStateEnum.convert(stockTaskStorage.getReceivingState()).toNextState())
                .arrivalTime(StockTaskLogOperateType.FINISH_ARR.equals(operateType) ? null : LocalDateTime.now())
                .build();

        // 更新任务状态
        if (StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId().equals(stockTaskStorage.getProcessState()) &&
                StockTaskStorageReceivingStateEnum.NOT_CONFIRMED.getId().equals(stockTaskStorage.getReceivingState())) {
            updateStockTaskStorage.setProcessState(StockTaskStorageCabinetDelicacyProcessStateEnum.RECEIVED.getId());
        }
        stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);
    }

    private Long getInWarehouseTenantId(Integer inWarehouseNo) {
        if (Objects.isNull(inWarehouseNo)) {
            return null;
        }
        WarehouseStorageCenter storageCenter = warehouseStorageService.selectByWarehouseNo(inWarehouseNo);
        return Objects.nonNull(storageCenter) ? storageCenter.getTenantId() : null;
    }

    /**
     * 变更多出入库的条目信息
     *
     * @param outTaskId   出库任务id
     * @param warehouseNo 仓库编号
     * @param sku         sku
     * @param release     释放数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOutMoreInStockStorage(Integer outTaskId, Integer warehouseNo, String sku, Integer release) {
        log.info("更新多出入库明细 入参: 出库任务id:{}, 仓库编号:{}, sku:{}, 取消数量:{}", outTaskId, warehouseNo, sku, release);
        if (Objects.isNull(release) || release <= 0) {
            return;
        }
        // 通过出库任务id查询多出入库任务
        StockTaskStorage stockTaskStorageQuery = StockTaskStorage.builder()
                .sourceId(outTaskId.toString())
                .inWarehouseNo(warehouseNo)
                .type(StoreRecordType.OUT_MORE_IN.getId()).build();
        List<StockTaskStorage> taskStorageList = stockTaskStorageQueryRepository.queryStockTaskStorageByWarehouseNoAndSourceId(stockTaskStorageQuery);
        // 没有查询到多出入库信息
        if (CollectionUtils.isEmpty(taskStorageList)) {
            return;
        }
        // 租户
        Long tenantId = taskStorageList.get(0).getTenantId();
        // 入库任务过滤
        taskStorageList = taskStorageList.stream().filter(it -> {
            return Objects.equals(it.getState(), StockTaskStorageStateEnum.NORMAL.getId());
        }).collect(Collectors.toList());
        // 过滤任务状态后的任务
        if (CollectionUtils.isEmpty(taskStorageList)) {
            return;
        }
        List<Long> stockStorageIdList = taskStorageList.stream().map(StockTaskStorage::getId).collect(Collectors.toList());
        // 通过出库任务id查询入库明细id
        List<StockStorageItem> itemList = stockStorageItemRepository.batchQueryStockStorageItemByIdAndSku(stockStorageIdList, sku);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        // 排序
        itemList = itemList.stream()
                .sorted(Comparator.comparing(StockStorageItem::getId))
                .sorted().collect(Collectors.toList());
        // 需要释放sku数量
        Map<String, Integer> needReleaseSkuQuantityMap = new HashMap<>();
        // 对明细进行消费扣减
        for (StockStorageItem itemDetail : itemList) {
            if (release <= 0) {
                break;
            }
            Integer shouldQuantity = itemDetail.getQuantity();
            log.info("多出入库任务条目id【{}】原始需入库数量【{}】已入库数量【{}】", itemDetail.getId(), itemDetail.getQuantity(), itemDetail.getActualQuantity());
            int diffQuantity = itemDetail.getQuantity() - itemDetail.getActualQuantity();
            if (diffQuantity <= 0) {
                continue;
            }
            // 更新剩余待入库数量
            int doUpdateQuantity = release >= diffQuantity ? shouldQuantity - diffQuantity : shouldQuantity - release;
            log.info("多出入库任务条目id【{}】更新需入库数量【{}】原始需入库数量【{}】", itemDetail.getId(), doUpdateQuantity, shouldQuantity);
            // 移除对应的release数量 添加乐观锁
            int row = stockStorageItemRepository.updateQuantityByIdAndQuantity(itemDetail.getId(), doUpdateQuantity, shouldQuantity);
            if (row == 0) {
                throw new net.xianmu.common.exception.BizException("多出入库数据出现并发修改的情况, 需要重试");
            }
            int removeNum = release >= diffQuantity ? diffQuantity : release;
            needReleaseSkuQuantityMap.merge(itemDetail.getStockTaskStorageId() + "_" + itemDetail.getSku(), removeNum, Integer::sum);
            // 获取任务id 明细是通过条码查询出来的，不会空指针
            String sb = "取消入库明细 sku:" + sku +
                    " 待入库数量:" + shouldQuantity +
                    " 取消数量:" + removeNum +
                    " 剩余待入库数量:" + (shouldQuantity - removeNum);
            // 记录日志
            StoreTaskLog storeTaskLog = StoreTaskLog.builder()
                    .bizId(String.valueOf(itemDetail.getStockTaskStorageId()))
                    .bizType(StoreRecordType.OUT_MORE_IN.getId())
                    .operator(AdminUtil.getCurrentLoginAdminIdV2().toString())
                    .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                    .operateType(StockTaskLogOperateType.CANCEL_TASK)
                    .operateInfo(sb)
                    .build();
            storeTaskLogRepository.create(storeTaskLog);
            release = release - removeNum;
        }
        // 调用库存中心释放
        if (!needReleaseSkuQuantityMap.isEmpty()) {
            Map<Long, List<StockStorageItem>> groupStockStorageItem = itemList.stream().collect(Collectors.groupingBy(StockStorageItem::getStockTaskStorageId));
            groupStockStorageItem.forEach((stockTaskStorageId, StockStorageItems) -> {
                OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO = saleInventoryCenterCommandReqFactory.buildOrderReleaseBySpecifySkuReqDTO(tenantId, warehouseNo.longValue(), stockTaskStorageId, StockStorageItems, needReleaseSkuQuantityMap);
                saleInventoryCenterCommandFacade.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);
            });
        }
        // 更新入库任务状态
        for (Long stockStorageId : stockStorageIdList) {
            List<StockStorageItem> stockStorageItemList = stockStorageItemRepository.queryStockStorageItem(stockStorageId);
            if (CollectionUtils.isEmpty(stockStorageItemList)) {
                continue;
            }
            List<StockStorageItem> unfinishItemList = stockStorageItemList.stream().filter(item -> !item.getQuantity().equals(item.getActualQuantity())).collect(Collectors.toList());
            // 全部明细完结
            if (CollectionUtils.isEmpty(unfinishItemList)) {
                int shouldQuantity = stockStorageItemList.stream().map(StockStorageItem::getQuantity).reduce(Integer::sum).orElse(0);
                // 需入数量为0则更新取消状态
                if (shouldQuantity == 0) {
                    StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder().id(stockStorageId).state(StockTaskStorageStateEnum.CANCEL.getId()).build();
                    stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);
                    // 记录日志信息内容
                    StoreTaskLog storeTaskLog = StoreTaskLog.builder()
                            .bizId(String.valueOf(stockStorageId))
                            .bizType(StoreRecordType.OUT_MORE_IN.getId())
                            .operator(AdminUtil.getCurrentLoginAdminIdV2().toString())
                            .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                            .operateType(StockTaskLogOperateType.CANCEL_TASK)
                            .operateInfo("系统自动取消多出入库任务")
                            .build();
                    storeTaskLogRepository.create(storeTaskLog);
                } else {
                    // 更新完成状态
                    StockTaskStorage updateStockTaskStorage = StockTaskStorage.builder().id(stockStorageId).state(StockTaskStorageStateEnum.FINISH.getId()).processState(StockTaskStorageProcessStateEnums.ALL_IN_STORE.getId()).build();
                    stockTaskStorageRepository.updateStockTaskStorage(updateStockTaskStorage);
                    // 记录日志信息内容
                    StoreTaskLog storeTaskLog = StoreTaskLog.builder()
                            .bizId(String.valueOf(stockStorageId))
                            .bizType(StoreRecordType.OUT_MORE_IN.getId())
                            .operator(AdminUtil.getCurrentLoginAdminIdV2().toString())
                            .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                            .operateType(StockTaskLogOperateType.FINISH_TASK)
                            .operateInfo("系统自动完成多出入库任务")
                            .build();
                    storeTaskLogRepository.create(storeTaskLog);
                }
            }
        }

    }

    /**
     * 查询到货时间限制
     *
     * @param input 输入条件
     * @return 返回是否可以确认到货
     */
    @Override
    public StockStorageArrivalCargoLimitVO arrivalCargoLimit(StockStorageArrivalCargoLimitInput input) {
        // 查询任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(input.getStockTaskStorageId());
        if (stockTaskStorage == null) {
            throw new net.xianmu.common.exception.BizException("入库任务不存在");
        }
        StockStorageArrivalCargoLimitVO cargoLimitVO = StockStorageArrivalCargoLimitVO.builder().build();
        // 配置不生效
        if (crossWarehouseArrivalCargoConfig.getEffect() == null || !crossWarehouseArrivalCargoConfig.getEffect()) {
            cargoLimitVO.setCanArrivalCargo(true);
            return cargoLimitVO;
        }
        // 越库入库类型收货时间校验
        if (Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())) {
            LocalDateTime expectTime = stockTaskStorage.getExpectTime();
            //   读取nacos 动态配置 时间限制
            LocalDateTime startTime =  expectTime.withHour(crossWarehouseArrivalCargoConfig.getHour())
                    .withMinute(crossWarehouseArrivalCargoConfig.getMinute())
                    .withSecond(crossWarehouseArrivalCargoConfig.getSecond());
            // 期望入库时间当前的时间 > 限制时间不做限制
            if (LocalDateTime.now().isBefore(startTime)) {
                cargoLimitVO.setCanArrivalCargo(false);
                cargoLimitVO.setLimitTime(startTime);
                return cargoLimitVO;
            }
        }
        cargoLimitVO.setCanArrivalCargo(true);
        return cargoLimitVO;
    }

    @Override
    public void updateExpectTimeByThirdOrderNo(StockStorageExpectTimeUpdateCommand updateCommand) {
        // 状态校验
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.selectOneByThirdOrderNoAndType(updateCommand.getThirdOrderNo(), updateCommand.getType());
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskStorage), "未查询到对应入库任务信息");
        ExceptionUtil.checkAndThrow(StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId().equals(stockTaskStorage.getProcessState())
                && StockTaskStorageStateEnum.NORMAL.getId() == stockTaskStorage.getState(), "已开始作业，不能再修改预约入库时间了");
        StockTaskStorage updateStockStorage = StockTaskStorage.builder()
                .id(stockTaskStorage.getId())
                .thirdOrderNo(updateCommand.getThirdOrderNo())
                .type(updateCommand.getType())
                .expectTime(updateCommand.getExpectTime())
                .operatorName(updateCommand.getOperator())
                .build();
        stockTaskStorageRepository.updateExpectTimeById(updateStockStorage);
        // 调用外部服务修改预约入库时间
        stockInExternalService.changeStockInTaskExpectTime(stockTaskStorage, updateCommand.getExpectTime());
    }

    @Override
    public void updateExpectTimeByStorageId(StockStorageExpectTimeUpdateCommand updateCommand) {
        log.info("更新入库任务预计时间：{}", JSON.toJSONString(updateCommand));
        if (null == updateCommand || null == updateCommand.getExpectTime() || null == updateCommand.getStockTaskStorageId()) {
            return;
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(updateCommand.getStockTaskStorageId());
        if (null == stockTaskStorage) {
            log.info("未查到入库任务");
            return;
        }
        if (!StockTaskStorageProcessStateEnums.WAIT_IN_STORE.getId().equals(stockTaskStorage.getProcessState())) {
            log.info("入库任务已开始作业，不变更预计时间");
            return;
        }
        StockTaskStorage updateStockStorage = StockTaskStorage.builder()
                .id(stockTaskStorage.getId())
                .expectTime(updateCommand.getExpectTime())
                .build();
        stockTaskStorageRepository.updateExpectTimeById(updateStockStorage);
    }
}
