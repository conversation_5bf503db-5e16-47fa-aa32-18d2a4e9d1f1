package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/11/30
 */
@ApiModel(description = "毛利率配置实体类")
@Data
public class InterestRateConfig {

    /** 自动调价-关闭 */
    public static final int AUTO_FLAG_CLOSE  = 0;

    /** 自动调价-开启 */
    public static final int AUTO_FLAG_OPEN  = 1;

    private Integer id;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal interestRate;

    @ApiModelProperty(value = "自动调价标识", allowableValues = "0、关闭 1、开启")
    private Integer autoFlag;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "发起时间")
    private Date createTime;

    @ApiModelProperty(value = "发起人")
    private String createAdminName;

}
