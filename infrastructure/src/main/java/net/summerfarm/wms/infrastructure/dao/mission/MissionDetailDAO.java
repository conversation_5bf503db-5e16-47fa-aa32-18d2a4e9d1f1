package net.summerfarm.wms.infrastructure.dao.mission;

import net.summerfarm.wms.domain.mission.domainobject.queryres.MissionDetailCategoryRes;
import net.summerfarm.wms.domain.mission.domainobject.queryres.MissionDetailSupplierRes;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.FindMissionDetailDO;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageOrderPickingDetailDO;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageStocktakingDetailDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface MissionDetailDAO {
    int deleteByPrimaryKey(Long id);

    int insert(MissionDetailDO record);

    int insertSelective(MissionDetailDO record);

    int batchInsert(List<MissionDetailDO> record);

    MissionDetailDO selectByPrimaryKey(Long id);

    List<MissionDetailDO> selectByPrimaryKeys(List<Long> ids);

    List<MissionDetailDO> selectByMissionNo(String missionNo);

    List<MissionDetailDO> selectByMissionNoAndSkus(@Param("missionNo") String missionNo,
                                                   @Param("skus") List<String> skus,
                                                   @Param("batch") String batch,
                                                   @Param("produceTime") LocalDate produceTime,
                                                   @Param("qualityDate") LocalDate qualityDate,
                                                   @Param("splitType") Integer splitType,
                                                   @Param("splitInfoList") List<String> splitInfoList);

    List<MissionDetailDO> selectByMissionNos(List<String> missionNo);

    List<MissionDetailDO> findDetailByMissionNoListAndSkuList(@Param("missionNoList")List<String> missionNo, @Param("skuList") List<String> skuList);

    List<MissionDetailDO> pagePda(PageOrderPickingDetailDO query);

    List<MissionDetailDO> listStocktakingSort(PageStocktakingDetailDO query);

    List<MissionDetailDO> listStocktakingSortByCabinetNo(PageStocktakingDetailDO query);

    int updateByPrimaryKeySelective(MissionDetailDO record);

    int updateQuantityAndState(MissionDetailDO record);

    int updateExeQuantityById(MissionDetailDO record);

    int updateByPrimaryKey(MissionDetailDO record);

    /**
     * 查询拣货明细sku列表
     *
     * @param missionNo 任务编号
     * @return 返回待拣货明细列表
     */
    List<MissionDetailDO> queryPickSkuListByMissionNo(@Param("missionNo") String missionNo, @Param("warehouseNo") Long warehouseNo);

    List<MissionDetailDO> listMissionDetail(FindMissionDetailDO findMissionDetailDO);

    MissionDetailDO findMissionDetailForStocktaking(@Param("missionNo") String missionNo, @Param("sku") String sku, @Param("cabinetNo") String cabinetNo);

    List<MissionDetailDO> findMissionDetailForStocktakingUnfinishedSku(@Param("missionNo") String missionNo, @Param("skus") List<String> skus);

    /**
     * 统计任务拣货数量进度
     *
     * @param missionNo 拣货任务编号
     * @return 返回拣货数量的进度
     */
    MissionDetailDO sumMissionPickRate(String missionNo);

    /**
     * PC明细任务分页
     * @param queryDO
     * @return
     */
    List<MissionDetailDO> pcDetailPage(PageMissionQueryDO queryDO);

    /**
     * PDA明细任务分页
     * @param queryDO
     * @return
     */
    List<MissionDetailDO> pdaDetailPage(PageMissionQueryDO queryDO);

    /**
     * 类目分页
     * @param queryDO
     * @return
     */
    List<MissionDetailCategoryRes> pageMissionCategoryPda(PageMissionQueryDO queryDO);

    /**
     * 供应商分页
     * @param queryDO
     * @return
     */
    List<MissionDetailSupplierRes> pageMissionSupplierPda(PageMissionQueryDO queryDO);
}