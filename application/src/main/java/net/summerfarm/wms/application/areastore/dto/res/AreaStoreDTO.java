package net.summerfarm.wms.application.areastore.dto.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/9/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaStoreDTO implements Serializable {


    private static final long serialVersionUID = -6292377766883161986L;

    /**
     * 库存编码
     */
    private Long warehouseNo;
    /**
     * 库存名称
     */
    private String warehouseName;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 库存数量
     */
    private Integer stockQuantity;

}
