package net.summerfarm.wms.domain.safeinventorylock.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 安全库存锁定类型枚举
 * <AUTHOR>
 * @date 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum SafeInventoryLockTypeEnum {

    INVENTORY_LOCK(1, "库存锁定"),
    BATCH_LOCK(2, "批次锁定"),
    ;

    private final Integer code;
    private final String desc;

    public static SafeInventoryLockTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SafeInventoryLockTypeEnum lockType : values()) {
            if (lockType.getCode().equals(code)) {
                return lockType;
            }
        }
        return null;
    }
}
