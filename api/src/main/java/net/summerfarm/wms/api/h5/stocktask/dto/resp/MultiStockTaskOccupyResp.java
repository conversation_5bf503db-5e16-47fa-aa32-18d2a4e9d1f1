package net.summerfarm.wms.api.h5.stocktask.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiStockTaskOccupyResp implements Serializable {

    private static final long serialVersionUID = -8927718911839918472L;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    List<StockTaskOccupySkuDTO> stockTaskOccupySkus;
}
