package net.summerfarm.wms.domain.stockdamage.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 货损创建事件
 * @date 2024/7/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StockDamageCreateCommandParam implements Serializable {

    private static final long serialVersionUID = -7811174152070631027L;

    /**
     * 幂等单
     */
    private String idempotentNo;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 货损任务类型
     */
    private Integer damageTaskType;

    /**
     * 货损明细
     */
    private List<StockDamageCreateItemValueObject> stockDamageCreateItemValueObjects;

}
