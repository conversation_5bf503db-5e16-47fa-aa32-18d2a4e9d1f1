package net.summerfarm.wms.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.api.inner.batch.req.BatchCreateByTaskDTO;
import net.summerfarm.wms.api.inner.instore.dto.StockStorageReqDTO;
import net.summerfarm.wms.application.prove.event.dto.ProveSaveDTO;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InBoundRepairReqDTO implements Serializable {
    String data;
    Integer type;
    StockStorageReqDTO stockStorage;
    List<BatchCreateByTaskDTO> costs;
    Long inboundOrderId;
    ProveSaveDTO proveSave;
    Long time;
    String token;
}
