package net.summerfarm.wms.application.skucodetrace.inbound.controller.skucodetrace;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.FulfillmentWayEnum;
import net.summerfarm.wms.application.skucodetrace.assmbler.SkuBatchCodeTraceAssembler;
import net.summerfarm.wms.application.skucodetrace.inbound.input.command.ThresholdUpdateInput;
import net.summerfarm.wms.application.skucodetrace.inbound.vo.ThresholdUpdateResultVO;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceCommandService;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceQueryService;
import net.summerfarm.wms.application.skucodetrace.input.SkuBatchCodeTraceQuery;
import net.summerfarm.wms.application.skucodetrace.inbound.input.query.TraceBatchPrintQueryInput;
import net.summerfarm.wms.application.skucodetrace.inbound.input.query.TraceOnlyQueryInput;
import net.summerfarm.wms.application.skucodetrace.inbound.vo.TraceBatchPrintVO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.tms.TmsCityDeliveryBatchQueryFacade;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.summerfarm.wms.initConfig.PopDeliveryTimeStatisticPeriodConfig;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批次唯一溯源码 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Slf4j
@RestController
@RequestMapping("/sku/sku-batch-code-trace")
public class SkuBatchCodeTraceController {

    @Resource
    private SkuBatchCodeTraceCommandService skuBatchCodeTraceCommandService;
    @Resource
    private SkuBatchCodeTraceQueryService skuBatchCodeTraceQueryService;
    @Resource
    private WarehouseLogisticsFacade warehouseLogisticsFacade;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private TmsCityDeliveryBatchQueryFacade tmpCityDeliveryBatchQueryFacade;
    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;
    @Resource
    private PopDeliveryTimeStatisticPeriodConfig popDeliveryTimeStatisticPeriodConfig;

    /**
     * 批次溯源码分页查询接口
     * <p>
     * 用于称重记录查询、批次打印
     *
     * @param input 查询参数
     * @return 结果
     */
    @PostMapping(value = "/query/batch-print")
    public CommonResult<PageInfo<TraceBatchPrintVO>> batchPrint(@Valid @RequestBody TraceBatchPrintQueryInput input) {
        if (input.getStockTaskStorageId() == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请指定入库任务id");
        }

        if (input.getState() == null || CollectionUtils.isEmpty(input.getStateList())){
            input.setStateList(Arrays.asList(
                    SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue(),
                    SkuBatchCodeTraceEnums.State.HAVE_WEIGHT.getValue()
            ));
        }

        // 校验是否可打印
        CommonResult<PageInfo<TraceBatchPrintVO>> checkAble = checkPrintAble(input);
        if (!ResultStatusEnum.OK.getStatus().equals(checkAble.getStatus())) {
            return checkAble;
        }

        PageInfo<SkuBatchCodeTraceEntity> pageInfo = skuBatchCodeTraceQueryService.batchPrint(
                SkuBatchCodeTraceQuery.builder()
                        .stockTaskStorageId(input.getStockTaskStorageId())
                        .sku(input.getSku())
                        .beginStockTaskStorageSeq(input.getBeginStockTaskStorageSeq())
                        .endStockTaskStorageSeq(input.getEndStockTaskStorageSeq())
                        .skuBatchTraceCode(input.getSkuBatchTraceCode())
                        .orderNo(input.getOrderNo())
                        .pdId(input.getPdId())
                        .purchaseNo(input.getPurchaseNo())
                        .warehouseNo(input.getWarehouseNo())
                        .psoNo(input.getPsoNo())
                        .deliveryTime(input.getDeliveryTime())
                        .orderNo(input.getOrderNo())
                        .merchantNameLike(input.getMerchantNameLike())
                        .pageIndex(input.getPageIndex())
                        .pageSize(input.getPageSize())
                        .state(input.getState())
                        .stateList(input.getStateList())
                        .createDateStart(input.getCreateDateStart())
                        .createDateEnd(input.getCreateDateEnd())
                        .storeNo(input.getStoreNo())
                        .build());
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = pageInfo.getList();
        List<Integer> storeNoList = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getStoreNo)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> storeNameMap = warehouseLogisticsFacade.queryStoreNameMap(storeNoList);

        List<String> skuList = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getSku)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                WmsConstant.XIANMU_TENANT_ID,
                skuList);
        Integer deliveryTimeStatisticPeriod = popDeliveryTimeStatisticPeriodConfig.getPeriod();
        log.info("pop配送统计周期配置：{}", deliveryTimeStatisticPeriod);
        // 未配置赋默认值
        if (null == deliveryTimeStatisticPeriod) {
            deliveryTimeStatisticPeriod = 28;
        }
        // 商户第一次配送时间
        Map<String, LocalDate> merchantDeliveryTimeFirstMap = skuBatchCodeTraceQueryService.findMerchantDeliveryTimeFirst(skuBatchCodeTraceEntityList);
        log.info("商户第一次配送时间：{}", JSON.toJSONString(merchantDeliveryTimeFirstMap));
        // 商户最近一次配送时间
        Map<String, LocalDate> merchantDeliveryTimeRecentlyMap = skuBatchCodeTraceQueryService.findMerchantDeliveryTimeRecently(skuBatchCodeTraceEntityList);
        log.info("商户最近一次配送时间：{}", JSON.toJSONString(merchantDeliveryTimeRecentlyMap));

        List<TraceBatchPrintVO> traceBatchPrintVOList = SkuBatchCodeTraceAssembler
                .assembleVOList(pageInfo.getList(), storeNameMap, goodsInfoDTOMap, merchantDeliveryTimeFirstMap, merchantDeliveryTimeRecentlyMap, deliveryTimeStatisticPeriod);


        PageInfo<TraceBatchPrintVO> result = new PageInfo<>();
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setList(traceBatchPrintVOList);
        result.setTotal(pageInfo.getTotal());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        return CommonResult.ok(result);
    }

    private CommonResult<PageInfo<TraceBatchPrintVO>> checkPrintAble(TraceBatchPrintQueryInput input) {
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = skuBatchCodeTraceQueryRepository
                .findByStockTaskStorageId(input.getStockTaskStorageId().longValue());
        List<Integer> storeNoList1 = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getStoreNo)
                .distinct()
                .collect(Collectors.toList());
        LocalDate deliveryTime = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getDeliveryTime)
                .findFirst()
                .orElse(null);
        // 履约方式
        if (skuBatchCodeTraceEntityList.stream().anyMatch(entity -> Objects.equals(entity.getFulfillmentWay(),FulfillmentWayEnum.SELF_PICKUP.getValue()))) {
            return CommonResult.ok();
        }
        if (skuBatchCodeTraceEntityList.stream().anyMatch(entity -> Objects.equals(entity.getLabelType(),SkuBatchCodeTraceEnums.LabelType.POP_T2.getValue()))) {
            return CommonResult.ok();
        }
        for (Integer storeNo : storeNoList1) {
            Boolean cityStoreUnCompletePath = tmpCityDeliveryBatchQueryFacade.cityStoreUnCompletePath(
                    storeNo, deliveryTime);
            if (cityStoreUnCompletePath){
                String storeName = warehouseLogisticsFacade.queryStoreName(storeNo);
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, storeName + "未完成排线");
            }
        }
        return CommonResult.ok();
    }

    /**
     * 溯源唯一码查询接口
     *
     * @param input 查询
     * @return 结果
     */
    @PostMapping(value = "/query/trace-only")
    public CommonResult<TraceBatchPrintVO> traceOnlyQuery(@Valid @RequestBody TraceOnlyQueryInput input) {
        SkuBatchCodeTraceEntity entity = skuBatchCodeTraceQueryService.traceOnlyQuery(SkuBatchCodeTraceQuery.builder()
                .skuBatchTraceCode(input.getSkuBatchTraceCode())
                .build());
        if (entity == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到批次唯一溯源码");
        }
        String storeName = warehouseLogisticsFacade.queryStoreName(entity.getStoreNo());

        // 查询POP专属城配仓
        List<Integer> popStoreNosList = warehouseLogisticsFacade.queryPopStoreNosList();


        // 非自提需要验证是否完成排线
        if(!Objects.equals(FulfillmentWayEnum.SELF_PICKUP.getValue(), entity.getFulfillmentWay()) && popStoreNosList.contains(entity.getStoreNo())){
            // 是否未排线
            Boolean cityStoreUnCompletePath = tmpCityDeliveryBatchQueryFacade.cityStoreUnCompletePath(
                    entity.getStoreNo(), entity.getDeliveryTime());
            if (cityStoreUnCompletePath){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, storeName + "未完成排线，打印前置依赖");
            }
        }

        WarehouseStorageCenterEntity warehouse = warehouseStorageRepository.selectByWarehouseNo(entity.getWarehouseNo());
        GoodsInfoDTO goodsInfoDTO = goodsReadFacade.findGoodsInfoBySkuList(
                warehouse != null ? warehouse.getTenantId() : WmsConstant.XIANMU_TENANT_ID,
                entity.getSku());
        TraceBatchPrintVO traceBatchPrintVO = SkuBatchCodeTraceAssembler.assembleVO(entity, storeName, goodsInfoDTO);
        return CommonResult.ok(traceBatchPrintVO);
    }

    /**
     * 称重列表
     *
     * @param input 查询
     * @return 结果
     */
    @PostMapping(value = "/query/weight-list")
    public CommonResult<PageInfo<TraceBatchPrintVO>> weightListQuery(@Valid @RequestBody TraceBatchPrintQueryInput input) {
        if (input.getWarehouseNo() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请指定仓库");
        }

        if (input.getState() == null || CollectionUtils.isEmpty(input.getStateList())){
            input.setStateList(Arrays.asList(
                    SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue(),
                    SkuBatchCodeTraceEnums.State.HAVE_WEIGHT.getValue()
            ));
        }

        PageInfo<SkuBatchCodeTraceEntity> pageInfo = skuBatchCodeTraceQueryService.batchPrint(
                SkuBatchCodeTraceQuery.builder()
                        .stockTaskStorageId(input.getStockTaskStorageId())
                        .sku(input.getSku())
                        .beginStockTaskStorageSeq(input.getBeginStockTaskStorageSeq())
                        .endStockTaskStorageSeq(input.getEndStockTaskStorageSeq())
                        .skuBatchTraceCode(input.getSkuBatchTraceCode())
                        .orderNo(input.getOrderNo())
                        .pdId(input.getPdId())
                        .purchaseNo(input.getPurchaseNo())
                        .warehouseNo(input.getWarehouseNo())
                        .psoNo(input.getPsoNo())
                        .deliveryTime(input.getDeliveryTime())
                        .orderNo(input.getOrderNo())
                        .merchantNameLike(input.getMerchantNameLike())
                        .buyerNameLike(input.getBuyerNameLike())
                        .pageIndex(input.getPageIndex())
                        .pageSize(input.getPageSize())
                        .state(input.getState())
                        .stateList(input.getStateList())
                        .createDateStart(input.getCreateDateStart())
                        .createDateEnd(input.getCreateDateEnd())
                        .labelType(input.getLabelType())
                        .build());
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = pageInfo.getList();
        List<Integer> storeNoList = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getStoreNo)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> storeNameMap = warehouseLogisticsFacade.queryStoreNameMap(storeNoList);
        Integer deliveryTimeStatisticPeriod = popDeliveryTimeStatisticPeriodConfig.getPeriod();
        log.info("pop配送统计周期配置：{}", deliveryTimeStatisticPeriod);
        // 未配置赋默认值
        if (null == deliveryTimeStatisticPeriod) {
            deliveryTimeStatisticPeriod = 28;
        }
        // 商户第一次配送时间
        Map<String, LocalDate> merchantDeliveryTimeFirstMap = skuBatchCodeTraceQueryService.findMerchantDeliveryTimeFirst(skuBatchCodeTraceEntityList);
        log.info("商户第一次配送时间：{}", JSON.toJSONString(merchantDeliveryTimeFirstMap));
        // 商户最近一次配送时间
        Map<String, LocalDate> merchantDeliveryTimeRecentlyMap = skuBatchCodeTraceQueryService.findMerchantDeliveryTimeRecently(skuBatchCodeTraceEntityList);
        log.info("商户最近一次配送时间：{}", JSON.toJSONString(merchantDeliveryTimeRecentlyMap));

        List<TraceBatchPrintVO> traceBatchPrintVOList = SkuBatchCodeTraceAssembler
                .assembleVOList(pageInfo.getList(), storeNameMap, null, merchantDeliveryTimeFirstMap, merchantDeliveryTimeRecentlyMap, deliveryTimeStatisticPeriod);


        PageInfo<TraceBatchPrintVO> result = new PageInfo<>();
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setList(traceBatchPrintVOList);
        result.setTotal(pageInfo.getTotal());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        return CommonResult.ok(result);
    }

    /**
     * 订单称重列表
     *
     * @param input 查询
     * @return 结果
     */
    @PostMapping(value = "/query/order-weight-list")
    public CommonResult<PageInfo<TraceBatchPrintVO>> orderWeightListQuery(@Valid @RequestBody TraceBatchPrintQueryInput input) {
        if (StringUtils.isEmpty(input.getOrderNo() == null) && StringUtils.isEmpty(input.getFulfillmentNo()) &&
                StringUtils.isEmpty(input.getPurchaseNo())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请指定订单号或履约单号或采购单号");
        }

        if (input.getState() == null || CollectionUtils.isEmpty(input.getStateList())){
            input.setStateList(Arrays.asList(
                    SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue(),
                    SkuBatchCodeTraceEnums.State.HAVE_WEIGHT.getValue()
            ));
        }

        PageInfo<SkuBatchCodeTraceEntity> pageInfo = skuBatchCodeTraceQueryService.batchPrint(
                SkuBatchCodeTraceQuery.builder()
                        .stockTaskStorageId(input.getStockTaskStorageId())
                        .sku(input.getSku())
                        .beginStockTaskStorageSeq(input.getBeginStockTaskStorageSeq())
                        .endStockTaskStorageSeq(input.getEndStockTaskStorageSeq())
                        .skuBatchTraceCode(input.getSkuBatchTraceCode())
                        .orderNo(input.getOrderNo())
                        .pdId(input.getPdId())
                        .purchaseNo(input.getPurchaseNo())
                        .warehouseNo(input.getWarehouseNo())
                        .psoNo(input.getPsoNo())
                        .deliveryTime(input.getDeliveryTime())
                        .orderNo(input.getOrderNo())
                        .fulfillmentNo(input.getFulfillmentNo())
                        .merchantNameLike(input.getMerchantNameLike())
                        .buyerNameLike(input.getBuyerNameLike())
                        .pageIndex(input.getPageIndex())
                        .pageSize(input.getPageSize())
                        .state(input.getState())
                        .stateList(input.getStateList())
                        .createDateStart(input.getCreateDateStart())
                        .createDateEnd(input.getCreateDateEnd())
                        .build());
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = pageInfo.getList();
        List<Integer> storeNoList = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getStoreNo)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> storeNameMap = warehouseLogisticsFacade.queryStoreNameMap(storeNoList);

        List<String> skuCodeList = skuBatchCodeTraceEntityList.stream()
                .map(SkuBatchCodeTraceEntity::getSku)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> goodsInfoMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                WmsConstant.XIANMU_TENANT_ID, skuCodeList);


        List<TraceBatchPrintVO> traceBatchPrintVOList = SkuBatchCodeTraceAssembler
                .assembleVOList(pageInfo.getList(), storeNameMap, goodsInfoMap);


        PageInfo<TraceBatchPrintVO> result = new PageInfo<>();
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setList(traceBatchPrintVOList);
        result.setTotal(pageInfo.getTotal());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        return CommonResult.ok(result);
    }

    /**
     * 称重提交
     */
    @PostMapping(value = "/upsert/weight")
    public CommonResult<ThresholdUpdateResultVO> weight(ThresholdUpdateInput input) {
        return skuBatchCodeTraceCommandService.upsetWeight(input);
    }

}
