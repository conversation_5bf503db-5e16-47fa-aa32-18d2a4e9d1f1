package net.summerfarm.wms.domain.stockalert.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stockalert.entity.SafeStockWarningEntity;
import net.summerfarm.wms.domain.stockalert.param.query.SafeStockWarningQueryParam;
import net.summerfarm.wms.domain.stockalert.param.query.StoreAlertCountByStatusQueryParam;
import net.summerfarm.wms.domain.stockalert.valueobject.StoreAlertCountByStatusValueObject;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-03-05 15:03:04
 */
public interface SafeStockWarningQueryRepository {

    PageInfo<SafeStockWarningEntity> getPage(SafeStockWarningQueryParam param);

    List<StoreAlertCountByStatusValueObject> countByStatus(StoreAlertCountByStatusQueryParam queryParam);

}