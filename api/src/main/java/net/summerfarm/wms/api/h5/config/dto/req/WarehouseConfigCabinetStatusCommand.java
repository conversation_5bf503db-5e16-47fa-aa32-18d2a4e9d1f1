package net.summerfarm.wms.api.h5.config.dto.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 仓库库位管理配置查询
 * @date 2023/5/4
 */
@Data
public class WarehouseConfigCabinetStatusCommand implements Serializable {

    private static final long serialVersionUID = 3912310411288596910L;

    @NotEmpty
    private List<WarehouseConfigCabinetStatusDTO> cabinetStatusDTOs;
}
