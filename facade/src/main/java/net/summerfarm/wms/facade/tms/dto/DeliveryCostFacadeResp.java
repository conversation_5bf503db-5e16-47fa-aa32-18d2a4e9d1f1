package net.summerfarm.wms.facade.tms.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DeliveryCostFacadeResp {

    /**
     * 成本批次id
     */
    private Long costBatchId;

    /**
     * 委托单ID
     */
    private Long distOrderId;

    /**
     * 外部货品ID(sku)
     */
    private String sku;

    /**
     * 委托单来源，100：调拨，101：采购，102：销售出库，103，出样出库，104，补货出库，105，自提销售
     */
    private Integer source;
    /**
     * 委托单来源描述
     */
    private String sourceDesc;
    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 平均成本
     */
    private BigDecimal averageCost;
}
