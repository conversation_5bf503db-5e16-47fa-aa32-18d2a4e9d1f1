package net.summerfarm.wms.application.stocktask.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.outstore.OrderPickingCommandService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.ContainerReleaseForPickEvent;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/2
 */
@Slf4j
@Component
@MqListener(topic = Global.MQ_TOPIC,
        tag = WmsConstant.CONTAINER_RELEASE_PICK_TAG,
        consumerGroup = WmsConstant.CONTAINER_RELEASE_PICK_GID)
public class ContainerReleaseForPickListener extends AbstractMqListener<ContainerReleaseForPickEvent> {

    @Resource
    private OrderPickingCommandService orderPickingCommandService;

    @Override
    public void process(ContainerReleaseForPickEvent containerReleaseForPickEvent) {
        log.info("拣货完成释放容器处理：{}", JSON.toJSONString(containerReleaseForPickEvent));
        if (null == containerReleaseForPickEvent || CollectionUtils.isEmpty(containerReleaseForPickEvent.getMissionNoList())) {
            return;
        }
        orderPickingCommandService.releasePickingContainer(containerReleaseForPickEvent.getMissionNoList());
        log.info("拣货完成释放容器处理完成：{}", JSON.toJSONString(containerReleaseForPickEvent));
    }
}
