package net.summerfarm.wms.infrastructure.repository.transaction.converter;

import net.summerfarm.wms.domain.transaction.domainobject.AreaStoreInventoryTransaction;
import net.summerfarm.wms.domain.transaction.domainobject.TransactionLog;
import net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO;
import net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TransactionConverter {
    TransactionConverter INSTANCE = Mappers.getMapper(TransactionConverter.class);

    TransactionLogDO convert(TransactionLog transactionLog);

    TransactionLog convert(TransactionLogDO transactionLog);

    AreaStoreInventoryTransactionDO convert(AreaStoreInventoryTransaction transaction);

    AreaStoreInventoryTransaction convert(AreaStoreInventoryTransactionDO transaction);
}
