package net.summerfarm.wms.infrastructure.dao.instore.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  15:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockStorageItemDO {


    private Long id;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 供应商
     */
    private Long supplierId;

    /**
     * 任务编号
     */
    private Integer stockTaskId;

    /**
     * 入库任务编号
     */
    private Long stockTaskStorageId;

    /**
     * 入库数量
     */
    private Integer quantity;

    /**
     * 实际入库数量
     */
    private Integer actualQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类目类型
     */
    private Integer categoryType;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 存储区域
     */
    private Integer temperature;

    /**
     * 包装
     */
    private String packaging;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 商品归属
     */
    private Integer skuType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 码放规则
     */
    private String stackRule;

    /**
     * 三级类型
     */
    private String category;

    /**
     * 外部自有编码
     */
    private String customerSkuCode;
}
