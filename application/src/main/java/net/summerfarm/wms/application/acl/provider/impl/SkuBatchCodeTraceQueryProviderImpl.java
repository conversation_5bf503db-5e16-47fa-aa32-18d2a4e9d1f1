package net.summerfarm.wms.application.acl.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.acl.provider.convert.SkuBatchCodeTraceRespConvert;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import net.summerfarm.wms.skucodetrace.SkuBatchCodeTraceQueryProvider;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodeOrderNoReq;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodePurchaseNoReq;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodeReq;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceBatchResp;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@DubboService
@Component
public class SkuBatchCodeTraceQueryProviderImpl implements SkuBatchCodeTraceQueryProvider {

    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;

    @Override
    public DubboResponse<SkuBatchCodeTraceResp> queryByBatchTraceCode(@Valid BatchTraceCodeReq batchTraceCodeReq) {
        SkuBatchCodeTraceEntity skuBatchCodeTraceEntity = skuBatchCodeTraceQueryRepository
                .findBySkuBatchTraceCode(batchTraceCodeReq.getSkuBatchTraceCode());
        if (skuBatchCodeTraceEntity == null){
            return DubboResponse.getDefaultError("请求批次不存在");
        }
        SkuBatchCodeTraceResp resp = SkuBatchCodeTraceRespConvert.convert(skuBatchCodeTraceEntity);
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<SkuBatchCodeTraceBatchResp> queryByPurchaseNo(@Valid BatchTraceCodePurchaseNoReq batchTraceCodePurchaseNoReq) {
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = skuBatchCodeTraceQueryRepository
                .findByPurchaseNo(batchTraceCodePurchaseNoReq.getPurchaseNo());

        SkuBatchCodeTraceBatchResp resp = new SkuBatchCodeTraceBatchResp();
        resp.setSkuBatchCodeTraceList(SkuBatchCodeTraceRespConvert.convertList(skuBatchCodeTraceEntityList));
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<SkuBatchCodeTraceBatchResp> queryByOrderNos(@Valid BatchTraceCodeOrderNoReq batchTraceCodeOrderNoReq) {
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList = skuBatchCodeTraceQueryRepository
                .findByOrderNos(batchTraceCodeOrderNoReq.getOrderNos(),
                        batchTraceCodeOrderNoReq.getDeliveryDate(),
                        batchTraceCodeOrderNoReq.getContactId());

        SkuBatchCodeTraceBatchResp resp = new SkuBatchCodeTraceBatchResp();
        resp.setSkuBatchCodeTraceList(SkuBatchCodeTraceRespConvert.convertList(skuBatchCodeTraceEntityList));
        return DubboResponse.getOK(resp);
    }
}
