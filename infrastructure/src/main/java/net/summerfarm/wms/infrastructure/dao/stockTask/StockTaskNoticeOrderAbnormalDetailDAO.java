package net.summerfarm.wms.infrastructure.dao.stockTask;


import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface StockTaskNoticeOrderAbnormalDetailDAO {
    int deleteByPrimaryKey(Long id);

    int insert(StockTaskNoticeOrderAbnormalDetailDO record);

    int batchInsert(List<StockTaskNoticeOrderAbnormalDetailDO> record);

    int insertSelective(StockTaskNoticeOrderAbnormalDetailDO record);

    int finishAbnormalDetail(@Param("ids") List<Long> ids);

    StockTaskNoticeOrderAbnormalDetailDO selectByPrimaryKey(Long id);

    List<StockTaskNoticeOrderAbnormalDetailDO> queryNoticeOrderAbnormal(@Param("goodsSupplyNo") String goodsSupplyNo,
                                                                        @Param("skus") List<String> skus);


    int updateByPrimaryKeySelective(StockTaskNoticeOrderAbnormalDetailDO record);

    int updateByPrimaryKey(StockTaskNoticeOrderAbnormalDetailDO record);

    /**
     * 批量查询异常出库通知单明细
     * @param goodsSupplyOrderNoList 出库通知单列表
     * @return 返回异常出库通知单列表
     */
    List<StockTaskNoticeOrderAbnormalDetailDO> findByGoodsSupplyNoList(@Param("goodsSupplyOrderNoList") List<String> goodsSupplyOrderNoList);

    List<StockTaskNoticeOrderAbnormalDetailDO> selectByIds(@Param("ids") List<Long> ids);

    List<StockTaskNoticeOrderAbnormalDetailDO> findByExceptTimeAndWarehouseStore(@Param("warehouseNo") Integer warehouseNo, @Param("storeNo") Integer storeNo, @Param("exceptTime") LocalDate exceptTime, @Param("type") Integer type, @Param("supplyMode") Integer supplyMode, @Param("orderNos") List<String> orderNos);

    List<StockTaskNoticeOrderAbnormalDetailDO> findByStockTaskId(@Param("stockTaskId") Long stockTaskId);
}