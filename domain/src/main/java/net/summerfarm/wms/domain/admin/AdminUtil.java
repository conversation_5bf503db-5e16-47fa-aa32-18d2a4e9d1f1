package net.summerfarm.wms.domain.admin;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.facade.admin.AdminFacade;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.user.UserBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AdminUtil {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AdminFacade adminFacade;

    public static ThreadLocal<AdminFacadeDTO> LOGIN = new ThreadLocal<>();

    public static ThreadLocal<UserBase> USER_BASE = new ThreadLocal<>();

    public static Long getCurrentLoginAdminId() {
        AdminFacadeDTO adminFacadeDTO = LOGIN.get();
        if (Objects.isNull(adminFacadeDTO)) {
            return 0L;
        }
        return adminFacadeDTO.getAdminId();
    }

    /**
     * 返回的adminId可能为空
     * @return 返回用户id
     */
    public static Long getCurrentLoginAdminIdCanNull() {
        AdminFacadeDTO adminFacadeDTO = LOGIN.get();
        if (Objects.isNull(adminFacadeDTO)) {
            return null;
        }
        return adminFacadeDTO.getAdminId();
    }

    public static Long getCurrentLoginAdminIdV2() {
        UserBase userBase = USER_BASE.get();
        if (Objects.isNull(userBase)) {
            return 0L;
        }
        Integer bizUserId = userBase.getBizUserId();
        if (bizUserId != null) {
            return bizUserId.longValue();
        }
        return 0L;
    }

    public static String getCurrentLoginAdminName() {
        AdminFacadeDTO adminFacadeDTO = LOGIN.get();
        if (Objects.isNull(adminFacadeDTO)) {
            return "";
        }
        return adminFacadeDTO.getRealName();
    }

    public static String getCurrentLoginAdminNameV2() {
        UserBase userBase = USER_BASE.get();
        if (Objects.isNull(userBase)) {
            return "";
        }
        return userBase.getNickname();
    }

    public static Long getTenantId() {
        UserBase userBase = USER_BASE.get();
        if (Objects.isNull(userBase)) {
            return null;
        }
        return userBase.getTenantId();
    }

    public String convertAdminId(Long adminId) {
        if (Objects.isNull(adminId) || adminId == 0L) {
            return "0";
        }
        return adminId.toString();
    }

    public AdminFacadeDTO getAdmin(Long adminId) {
        if (Objects.isNull(adminId)) {
            return null;
        }
        String adminJson = redisUtil.get(RedisKeys.buildAdminKey(adminId));
        if (StringUtils.isEmpty(adminJson)) {
            if (adminId == 0L) {
                AdminFacadeDTO system = AdminFacadeDTO.builder().adminId(0L).realName(WmsConstant.SYSTEM).build();
                redisUtil.setNx(RedisKeys.buildAdminKey(0L), JSON.toJSONString(system), 3L, TimeUnit.DAYS);
                return system;
            }
            AdminFacadeDTO adminFacadeDTO = adminFacade.queryAdmin(AdminFacadeDTO.builder().adminId(adminId).build());
            if (Objects.isNull(adminFacadeDTO)) {
                AdminFacadeDTO system = AdminFacadeDTO.builder().adminId(adminId).realName(WmsConstant.SYSTEM).build();
                redisUtil.set(RedisKeys.buildAdminKey(adminId), JSON.toJSONString(system));
                return system;
            }
            redisUtil.setNx(RedisKeys.buildAdminKey(adminFacadeDTO.getAdminId()), JSON.toJSONString(adminFacadeDTO), 3L, TimeUnit.DAYS);
            return adminFacadeDTO;
        }
        return JSON.parseObject(adminJson, AdminFacadeDTO.class);
    }

    public String getNameRemarks(Long adminId) {
        // 老数据兼容
        AdminFacadeDTO adminFacadeDTO = getAdmin(adminId);
        if (Objects.isNull(adminFacadeDTO)) {
            return "";
        }
        return adminFacadeDTO.getNameRemakes();
    }

    public String getAdminName(String operator) {
        // 老数据兼容
        String adminName;
        try {
            long adminId = Long.parseLong(operator);
            AdminFacadeDTO adminFacadeDTO = getAdmin(adminId);
            if (Objects.isNull(adminFacadeDTO)) {
                return WmsConstant.SYSTEM;
            }
            adminName = adminFacadeDTO.getRealName();
        } catch (Exception e) {
            log.info("操作人老数据兼容");
            adminName = operator;
        }
        return adminName;
    }

    public static void setDefaultUserBase() {
        UserBase userBase = new UserBase();
        userBase.setSystemOrigin(SystemOriginEnum.ADMIN.getType());
        userBase.setBizUserId(0);
        userBase.setTenantId(1L);
        userBase.setUsername("系统默认");
        userBase.setPhone("");
        userBase.setNickname("系统默认");
        USER_BASE.set(userBase);
    }

}
