package net.summerfarm.wms.application.stocktask.validator;

import com.google.common.collect.Sets;
import net.summerfarm.wms.base.enums.StockTaskType;
import net.summerfarm.wms.domain.stocktask.OldAllocationOrderRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.AllocationOrder;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/28
 */
@Component
public class StockTaskPickCreateValidator {

    @Resource
    private OldAllocationOrderRepository oldAllocationOrderRepository;

    /**
     * 生成拣货任务前置校验出库任务类型
     * 销售、补货、出样合并
     * 调拨
     * 采退
     * @param stockTaskList
     * @return
     */
    public Boolean validStockTasks(List<StockTask> stockTaskList) {
        List<Integer> taskTypeList = stockTaskList.stream().map(StockTask::getType).collect(Collectors.toList());
        Set<Integer> saleTypeSet = Sets.newHashSet(StockTaskType.SALE_OUT.getId(), StockTaskType.DEMO_OUT.getId(), StockTaskType.SUPPLY_AGAIN_TASK.getId());
        List<Integer> saleTypeList = taskTypeList.stream().filter(saleTypeSet::contains).collect(Collectors.toList());
        // 校验销售
        if (!CollectionUtils.isEmpty(saleTypeList)) {
            if (taskTypeList.size() != saleTypeList.size()) {
                throw new BizException("出库类型冲突，不支持操作批量拣货");
            }
            List<Integer> outStoreNoList = stockTaskList.stream().filter(item -> null != item.getOutStoreNo()).map(StockTask::getOutStoreNo).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(outStoreNoList) && outStoreNoList.size() > 1) {
                throw new BizException("不同城配仓，不支持操作批量拣货");
            }
            List<LocalDateTime> distExpectTime = stockTaskList.stream().map(StockTask::getExpectTime).distinct().collect(Collectors.toList());
            if (distExpectTime.size() > 1) {
                throw new BizException("预计出库时间不一致，无法批量拣货");
            }
        }
        // 校验调拨
        Set<Integer> allocationTypeSet = Sets.newHashSet(StockTaskType.STORE_ALLOCATION_OUT.getId());
        List<Integer> allocationTypeList = taskTypeList.stream().filter(allocationTypeSet::contains).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(allocationTypeList)) {
            if (taskTypeList.size() != allocationTypeList.size()) {
                throw new BizException("出库类型冲突，不支持操作批量拣货");
            }
            // 多个调拨预计出库时间需相同
            if (allocationTypeList.size() > 1) {
                List<String> taskNo = stockTaskList.stream().map(StockTask::getTaskNo).distinct().collect(Collectors.toList());
                List<AllocationOrder> allocationOrderEntityVOList = oldAllocationOrderRepository.selectList(taskNo);
                List<LocalDateTime> distExpectTime = allocationOrderEntityVOList.stream().map(AllocationOrder::getExpectOutTime).distinct().collect(Collectors.toList());
                if (distExpectTime.size() > 1) {
                    throw new BizException("预计出库时间不一致，无法批量拣货");
                }
            }
        }
        // 校验采退
        Set<Integer> purchaseTypeSet = Sets.newHashSet(StockTaskType.PURCHASES_BACK.getId());
        List<Integer> purchaseTypeList = taskTypeList.stream().filter(purchaseTypeSet::contains).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(purchaseTypeList)) {
            if (taskTypeList.size() != purchaseTypeList.size()) {
                throw new BizException("出库类型冲突，不支持操作批量拣货");
            }
            // 多个采退预计出库时间需相同
            if (purchaseTypeList.size() > 1) {
                List<LocalDateTime> distExpectTime = stockTaskList.stream().map(StockTask::getExpectTime).distinct().collect(Collectors.toList());
                if (distExpectTime.size() > 1) {
                    throw new BizException("预计出库时间不一致，无法批量拣货");
                }
            }
        }
        return true;
    }
}
