package net.summerfarm.wms.application.instore.factory;

import net.summerfarm.wms.api.h5.instore.dto.req.ProveReqDTO;
import net.summerfarm.wms.api.h5.prove.enums.ProveBizTypeEnum;
import net.summerfarm.wms.api.inner.prove.dto.ProveInnerReqDTO;
import net.summerfarm.wms.application.prove.converter.ProveBizTypeConvert;
import net.summerfarm.wms.domain.prove.ProveDelegate;
import net.summerfarm.wms.domain.prove.domainobject.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
public class ProveFactory {

    @Resource
    private ProveDelegate proveDelegate;

    public Prove createProve(ProveReqDTO reqDTO) {
        if (Objects.isNull(reqDTO)) {
            return Prove.builder().build();
        }
        return Prove.builder()
                .proveDelegate(proveDelegate)
                .customsDeclarationCertificate(CustomsDeclarationCertificate.builder().proofUrl(reqDTO.getCustomsDeclarationCertificate()).build())
                .operator(reqDTO.getOperator())
                .inspectionReport(InspectionReport.builder().reportUrl(reqDTO.getQualityInspectionReport()).build())
                .proveBelong(ProveBelong.builder()
                        .produceAt(reqDTO.getProduceAt())
                        .purchaseNo(reqDTO.getPurchaseNo())
                        .shelfLife(reqDTO.getShelfLife())
                        .sku(reqDTO.getSku())
                        .build())
                .disinfectionCertificate(DisinfectionCertificate.builder().proofUrl(reqDTO.getDisinfectionCertificate()).build())
                .nucleicAcidDetection(NucleicAcidDetection.builder().reportUrl(reqDTO.getNucleicAcidDetection()).build())
                .pesticideResidueReport(PesticideResidueReport.builder()
                        .reportUrl(Objects.nonNull(reqDTO.getResidueReport()) ? reqDTO.getResidueReport().getPesticideResiduePictures() : null)
                        .detectionResult(Objects.nonNull(reqDTO.getResidueReport()) ? reqDTO.getResidueReport().getDetectionResult() : null)
                        .inhibitionRate(Objects.nonNull(reqDTO.getResidueReport()) ? reqDTO.getResidueReport().getInhibitionRate() : null)
                        .samples(Objects.nonNull(reqDTO.getResidueReport()) ? reqDTO.getResidueReport().getNumberSamples() : null)
                        .samplingBase(Objects.nonNull(reqDTO.getResidueReport()) ? reqDTO.getResidueReport().getSamplingBase() : null)
                        .build())
                .supervisedWarehouseProof(SupervisedWarehouseProof.builder().proofUrl(reqDTO.getSupervisionWarehouseCertificate()).build())
                .proofComplete(reqDTO.getIsComplete())
                .build();
    }

    public Prove createProve(ProveInnerReqDTO reqDTO) {
        if (Objects.isNull(reqDTO)) {
            return Prove.builder().build();
        }
        return Prove.builder()
                .proveDelegate(proveDelegate)
                .customsDeclarationCertificate(CustomsDeclarationCertificate.builder().proofUrl(reqDTO.getCustomsDeclarationCertificate()).build())
                .operator(reqDTO.getOperator())
                .inspectionReport(InspectionReport.builder().reportUrl(reqDTO.getInspectionReport()).build())
                .proveBelong(ProveBelong.builder()
                        .produceAt(reqDTO.getProduceAt())
                        .purchaseNo(reqDTO.getPurchaseNo())
                        .shelfLife(reqDTO.getShelfLife())
                        .sku(reqDTO.getSku())
                        .build())
                .nucleicAcidDetection(NucleicAcidDetection.builder().reportUrl(reqDTO.getNucleicAcidDetection()).build())
                .pesticideResidueReport(PesticideResidueReport.builder()
                        .reportUrl(reqDTO.getPesticideResidueReport())
                        .detectionResult(reqDTO.getDetectionResult())
                        .inhibitionRate(reqDTO.getInhibitionRate())
                        .samples(reqDTO.getSamples())
                        .samplingBase(reqDTO.getSamplingBase())
                        .build())
                .supervisedWarehouseProof(SupervisedWarehouseProof.builder().proofUrl(reqDTO.getSupervisedWarehouseProof()).build())
                .sourceId(reqDTO.getSourceId())
                .type(ProveBizTypeConvert.convert(ProveBizTypeEnum.convert(reqDTO.getType())).getProveBizType().getCode())
                .build();
    }

}
