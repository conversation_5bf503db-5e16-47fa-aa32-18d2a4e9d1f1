package net.summerfarm.wms.infrastructure.repository.processingtask.converter;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskProductDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProcessingTaskProductConverter {
    ProcessingTaskProductConverter INSTANCE = Mappers.getMapper(ProcessingTaskProductConverter.class);

    ProcessingTaskProduct convert(WmsProcessingTaskProductDO productDO);

    List<ProcessingTaskProduct> convertList(List<WmsProcessingTaskProductDO> productDOList);
}
