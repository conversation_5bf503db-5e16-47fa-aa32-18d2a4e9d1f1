package net.summerfarm.wms.application.areastore.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AreaStoreSafeStockUpdateDto implements Serializable {

    @ApiModelProperty(value = "仓库编码")
    private Long warehouseNo;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "saasSkuId")
    private Long saasSkuId;

    @ApiModelProperty(value = "修改后的安全库存")
    private Integer safeQuantity;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 是否旧版安全库存更新
     */
    private Boolean oldSafeInventoryUpdate = true;
}
