package net.summerfarm.wms.domain.stocktask.domainobject;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskItemCabinetPickDO {

    /**
     * 出库任务编码
     */
    private Long stockTaskId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD, timezone = "GMT+8")
    @JSONField(format = DateUtil.YYYY_MM_DD)
    private Date productionDate;

    /**
     * 保质期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD, timezone = "GMT+8")
    @JSONField(format = DateUtil.YYYY_MM_DD)
    private Date qualityDate;

    /**
     * 拣货数量
     */
    private Integer pickQuantity;


}
