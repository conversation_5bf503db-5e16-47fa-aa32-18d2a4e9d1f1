package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.Category;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Deprecated
@Repository
public interface CategoryMapper {

    /**
     * 根据sku获得类目
     * @param sku
     * @return
     */
    @Deprecated
    Category selectBySku(@Param("sku") String sku);

    @Deprecated
    List<Category> selectTypeByIds(@Param("ids") List<Integer> ids);
}
