package net.summerfarm.wms.application.processingtask.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialRestoreReqDTO;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialRestoreAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialRestoreRecord;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Slf4j
public class ProcessingTaskMaterialRestoreAggregateFactory {

    /**
     * 实例
     *
     * @param reqDTO
     * @param materialReceiveRecord
     * @param material
     * @return
     */
    public static ProcessingTaskMaterialRestoreAggregate newInstance(
            ProcessingTaskMaterialRestoreReqDTO reqDTO,
            ProcessingTaskMaterialReceiveRecord materialReceiveRecord,
            ProcessingTaskMaterial material) {
        if (materialReceiveRecord.getMaterialSkuRemainWeight() == null){
            throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), "归还前请先提交剩余原料重量");
        }

        Integer restoreQuantity = reqDTO.getMaterialSkuRestoreQuantity();
        BigDecimal restoreWeight = material.getMaterialSkuWeight().multiply(
                BigDecimal.valueOf(reqDTO.getMaterialSkuRestoreQuantity()));

        ProcessingTaskMaterialRestoreAggregate restoreAggregate = new ProcessingTaskMaterialRestoreAggregate();

        restoreAggregate.setMaterialReceiveId(reqDTO.getMaterialReceiveId());
        restoreAggregate.setMaterialSkuRestoreQuantity(restoreQuantity);
        restoreAggregate.setMaterialSkuRestoreWeight(restoreWeight);
        restoreAggregate.setProcessingTaskMaterialId(reqDTO.getProcessingTaskMaterialId());
        restoreAggregate.setProcessingTaskProductId(reqDTO.getProcessingTaskProductId());

        restoreAggregate.setProcessingTaskMaterialRestoreRecord(
                convertRestore(reqDTO, materialReceiveRecord, material, restoreQuantity));

        return restoreAggregate;
    }

    private static ProcessingTaskMaterialRestoreRecord convertRestore(
            ProcessingTaskMaterialRestoreReqDTO reqDTO,
            ProcessingTaskMaterialReceiveRecord materialReceiveRecord,
            ProcessingTaskMaterial material,
            Integer restoreQuantity) {

        ProcessingTaskMaterialRestoreRecord restoreRecord = new ProcessingTaskMaterialRestoreRecord();

        restoreRecord.setWarehouseNo(materialReceiveRecord.getWarehouseNo());
        restoreRecord.setProcessingTaskCode(materialReceiveRecord.getProcessingTaskCode());
        restoreRecord.setProcessingTaskProductId(materialReceiveRecord.getProcessingTaskProductId());

        restoreRecord.setProductSkuCode(materialReceiveRecord.getProductSkuCode());

        restoreRecord.setMaterialSkuCode(materialReceiveRecord.getMaterialSkuCode());
        restoreRecord.setMaterialSkuName(materialReceiveRecord.getMaterialSkuName());
        restoreRecord.setMaterialSkuWeight(materialReceiveRecord.getMaterialSkuWeight());
        restoreRecord.setMaterialSkuUnit(materialReceiveRecord.getMaterialSkuUnit());
        restoreRecord.setMaterialSkuPurchaseBatch(materialReceiveRecord.getMaterialSkuPurchaseBatch());

        // 计算归还数量
        restoreRecord.setMaterialSkuRestoreQuantity(restoreQuantity);

        restoreRecord.setMaterialSkuProductionDate(materialReceiveRecord.getMaterialSkuProductionDate());
        restoreRecord.setMaterialSkuQualityDate(materialReceiveRecord.getMaterialSkuQualityDate());
        restoreRecord.setProcessingTaskMaterialId(materialReceiveRecord.getProcessingTaskMaterialId());
        restoreRecord.setMaterialSkuCabinetCode(materialReceiveRecord.getMaterialSkuCabinetCode());

        restoreRecord.setCreator(LoginInfoThreadLocal.getCurrentUserName());
        restoreRecord.setCreateTime(new Date());
        restoreRecord.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
        restoreRecord.setUpdateTime(new Date());
        restoreRecord.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        restoreRecord.setTenantId(reqDTO.getTenantId());

        return restoreRecord;
    }
}
