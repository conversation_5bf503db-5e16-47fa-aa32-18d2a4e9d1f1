package net.summerfarm.wms.manage.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
public interface AllocationOrderEnums {

    @AllArgsConstructor
    @Getter
    enum StorageLocation {
        FREEZING(1, "冷冻"),
        REFRIGERATE(2, "冷藏"),
        FREEZING_REFRIGERATE(3, "冷冻+冷藏"),
        ORDINARY(4, "常温"),
        FREEZING_ORDINARY(5, "冷冻+常温"),
        REFRIGERATE_ORDINARY(6, "冷藏+常温"),
        FREEZING_REFRIGERATE_ORDINARY(7, "冷冻+冷藏+常温");

        private Integer state;

        private String description;

    }

    @AllArgsConstructor
    @Getter
    enum TrunkFlag {
        CLOSE(0, "关闭"),
        OPEN(1, "开启");

        private Integer state;

        private String description;
    }

    @AllArgsConstructor
    @Getter
    enum StockAllocationListStatus {
        //    WAIT_START, WAIT_CONFIRM, WAIT_AUDIT, OUTING, DELIVERY, IN, FINISH, RETURNING, RETURN_FINISH, CANCEL
        WAIT_START(0, "我的草稿"),
        OUTING(4, "待出库"),
        DELIVERY(5, "待入库"),
        IN(6, "已入库"),
        CANCEL(7, "已关闭");
        private Integer state;
        private String description;
    }

    @AllArgsConstructor
    @Getter
    enum StockAllocationListType {
        NORMAL(0, "正常"),
        NEXT_DAY(1, "次日达");

        private Integer state;

        private String description;
    }

    @AllArgsConstructor
    @Getter
    enum StockAllocationOrderTypeEnum {

        MANUAL_CREATE(1, "手动创建"),
        PLAN_CREATE(4, "调拨计划创建");


        private Integer type;

        private String description;

    }


}
