package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 安全库存预警配置
 * @date 2023/11/15
 */
@NacosConfigurationProperties(prefix = "safeinventory.warn", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Configuration
@Data
public class SafeInventoryWarnConfig {

    /**
     * 预警周期（单位：天），默认7天
     */
    private Integer period;

}
