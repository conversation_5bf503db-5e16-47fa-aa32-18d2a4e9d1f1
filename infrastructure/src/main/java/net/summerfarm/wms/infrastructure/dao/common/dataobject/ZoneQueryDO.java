package net.summerfarm.wms.infrastructure.dao.common.dataobject;

import lombok.*;
import net.summerfarm.wms.infrastructure.dao.base.dataobject.BaseQueryDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 库区DO
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ZoneQueryDO extends BaseQueryDO implements Serializable {

    private static final long serialVersionUID = -4546649843830913829L;

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 库区编码
     */
    private String zoneCode;

    /**
     * 库区名称
     */
    private String zoneName;

    /**
     * 库区类型（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    private Integer zoneType;

    /**
     * 库区类型集合（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    private List<Integer> zoneTypes;

    /**
     * 库区状态（0：禁用，1：启用）
     */
    private Integer zoneStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 初始化库区标签（0：非系统初始化库区，1：系统初始化库区）
     */
    private Integer initOption;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 是否支持拣货(0-不支持  1-支持)
     */
    private Integer pickOption;

    /**
     * 库区编码集合
     */
    private List<String> zoneCodes;
}
