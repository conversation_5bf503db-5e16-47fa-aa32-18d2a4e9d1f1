package net.summerfarm.wms.api.h5.mission.stocktaking.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StocktakingMissionCreateItemReq implements Serializable {

    /**
     * sku
     */
    String sku;

    /**
     * 生产日期
     */
    LocalDate produceDate;

    /**
     * 保质期
     */
    LocalDate qualityDate;

    /**
     * 库位
     */
    String cabinetNo;

    /**
     * 库区
     */
    String zone;

    /**
     * 盘点库存
     */
    Integer quantity;
}
