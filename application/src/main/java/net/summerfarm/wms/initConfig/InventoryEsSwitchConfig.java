package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 库存es开关配置
 */
@Data
@Configuration
@NacosConfigurationProperties(prefix = "inventory.es.switch", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class InventoryEsSwitchConfig {

    /**
     * saas查询
     */
    private Boolean saasQueryInventory;

    public Boolean getSaasQueryInventory() {
        return saasQueryInventory != null && saasQueryInventory;
    }
}
