package net.summerfarm.wms.common.util;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorBizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.xianmu.common.result.DubboResponse;

public class DubboResponseUtil {

    /**
     * 抛出自定义异常
     *
     * @param response  r
     * @param errorCode e
     */
    public static void isSuccess(DubboResponse<?> response, ErrorCode errorCode) {
        if (!response.isSuccess()) {
            throw new BizException(errorCode);
        }
    }

    /**
     * 抛出自定义异常
     *
     * @param response  r
     * @param errorCode e
     */
    public static void isSuccess(DubboResponse<?> response, ErrorCodeNew errorCode) {
        if (!response.isSuccess()) {
            throw new ErrorBizException(errorCode);
        }
    }

    /**
     * 透传异常消息
     *
     * @param response r
     */
    public static void isSuccess(DubboResponse<?> response) {
        if (!response.isSuccess()) {
            throw new DefaultServiceException(response.getCode(), response.getMsg());
        }
    }
}
