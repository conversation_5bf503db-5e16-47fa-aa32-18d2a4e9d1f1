package net.summerfarm.wms.domain.outstore.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/25 16:55
 * @Version 1.0
 */
public interface WmsDamageStockRepository {
    /**
     * 查询货损任务的任务面板 - 数量
     *
     * @param warehouseNo     仓库编号
     * @param statusList      任务状态数组
     * @param theStatus       任务状态
     * @param creator         创建人
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd   数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo,
                                                List<Integer> statusList,
                                                Integer theStatus,
                                                String creator,
                                                LocalDateTime createTimeStart,
                                                LocalDateTime createTimeEnd);

    PageInfo<String> queryTaskPanelWindowPageData(Integer panelQueryTypeEnum,
                                                  Integer pageNum,
                                                  Integer pageSize,
                                                  Integer warehouseNo,
                                                  Integer initStatus,
                                                  Integer waitAuditStatus,
                                                  String creator,
                                                  LocalDateTime createTimeStart,
                                                  LocalDateTime createTimeEnd);
}
