package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * @Date 2023/10/23 14:54
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInFinishDTO implements Serializable {

    private static final long serialVersionUID = 254076015243583592L;
    /**
     * 采购单号
     */
    private String outPurchaseNo;
    /**
     * 入库仓
     */
    private Integer inWarehouseNo;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 明细
     */
    private List<InBoundOrderDetailDTO> detailList;

}
