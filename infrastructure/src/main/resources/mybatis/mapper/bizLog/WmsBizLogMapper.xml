<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.bizLog.WmsBizLogMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsBizLogResultMap" type="net.summerfarm.wms.infrastructure.dao.bizLog.dataobject.WmsBizLog">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
		<result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
		<result column="biz_id" property="bizId" jdbcType="NUMERIC"/>
		<result column="title" property="title" jdbcType="VARCHAR"/>
		<result column="content" property="content" jdbcType="VARCHAR"/>
		<result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsBizLogColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.tenant_id,
          t.biz_type,
          t.biz_no,
          t.biz_id,
          t.title,
          t.content,
          t.operator_name
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="tenantId != null and tenantId !=''">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="bizType != null and bizType !=''">
                AND t.biz_type = #{bizType}
            </if>
			<if test="bizNo != null and bizNo !=''">
                AND t.biz_no = #{bizNo}
            </if>
			<if test="bizId != null">
                AND t.biz_id = #{bizId}
            </if>
			<if test="title != null">
                AND t.title = #{title}
            </if>
			<if test="content != null">
                AND t.content = #{content}
            </if>
			<if test="operatorName != null and operatorName !=''">
                AND t.operator_name = #{operatorName}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="bizType != null">
                    t.biz_type = #{bizType},
                </if>
                <if test="bizNo != null">
                    t.biz_no = #{bizNo},
                </if>
                <if test="bizId != null">
                    t.biz_id = #{bizId},
                </if>
                <if test="title != null">
                    t.title = #{title},
                </if>
                <if test="content != null">
                    t.content = #{content},
                </if>
                <if test="operatorName != null">
                    t.operator_name = #{operatorName},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsBizLogResultMap" >
        SELECT <include refid="wmsBizLogColumns" />
        FROM wms_biz_log t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.bizLog.param.query.WmsBizLogQueryParam"  resultType="net.summerfarm.wms.domain.bizLog.entity.WmsBizLogEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.tenant_id tenantId,
            t.biz_type bizType,
            t.biz_no bizNo,
            t.biz_id bizId,
            t.title title,
            t.content content,
            t.operator_name operatorName
        FROM wms_biz_log t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.bizLog.param.query.WmsBizLogQueryParam" resultMap="wmsBizLogResultMap" >
        SELECT <include refid="wmsBizLogColumns" />
        FROM wms_biz_log t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.bizLog.dataobject.WmsBizLog" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_biz_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="bizType != null">
				  biz_type,
              </if>
              <if test="bizNo != null">
				  biz_no,
              </if>
              <if test="bizId != null">
				  biz_id,
              </if>
              <if test="title != null">
				  title,
              </if>
              <if test="content != null">
				  content,
              </if>
              <if test="operatorName != null">
				  operator_name,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=VARCHAR},
              </if>
              <if test="bizType != null">
				#{bizType,jdbcType=VARCHAR},
              </if>
              <if test="bizNo != null">
				#{bizNo,jdbcType=VARCHAR},
              </if>
              <if test="bizId != null">
				#{bizId,jdbcType=NUMERIC},
              </if>
              <if test="title != null">
				#{title,jdbcType=INTEGER},
              </if>
              <if test="content != null">
				#{content,jdbcType=INTEGER},
              </if>
              <if test="operatorName != null">
				#{operatorName,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.bizLog.dataobject.WmsBizLog" >
        UPDATE wms_biz_log t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.bizLog.dataobject.WmsBizLog" >
        DELETE FROM wms_biz_log t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>


</mapper>