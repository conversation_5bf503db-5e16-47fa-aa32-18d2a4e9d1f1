package net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import net.summerfarm.wms.infrastructure.dao.base.dataobject.BaseQueryDO;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 库位库存流水DO
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CabinetInventoryFlowDO extends BaseQueryDO implements Serializable {

    private static final long serialVersionUID = -1953973627331955544L;

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 关联库位库存id
     */
    private Long cabinetInventoryId;

    /**
     * 变更前数量
     */
    private Integer beforeChangeQuantity;

    /**
     * 变更数量
     */
    private Integer changeQuantity;

    /**
     * 变更后数量
     */
    private Integer afterChangeQuantity;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 库存变动类型
     */
    private String orderTypeName;

    /**
     * 内部流转单号
     */
    private String internalNo;

    /**
     * 生产日期
     */
    private Date produceDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 所属库区编码
     */
    private String zoneCode;

    /**
     * 库区类型（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    private Integer zoneType;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 操作类别
     *
     * @see net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType
     */
    @ApiModelProperty(value = "操作类别")
    private Integer operatorType;

    /**
     * 变更前锁库
     */
    private Integer beforeChangeLockQuantity;

    /**
     * 变更锁库
     */
    private Integer changeLockQuantity;

    /**
     * 变更后锁库
     */
    private Integer afterChangeLockQuantity;
}
