package net.summerfarm.wms.config;

import com.github.pagehelper.PageHelper;
import net.summerfarm.wms.interceptor.DataPermissionInterceptor;
import net.summerfarm.wms.interceptor.PrepareInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @author: dongcheng
 * @date: 2023/7/28
 */
@ComponentScan
@EnableTransactionManagement
@Configuration
public class MybatisConfig {
    @Bean
    public DataPermissionInterceptor dataPermissionInterceptor() {
        return  new DataPermissionInterceptor();
    }

//    @Bean
//    public PageHelper pageHelper() {
//        return new PageHelper();
//    }

    @Bean
    public PrepareInterceptor prepareInterceptor() {
        return  new PrepareInterceptor();
    }


}
