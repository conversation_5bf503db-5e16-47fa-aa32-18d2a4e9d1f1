package net.summerfarm.wms.infrastructure.dao.stockTask.dataobject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "库存调拨单实体类")
public class AllocationOrderDO implements Serializable {

    private static final long serialVersionUID = 4758224889711904114L;
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "调拨单号")
    private String listNo;

    @ApiModelProperty(value = "调拨发起人admin_id")
    private Integer createAdmin;

    @ApiModelProperty(value = "调拨发起人")
    private String createAdminName;

    @ApiModelProperty(value = "审核人admin_id")
    private Integer auditAdmin;

    @ApiModelProperty(value = "审核人")
    private String auditAdminName;

    @ApiModelProperty(value = "调出仓")
    private Integer outStore;

    @ApiModelProperty(value = "调出仓名")
    private String outStoreName;

    @ApiModelProperty(value = "调入仓")
    private Integer inStore;

    @ApiModelProperty(value = "调入仓名")
    private String inStoreName;

    @ApiModelProperty(value = "入库仓库负责人admin_id")
    private Integer inStoreAdmin;

    @ApiModelProperty(value = "入库仓库负责人")
    private String inStoreAdminName;

    @ApiModelProperty(value = "出库仓库负责人admin_id")
    private Integer outStoreAdmin;

    @ApiModelProperty(value = "出库仓库负责人")
    private String outStoreAdminName;

    @ApiModelProperty(value = "调出仓负责人确认,0待确认，1通过2不通过")
    private Integer outStatus;

    @ApiModelProperty(value = "调入仓负责人确认,0待确认，1通过2不通过")
    private Integer inStatus;

    @ApiModelProperty(value = "出库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime outTime;

    @ApiModelProperty(value = "期望出库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime expectOutTime;

    @ApiModelProperty(value = "期望入库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime expectTime;

    @ApiModelProperty(value = "调拨单状态")
    private Integer status;

    @ApiModelProperty(value = "到货时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime inTime;

    @ApiModelProperty(value = "运输方式")
    private Integer transport;

    @ApiModelProperty(value = "物流单号")
    private String trackingNo;

    @ApiModelProperty(value = "添加时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime addtime;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime updatetime;


    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 调拨计划单号
     */
    private String planListNo;

    /**
     * 是否次日达：0、是  1、不是 （空表示次日达）
     */
    private Integer nextDayArrive;

    /**
     * 调拨计划id
     */
    private Long planListId;

    /**
     * 干线调度
     */
    private Integer trunkFlag;


    /**
     * 温区
     */
    private Integer storageLocation;

    /**
     * 租户
     */
    private Long tenantId;
}
