package net.summerfarm.wms.application.inventory.biz.converter;

import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryDTO;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/25
 */
@Mapper
public interface CabinetBatchInventoryDTOConverter {

    CabinetBatchInventoryDTOConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryDTOConverter.class);

    CabinetBatchInventoryDTO convert(CabinetBatchInventory cabinetBatchInventory);
}
