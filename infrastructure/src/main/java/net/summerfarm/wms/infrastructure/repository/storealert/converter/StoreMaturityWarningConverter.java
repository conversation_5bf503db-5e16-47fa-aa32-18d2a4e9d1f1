package net.summerfarm.wms.infrastructure.repository.storealert.converter;

import net.summerfarm.wms.domain.stockalert.domainobject.StoreMaturityWarning;
import net.summerfarm.wms.infrastructure.offline.storealert.dataobject.StoreMaturityWarningDO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-01 12:01:21
 */
public class StoreMaturityWarningConverter {

    private StoreMaturityWarningConverter() {
        // 无需实现
    }

    public static List<StoreMaturityWarning> toStoreMaturityWarningList(List<StoreMaturityWarningDO> storeMaturityWarningDOList) {
        if (storeMaturityWarningDOList == null) {
            return Collections.emptyList();
        }
        List<StoreMaturityWarning> storeMaturityWarningList = new ArrayList<>();
        for (StoreMaturityWarningDO maturityWarningDO : storeMaturityWarningDOList) {
            storeMaturityWarningList.add(toStoreMaturityWarning(maturityWarningDO));
        }
        return storeMaturityWarningList;
    }

    public static StoreMaturityWarning toStoreMaturityWarning(StoreMaturityWarningDO storeMaturityWarningDO) {
        if (storeMaturityWarningDO == null) {
            return null;
        }
        StoreMaturityWarning storeMaturityWarning = new StoreMaturityWarning();
        storeMaturityWarning.setId(storeMaturityWarningDO.getId());
        storeMaturityWarning.setCreateTime(storeMaturityWarningDO.getCreateTime());
        storeMaturityWarning.setUpdateTime(storeMaturityWarningDO.getUpdateTime());
        storeMaturityWarning.setWarehouseNo(storeMaturityWarningDO.getWarehouseNo());
        storeMaturityWarning.setWarehouseName(storeMaturityWarningDO.getWarehouseName());
        storeMaturityWarning.setWarehouseProvider(storeMaturityWarningDO.getWarehouseProvider());
        storeMaturityWarning.setPdId(storeMaturityWarningDO.getPdId());
        storeMaturityWarning.setSku(storeMaturityWarningDO.getSku());
        storeMaturityWarning.setSaasSkuId(storeMaturityWarningDO.getSaasSkuId());
        storeMaturityWarning.setCategoryId(storeMaturityWarningDO.getCategoryId());
        storeMaturityWarning.setSkuTenantId(storeMaturityWarningDO.getSkuTenantId());
        storeMaturityWarning.setWarehouseTenantId(storeMaturityWarningDO.getWarehouseTenantId());
        storeMaturityWarning.setBatch(storeMaturityWarningDO.getBatch());
        storeMaturityWarning.setQuantity(storeMaturityWarningDO.getQuantity());
        storeMaturityWarning.setStorageDays(storeMaturityWarningDO.getStorageDays());
        storeMaturityWarning.setInStoreDate(storeMaturityWarningDO.getInStoreDate());
        storeMaturityWarning.setStorageExpirationDate(storeMaturityWarningDO.getStorageExpirationDate());
        storeMaturityWarning.setStorageRemainingDays(storeMaturityWarningDO.getStorageRemainingDays());
        storeMaturityWarning.setApproachingMaturityPercentage(storeMaturityWarningDO.getApproachingMaturityPercentage());
        storeMaturityWarning.setStatus(storeMaturityWarningDO.getStatus());
        storeMaturityWarning.setDayTag(storeMaturityWarningDO.getDayTag());
        storeMaturityWarning.setUseFlag(storeMaturityWarningDO.getUseFlag());
        return storeMaturityWarning;
    }

    public static List<StoreMaturityWarningDO> toStoreMaturityWarningDOList(List<StoreMaturityWarning> storeMaturityWarningList) {
        if (storeMaturityWarningList == null) {
            return Collections.emptyList();
        }
        List<StoreMaturityWarningDO> storeMaturityWarningDOList = new ArrayList<>();
        for (StoreMaturityWarning storeMaturityWarning : storeMaturityWarningList) {
            storeMaturityWarningDOList.add(toStoreMaturityWarningDO(storeMaturityWarning));
        }
        return storeMaturityWarningDOList;
    }

    public static StoreMaturityWarningDO toStoreMaturityWarningDO(StoreMaturityWarning storeMaturityWarning) {
        if (storeMaturityWarning == null) {
            return null;
        }
        StoreMaturityWarningDO maturityWarningDO = new StoreMaturityWarningDO();
        maturityWarningDO.setId(storeMaturityWarning.getId());
        maturityWarningDO.setCreateTime(storeMaturityWarning.getCreateTime());
        maturityWarningDO.setUpdateTime(storeMaturityWarning.getUpdateTime());
        maturityWarningDO.setWarehouseNo(storeMaturityWarning.getWarehouseNo());
        maturityWarningDO.setWarehouseName(storeMaturityWarning.getWarehouseName());
        maturityWarningDO.setWarehouseProvider(storeMaturityWarning.getWarehouseProvider());
        maturityWarningDO.setPdId(storeMaturityWarning.getPdId());
        maturityWarningDO.setSku(storeMaturityWarning.getSku());
        maturityWarningDO.setSaasSkuId(storeMaturityWarning.getSaasSkuId());
        maturityWarningDO.setCategoryId(storeMaturityWarning.getCategoryId());
        maturityWarningDO.setSkuTenantId(storeMaturityWarning.getSkuTenantId());
        maturityWarningDO.setWarehouseTenantId(storeMaturityWarning.getWarehouseTenantId());
        maturityWarningDO.setBatch(storeMaturityWarning.getBatch());
        maturityWarningDO.setQuantity(storeMaturityWarning.getQuantity());
        maturityWarningDO.setStorageDays(storeMaturityWarning.getStorageDays());
        maturityWarningDO.setInStoreDate(storeMaturityWarning.getInStoreDate());
        maturityWarningDO.setStorageExpirationDate(storeMaturityWarning.getStorageExpirationDate());
        maturityWarningDO.setStorageRemainingDays(storeMaturityWarning.getStorageRemainingDays());
        maturityWarningDO.setApproachingMaturityPercentage(storeMaturityWarning.getApproachingMaturityPercentage());
        maturityWarningDO.setStatus(storeMaturityWarning.getStatus());
        maturityWarningDO.setDayTag(storeMaturityWarning.getDayTag());
        maturityWarningDO.setUseFlag(storeMaturityWarning.getUseFlag());
        return maturityWarningDO;
    }
}
