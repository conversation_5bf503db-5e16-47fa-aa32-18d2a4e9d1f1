package net.summerfarm.wms.application.inventory.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.ding.enums.BizTypeEnum;
import net.summerfarm.wms.api.h5.goods.enums.SkuTypeEnums;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryWarnCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryWarnCommand;
import net.summerfarm.wms.application.inventory.biz.factory.SafeInventoryWarnFactory;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.SafeInventoryWarnStatusEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryWarn;
import net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery;
import net.summerfarm.wms.domain.inventory.repository.SafeInventoryWarnRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.enums.SafeInventoryLockStatusEnum;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockQueryRepository;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.summerfarm.wms.facade.process.ProcessFacade;
import net.summerfarm.wms.facade.process.dto.ProcessCreateFacadeDTO;
import net.summerfarm.wms.facade.process.dto.ProcessFormFacadeDTO;
import net.summerfarm.wms.facade.warehouse.WarehouseWmsFacade;
import net.summerfarm.wms.initConfig.SafeInventoryWarnConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/15
 */
@Service
@Slf4j
public class SafeInventoryWarnCommandServiceImpl implements SafeInventoryWarnCommandService {

    @Resource
    private SafeInventoryWarnConfig safeInventoryWarnConfig;
    @Resource
    private SafeInventoryWarnRepository safeInventoryWarnRepository;
    @Resource
    private SafeInventoryWarnFactory safeInventoryWarnFactory;
    @Resource
    private WarehouseWmsFacade warehouseWmsFacade;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProcessFacade processFacade;
    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private AdminUtil adminUtil;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private WmsSafeInventoryLockQueryRepository wmsSafeInventoryLockQueryRepository;

    @Override
    public Boolean lockWarn(SafeInventoryWarnCommand safeInventoryWarnCommand) {
        log.info("安全库存锁定，预警记录，入参：{}", JSON.toJSONString(safeInventoryWarnCommand));
        if (null == safeInventoryWarnCommand) {
            return false;
        }
        // 默认预警7天
        Integer warnPeriod = 7;
        Integer period = safeInventoryWarnConfig.getPeriod();
        if (null != period && !warnPeriod.equals(period)) {
            warnPeriod = period;
        }
        // 预警实体组装
        SafeInventoryWarn safeInventoryWarn = safeInventoryWarnFactory.buildForLock(safeInventoryWarnCommand, warnPeriod);
        if (null == safeInventoryWarn) {
            return false;
        }
        Long id = safeInventoryWarnRepository.create(safeInventoryWarn);
        log.info("安全库存锁定，预警记录完成，id：{}", id);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean releaseWarn(SafeInventoryWarnCommand safeInventoryWarnCommand) {
        log.info("安全库存释放，预警记录，入参：{}", JSON.toJSONString(safeInventoryWarnCommand));
        if (null == safeInventoryWarnCommand) {
            return false;
        }
        SafeInventoryWarnQuery safeInventoryWarnQuery = safeInventoryWarnFactory.buildQuery(safeInventoryWarnCommand);
        if (null == safeInventoryWarnQuery) {
            return false;
        }
        List<SafeInventoryWarn> safeInventoryWarnList = safeInventoryWarnRepository.list(safeInventoryWarnQuery);
        if (CollectionUtils.isEmpty(safeInventoryWarnList)) {
            return false;
        }
        // 按锁定时间依次释放
        Integer releaseQuantity = safeInventoryWarnCommand.getReleaseQuantity();
        for (SafeInventoryWarn safeInventoryWarn : safeInventoryWarnList) {
            Integer lockQuantity = safeInventoryWarn.getLockQuantity();
            // 原有锁定记录满足本次释放
            if (releaseQuantity >= lockQuantity) {
                safeInventoryWarn.setWarnStatus(SafeInventoryWarnStatusEnum.CLOSE.getCode());
                safeInventoryWarn.setUpdateOperator(safeInventoryWarnCommand.getCreateOperator());
                safeInventoryWarn.setUpdateOperatorId(safeInventoryWarnCommand.getCreateOperatorId());
                safeInventoryWarn.setReleaseQuantity(lockQuantity);
                // 原因超长截断
                if (null != safeInventoryWarnCommand.getReleaseReason() && safeInventoryWarnCommand.getReleaseReason().length() > 254) {
                    safeInventoryWarn.setReleaseReason(safeInventoryWarnCommand.getReleaseReason().substring(0, 254));
                } else {
                    safeInventoryWarn.setReleaseReason(safeInventoryWarnCommand.getReleaseReason());
                }
                safeInventoryWarn.setUpdateTime(new Date());
                safeInventoryWarnRepository.update(safeInventoryWarn);
                releaseQuantity -= lockQuantity;
                log.info("安全库存释放，更新原有锁定记录完成，原有锁定记录满足本次释放，id：{}", safeInventoryWarn.getId());
            } else {
                // 原锁定记录去掉本次释放数量仍有剩余数量
                // 剩余数量
                Integer diffQuantity = lockQuantity - releaseQuantity;
                // 根据原有记录数据和本次剩余数量新增记录
                SafeInventoryWarn createSafeInventoryWarn = safeInventoryWarnFactory.buildForRelease(
                        safeInventoryWarn, diffQuantity, safeInventoryWarnCommand.getTenantId());
                Long id = safeInventoryWarnRepository.create(createSafeInventoryWarn);
                log.info("安全库存释放，依据原有记录生成新记录完成，id：{}", id);
                // 原有记录删除&更新操作人及释放原因
                safeInventoryWarn.setWarnStatus(SafeInventoryWarnStatusEnum.CLOSE.getCode());
                safeInventoryWarn.setUpdateOperator(safeInventoryWarnCommand.getCreateOperator());
                safeInventoryWarn.setUpdateOperatorId(safeInventoryWarnCommand.getCreateOperatorId());
                safeInventoryWarn.setReleaseQuantity(releaseQuantity);
                // 原因超长截断
                if (null != safeInventoryWarnCommand.getReleaseReason() && safeInventoryWarnCommand.getReleaseReason().length() > 254) {
                    safeInventoryWarn.setReleaseReason(safeInventoryWarnCommand.getReleaseReason().substring(0, 254));
                } else {
                    safeInventoryWarn.setReleaseReason(safeInventoryWarnCommand.getReleaseReason());
                }
                safeInventoryWarn.setUpdateTime(new Date());
                safeInventoryWarnRepository.update(safeInventoryWarn);
                releaseQuantity = 0;
                log.info("安全库存释放，更新原有锁定记录完成，原有锁定记录锁定有剩余，id：{}", safeInventoryWarn.getId());
                break;
            }
        }
        if (releaseQuantity > 0) {
            log.error("安全库存释放数量异常，仓库：{}，sku：{}，仍有剩余数量：{}", safeInventoryWarnCommand.getWarehouseNo(), safeInventoryWarnCommand.getSku(), releaseQuantity);
        }
        return true;
    }

    @Override
    public Boolean exeWarn() {
        log.info("安全库存预警执行");
        List<Integer> lockedWarehouseNoList = wmsSafeInventoryLockQueryRepository.selectWarehouseNoByLockStatusAndWarehouseTenantId(SafeInventoryLockStatusEnum.LOCKED.getCode(), WmsConstant.XIANMU_TENANT_ID);
        if (CollectionUtils.isEmpty(lockedWarehouseNoList)) {
            log.info("查询无可执行预警数据");
            return true;
        }
        for (Integer warehouseNo : lockedWarehouseNoList) {
            List<WmsSafeInventoryLockEntity> wmsSafeInventoryLockEntityList = wmsSafeInventoryLockQueryRepository.selectByLockStatusAndWarehouseNo(SafeInventoryLockStatusEnum.LOCKED.getCode(), warehouseNo);
            // 默认预警7天
            Integer warnPeriod = 7;
            Integer period = safeInventoryWarnConfig.getPeriod();
            if (null != period && !warnPeriod.equals(period)) {
                warnPeriod = period;
            }
            Integer finalWarnPeriod = warnPeriod;
            List<WmsSafeInventoryLockEntity> filterSafeInventoryWarnList = wmsSafeInventoryLockEntityList.stream()
                    .filter(item -> DateUtil.datePlusDays(DateUtil.toDate(item.getCreateTime()), finalWarnPeriod).after(new Date()))
                    .filter(item -> item.getCreateOperatorId() != null && StringUtils.isNotEmpty(item.getCreateOperator()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSafeInventoryWarnList)) {
                log.info("当前时间无需要执行预警数据");
                return true;
            }
            // 按仓分组通知
            Map<String, List<WmsSafeInventoryLockEntity>> groupWarnMap = filterSafeInventoryWarnList.stream().collect(Collectors.groupingBy(item -> groupKey(item.getCreateOperatorId(), item.getWarehouseNo())));
            groupWarnMap.forEach((groupKey, warnList) -> {
                WmsSafeInventoryLockEntity safeInventoryWarn = warnList.get(0);
                String warehouseName = warehouseWmsFacade.queryWarehouseName(safeInventoryWarn.getWarehouseNo());
                List<String> skuList = warnList.stream().map(WmsSafeInventoryLockEntity::getSku).collect(Collectors.toList());
                QueryProduct queryProduct = QueryProduct.builder().skus(skuList).build();
                List<Product> productList = productRepository.findProducts(queryProduct);
                if (CollectionUtils.isEmpty(productList)) {
                    return;
                }
                Map<String, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (a, b) -> a));

                StringBuilder msgBuilder = new StringBuilder();
                for (WmsSafeInventoryLockEntity inventoryWarn : warnList) {
                    Product product = productMap.get(inventoryWarn.getSku());
                    if (null == product) {
                        String join = Joiner.on("，").join(inventoryWarn.getSku(), "锁定数量：" + inventoryWarn.getLockQuantity(), "锁定原因：" + inventoryWarn.getLockReason(), "锁定时间：" + DateUtil.formatYmdDate(DateUtil.toDate(inventoryWarn.getCreateTime())));
                        msgBuilder.append(join).append("\n");
                    } else {
                        String pdName = null != product.getPdName() ? product.getPdName() : "";
                        String specification = null != product.getSpecification() ? product.getSpecification() : "";
                        String skuTypeDesc = SkuTypeEnums.getNameByType(product.getSkuType());
                        String productJoin = Joiner.on("-").join(inventoryWarn.getSku(), pdName, specification, skuTypeDesc);
                        String join = Joiner.on("，").join( productJoin, "锁定数量：" + inventoryWarn.getLockQuantity() + product.getPackaging(), "锁定原因：" + inventoryWarn.getLockReason(), "锁定时间：" + DateUtil.formatYmdDate(DateUtil.toDate(inventoryWarn.getCreateTime())));
                        msgBuilder.append(join).append("\n");
                    }
                }
                // 审批流通知
                try {
                    ProcessCreateFacadeDTO processCreateFacadeDTO = ProcessCreateFacadeDTO.builder()
                            .adminId(safeInventoryWarn.getCreateOperatorId().intValue())
                            .bizType(BizTypeEnum.SAFE_INVENTORY_WARN.getId())
                            .bizId(safeInventoryWarn.getId())
                            .dingdingForms(Lists.newArrayList(
                                    ProcessFormFacadeDTO.builder().formName("仓库").formValue(warehouseName).build(),
                                    ProcessFormFacadeDTO.builder().formName("锁定人").formValue(safeInventoryWarn.getCreateOperator()).build(),
                                    ProcessFormFacadeDTO.builder().formName("锁定商品详情").formValue(msgBuilder.toString()).build()
                            ))
                            .build();
                    processFacade.createProcess(processCreateFacadeDTO);
                } catch (Exception e) {
                    log.error("安全库存预警发送通知异常，{}", groupKey, e);
                }
            });
        }
        return true;
    }

    @Override
    public Boolean initWarn() {
        List<AreaStore> areaStoreList = areaStoreRepository.querySafeQuantityGtZero();
        if (CollectionUtils.isEmpty(areaStoreList)) {
            return true;
        }
        Map<Long, List<AreaStore>> groupAreaStoreMap = areaStoreList.stream().collect(Collectors.groupingBy(AreaStore::getWarehouseNo));
        groupAreaStoreMap.forEach((warehouseNo, areaStores) -> {
            // 默认兜底
            Long defaultAdminId = 1223L;
            String defaultAdminName = "杜金海";
            WarehouseStorageCenterEntity warehouseStorageCenterEntity = warehouseStorageRepository.selectByWarehouseNo(warehouseNo.intValue());
            for (AreaStore areaStore : areaStores) {
                try {
                    SafeInventoryWarnQuery safeInventoryWarnQuery = SafeInventoryWarnQuery.builder()
                            .warehouseNo(warehouseNo.intValue())
                            .sku(areaStore.getSku())
                            .build();
                    SafeInventoryWarn safeInventoryWarn = safeInventoryWarnRepository.findOne(safeInventoryWarnQuery);
                    // 已存在跳过
                    if (null != safeInventoryWarn) {
                        continue;
                    }
                    SafeInventoryWarnCommand safeInventoryWarnCommand = SafeInventoryWarnCommand.builder()
                            .warehouseNo(areaStore.getWarehouseNo().intValue())
                            .sku(areaStore.getSku())
                            .lockQuantity(areaStore.getSafeQuantity())
                            .lockReason("历史锁定初始化通知记录")
                            .lockTime(new Date())
                            .tenantId(WmsConstant.XIANMU_TENANT_ID)
                            .build();
                    if (null != warehouseStorageCenterEntity && null != warehouseStorageCenterEntity.getManageAdminId()) {
                        AdminFacadeDTO adminFacadeDTO = adminUtil.getAdmin(warehouseStorageCenterEntity.getManageAdminId().longValue());
                        if (null != adminFacadeDTO) {
                            safeInventoryWarnCommand.setCreateOperator(adminFacadeDTO.getRealName());
                            safeInventoryWarnCommand.setCreateOperatorId(adminFacadeDTO.getAdminId());
                        } else {
                            safeInventoryWarnCommand.setCreateOperator(defaultAdminName);
                            safeInventoryWarnCommand.setCreateOperatorId(defaultAdminId);
                        }
                    }
                    // 安全库存锁定记录
                    lockWarn(safeInventoryWarnCommand);
                } catch (Exception e) {
                    log.error("安全库存预警历史数据初始化异常，仓：{}，sku：{}", areaStore.getWarehouseNo(), areaStore.getSku(), e);
                }
            }
        });

        return true;
    }

    private String groupKey(Long operatorId, Integer warehouseNo) {
        return operatorId + "_" + warehouseNo;
    }


}
