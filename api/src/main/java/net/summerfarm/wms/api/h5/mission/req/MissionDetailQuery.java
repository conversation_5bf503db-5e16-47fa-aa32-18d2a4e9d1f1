package net.summerfarm.wms.api.h5.mission.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务详情
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionDetailQuery implements Serializable {
    /**
     * 仓库号
     */
    @NotNull(message = "仓库号不可为空")
    Long warehouseNo;

    /**
     * 任务编号
     */
    @NotNull(message = "任务编号不可为空")
    String missionNo;

    /**
     * 容器号
     */
    String containerNo;

    /**
     * 任务类型
     */
    Integer missionType;

    /**
     * 任务明细Id
     */
    private Long missionDetailId;
}
