package net.summerfarm.wms.manage.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@Accessors(chain = true)
public class StockTaskAllocationDTO {

    private Integer id;

    private String taskNo;

    private Integer areaNo;

    private Integer type;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime expectTime;

    private Integer state;

    private Integer adminId;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime addtime;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime updatetime;

    /**
     * 调出仓
     */
    private Integer outStoreNo;

    private Integer outType;

    private String remark;

    private Integer dimension;

    private Integer processState;

    private String mismatchReason;

    /**
     * 任务类型 1、退货 2、拒收 3、拦截
     */

    private Integer taskType;

    /**
     * 类目名称 鲜果/非鲜果
     */

    private String category;

    private String closeReason;

    private String updater;

    /**
     * 标志位
     */
    private Long optionFlag;

    /**
     * 调入仓
     */
    private Integer inStore;

    /**
     * 预计出库时间
     */
    private LocalDateTime expectOutTime;
}
