<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.PurchasesBackDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.wms.manage.model.domain.PurchasesBackDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchases_back_no" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="out_quantity" property="outQuantity" jdbcType="INTEGER"/>
        <result column="total_cost" property="totalCost" jdbcType="DECIMAL"/>
        <result column="actual_out_quantity" property="actualOutQuantity" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.wms.manage.model.vo.PurchasesBackDetailVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchases_back_no" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="out_quantity" property="outQuantity" jdbcType="INTEGER"/>
        <result column="total_cost" property="totalCost" jdbcType="DECIMAL"/>
        <result column="actual_out_quantity" property="actualOutQuantity" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" javaType="INTEGER"/>
    </resultMap>

    <sql id="BaseColumn">
        id
        , purchases_back_no, batch, sku, area_no, quality_date, production_date, cost, out_quantity, total_cost, actual_out_quantity,type
    </sql>

    <select id="listByNo" parameterType="java.lang.String"
            resultType="net.summerfarm.wms.manage.model.vo.PurchasesBackDetailVO">
        /*FORCE_MASTER*/ SELECT pbd.id,
               pbd.purchases_back_no             purchasesBackNo,
               pbd.batch,
               pbd.sku,
               pbd.area_no                       areaNo,
               pbd.quality_date                  qualityDate,
               pbd.production_date               productionDate,
               pbd.cost,
               pbd.total_cost                    totalCost,
               pbd.out_quantity                  outQuantity,
               pbd.total_cost                    totalCost,
               pbd.actual_out_quantity           actualOutQuantity,
               ar.status,
               pbd.gl_no                         glNo
        FROM purchases_back_detail pbd
                 LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
    </select>

    <select id="listByNoAndSku" resultType="net.summerfarm.wms.manage.model.vo.PurchasesBackDetailVO">
        /*FORCE_MASTER*/ SELECT pbd.id,
                                pbd.purchases_back_no             purchasesBackNo,
                                pbd.batch,
                                pbd.sku,
                                pbd.area_no                       areaNo,
                                pbd.quality_date                  qualityDate,
                                pbd.production_date               productionDate,
                                pbd.cost,
                                pbd.total_cost                    totalCost,
                                pbd.out_quantity                  outQuantity,
                                pbd.total_cost                    totalCost,
                                pbd.actual_out_quantity           actualOutQuantity,
                                ar.status,
                                pbd.gl_no                         glNo
                         FROM purchases_back_detail pbd
                                  LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
                         WHERE pbd.purchases_back_no = #{purchasesBackNo}
                         AND pbd.sku in
                        <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
                            #{sku}
                        </foreach>
    </select>

    <select id="listByNoList" parameterType="java.lang.String"
            resultType="net.summerfarm.wms.manage.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id,
               pbd.purchases_back_no             purchasesBackNo,
               pbd.batch,
               pbd.sku,
               pbd.area_no                       areaNo,
               pbd.quality_date                  qualityDate,
               pbd.production_date               productionDate,
               pbd.cost,
               pbd.total_cost                    totalCost,
               pbd.out_quantity                  outQuantity,
               pbd.total_cost                    totalCost,
               pbd.actual_out_quantity           actualOutQuantity,
               ar.status,
               pbd.gl_no                         glNo
        FROM purchases_back_detail pbd
                 LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
        WHERE pbd.purchases_back_no IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByNoWithGoods" resultType="net.summerfarm.wms.manage.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id,
        pbd.purchases_back_no purchasesBackNo,
        pbd.batch,
        pbd.sku,
        pbd.area_no areaNo,
        pbd.quality_date qualityDate,
        pbd.production_date productionDate,
        pbd.cost,
        pbd.total_cost totalCost,
        pbd.out_quantity outQuantity,
        pbd.total_cost totalCost,
        pbd.actual_out_quantity actualOutQuantity,
        ar.status,
        pbd.gl_no glNo
        FROM purchases_back_detail pbd
        LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
        <if test="sku != null">
            and pbd.sku = #{sku}
        </if>
        <if test="querySkus != null and querySkus.size > 0">
            and pbd.sku in
            <foreach collection="querySkus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectLockBatch" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity -pbd.actual_out_quantity), 0)
        from purchases_back pb
        left join purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no
        left join stock_task st on st.task_no=pb.purchases_back_no
        <where>
            st.state in (0,1)
            <if test="batch != null">
                and pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="status != null">
                and pb.status = #{status}
            </if>
            <if test="areaNo != null">
                and pb.store_no = #{areaNo}
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_back_detail
        WHERE id = #{id}
    </select>

    <update id="update" parameterType="net.summerfarm.wms.manage.model.domain.PurchasesBackDetail">
        UPDATE purchases_back_detail
        <set>
            <if test="outQuantity != null">
                out_quantity = #{outQuantity} ,
            </if>
            <if test="actualOutQuantity != null">
                actual_out_quantity = #{actualOutQuantity} ,
            </if>
            <if test="totalCost != null">
                total_cost = #{totalCost},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectByBatch" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity),0)
        from purchases_back_detail pbd
                 left join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        where pbd.batch = #{purchasesNo} and pb.status = 2
    </select>
</mapper>