package net.summerfarm.wms.api.h5.common.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/3
 */
@Data
public class CabinetListQuery implements Serializable {

    private static final long serialVersionUID = 7310982425288841582L;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 库位类型（1：高位货架，2：轻型货架，3：地堆）
     */
    private Integer cabinetType;

    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    private Integer purpose;

    /**
     * 所属库区id
     */
    private Long zoneId;

    /**
     * 库位状态（0：禁用，1：启用）
     */
    private Integer cabinetStatus;

}
