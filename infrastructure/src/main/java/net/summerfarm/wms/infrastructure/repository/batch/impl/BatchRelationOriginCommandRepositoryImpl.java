package net.summerfarm.wms.infrastructure.repository.batch.impl;

import net.summerfarm.wms.domain.batch.entity.BatchRelationOriginEntity;
import net.summerfarm.wms.domain.batch.param.command.BatchRelationOriginCommandParam;
import net.summerfarm.wms.domain.batch.repository.BatchRelationOriginCommandRepository;
import net.summerfarm.wms.infrastructure.dao.batch.BatchRelationOriginDAO;
import net.summerfarm.wms.infrastructure.dao.batch.dataobject.BatchRelationOriginDO;
import net.summerfarm.wms.infrastructure.repository.batch.converter.BatchRelationOriginConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-07-22 17:14:42
* @version 1.0
*
*/
@Repository
public class BatchRelationOriginCommandRepositoryImpl implements BatchRelationOriginCommandRepository {

    @Autowired
    private BatchRelationOriginDAO batchRelationOriginMapper;

    @Override
    public void insertSelectiveList(List<BatchRelationOriginCommandParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)){
            return;
        }

        List<BatchRelationOriginDO> inserts = new ArrayList<>();
        for (BatchRelationOriginCommandParam param : paramList) {
            inserts.add(BatchRelationOriginConverter.toBatchRelationOrigin(param));
        }

        batchRelationOriginMapper.insertSelectiveBatch(inserts);
    }

    @Override
    public BatchRelationOriginEntity insertSelective(BatchRelationOriginCommandParam param) {
        BatchRelationOriginDO batchRelationOrigin = BatchRelationOriginConverter.toBatchRelationOrigin(param);
        batchRelationOriginMapper.insertSelective(batchRelationOrigin);
        return BatchRelationOriginConverter.toBatchRelationOriginEntity(batchRelationOrigin);
    }

    @Override
    public int updateSelectiveById(BatchRelationOriginCommandParam param){
        return batchRelationOriginMapper.updateSelectiveById(BatchRelationOriginConverter.toBatchRelationOrigin(param));
    }


    @Override
    public int remove(Long id) {
        if (id == null){
            return 0;
        }
        return batchRelationOriginMapper.remove(id);
    }
}