package net.summerfarm.wms.manage.model.input.transfer;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.manage.model.domain.TransferInInfo;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 转换任务创建req
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferCreateInput implements Serializable {
    private static final long serialVersionUID = 2102844766447317554L;
    /**
     * 库存仓编号
     */
    @NotNull(message = "库存仓编号不能为空")
    Long warehouseNo;

    /**
     * 转换维度
     *
     * @see net.summerfarm.wms.manage.web.enums.TransferDimensionEnum
     */
    @NotNull(message = "转换维度不能为空")
    Integer transferDimension;

    /**
     * 备注
     */
    @Length(max = 128, message = "备注不可超过128")
    String remark;

    /**
     * 转入信息
     */
    @NotEmpty(message = "转入信息不可为空")
    List<TransferInInfo> transferInInfos;

    /**
     * 转换任务类型
     */
    private String transferPurpose;
}
