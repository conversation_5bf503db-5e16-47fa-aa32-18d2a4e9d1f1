package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StockArrangeItem;
import net.summerfarm.wms.manage.model.vo.SrmStockArrangeItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
@Repository
public interface StockArrangeItemMapper {

    /**
     * 查询预约单条目
     */
    List<StockArrangeItem> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 查询预约单单个sku条目
     */
    StockArrangeItem selectByTaskIdAndSku(@Param("taskId") Integer taskId, @Param("sku") String sku);


    void addAbnormalQuantity(@Param("id") Integer id, @Param("quantity") Integer quantity);

    List<SrmStockArrangeItemVO> selectByStockArrangeId(Integer stockArrangeId);
}
