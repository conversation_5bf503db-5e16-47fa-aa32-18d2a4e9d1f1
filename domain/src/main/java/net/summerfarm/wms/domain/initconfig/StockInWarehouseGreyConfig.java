package net.summerfarm.wms.domain.initconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/3
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(prefix = "stock.in.warehouse.grey", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class StockInWarehouseGreyConfig {

    private List<Integer> warehouseNoList;

    /**
     * 判断当前仓库是否是灰度仓库
     *
     * @param warehouseNo 仓库编号
     * @return 返回是否是灰度仓库
     */
    public Boolean isGreyWarehouse(Integer warehouseNo) {
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return false;
        }
        return warehouseNoList.contains(warehouseNo);
    }

}
