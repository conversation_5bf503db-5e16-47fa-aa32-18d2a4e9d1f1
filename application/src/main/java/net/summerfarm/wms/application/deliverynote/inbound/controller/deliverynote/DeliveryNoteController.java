package net.summerfarm.wms.application.deliverynote.inbound.controller.deliverynote;

import net.summerfarm.wms.application.deliverynote.inbound.input.query.PrintDeliveryNoteQueryInput;
import net.summerfarm.wms.application.deliverynote.service.DeliveryNoteService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 配送单 前端控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/delivery-note")
public class DeliveryNoteController {

    @Resource
    private DeliveryNoteService deliveryNoteService;


    /**
     * 异步导出配送单
     */
    @PostMapping("/export-async/saas-self-support/print-delivery-note")
    public CommonResult<Long> downloadSaasSelfSupportPrintDeliveryNote(@Valid @RequestBody PrintDeliveryNoteQueryInput input) throws IOException {
        return CommonResult.ok(deliveryNoteService.downloadSaasSelfSupportPrintDeliveryNote(input.getStockTaskIdList()));
    }
}
