package net.summerfarm.wms.domain.inventory.domainService.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.inventory.domainService.CabinetBatchInventoryDomainService;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryUpdate;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventoryUpdate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.CabinetBatchInventoryAddAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.CabinetBatchInventoryReduceAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.SyncCloseCabinetBatchInventoryAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.SyncOpenCabinetBatchInventoryAggregate;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryFlowRepository;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryRepository;
import net.summerfarm.wms.domain.inventory.repository.CabinetInventoryFlowRepository;
import net.summerfarm.wms.domain.inventory.repository.CabinetInventoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class CabinetBatchInventoryDomainServiceImpl implements CabinetBatchInventoryDomainService {

    @Autowired
    private CabinetBatchInventoryRepository cabinetBatchInventoryRepository;
    @Autowired
    private CabinetBatchInventoryFlowRepository cabinetBatchInventoryFlowRepository;
    @Autowired
    private CabinetInventoryRepository cabinetInventoryRepository;
    @Autowired
    private CabinetInventoryFlowRepository cabinetInventoryFlowRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncOpenCabinetBatchInventory(SyncOpenCabinetBatchInventoryAggregate aggregate) {
        // 库位批次库存
        cabinetBatchInventoryRepository.creates(aggregate.getCreateCabinetBatchInventoryList());
        for (CabinetBatchInventoryUpdate cabinetBatchInventory :
                aggregate.getUpdateCabinetBatchInventoryList()) {
            cabinetBatchInventoryRepository.update(cabinetBatchInventory);
        }
        aggregate.updateCabinetBatchInventoryFlowInfoIdAfterCreate();
        cabinetBatchInventoryFlowRepository.creates(aggregate.getCabinetBatchInventoryFlowList());

        // 库位库存
        cabinetInventoryRepository.creates(aggregate.getCreateCabinetInventoryList());
        for (CabinetInventoryUpdate cabinetInventory :
                aggregate.getUpdateCabinetInventoryList()) {
            cabinetInventoryRepository.update(cabinetInventory);
        }
        aggregate.updateCabinetInventoryFlowInfoIdAfterCreate();
        cabinetInventoryFlowRepository.creates(aggregate.getCabinetInventoryFlowList());
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void syncCloseCabinetBatchInventory(SyncCloseCabinetBatchInventoryAggregate aggregate) {
        // 库位批次库存
        for (CabinetBatchInventoryUpdate cabinetBatchInventory :
                aggregate.getUpdateCabinetBatchInventoryList()) {
            cabinetBatchInventoryRepository.update(cabinetBatchInventory);
        }
        cabinetBatchInventoryFlowRepository.creates(aggregate.getCabinetBatchInventoryFlowList());

        // 库位库存
        for (CabinetInventoryUpdate cabinetInventory :
                aggregate.getUpdateCabinetInventoryList()) {
            cabinetInventoryRepository.update(cabinetInventory);
        }
        cabinetInventoryFlowRepository.creates(aggregate.getCabinetInventoryFlowList());
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void addCabinetBatchInventory(CabinetBatchInventoryAddAggregate aggregate) {
        // 库位批次库存
        cabinetBatchInventoryRepository.creates(aggregate.getCreateCabinetBatchInventoryList());
        for (CabinetBatchInventoryUpdate cabinetBatchInventory :
                aggregate.getUpdateCabinetBatchInventoryList()) {
            cabinetBatchInventoryRepository.update(cabinetBatchInventory);
        }
        aggregate.updateCabinetBatchInventoryFlowInfoIdAfterCreate();
        cabinetBatchInventoryFlowRepository.creates(aggregate.getCabinetBatchInventoryFlowList());

        // 库位库存
        cabinetInventoryRepository.creates(aggregate.getCreateCabinetInventoryList());
        for (CabinetInventoryUpdate cabinetInventory :
                aggregate.getUpdateCabinetInventoryList()) {
            cabinetInventoryRepository.update(cabinetInventory);
        }
        aggregate.updateCabinetInventoryFlowInfoIdAfterCreate();
        cabinetInventoryFlowRepository.creates(aggregate.getCabinetInventoryFlowList());

    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void reduceCabinetBatchInventory(CabinetBatchInventoryReduceAggregate aggregate) {
        // 库位批次库存
        for (CabinetBatchInventoryUpdate cabinetBatchInventory :
                aggregate.getUpdateCabinetBatchInventoryList()) {
            cabinetBatchInventoryRepository.update(cabinetBatchInventory);
        }
        cabinetBatchInventoryFlowRepository.creates(aggregate.getCabinetBatchInventoryFlowList());

        // 库位库存
        for (CabinetInventoryUpdate cabinetInventory :
                aggregate.getUpdateCabinetInventoryList()) {
            cabinetInventoryRepository.update(cabinetInventory);
        }
        cabinetInventoryFlowRepository.creates(aggregate.getCabinetInventoryFlowList());
    }
}
