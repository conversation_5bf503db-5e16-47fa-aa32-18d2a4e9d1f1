package net.summerfarm.wms.domain.materialManage.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingQueryParam;

import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialBindingQueryRepository {

    PageInfo<WmsMaterialBindingEntity> getPage(WmsMaterialBindingQueryParam param);

    List<WmsMaterialBindingEntity> getListForExport(WmsMaterialBindingQueryParam param);

    WmsMaterialBindingEntity selectById(Long id);

    List<WmsMaterialBindingEntity> selectByCondition(WmsMaterialBindingQueryParam param);

    WmsMaterialBindingEntity selectEnableByWnoAndSku(Integer warehouseNo, String sku);

    List<WmsMaterialBindingEntity> selectByWnoAndSkuList(Integer warehouseNo, List<String> skuList);

    Map<String, List<WmsMaterialBindingEntity>> mapByWnoAndSkuList(Integer warehouseNo, List<String> skuList);

}