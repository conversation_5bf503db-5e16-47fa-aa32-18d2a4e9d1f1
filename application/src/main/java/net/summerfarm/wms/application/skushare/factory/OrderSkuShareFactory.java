package net.summerfarm.wms.application.skushare.factory;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.summerfarm.wms.application.skushare.dto.*;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.skushare.domainobject.OrderSkuShare;
import net.summerfarm.wms.domain.skushare.domainobject.enums.OrderSkuShareStatus;
import net.summerfarm.wms.domain.skushare.domainobject.enums.SkuShareRecordType;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.OrderSkuShareOutOrderQuery;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.OrderSkuShareUpdateValue;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.SkuShareRate;
import net.summerfarm.wms.domain.skushare.repository.OrderSkuShareRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class OrderSkuShareFactory {

    @Autowired
    private OrderSkuShareRepository orderSkuShareRepository;

    public List<OrderSkuShare> buildOrderSkuShareForAdd(AddSkuShareCommand addSkuShareCommand, AddSkuShareDetail detail, List<SkuShareRate> skuShareRates) {
        List<OrderSkuShare> orderSkuShares = Lists.newArrayList();
        // 获取转出sku的共享比例，保存在remark中以便排查问题
        SkuShareRate sharedSkuShareRate = skuShareRates.stream().filter(x -> x.getTransferInSku().equals(detail.getSharedSku())).findFirst().orElse(null);
        for (SkuShareRate skuShareRate : skuShareRates) {
            // 过滤掉共享的sku
            if (detail.getSharedSku().equals(skuShareRate.getTransferInSku())) {
                continue;
            }
            OrderSkuShare orderSkuShare = OrderSkuShare.builder()
                    .outOrderNo(addSkuShareCommand.getOutOrderNo())
                    .outOrderType(addSkuShareCommand.getOutOrderType().getType())
                    .warehouseNo(detail.getWarehouseNo())
                    .transferOutSku(skuShareRate.getTransferOutSku())
                    .transferInSku(skuShareRate.getTransferInSku())
                    .shareTotalQuantity(detail.getSharedTotalQuantity())
                    .shareRate(skuShareRate.getShareRate())
                    .transferRate(skuShareRate.getTransferRate())
                    .remark(buildRemark(skuShareRate, sharedSkuShareRate))
                    .status(OrderSkuShareStatus.NORMAL.getStatus())
                    .recordType(addSkuShareCommand.getRecordType().getCode())
                    .recordTypeDesc(addSkuShareCommand.getRecordType().getTypeName())
                    .creator(addSkuShareCommand.getOperator() != null ? addSkuShareCommand.getOperator() : WmsConstant.SYSTEM)
                    .operator(addSkuShareCommand.getOperator() != null ? addSkuShareCommand.getOperator() : WmsConstant.SYSTEM)
                    .build();
            orderSkuShares.add(orderSkuShare);
        }
        orderSkuShares.sort(Comparator.comparing(x -> x.getTransferOutSku() + "_" + x.getTransferInSku()));
        return orderSkuShares;
    }

    public List<OrderSkuShare> buildOrderSkuShareForCancel(CancelSkuShareCommand cancelSkuShareCommand, CancelSkuShareDetail detail) {
        OrderSkuShareOutOrderQuery query = OrderSkuShareOutOrderQuery.builder()
                .outOrderNo(cancelSkuShareCommand.getOutOrderNo())
                .outOrderType(cancelSkuShareCommand.getOutOrderType().getType())
                .warehouseNo(detail.getWarehouseNo())
                .transferOutSku(detail.getSharedSku())
                .status(OrderSkuShareStatus.NORMAL.getStatus())
                .build();
        List<OrderSkuShare> orderSkuShares = orderSkuShareRepository.queryOrderSkuShare(query);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            return Lists.newArrayList();
        }
        for (OrderSkuShare orderSkuShare : orderSkuShares) {
            orderSkuShare.setRecordType(cancelSkuShareCommand.getRecordType().getCode());
            orderSkuShare.setRecordTypeDesc(cancelSkuShareCommand.getRecordType().getTypeName());
            orderSkuShare.setOperator(cancelSkuShareCommand.getOperator() != null ? cancelSkuShareCommand.getOperator() : WmsConstant.SYSTEM);
        }
        orderSkuShares.sort(Comparator.comparing(x -> x.getTransferOutSku() + "_" + x.getTransferInSku()));
        return orderSkuShares;
    }

    public List<OrderSkuShare> buildOrderSkuShareForUpdateTotalQuantity(UpdateSkuShareTotalQuantityCommand command, UpdateSkuShareTotalQuantityDetail detail) {
        OrderSkuShareOutOrderQuery query = OrderSkuShareOutOrderQuery.builder()
                .outOrderNo(command.getOutOrderNo())
                .outOrderType(command.getOutOrderType().getType())
                .warehouseNo(detail.getWarehouseNo())
                .transferOutSku(detail.getSharedSku())
                .status(OrderSkuShareStatus.NORMAL.getStatus())
                .build();
        List<OrderSkuShare> orderSkuShares = orderSkuShareRepository.queryOrderSkuShare(query);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            return Lists.newArrayList();
        }
        for (OrderSkuShare orderSkuShare : orderSkuShares) {
            OrderSkuShareUpdateValue updateValue = new OrderSkuShareUpdateValue();
            updateValue.setUpdatedShareTotalQuantity(detail.getSharedTotalQuantity());
            orderSkuShare.setUpdateValue(updateValue);
            orderSkuShare.setRecordType(command.getRecordType().getCode());
            orderSkuShare.setRecordTypeDesc(command.getRecordType().getTypeName());
            orderSkuShare.setOperator(command.getOperator() != null ? command.getOperator() : WmsConstant.SYSTEM);
        }
        orderSkuShares.sort(Comparator.comparing(x -> x.getTransferOutSku() + "_" + x.getTransferInSku()));
        return orderSkuShares;
    }

    public List<OrderSkuShare> buildOrderSkuShareForClear(Integer warehouseNo) {
        List<OrderSkuShare> orderSkuShares = orderSkuShareRepository.queryOrderSkuShareRemainTransferQuantityGtZero(warehouseNo);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            return Lists.newArrayList();
        }
        for (OrderSkuShare orderSkuShare : orderSkuShares) {
            orderSkuShare.setRecordType(SkuShareRecordType.SKU_SHARE_CLEAR.getCode());
            orderSkuShare.setRecordTypeDesc(SkuShareRecordType.SKU_SHARE_CLEAR.getTypeName());
            orderSkuShare.setOperator(WmsConstant.SYSTEM);
        }
        orderSkuShares.sort(Comparator.comparing(x -> x.getTransferOutSku() + "_" + x.getTransferInSku()));
        return orderSkuShares;
    }

    public List<OrderSkuShare> buildOrderSkuShareForModifySkuShare(ModifySkuShareCommand command) {
        OrderSkuShareOutOrderQuery query = OrderSkuShareOutOrderQuery.builder()
                .outOrderNo(command.getOutOrderNo())
                .outOrderType(command.getOutOrderType().getType())
                .warehouseNo(command.getWarehouseNo())
                .transferOutSku(command.getTransferOutSku())
                .status(OrderSkuShareStatus.NORMAL.getStatus())
                .build();
        List<OrderSkuShare> orderSkuShares = orderSkuShareRepository.queryOrderSkuShare(query);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            return Lists.newArrayList();
        }
        Map<String, String> updatedSkuShareRateMap = command.getItemCommandList().stream().collect(Collectors.toMap(
                ModifySkuShareItemCommand::getSharedSku, ModifySkuShareItemCommand::getShareRate, (v1, v2) -> v1));
        for (OrderSkuShare orderSkuShare : orderSkuShares) {
            if (updatedSkuShareRateMap.get(orderSkuShare.getTransferInSku()) != null) {
                OrderSkuShareUpdateValue updateValue = new OrderSkuShareUpdateValue();
                updateValue.setUpdatedShareRate(updatedSkuShareRateMap.get(orderSkuShare.getTransferInSku()));
                orderSkuShare.setUpdateValue(updateValue);
                orderSkuShare.setRecordType(command.getRecordType().getCode());
                orderSkuShare.setRecordTypeDesc(command.getRecordType().getTypeName());
                if (command.getOperator() != null) {
                    orderSkuShare.setOperator(command.getOperator());
                } else if (command.getBizUserId() != null) {
                    orderSkuShare.setOperator(String.valueOf(command.getBizUserId()));
                } else {
                    orderSkuShare.setOperator(WmsConstant.SYSTEM);
                }
            }
        }
        orderSkuShares.sort(Comparator.comparing(x -> x.getTransferOutSku() + "_" + x.getTransferInSku()));
        return orderSkuShares;
    }

    private String buildRemark(SkuShareRate skuShareRate, SkuShareRate sharedSkuShareRate) {
        Map<String, Object> remark = Maps.newHashMap();
        if (sharedSkuShareRate != null) {
            remark.put("sharedSkuPeakSales", sharedSkuShareRate.getSevenDayPeakSales());
            remark.put("sharedSkuOnSale", sharedSkuShareRate.getOnSale());
            remark.put("sharedSkuQuantity", sharedSkuShareRate.getQuantity());
        }
        remark.put("skuPeakSales", skuShareRate.getSevenDayPeakSales());
        remark.put("skuOnSale", skuShareRate.getOnSale());
        remark.put("skuQuantity", skuShareRate.getQuantity());
        return JSON.toJSONString(remark);
    }

}
