package net.summerfarm.wms.application.skucode.converter;

import net.summerfarm.wms.api.inner.skucode.dto.SkuBatchCodeReqDTO;
import net.summerfarm.wms.domain.skucode.domainobject.SkuBatchCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SkuCodeConvert {
    SkuCodeConvert INSTANCE = Mappers.getMapper(SkuCodeConvert.class);

    SkuBatchCode convert(SkuBatchCodeReqDTO skuBatchCodeReqDTO);
}
