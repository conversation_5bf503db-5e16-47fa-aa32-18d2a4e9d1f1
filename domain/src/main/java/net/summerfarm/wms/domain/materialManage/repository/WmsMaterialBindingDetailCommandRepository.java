package net.summerfarm.wms.domain.materialManage.repository;

import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingDetailCommandParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialBindingDetailCommandRepository {

    WmsMaterialBindingDetailEntity insertSelective(WmsMaterialBindingDetailCommandParam param);

    int updateSelectiveById(WmsMaterialBindingDetailCommandParam param);

    int remove(Long id);

    void insertBatch(List<WmsMaterialBindingDetailCommandParam> wmsMaterialBindingDetailCommandParamList);

    void updateDeletedByBindingId(Long bindingId, String updater);
}