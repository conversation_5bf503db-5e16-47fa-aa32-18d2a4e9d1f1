package net.summerfarm.wms.application.instore.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.api.h5.instore.InBoundOrderCommandService;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderBatchCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderDetailReqDTO;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.application.instore.InBoundOrderOpFactory;
import net.summerfarm.wms.application.prove.converter.ProveConverte;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.batch.domainobject.query.BatchElement;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.batch.repository.ProduceBatchRepository;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.instore.domainobject.InBoundOrderDetail;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItem;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageReceivingStateEnum;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.mission.domainobject.query.ContainerRuleCheck;
import net.summerfarm.wms.domain.mission.enums.MissionTypeEnum;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.prove.domainobject.FindBatchProve;
import net.summerfarm.wms.domain.prove.domainobject.Prove;
import net.summerfarm.wms.domain.prove.repository.ProveQueryRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InBoundOrderCommandServiceImpl implements InBoundOrderCommandService {

    @Resource
    private InBoundOrderOpFactory inBoundOrderOpFactory;

    @Resource
    private StockStorageItemRepository stockStorageItemRepository;

    @Resource
    private ProveQueryRepository proveQueryRepository;

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;

    @Resource
    private InBoundOrderQueryRepository inBoundOrderQueryRepository;

    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;

    @Resource
    private MissionRepository missionRepository;

    @Resource
    private ProduceBatchRepository produceBatchRepository;
    @Resource
    private ProductRepository productRepository;

    @Override
    @OperatorAnnotation
    public void createInBoundOrder(InBoundOrderCommand command) {
        log.info("开始处理入库单:{}", JSON.toJSONString(command));
        Boolean open = warehouseConfigRepository.openCabinetManagement(command.getWarehouseNo().intValue());
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskStorage), "入库单创建失败，未查询到对应入库任务");
        command.setSystemSource(stockTaskStorage.getSystemSource());
        command.setOpenCabinet(open);
        if (Boolean.TRUE.equals(open)) {
            if (!StockTaskStorageReceivingStateEnum.CONFIRMED.getId().equals(stockTaskStorage.getReceivingState())) {
                throw new net.xianmu.common.exception.BizException("请先确认到货,记录到货时间");
            }
            // 特殊处理生产日期和效期望 越库入库类型
            fullProductDate(command, stockTaskStorage);

            // 校验容器，排除其他入库和期初入库
            if (!InBoundOrderEnum.OTHER_IN.getCode().equals(command.getType()) && !InBoundOrderEnum.INIT_IN.getCode().equals(command.getType())) {
                // 容器校验
                List<ContainerRuleCheck> checks = command.getOrderDetails().stream().map(item -> ContainerRuleCheck.builder()
                        .qualityDate(DateUtil.toLocalDate(item.getShelfLife()))
                        .produceTime(DateUtil.toLocalDate(item.getProduceAt()))
                        .containerNo(item.getReceivingContainer())
                        .sku(item.getSku())
                        .build()).collect(Collectors.toList());
                // 任务容器校验
                missionRepository.checkContainerNo(command.getWarehouseNo(), command.getStockStorageTaskId().toString(), MissionTypeEnum.IN_STORE_TASK.getCode(), command.getTenantId(), checks);
            }
            // 缺货入库和退货入库需要自己组装批次
            assembleBatchIfAfterSaleOrLack(command);
            inBoundOrderOpFactory.getService(command.getType()).inStoreForCabinetManagement(command);
        } else {
            inBoundOrderOpFactory.getService(command.getType()).inStore(command);
        }
    }

    /**
     * 填充生产日期和效期
     *
     * @param command          入库单创建命令
     * @param stockTaskStorage 入库任务信息
     */
    private void fullProductDate(InBoundOrderCommand command, StockTaskStorage stockTaskStorage) {
        if (OperationType.CROSS_WAREHOUSE_IN.getId() != stockTaskStorage.getType()) {
            return;
        }
        // 检查日期是否都有填写 填写一半的话报错
        List<InBoundOrderDetailReqDTO> orderDetails = command.getOrderDetails();
        // 收集没有填写生产日期和效期的明细
        Set<String> skuSet = Sets.newHashSet();
        for (InBoundOrderDetailReqDTO reqDetail : orderDetails) {
            // 填写了保质期 没有填写 生产日期
            if (Objects.isNull(reqDetail.getProduceAt()) && Objects.nonNull(reqDetail.getShelfLife())) {
                throw new net.xianmu.common.exception.BizException("生产日期不能为空");
            }
            // 填写了生产日期 没有填写 保质期
            if (Objects.isNull(reqDetail.getShelfLife()) && Objects.nonNull(reqDetail.getProduceAt())) {
                throw new net.xianmu.common.exception.BizException("保质期不能为空");
            }
            // 生产日期和保质期都没有填写
            if (Objects.isNull(reqDetail.getShelfLife()) && Objects.isNull(reqDetail.getProduceAt())) {
                skuSet.add(reqDetail.getSku());
            }
        }
        if (CollectionUtils.isEmpty(skuSet)) {
            return;
        }
        // 按照sku查询商品信息
        ArrayList<String> skuList = Lists.newArrayList(skuSet);
        Lists.partition(skuList, WmsConstant.PARTITION_SIZE).forEach(list -> {
            Set<String> skuSets = list.stream().collect(Collectors.toSet());
            List<Product> products = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                    .warehouseNo(stockTaskStorage.getInWarehouseNo().longValue()).skus(list).tenantId(stockTaskStorage.getTenantId()).build());
            Map<String, Product> productListMap = products.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));
            orderDetails.stream()
                    .filter(item -> skuSets.contains(item.getSku()))
                    .forEach(it -> {
                        if (Objects.isNull(it.getShelfLife()) && Objects.isNull(it.getProduceAt())) {
                            Product product = productListMap.get(it.getSku());
                            // 获取商品的有效期信息 单位为日
                            Integer effectiveTime = product.getEffectiveTimeForDay();
                            // 止保时常 取有效期的 1/3 不能整除加1
                            int qualityDateDiff = (effectiveTime / 3) + (effectiveTime % 3 == 0 ? 0 : 1);
                            int productionDateDiff = effectiveTime - qualityDateDiff;
                            // 生成日期
                            LocalDate productionDate = LocalDate.now().plusDays(-productionDateDiff);
                            // 有效日期
                            LocalDate qualityDate = LocalDate.now().plusDays(qualityDateDiff);
                            it.setProduceAt(DateUtil.toMill(productionDate));
                            it.setShelfLife(DateUtil.toMill(qualityDate));
                        }
                    });
        });

    }

    @Override
    @OperatorAnnotation
    public void batchCreateInBoundOrder(InBoundOrderBatchCommand inBoundOrderBatchCommand) {
        List<InBoundOrderDetailReqDTO> orderDetails = inBoundOrderBatchCommand.getOrderDetails();
        // key:sku,value:req
        Map<String, List<InBoundOrderDetailReqDTO>> reqMap = orderDetails.stream().collect(Collectors.groupingBy(InBoundOrderDetailReqDTO::getSku));
        // 需要生产入库单的sku
        Set<String> preSku = reqMap.keySet();
        // 数量状态校验
        List<StockStorageItem> stockStorageItems = check(inBoundOrderBatchCommand, orderDetails, preSku);

        Map<Long, List<StockStorageItem>> taskIdMap = stockStorageItems.stream()
                .filter(item -> preSku.contains(item.getSku()))
                .collect(Collectors.groupingBy(StockStorageItem::getStockTaskStorageId));
        List<String> batches = orderDetails.stream().map(InBoundOrderDetailReqDTO::getPurchaseNo).collect(Collectors.toList());
        List<Long> produceAts = orderDetails.stream().map(InBoundOrderDetailReqDTO::getProduceAt).collect(Collectors.toList());
        List<LocalDate> produceDates = Lists.newArrayList();
        produceAts.forEach(p -> {
            produceDates.add(DateUtil.toLocalDate(p));
        });

        // 查询最近的证明
        List<Prove> batchProves = proveQueryRepository.findBatchProvesProductionDate(FindBatchProve.builder()
                .batches(batches).skus(Lists.newArrayList(preSku)).productionDates(produceDates).build());
        Map<String, Prove> proveMap = batchProves.stream().collect(
                Collectors.toMap(item -> item.getProveBelong().getSku() + WmsConstant.SPLIT + item.getProveBelong().getPurchaseNo()
                        + WmsConstant.SPLIT + item.getProveBelong().getProduceAt(), Function.identity()));

        // global enterMap : key:batch(purchaseNo), value:已入数量
        HashMap<String, Integer> enteredNumMap = Maps.newHashMap();

        // 批量生产入库单
        taskIdMap.forEach((key, value) -> {
            // key :sku , value: item
            Map<String, StockStorageItem> itemMap = value.stream().collect(Collectors.toMap(StockStorageItem::getSku, Function.identity()));
            // skus need create inboundOrder of this task
            Set<String> acSkus = itemMap.keySet();
            // this task enterMap : key:sku, value:已入数量
            HashMap<String, Integer> enteredNumInTaskMap = Maps.newHashMap();
            // 创建入库单
            List<InBoundOrderDetailReqDTO> reqList = orderDetails.stream()
                    .filter(item -> acSkus.contains(item.getSku()))
                    .map(item -> {
                        StockStorageItem stockStorageItem = itemMap.get(item.getSku());
                        Prove prove = proveMap.getOrDefault(item.getSku() + WmsConstant.SPLIT + item.getPurchaseNo()
                                + WmsConstant.SPLIT + item.getProduceAt(), Prove.builder().build());
                        // 分配数量
                        // 这个任务中该sku已分配过的数量
                        Integer enteredNum = enteredNumInTaskMap.getOrDefault(item.getSku(), 0);
                        Integer globalPurchaseEnteredNum = enteredNumMap.getOrDefault(item.getPurchaseNo()
                                + WmsConstant.SPLIT + item.getProduceAt(), 0);
                        // 这个采购批次余下入库的数量
                        int remainEnterNum = item.getStockNum() - globalPurchaseEnteredNum;
                        // 该sku余下能分配的数量
                        int skuEnteredNum = stockStorageItem.getQuantity() - stockStorageItem.getActualQuantity() - enteredNum;
                        if (enteredNum.equals(stockStorageItem.getQuantity() - stockStorageItem.getActualQuantity()) ||
                                remainEnterNum <= 0) {
                            return null;
                        }
                        // 该批次本次可入数量
                        Integer stockNum = (Math.min(remainEnterNum, skuEnteredNum));
                        if (stockNum <= 0) {
                            return null;
                        }
                        enteredNumInTaskMap.put(item.getSku(), enteredNum + stockNum);
                        enteredNumMap.put(item.getPurchaseNo() + WmsConstant.SPLIT + item.getProduceAt(), stockNum + globalPurchaseEnteredNum);

                        return InBoundOrderDetailReqDTO.builder()
                                .temperature(item.getTemperature())
                                .sku(item.getSku())
                                .supplier(item.getSupplier())
                                .stockNum(stockNum)
                                .specification(item.getSpecification())
                                .shelfLife(item.getShelfLife())
                                .purchaseNo(item.getPurchaseNo())
                                .produceAt(item.getProduceAt())
                                .pdName(item.getPdName())
                                .packaging(item.getPackaging())
                                .prove(ProveConverte.INSTANCE.convertReq(prove))
                                .categoryType(stockStorageItem.getCategoryType())
                                .remark(item.getRemark())
                                .build();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 创建入库单
            createInBoundOrder(InBoundOrderCommand.builder()
                    .stockStorageTaskId(key)
                    .type(inBoundOrderBatchCommand.getType())
                    .operator(inBoundOrderBatchCommand.getOperator())
                    .userOperatorName(inBoundOrderBatchCommand.getUserOperatorName())
                    .warehouseNo(inBoundOrderBatchCommand.getWarehouseNo())
                    .bizUserId(inBoundOrderBatchCommand.getBizUserId())
                    .tenantId(inBoundOrderBatchCommand.getTenantId())
                    .orderDetails(reqList)
                    .build());
        });

    }

    public List<StockStorageItem> check(InBoundOrderBatchCommand inBoundOrderBatchCommand, List<InBoundOrderDetailReqDTO> orderDetails, Set<String> preSku) {
        // 校验仓库是同一个仓库
        List<StockTaskStorage> stockTaskStorages = stockTaskStorageQueryRepository.selectStockStorageBatch(inBoundOrderBatchCommand.getStockStorageTaskId());
        List<Integer> warehouseNos = stockTaskStorages.stream().map(StockTaskStorage::getInWarehouseNo).distinct().collect(Collectors.toList());
        if (warehouseNos.size() > 1 ||
                !warehouseNos.get(NumberUtils.INTEGER_ZERO).equals(inBoundOrderBatchCommand.getWarehouseNo().intValue())) {
            throw new BizException(ErrorCode.PARAM_ERROR.getCode(), "批量创建入库单需要在同一仓库下操作");
        }

        List<StockStorageItem> stockStorageItems =
                stockStorageItemRepository.batchStockStorageItem(inBoundOrderBatchCommand.getStockStorageTaskId());
        if (CollectionUtils.isEmpty(stockStorageItems)) {
            throw new BizException(ErrorCode.PARAM_ERROR.getCode(), "入库任务无明细");
        }

        // region 应入数量校验
        // 查询入库任务条目应入数量
        int needNum = stockStorageItems.stream()
                .filter(item -> preSku.contains(item.getSku()))
                .mapToInt(StockStorageItem::getQuantity)
                .sum();
        // 查询入库单详情实入数量
        List<InBoundOrderDetail> inboundDetails = inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(
                InBoundTaskId.builder()
                        .taskIds(inBoundOrderBatchCommand.getStockStorageTaskId())
                        .build());
        int receiveNum = NumberUtils.INTEGER_ZERO;
        if (CollectionUtils.isNotEmpty(inboundDetails)) {
            receiveNum = inboundDetails.stream()
                    .filter(item -> preSku.contains(item.getSku()))
                    .mapToInt(InBoundOrderDetail::getStockNum)
                    .sum();
        }
        // 本次要入库的数量
        int acNum = orderDetails.stream().mapToInt(InBoundOrderDetailReqDTO::getStockNum).sum();
        // 如果数量不等, 会报错
        if (needNum - receiveNum != acNum) {
            throw new BizException(ErrorCode.PARAM_ERROR.getCode(), "入库数量与任务应入数量和不一致");
        }
        // end region
        return stockStorageItems;
    }

    /**
     * 精细化仓库组装参数
     *
     * @param command c
     */
    public void assembleBatchIfAfterSaleOrLack(InBoundOrderCommand command) {
        // 缺货入库和退货入库需要自查批次
        if (!Sets.newHashSet(InBoundOrderEnum.LACK_GOODS_APPROVED.getCode(), InBoundOrderEnum.AFTER_SALE_IN_NEW.getCode(),
                InBoundOrderEnum.STOP_STORE_IN.getCode(), InBoundOrderEnum.RETURN_IN.getCode(), InBoundOrderEnum.STOP_STORE_IN.getCode(),
                InBoundOrderEnum.OUT_MORE_IN.getCode()).contains(command.getType())) {
            return;
        }
        List<String> querySku = Lists.newArrayList();
        List<Long> queryProduceDate = Lists.newArrayList();
        List<Long> queryQualtityDate = Lists.newArrayList();
        command.getOrderDetails().forEach(item -> {
            querySku.add(item.getSku());
            queryProduceDate.add(item.getProduceAt());
            queryQualtityDate.add(item.getShelfLife());
        });
        List<BatchElement> batchElements = produceBatchRepository.queryBatchElementBySkuAndDate(command.getWarehouseNo().intValue(),
                querySku, queryProduceDate, queryQualtityDate);
        Map<String, String> batchMap = batchElements.stream().collect(Collectors.toMap(BatchElement::assembleBatchWithOutWarehouseNoKey, BatchElement::getBatch, (o1, o2) -> o1));
        command.getOrderDetails().forEach(item -> {
            // 已存在批次不需要重新获取
            if (!StringUtils.isBlank(item.getPurchaseNo())) {
                return;
            }
            String batch = batchMap.get(item.assembleBatchKey());
            if (StringUtils.isEmpty(batch)) {
                throw new net.xianmu.common.exception.BizException("sku:" + item.getSku() +
                        "效期为:" + DateUtil.toLocalDate(item.getProduceAt()) + "-" + DateUtil.toLocalDate(item.getShelfLife()) + "查询不到批次，请人为选择");
            }
            item.setPurchaseNo(batch);
        });
    }
}
