package net.summerfarm.wms.domain.mission.domainobject.query;

import com.aliyun.odps.utils.StringUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.domain.mission.enums.MissionCarrierTypeEnum;

import java.time.LocalDate;

/**
 * 任务提交数量校验
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionSubmitCheck {
    /**
     * 来源载体编号（容器编号）
     */
    String sourceCarrierCode;

    /**
     * 来源载体类型
     *
     * @see MissionCarrierTypeEnum
     */
    Integer sourceCarrierType;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * sku
     */
    String sku;

    /**
     * 生产日期
     */
    LocalDate produceTime;

    /**
     * 保质期
     */
    LocalDate shelfLife;

    /**
     * 采购批次
     */
    String batchNo;

    /**
     * 执行数量
     */
    @Builder.Default
    Integer quantity = 0;

    /**
     * 异常数量
     */
    @Builder.Default
    Integer abnormalQuantity = 0;

    /**
     * 拆分类型
     */
    Integer splitType;

    /**
     * 拆分信息
     */
    String splitInfo;

    public String dupKey() {
        // 库位五要素,非库位四要素
        String carrier = MissionCarrierTypeEnum.CABINET.getCode().equals(sourceCarrierType) ? sourceCarrierCode : null;
        return sku + batchNo + produceTime + shelfLife + carrier + getSplitTypeForDp() + getSplitInfoForDp();
    }

    public String getSplitTypeForDp() {
        if (splitType == null) {
            return "";
        }
        return splitType.toString();
    }

    public String getSplitInfoForDp() {
        if (StringUtils.isBlank(splitInfo)) {
            return "";
        }
        return splitInfo.trim();
    }

    public Integer getExeQuantity() {
        return quantity + abnormalQuantity;
    }
}
