package net.summerfarm.wms.domain.processingtask.domainobject.param;

import java.time.LocalDateTime;
import java.math.BigDecimal;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-01-10 18:59:23
 * @version 1.0
 *
 */
@Data
public class WmsProcessingMaterialConfigCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 加工配置id
	 */
	private Long processingConfigId;

	/**
	 * 原料sku
	 */
	private String materialSkuCode;

	/**
	 * 原料sku名称
	 */
	private String materialSkuName;

	/**
	 * 原料重量
	 */
	private BigDecimal materialSkuWeight;

	/**
	 * 原料sku单位
	 */
	private String materialSkuUnit;

	/**
	 * 原料sku单位描述
	 */
	private String materialSkuUnitDesc;

	/**
	 * 原料sku比例数量
	 */
	private Integer materialSkuRatioNum;

	/**
	 * 成品sku
	 */
	private String productSkuCode;

	/**
	 * 成品sku名称
	 */
	private String productSkuName;

	/**
	 * 成品sku重量
	 */
	private BigDecimal productSkuWeight;

	/**
	 * 成品sku单位
	 */
	private String productSkuUnit;

	/**
	 * 成品sku单位描述
	 */
	private String productSkuUnitDesc;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 是否删除标识，0：否，1：是
	 */
	private Integer deleteFlag;

	

	
}