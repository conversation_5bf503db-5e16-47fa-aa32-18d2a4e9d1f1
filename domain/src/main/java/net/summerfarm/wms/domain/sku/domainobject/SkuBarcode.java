package net.summerfarm.wms.domain.sku.domainobject;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description sku条码DO
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class SkuBarcode implements Serializable {

    private static final long serialVersionUID = 8697961616983156742L;

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
}
