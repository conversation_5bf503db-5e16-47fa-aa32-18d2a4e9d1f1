package net.summerfarm.wms.manage.web.constants;

import java.text.MessageFormat;

/**
 * @author: dongcheng
 * @date: 2023/12/7
 */
public class RedisKeys {

    /**
     * 阻塞key前缀
     */
    private static final String SUMMER_FARM_BLOCKING_KEY = "summer_farm_manage_block";

    private static final MessageFormat KEY_FORMAT = new MessageFormat("lock:LockId:transfer:{0}");

    /**
     * 非阻塞key前缀
     */
    private static final String SUMMER_FARM_NON_BLOCKING_KEY = "summer_farm_manage_non_block";

    public static String buildTransferKey(String inSku, String outSku) {
        return buildRedisKey(SUMMER_FARM_NON_BLOCKING_KEY, inSku, outSku);
    }

    public static String buildTransferKeyLock(String inSku, String outSku) {
        String key = buildRedisKey(SUMMER_FARM_NON_BLOCKING_KEY, inSku, outSku);
        return KEY_FORMAT.format(new String[]{key});
    }

    public static String buildDingCallBackInfoKey() {
        return buildRedisKey(SUMMER_FARM_NON_BLOCKING_KEY, "dingCallBackInfo");
    }

    public static String buildTransferExKey() {
        return buildRedisKey(SUMMER_FARM_NON_BLOCKING_KEY, "transfer_exception");
    }


    // prefix 末尾不要有 ":"
    private static String buildRedisKey(String prefix, Object... params) {
        StringBuilder builder = new StringBuilder(prefix);
        for (Object param : params) {
            builder.append(":").append(param);
        }
        return builder.toString();
    }
}