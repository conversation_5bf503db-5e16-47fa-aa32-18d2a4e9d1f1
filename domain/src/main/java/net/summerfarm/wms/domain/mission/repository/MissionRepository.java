package net.summerfarm.wms.domain.mission.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.domain.mission.domainobject.*;
import net.summerfarm.wms.domain.mission.domainobject.query.*;
import net.summerfarm.wms.domain.mission.domainobject.queryres.*;
import net.summerfarm.wms.domain.mission.param.command.WmsSubmitDetailExtendCommandParam;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface MissionRepository {
    /*----------------------------------------command------------------------------------------*/

    /**
     * 任务保存
     *
     * @param mission m
     */
    void saveMission(Mission mission);

    /**
     * 批量任务保存
     *
     * @param mission m
     */
    void batchSaveMission(List<Mission> mission);

    /**
     * 任务变更
     *
     * @param mission m
     */
    void changeMission(Mission mission);

    /**
     * 变更任务状态
     * @param mission
     */
    void changeMissionState(Mission mission);

    /**
     * 任务明细变更
     *
     * @param missionDetail md
     */
    void changeMissionDetail(MissionDetail missionDetail);

    /**
     * 执行单元变更
     */
    void changeExeUnit(ExecuteUnit executeUnit);

    /**
     * 任务分配
     *
     * @param operators o
     */
    void missionAssignOperator(Mission mission, List<MissionOperator> operators);

    /**
     * 任务明细领取
     *
     * @param operators o
     */
    void missionDetailReceive(Mission mission, MissionDetail missionDetail, MissionOperator operators);

    /**
     * 任务变更
     *
     * @param operators o
     */
    void missionCancelOperator(Mission mission, List<MissionOperator> operators);

    /**
     * 保存提交明细
     *
     * @param mission
     * @param submitDetails          d
     * @param submitDetailExtendList de
     * @param abnormalSubmits        a
     */
    void saveSubmitDetail(Mission mission, List<SubmitDetail> submitDetails,
                          List<WmsSubmitDetailExtendCommandParam> submitDetailExtendList,
                          List<AbnormalSubmitDetail> abnormalSubmits);

    /**
     * 保存任务明细
     *
     * @param missionDetails m
     */
    void saveMissionDetail(List<MissionDetail> missionDetails);

    /**
     * 追加明细
     *
     * @param mission        m
     * @param missionDetails md
     */
    void appendMissionDetail(Mission mission, List<MissionDetail> missionDetails);

    /**
     * 保存执行单元
     *
     * @param executeUnits e
     */
    void saveExecuteUnit(List<ExecuteUnit> executeUnits);

    /**
     * 删除明细
     */
    void deleteMissionDetail(Long detailId);

    void deleteExecuteUnit(Long unitId);

    /**
     * 批量保存来源属性
     *
     * @param sourceProperties s
     */
    void saveSourceProperty(List<MissionSourceProperty> sourceProperties);

    /**
     * 保存任务日志
     *
     * @param missionLogs m
     */
    void saveMissionLog(List<MissionLog> missionLogs);

    /**
     * 保存任务时效
     *
     * @param missionAgeing ma
     */
    void saveMissionAgeing(MissionAgeing missionAgeing);


    /*----------------------------------------query------------------------------------------*/

    /**
     * 查询任务聚合根
     */
    Mission findMission(String missionNo);

    /**
     * 查询任务聚合根
     */
    List<Mission> findMissionByPMissionNo(String pMissionNo, Integer pMissionType);

    /**
     * 查询所有待提交明细
     *
     * @param missionNo 任务编号
     * @return 待提交明细
     */
    List<MissionDetail> findMissionDetailForStocktaking(String missionNo);

    /**
     * 查询sku明细
     * @param missionNo
     * @param skus
     * @return
     */
    List<MissionDetail> findMissionDetailForStocktakingUnfinishedSku(String missionNo, List<String> skus);

    /**
     * 条件查询任务详情
     * sql条件不排空
     *
     * @param findMissionDetail f
     * @return missionDetail
     */
    List<MissionDetail> findMissionDetailCd(FindMissionDetail findMissionDetail);

    /**
     * 根据id查询详情
     *
     * @param id id
     * @return detail
     */
    MissionDetail findMissionDetail(Long id);

    /**
     * 根据sku+库位查询库位存在or库位为null明细
     * 盘点专用
     *
     * @param sku       sku
     * @param cabinetNo 库位
     * @return 明细
     */
    MissionDetail findMissionDetailForStocktaking(String missionNo, String sku, String cabinetNo);

    /**
     * 校验明细是否完结
     *
     * @param id id
     * @return boolean
     */
    Boolean checkFinish(List<Long> id);

    /**
     * 查询任务下的执行单元
     *
     * @param missionNo m
     * @return e
     */
    List<ExecuteUnit> findExecuteUnit(String missionNo);

    /**
     * 批量查询执行单元
     *
     * @param missionNos m
     * @return s
     */
    List<ExecuteUnit> findExecuteUnits(List<String> missionNos);

    /**
     * 查询任务下的提交明细
     *
     * @param missionNo m
     * @return s
     */
    List<SubmitDetail> findSubmitDetail(String missionNo);

    /**
     * 查询任务下异常提交明细
     *
     * @param missionNo
     * @return
     */
    List<AbnormalSubmitDetail> findAbnormalSubmitDetail(String missionNo);

    /**
     * 查询任务下的操作人
     *
     * @param missionNo m
     * @return mo
     */
    List<MissionOperator> findMissionOperator(String missionNo);

    /**
     * 查询任务来源属性
     *
     * @param missionNo m
     * @return l
     */
    List<MissionSourceProperty> findMissionSourceProperty(String missionNo);

    /**
     * 查询任务来源属性
     *
     * @param sourceId m
     * @return l
     */
    List<MissionSourceProperty> findMissionSourcePropertyBySourceId(String sourceId, Integer sourceType, Integer missionType);

    /**
     * 查询任务来源属性
     *
     * @param missionNo 任务编号
     * @return 返回城配仓编号
     */
    Long findStoreNo(String missionNo);

    /**
     * 查询任务下的操作人
     *
     * @param findMissionOp m
     * @return mo
     */
    List<MissionOperator> findMissionOperator(FindMissionOp findMissionOp);

    /**
     * 查询任务下的日志
     *
     * @param findMissionLog m
     * @return ml
     */
    List<MissionLog> findMissionLog(FindMissionLog findMissionLog);

    /**
     * 根据单元编码（容器编号等）查询终态执行单元
     *
     * @param unitCodes u
     * @return e
     */
    List<ExecuteUnit> findFinalStateUnits(Long warehouseNo, List<String> unitCodes, Long tenantId);

    /**
     * 查询初始化的执行单元
     *
     * @param unitCodes
     * @return
     */
    List<ExecuteUnit> findInitOrExeUnits(Long warehouseNo, List<String> unitCodes, Long tenantId);

    /**
     * 查询任务编号通过执行单元
     *
     * @param warehouseNo 仓库号
     * @param unitCodes   载体编号
     * @param missionType 任务类型
     * @return 任务编号
     */
    String findMissionNoFromUnitByCodeAndType(Long warehouseNo, String unitCodes, Integer missionType);

    /**
     * 分页查询任务列表pc
     *
     * @param pageMission p
     * @return r
     */
    PageInfo<PageMissionRes> pageMissionPc(PageMission pageMission);

    /**
     * 分页查询任务列表pda
     *
     * @param pageMission p
     * @return r
     */
    PageInfo<PageMissionRes> pageMissionPda(PageMission pageMission);

    /**
     * 查询任务根据容器号和sku
     *
     * @param missionQueryByUnit m
     * @return r
     */
    PageMissionRes findMissionByContainerNoAndSku(MissionQueryByUnit missionQueryByUnit);

    /**
     * 查询各个状态的任务数量
     *
     * @param missionCountByState m
     * @return r
     */
    List<MissionStateCount> countMissionState(MissionCountByState missionCountByState);

    /**
     * 查询越库投线任务各个状态的任务数量
     *
     * @param missionCountByState m
     * @return r
     */
    List<MissionStateCount> countCrossWarehouseMissionState(MissionCountByState missionCountByState);

    /**
     * 查询各个状态的任务数量
     *
     * @param missionCountByState m
     * @return r
     */
    List<MissionStateCount> countPickMissionState(MissionCountByState missionCountByState);

    /**
     * pda查询任务明细
     *
     * @param findMissionItem f
     * @return r
     */
    List<MissionDetail> findMissionDetailForPda(FindMissionPdaItem findMissionItem);

    PageInfo<String> queryAllMissionNo(Integer pageNum, Integer pageSize, LocalDate time);

    PageInfo<String> queryAllUnitNo(Integer pageNum, Integer pageSize, LocalDate time);

    /**
     * 容器校验
     *
     * @param containerRuleChecks c
     */
    void checkContainerNo(Long warehouseNo, String pMissionNo, Integer pMissionType, Long tenantId, List<ContainerRuleCheck> containerRuleChecks);

    /**
     * 分页查询拣货任务
     *
     * @param pageMission 分页查询条件
     * @return 返回拣货任务pda列表
     */
    PageInfo<PagePickMissionPdaRes> pagePickTaskPda(PageMission pageMission);

    /**
     * 统计任务拣货数量进度
     *
     * @param missionNo 拣货任务编号
     * @return 返回拣货数量的进度
     */
    MissionDetail sumMissionPickRate(String missionNo);

    /**
     * 查询任务未完成的城配仓
     *
     * @param warehouseNo 查询条件
     * @param missionType 任务类型
     */
    List<Long> queryUnFinishStoreNoList(Long warehouseNo, Integer missionType);

    /**
     * 根据任务条件查询任务信息
     *
     * @param mission 任务查询条件
     * @return 返回任务的完整信息
     */
    List<Mission> findMission(Mission mission);

    /**
     * 查询投线任务列表
     *
     * @param pageMission 任务分页
     * @return 返回查询到的投线任务列表
     */
    PageInfo<PageMissionRes> pageCrossWarehouseMissionPc(PageMission pageMission);

    /**
     * 查询投线任务列表
     *
     * @param pageMission 任务分页
     * @return 返回查询到的投线任务列表
     */
    PageInfo<PageMissionRes> pageCrossWarehouseMissionPda(PageMission pageMission);

    /**
     * 根据任务编号查询任务总信息
     *
     * @param missionNoList 任务编号列表
     * @param skuList       sku列表
     * @return 返回任务的明细信息
     */
    List<MissionDetail> findDetailByMissionNoListAndSkuList(List<String> missionNoList, List<String> skuList);

    /**
     * 查询任务聚合根
     *
     * @param pMissionNoList 父任务编码列表
     * @param pMissionType   父任务类型
     * @param missionType    任务类型
     * @return 返回任务列表
     */
    List<Mission> findMissionByPMissionNoList(List<String> pMissionNoList, Integer pMissionType, Integer missionType);

    /**
     * 通过父任务号和容器编码查询任务明细列表
     *
     * @param pMissionNo   父任务编码
     * @param pMissionType 父任务类型
     * @param missionType  查询的任务类型
     * @param containerNo  容器编码
     * @return 返回任务信息列表
     */
    List<MissionDetail> findDetailByPMissionNoAndContainerNo(String pMissionNo, Integer pMissionType, Integer missionType, String containerNo);


    /**
     * pc盘点分页
     *
     * @param query q
     * @return page
     */
    PageInfo<PageStocktakingPCRes> pageStocktakingMissionPc(PageStocktakingPCQuery query);

    /**
     * 根据来源id查询对应的Mission
     *
     * @param sourceId
     * @return
     */
    List<Mission> findMissionBySourceId(String sourceId, Integer sourceType, Integer missionType);

    /**
     * 盘点任务列表pda
     *
     * @param query q
     * @return page
     */
    PageInfo<PageStocktakingPdaRes> pageStocktakingMissionPda(PageStocktakingSortQuery query);

    /**
     * 盘点pda明细行查询
     *
     * @param query
     * @return
     */
    PageInfo<PageStocktakingItemPdaRes> pageStocktakingItemPda(PageStocktakingSortQuery query);

    /**
     * 库位顺序排序查询
     *
     * @param query q
     * @return list
     */
    List<MissionDetail> listStocktakingItemSort(PageStocktakingSortQuery query);

    /**
     * 盘点库位排序，去除状态
     *
     * @param query
     * @return
     */
    List<MissionDetail> listStocktakingItemSortByCabinetCode(PageStocktakingSortQuery query);

    /**
     * 查询拣货未完成sku库位数量（动销盘点使用）
     *
     * @param warehouseNo
     * @param expectTime
     * @param bizTypes
     * @param zoneCodes
     * @return
     */
    Integer countPickNotFinishedForOnSaleStocktaking(Long warehouseNo, LocalDate expectTime, List<Integer> bizTypes, List<String> zoneCodes);

    /**
     * 查询拣货完成sku库位数据（动销盘点使用）
     *
     * @param warehouseNo
     * @param expectTime
     * @param bizTypes
     * @param zoneCode
     * @return
     */
    List<MissionSkuCabinet> findPickFinishedForOnSaleStocktaking(Long warehouseNo, LocalDate expectTime, List<Integer> bizTypes, String zoneCode);

    /**
     * 查询拣货完成库区（动销盘点使用）
     *
     * @param warehouseNo
     * @param expectTime
     * @param bizTypes
     * @param zoneCodes
     * @return
     */
    List<String> findPickFinishedZone(Long warehouseNo, LocalDate expectTime, List<Integer> bizTypes, List<String> zoneCodes);

    /**
     * 查询过程任务的任务面板 - 数量
     *
     * @param warehouseNo     仓库编号
     * @param statusList      任务状态数组
     * @param type            类型
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd   数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo,
                                                LocalDateTime createTimeStart,
                                                LocalDateTime createTimeEnd,
                                                List<Integer> statusList,
                                                Integer type
    );

    PageInfo<String> queryMissionPanelWindowPageData(Integer pageNum,
                                                     Integer pageSize,
                                                     Integer warehouseNo,
                                                     LocalDateTime createTimeStart,
                                                     LocalDateTime createTimeEnd,
                                                     List<Integer> statusList,
                                                     Integer type);

    PageInfo<String> queryMissionPanelWindowPageDataNotInTime(Integer pageNum,
                                                              Integer pageSize,
                                                              Integer warehouseNo,
                                                              LocalDateTime createTimeStart,
                                                              LocalDateTime createTimeEnd,
                                                              List<Integer> statusList,
                                                              Integer type);


    List<Integer> getSourceOrderType(String missionNo);
}
