package net.summerfarm.wms.api.h5.goods;

import net.summerfarm.wms.api.h5.goods.dto.req.BoardPositionDetailQuery;
import net.summerfarm.wms.api.h5.goods.dto.req.BoardPositionDownloadCommand;
import net.summerfarm.wms.api.h5.goods.dto.req.BoardPositionUpdateCommand;
import net.summerfarm.wms.api.h5.goods.dto.req.BoardPositionUploadCommand;
import net.summerfarm.wms.api.h5.goods.dto.res.BoardPositionDetailResDTO;

/**
 * 板位命令服务
 *
 * <AUTHOR>
 */
public interface BoardPositionQueryService {

    /**
     * 详情查询
     * @param query q
     * @return r
     */
    BoardPositionDetailResDTO queryBoardPositionDetail(BoardPositionDetailQuery query);
}
