package net.summerfarm.wms.manage.web.service.impl;

import net.summerfarm.wms.manage.model.domain.StockTaskOrderSku;
import net.summerfarm.wms.manage.model.domain.StockTaskOrderSkuDO;
import net.summerfarm.wms.manage.web.mapper.manage.StockTaskOrderSkuMapper;
import net.summerfarm.wms.manage.web.service.StockTaskOrderSkuService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
@Service
public class StockTaskOrderSkuServiceImpl implements StockTaskOrderSkuService {

    @Resource
    private StockTaskOrderSkuMapper stockTaskOrderSkuMapper;

    @Override
    public void insertStockTaskOrderSku(StockTaskOrderSku orderSku) {
        StockTaskOrderSkuDO record = this.convertStockTaskOrderSkuDO(orderSku);
        stockTaskOrderSkuMapper.insert(record);
    }

    @Override
    public void batchInsert(List<StockTaskOrderSku> orderSkuList) {
        if (CollectionUtils.isEmpty(orderSkuList)) {
            return;
        }
        // 分页批次插入数据
        List<List<StockTaskOrderSku>> stockTaskOrderSkuListList = com.google.common.collect.Lists.partition(orderSkuList, 1000);
        for (List<StockTaskOrderSku> stockTaskOrderSkuList : stockTaskOrderSkuListList) {
            List<StockTaskOrderSkuDO> orderSkuDOList = stockTaskOrderSkuList.stream()
                    .map(this::convertStockTaskOrderSkuDO)
                    .collect(Collectors.toList());
            stockTaskOrderSkuMapper.batchInsert(orderSkuDOList);
        }
    }

    private StockTaskOrderSkuDO convertStockTaskOrderSkuDO(StockTaskOrderSku orderSku) {
        return StockTaskOrderSkuDO.builder()
                .stockTaskId(orderSku.getStockTaskId())
                .outOrderNo(orderSku.getOutOrderNo())
                .sku(orderSku.getSku())
                .quantity(orderSku.getQuantity())
                .actualQuantity(orderSku.getActualQuantity())
                .abnormalQuantity(orderSku.getAbnormalQuantity())
                .creator(orderSku.getCreator())
                .operator(orderSku.getOperator())
                .gmtCreated(System.currentTimeMillis())
                .gmtModified(System.currentTimeMillis())
                .isDeleted(0)
                .lastVer(1)
                .build();
    }
}
