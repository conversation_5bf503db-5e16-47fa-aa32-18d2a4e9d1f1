package net.summerfarm.wms.domain.skushare.repository;

import net.summerfarm.wms.domain.skushare.domainobject.OrderSkuShare;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.OrderSkuShareOutOrderQuery;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.UpdateOrderSkuShare;
import net.summerfarm.wms.domain.skushare.param.query.OrderSkuShareQueryParam;

import java.util.List;

/**
 * 订单sku规格共享存储接口
 *
 * <AUTHOR>
 */
public interface OrderSkuShareRepository {
    List<OrderSkuShare> queryOrderSkuShare(OrderSkuShareOutOrderQuery query);

    void createOrderSkuShare(OrderSkuShare orderSkuShare);

    boolean updateOrderSkuShare(UpdateOrderSkuShare updateOrderSkuShare);

    List<OrderSkuShare> queryOrderSkuShareRemainTransferQuantityGtZero(Integer warehouseNo);

    List<OrderSkuShare> queryOrderSkuShareOfWarehouse(OrderSkuShareQueryParam queryParam);

}
