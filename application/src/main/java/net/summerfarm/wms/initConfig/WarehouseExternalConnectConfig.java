package net.summerfarm.wms.initConfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 开启外部对接仓库列表
 * @date 2024/7/30
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(prefix = "warehouse.external.connect", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class WarehouseExternalConnectConfig {


    private List<Integer> warehouseNos;

    /**
     * 仓库开启外部对接状态
     * @param warehouseNo
     * @return
     */
    public Integer openStatus(Integer warehouseNo) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return 0;
        }
        if (warehouseNos.contains(warehouseNo)) {
            return 1;
        }
        return 0;
    }

    /**
     * 仓库是否已开启外部对接
     * @param warehouseNo
     * @return
     */
    public Boolean isOpen(Integer warehouseNo) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return false;
        }
        return warehouseNos.contains(warehouseNo);
    }
}
