package net.summerfarm.wms.infrastructure.repository.safeinventorylock.impl;

import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockCommandRepository;
import net.summerfarm.wms.infrastructure.dao.safeinventorylock.WmsSafeInventoryLockMapper;
import net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock;
import net.summerfarm.wms.infrastructure.repository.safeinventorylock.converter.WmsSafeInventoryLockConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 安全库存锁定表命令仓储实现
 * <AUTHOR>
 * @date 2025-07-30
 */
@Repository
public class WmsSafeInventoryLockCommandRepositoryImpl implements WmsSafeInventoryLockCommandRepository {

    @Autowired
    private WmsSafeInventoryLockMapper wmsSafeInventoryLockMapper;

    @Override
    public WmsSafeInventoryLockEntity insertSelective(WmsSafeInventoryLockCommandParam param) {
        WmsSafeInventoryLock wmsSafeInventoryLock = WmsSafeInventoryLockConverter.toWmsSafeInventoryLock(param);
        wmsSafeInventoryLockMapper.insertSelective(wmsSafeInventoryLock);
        return WmsSafeInventoryLockConverter.toWmsSafeInventoryLockEntity(wmsSafeInventoryLock);
    }

    @Override
    public int updateSelectiveById(WmsSafeInventoryLockCommandParam param) {
        WmsSafeInventoryLock wmsSafeInventoryLock = WmsSafeInventoryLockConverter.toWmsSafeInventoryLock(param);
        return wmsSafeInventoryLockMapper.updateSelectiveById(wmsSafeInventoryLock);
    }

    @Override
    public int remove(Long id) {
        return wmsSafeInventoryLockMapper.remove(id);
    }

    @Override
    public int updateLockStatusByLockNo(Long id, Integer lockStatus) {
        return wmsSafeInventoryLockMapper.updateLockStatusById(id, lockStatus);
    }

    @Override
    public int updateLockQuantityById(Long id, Integer unlockQuantity, String updateOperator, Integer oldLockQuantity) {
        int i = wmsSafeInventoryLockMapper.unlockQuantityById(id, unlockQuantity, updateOperator, oldLockQuantity);
        if (i <= 0) {
            throw new RuntimeException("并发更新释放失败，请重试");
        }
        return i;
    }
}
