package net.summerfarm.wms.application.inventory.provider.converter.cabinetInventory;

import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryReleaseReq;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryReleaseReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface CabinetInventoryReleaseReqConverter {

    CabinetInventoryReleaseReqConverter INSTANCE = Mappers.getMapper(CabinetInventoryReleaseReqConverter.class);

    CabinetInventoryReleaseReq convert(CabinetInventoryReleaseReqDTO reqDTO);
}
