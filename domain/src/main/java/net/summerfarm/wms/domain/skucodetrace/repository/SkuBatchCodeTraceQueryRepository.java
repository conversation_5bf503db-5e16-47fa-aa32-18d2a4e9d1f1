package net.summerfarm.wms.domain.skucodetrace.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceQueryParam;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 溯源码查询类<br/>
 * date: 2024/8/9 10:36<br/>
 *
 * <AUTHOR> />
 */
public interface SkuBatchCodeTraceQueryRepository {

    /**
     * 分页查询
     * @param param
     * @return
     */
    PageInfo<SkuBatchCodeTraceEntity> getPage(SkuBatchCodeTraceQueryParam param);


    /**
     * 查询sku批次溯源码
     * @param skuBatchTraceCode
     * @return
     */
    SkuBatchCodeTraceEntity findBySkuBatchTraceCode(String skuBatchTraceCode);

    /**
     * 查询sku批次溯源码
     * @param purchaseNo
     * @return
     */
    List<SkuBatchCodeTraceEntity> findByPurchaseNo(String purchaseNo);

    /**
     * 查询sku批次溯源码
     *
     * @param orderNos
     * @param deliveryDate
     * @param contactId
     * @return
     */
    List<SkuBatchCodeTraceEntity> findByOrderNos(List<String> orderNos, LocalDate deliveryDate, Long contactId);

    /**
     * 根据配送日期+状态 查询sku批次溯源码
     *
     * @param deliveryTime 配送日期
     * @param state 状态
     * @return 结果
     */
    List<SkuBatchCodeTraceEntity> findListByDeliveryDateAndState(LocalDate deliveryTime, Integer state);

    /**
     * 根据入库任务ID查询批次溯源码
     * @param stockTaskStorageId 入库任务ID
     * @return 结果
     */
    List<SkuBatchCodeTraceEntity> findByStockTaskStorageId(Long stockTaskStorageId);

    /**
     * 根据查询参数查询
     * @param param 参数
     * @return 结果
     */
    List<SkuBatchCodeTraceEntity> findList(SkuBatchCodeTraceQueryParam param);

    /**
     * 获取最近一次商户配送时间
     * @param merchantIds
     * @param deliveryTime
     * @return
     */
    List<SkuBatchCodeTraceEntity> findDeliveryTimeRecentlyByMerchantId(List<String> merchantIds, LocalDate deliveryTime);

    /**
     * 获取第一次商户配送时间
     * @param merchantIds
     * @return
     */
    List<SkuBatchCodeTraceEntity> findDeliveryTimeFirstByMerchantId(List<String> merchantIds);

    /**
     * 根据批次溯码批量查询
     * @param skuBatchTraceCodeList 溯源码集合
     * @return 结果
     */
    List<SkuBatchCodeTraceEntity> findListBySkuBatchTraceCode(List<String> skuBatchTraceCodeList);
}
