package net.summerfarm.wms.manage.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.wms.common.dto.SaasSku;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.manage.model.domain.StockTaskItem;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskVO implements SaasSku {

    private Integer id;

    private String taskNo;

    @NotNull(groups = {Add.class}, message = "areaNo.not.null")
    private Integer areaNo;

    /**
     * 仓库名称
     */
    private String areaName;

    /**
     * 出库类型
     * 62-越库出库
     */
    private Integer type;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectTime;

    /**
     * 出入库进展: 0待入(出)库 1部分入(出)库 2已入(出)库
     */
    private Integer state;

    private Integer adminId;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addtime;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 出库仓
     */
    private Integer outStoreNo;

    /**
     * 出库性质,0普通 1越库
     */
    private Integer outType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点维度：0、SKU 1、类目 2、批次 3、货位,转换维度：0、库存转换 1、降级转换
     */
    private Integer dimension;

    /**
     * 入库进度:0.全未入库1.部分入库2.完全入库
     */
    private Integer processState;

    private String mismatchReason;

    /**
     * 任务类型 1、退货 2、拒收 3、拦截
     */
    private Integer taskType;

    /**
     * 类目名称 鲜果/非鲜果
     */
    private String category;

    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 最后修改人admin_id
     */
    private String updater;

    /**
     * 标志位
     */
    private Long optionFlag;

    /**
     * 租户ID
     **/
    private Long tenantId;

    /**
     * 盘点类型：1全盘、2循环盘点
     */
    private Integer stockTakingType;

    /**
     * 发起人
     */
    private String adminName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 调入仓名称
     */
    private String inStoreName;


    /**
     * 调出仓名称
     */
    private String outStoreName;

    /**
     * 新任务id
     */
    private Integer stockStorageId;

    private Integer mailPushState;

    private Long saasSkuId;

    private String sku;


    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    private Long damageStockTaskId;

    /**
     * 仓租户ID
     */
    private Long warehouseTenantId;

    /**
     * 供应商名称
     */
    private String supplierName;

    private List<StockTaskItem> stockTaskItems;

    private List<StockTaskItemVO> stockTaskItemVOS;

    private String companyName;

    /**
     * 入库仓租户ID
     */
    private Long inWarehouseTenantId;
    /**
     * 入库仓仓库服务商
     */
    private String inWarehouseCompanyName;

    /**
     * 出库类型模式 0默认 1-POP出库
     */
    private Integer outboundCategory;

    /**
     * 外部仓
     */
    private String externalWarehouseNo;
}
