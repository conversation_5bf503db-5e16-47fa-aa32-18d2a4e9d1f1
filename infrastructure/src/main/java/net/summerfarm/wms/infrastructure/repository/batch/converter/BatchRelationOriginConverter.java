package net.summerfarm.wms.infrastructure.repository.batch.converter;

import net.summerfarm.wms.domain.batch.entity.BatchRelationOriginEntity;
import net.summerfarm.wms.domain.batch.param.command.BatchRelationOriginCommandParam;
import net.summerfarm.wms.infrastructure.dao.batch.dataobject.BatchRelationOriginDO;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-07-22 17:14:42
 * @version 1.0
 *
 */
public class BatchRelationOriginConverter {

    private BatchRelationOriginConverter() {
        // 无需实现
    }




    public static List<BatchRelationOriginEntity> toBatchRelationOriginEntityList(List<BatchRelationOriginDO> batchRelationOriginList) {
        if (batchRelationOriginList == null) {
            return Collections.emptyList();
        }
        List<BatchRelationOriginEntity> batchRelationOriginEntityList = new ArrayList<>();
        for (BatchRelationOriginDO batchRelationOrigin : batchRelationOriginList) {
            batchRelationOriginEntityList.add(toBatchRelationOriginEntity(batchRelationOrigin));
        }
        return batchRelationOriginEntityList;
}


    public static BatchRelationOriginEntity toBatchRelationOriginEntity(BatchRelationOriginDO batchRelationOrigin) {
        if (batchRelationOrigin == null) {
             return null;
        }
        BatchRelationOriginEntity batchRelationOriginEntity = new BatchRelationOriginEntity();
        batchRelationOriginEntity.setId(batchRelationOrigin.getId());
        batchRelationOriginEntity.setCreateTime(batchRelationOrigin.getCreateTime());
        batchRelationOriginEntity.setUpdateTime(batchRelationOrigin.getUpdateTime());
        batchRelationOriginEntity.setTenantId(batchRelationOrigin.getTenantId());
        batchRelationOriginEntity.setCurrentBatchId(batchRelationOrigin.getCurrentBatchId());
        batchRelationOriginEntity.setSku(batchRelationOrigin.getSku());
        batchRelationOriginEntity.setWarehouseNo(batchRelationOrigin.getWarehouseNo());
        batchRelationOriginEntity.setBatchNo(batchRelationOrigin.getBatchNo());
        batchRelationOriginEntity.setOriginWarehouseNo(batchRelationOrigin.getOriginWarehouseNo());
        batchRelationOriginEntity.setOriginSku(batchRelationOrigin.getOriginSku());
        batchRelationOriginEntity.setOriginPurchaseNo(batchRelationOrigin.getOriginPurchaseNo());
        batchRelationOriginEntity.setOriginProductionDate(batchRelationOrigin.getOriginProductionDate());
        batchRelationOriginEntity.setOriginQualityDate(batchRelationOrigin.getOriginQualityDate());
        batchRelationOriginEntity.setOriginSupplierId(batchRelationOrigin.getOriginSupplierId());
        batchRelationOriginEntity.setOriginBatchId(batchRelationOrigin.getOriginBatchId());
        batchRelationOriginEntity.setRatio(batchRelationOrigin.getRatio());
        batchRelationOriginEntity.setOriginCost(batchRelationOrigin.getOriginCost());
        return batchRelationOriginEntity;
    }

    public static BatchRelationOriginDO toBatchRelationOrigin(BatchRelationOriginCommandParam param) {
        if (param == null) {
            return null;
        }
        BatchRelationOriginDO batchRelationOrigin = new BatchRelationOriginDO();
        batchRelationOrigin.setId(param.getId());
        batchRelationOrigin.setCreateTime(param.getCreateTime());
        batchRelationOrigin.setUpdateTime(param.getUpdateTime());
        batchRelationOrigin.setTenantId(param.getTenantId());
        batchRelationOrigin.setCurrentBatchId(param.getCurrentBatchId());
        batchRelationOrigin.setSku(param.getSku());
        batchRelationOrigin.setWarehouseNo(param.getWarehouseNo());
        batchRelationOrigin.setBatchNo(param.getBatchNo());
        batchRelationOrigin.setOriginWarehouseNo(param.getOriginWarehouseNo());
        batchRelationOrigin.setOriginSku(param.getOriginSku());
        batchRelationOrigin.setOriginPurchaseNo(param.getOriginPurchaseNo());
        batchRelationOrigin.setOriginProductionDate(param.getOriginProductionDate());
        batchRelationOrigin.setOriginQualityDate(param.getOriginQualityDate());
        batchRelationOrigin.setOriginSupplierId(param.getOriginSupplierId());
        batchRelationOrigin.setOriginBatchId(param.getOriginBatchId());
        batchRelationOrigin.setRatio(param.getRatio());
        batchRelationOrigin.setOriginCost(param.getOriginCost());
        return batchRelationOrigin;
    }
}
