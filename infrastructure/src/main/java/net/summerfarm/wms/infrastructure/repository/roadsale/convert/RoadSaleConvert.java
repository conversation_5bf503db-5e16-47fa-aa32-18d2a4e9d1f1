package net.summerfarm.wms.infrastructure.repository.roadsale.convert;

import net.summerfarm.wms.domain.roadsale.domainobject.RoadSale;
import net.summerfarm.wms.infrastructure.dao.roadsale.entity.InventoryRoadSaleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description
 * @Date 2023/9/8 18:47
 * @<AUTHOR>
 */
@Mapper
public interface RoadSaleConvert {

    RoadSaleConvert INSTANCE = Mappers.getMapper(RoadSaleConvert.class);

    RoadSale convert(InventoryRoadSaleDO inventoryRoadSaleDO);

    InventoryRoadSaleDO convert(RoadSale roadSale);

    List<RoadSale> convert(List<InventoryRoadSaleDO> inventoryRoadSaleDOS);

}
