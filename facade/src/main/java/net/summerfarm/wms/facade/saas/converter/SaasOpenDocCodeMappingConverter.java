package net.summerfarm.wms.facade.saas.converter;

import com.cosfo.manage.client.open.resp.OpenDocCodeMappingResp;
import net.summerfarm.wms.facade.saas.dto.SaasOpenDocCodeMapping;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
public class SaasOpenDocCodeMappingConverter {

    public static SaasOpenDocCodeMapping convert(OpenDocCodeMappingResp openDocCodeMappingResp) {
        SaasOpenDocCodeMapping saasOpenDocCodeMapping = new SaasOpenDocCodeMapping();
        if (null == openDocCodeMappingResp) {
            return null;
        }
        saasOpenDocCodeMapping.setOutCode(openDocCodeMappingResp.getOutCode());
        saasOpenDocCodeMapping.setDocCode(openDocCodeMappingResp.getDocCode());
        saasOpenDocCodeMapping.setDocType(openDocCodeMappingResp.getDocType());
        saasOpenDocCodeMapping.setOpenType(openDocCodeMappingResp.getOpenType());
        saasOpenDocCodeMapping.setReq(openDocCodeMappingResp.getReq());
        saasOpenDocCodeMapping.setResp(openDocCodeMappingResp.getResp());
        saasOpenDocCodeMapping.setCreateTime(openDocCodeMappingResp.getCreateTime());
        saasOpenDocCodeMapping.setUpdateTime(openDocCodeMappingResp.getUpdateTime());
        saasOpenDocCodeMapping.setTenantId(openDocCodeMappingResp.getTenantId());
        return saasOpenDocCodeMapping;
    }

}
