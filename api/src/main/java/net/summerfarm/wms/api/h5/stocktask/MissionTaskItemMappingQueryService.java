package net.summerfarm.wms.api.h5.stocktask;

import net.summerfarm.wms.api.h5.stocktask.dto.req.MissionTaskItemMappingSkuQuery;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingCountResp;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingForPCResp;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.MissionTaskItemMappingResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/5
 */
public interface MissionTaskItemMappingQueryService {

    /**
     * 查询已完成拣货映射明细
     * @param missionTaskItemMappingSkuQuery
     * @return
     */
    MissionTaskItemMappingResp queryCompletePickedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    MissionTaskItemMappingForPCResp queryCompletePickedMissionTaskItemMappingForPC(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    /**
     * 查询未完成拣货映射明细
     * @param missionTaskItemMappingSkuQuery
     * @return
     */
    MissionTaskItemMappingResp queryNoPickedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    /**
     * 查询已确认出库并且存在数量差异明细
     * @param missionTaskItemMappingSkuQuery
     * @return
     */
    MissionTaskItemMappingResp queryDiffConfirmedMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    /**
     * 通过id集合查询
     * @param idList
     * @return
     */
    MissionTaskItemMappingResp queryMissionTaskItemMappingByIds(List<Long> idList);

    MissionTaskItemMappingResp queryDiffOutMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    MissionTaskItemMappingResp queryInitMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    MissionTaskItemMappingResp queryAllMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);

    /**
     * 统计任务关系映射列表
     * @param missionTaskItemMappingSkuQuery 查询条件
     * @return 返回统计任务关系映射列表
     */
    MissionTaskItemMappingCountResp countMissionTaskItemMapping(MissionTaskItemMappingSkuQuery missionTaskItemMappingSkuQuery);
}
