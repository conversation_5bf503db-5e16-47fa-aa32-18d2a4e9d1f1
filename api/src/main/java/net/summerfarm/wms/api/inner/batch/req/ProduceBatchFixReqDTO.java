package net.summerfarm.wms.api.inner.batch.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2022/10/18  12:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProduceBatchFixReqDTO {

    /**
     * 生产批次id
     */
    private Long id;

    /**
     * 成本批次差异操作数量
     */
    private Integer costBatchDiffQuantity;


    /**
     * 采购批次操作数量
     */
    private Integer produceBatchDiffQuantity;

    /**
     * 业务操作类型
     * link OperationType
     */
    private Integer operationType;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 操作人
     */
    private String operationName;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 成本价
     */
    private BigDecimal customCost;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

}
