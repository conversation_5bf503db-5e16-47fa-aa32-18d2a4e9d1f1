package net.summerfarm.wms.domain.inventory.handler.sorter;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.common.domainobject.Cabinet;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.handler.entity.CabinetBatchInventorySorterEntity;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class CabinetBatchInventorySorter {

    private static final Integer MAX_SEQUENCE = 99999999;

    public static void main(String[] args){
        Map<String, Cabinet> cabinetMap = new HashMap<>();

        Cabinet cabinet1 = new Cabinet();
        cabinet1.setCabinetCode("A01-02-01");
        cabinet1.setSequence(null);
        cabinet1.setPurpose(1);

        Cabinet cabinet2 = new Cabinet();
        cabinet2.setCabinetCode("A01-01-01");
        cabinet2.setSequence(null);
        cabinet2.setPurpose(3);

        Cabinet cabinet3 = new Cabinet();
        cabinet3.setCabinetCode("B01-01-03");
        cabinet3.setSequence(null);
        cabinet3.setPurpose(2);

        cabinetMap.put(cabinet1.getCabinetCode(), cabinet1);
        cabinetMap.put(cabinet2.getCabinetCode(), cabinet2);
        cabinetMap.put(cabinet3.getCabinetCode(), cabinet3);

        List<CabinetBatchInventory> cabinetBatchInventoryList = new ArrayList<>();

        CabinetBatchInventory cabinetBatchInventory1 = new CabinetBatchInventory();
        cabinetBatchInventory1.setCabinetCode("A01-02-01");
        cabinetBatchInventory1.setQualityDate(DateUtil.parseDateTimeByYMD("2023-05-01"));
        cabinetBatchInventory1.setQuantity(1);

        CabinetBatchInventory cabinetBatchInventory2 = new CabinetBatchInventory();
        cabinetBatchInventory2.setCabinetCode("A01-01-01");
        cabinetBatchInventory2.setQualityDate(DateUtil.parseDateTimeByYMD("2023-02-01"));
        cabinetBatchInventory2.setProduceDate(DateUtil.parseDateTimeByYMD("2023-02-01"));
        cabinetBatchInventory2.setQuantity(1);

        CabinetBatchInventory cabinetBatchInventory3 = new CabinetBatchInventory();
        cabinetBatchInventory3.setCabinetCode("B01-01-03");
        cabinetBatchInventory3.setQualityDate(DateUtil.parseDateTimeByYMD("2023-02-01"));
        cabinetBatchInventory3.setProduceDate(DateUtil.parseDateTimeByYMD("2023-03-01"));
        cabinetBatchInventory3.setQuantity(1);

        cabinetBatchInventoryList.add(cabinetBatchInventory1);
        cabinetBatchInventoryList.add(cabinetBatchInventory2);
        cabinetBatchInventoryList.add(cabinetBatchInventory3);

        List<CabinetBatchInventory> result = sortCabinetInventory(cabinetBatchInventoryList, cabinetMap);
        System.out.println(JSONObject.toJSONString(result));
    }

    /**
     * 库位库存排序
     *
     * @param cabinetBatchInventoryList
     * @param cabinetMap
     * @return
     */
    public static List<CabinetBatchInventory> sortCabinetInventory(List<CabinetBatchInventory> cabinetBatchInventoryList,
                                                                   Map<String, Cabinet> cabinetMap) {
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort((CabinetBatchInventory a1, CabinetBatchInventory a2) -> {
            Cabinet cabinet1 = cabinetMap == null ? null : cabinetMap.get(a1.getCabinetCode());
            Cabinet cabinet2 = cabinetMap == null ? null : cabinetMap.get(a2.getCabinetCode());
            Integer sequence1 = cabinet1 != null && cabinet1.getSequence() != null ? cabinet1.getSequence() : MAX_SEQUENCE;
            Integer sequence2 = cabinet2 != null && cabinet2.getSequence() != null ? cabinet2.getSequence() : MAX_SEQUENCE;
            Integer purpose1 = cabinet1 != null && cabinet1.getPurpose() != null ? cabinet1.getPurpose() : MAX_SEQUENCE;
            Integer purpose2 = cabinet2 != null && cabinet2.getPurpose() != null ? cabinet2.getPurpose() : MAX_SEQUENCE;


            if (a1.getQualityDate() != null && a2.getQualityDate() != null) {
                Integer qualityDateCompare = a1.getQualityDate().compareTo(a2.getQualityDate());
                if (qualityDateCompare != 0) {
                    return qualityDateCompare;
                }
            }

            if (a1.getProduceDate() != null && a2.getProduceDate() != null) {
                Integer produceDateCompare = a1.getProduceDate().compareTo(a2.getProduceDate());
                if (produceDateCompare != 0) {
                    return produceDateCompare;
                }
            }

            Integer purposeCompare = purpose1.compareTo(purpose2);
            if (purposeCompare != 0) {
                return purposeCompare;
            }

            Integer sequenceCompare = sequence1.compareTo(sequence2);
            if (sequenceCompare != 0) {
                return sequenceCompare;
            }

            if (a1.getCabinetCode() != null && a2.getCabinetCode() != null) {
                Integer cabinetCodeCompare = a1.getCabinetCode().compareTo(a2.getCabinetCode());
                if (cabinetCodeCompare != 0) {
                    return cabinetCodeCompare;
                }
            }

            if (a1.getBatchNo() != null && a2.getBatchNo() != null) {
                Integer batchNoCompare = a1.getBatchNo().compareTo(a2.getBatchNo());
                if (batchNoCompare != 0) {
                    return batchNoCompare;
                }
            }

            if (a1.getId() != null && a2.getId() != null) {
                return a1.getId().compareTo(a2.getId());
            }

            return 0;
        });

        return cabinetBatchInventoryList;
    }

    public static List<CabinetBatchInventory> sortCabinetInventory(CabinetBatchInventorySorterEntity cabinetBatchInventorySorterEntity) {
        List<CabinetBatchInventory> cabinetBatchInventoryList = cabinetBatchInventorySorterEntity.getCabinetBatchInventoryList();
        Map<String, Cabinet> cabinetMap = cabinetBatchInventorySorterEntity.getCabinetMap();
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort((CabinetBatchInventory a1, CabinetBatchInventory a2) -> {
            Cabinet cabinet1 = cabinetMap == null ? null : cabinetMap.get(a1.getCabinetCode());
            Cabinet cabinet2 = cabinetMap == null ? null : cabinetMap.get(a2.getCabinetCode());
            Integer sequence1 = cabinet1 != null && cabinet1.getSequence() != null ? cabinet1.getSequence() : MAX_SEQUENCE;
            Integer sequence2 = cabinet2 != null && cabinet2.getSequence() != null ? cabinet2.getSequence() : MAX_SEQUENCE;
            Integer purpose1 = cabinet1 != null && cabinet1.getPurpose() != null ? cabinet1.getPurpose() : MAX_SEQUENCE;
            Integer purpose2 = cabinet2 != null && cabinet2.getPurpose() != null ? cabinet2.getPurpose() : MAX_SEQUENCE;


            if (a1.getQualityDate() != null && a2.getQualityDate() != null) {
                Integer qualityDateCompare = a1.getQualityDate().compareTo(a2.getQualityDate());
                if (qualityDateCompare != 0) {
                    return qualityDateCompare;
                }
            }

            if (a1.getProduceDate() != null && a2.getProduceDate() != null) {
                Integer produceDateCompare = a1.getProduceDate().compareTo(a2.getProduceDate());
                if (produceDateCompare != 0) {
                    return produceDateCompare;
                }
            }

            Integer purposeCompare = purpose1.compareTo(purpose2);
            if (purposeCompare != 0) {
                return purposeCompare;
            }

            Integer sequenceCompare = sequence1.compareTo(sequence2);
            if (sequenceCompare != 0) {
                return sequenceCompare;
            }

            if (a1.getCabinetCode() != null && a2.getCabinetCode() != null) {
                Integer cabinetCodeCompare = a1.getCabinetCode().compareTo(a2.getCabinetCode());
                if (cabinetCodeCompare != 0) {
                    return cabinetCodeCompare;
                }
            }

            if (a1.getBatchNo() != null && a2.getBatchNo() != null) {
                Integer batchNoCompare = a1.getBatchNo().compareTo(a2.getBatchNo());
                if (batchNoCompare != 0) {
                    return batchNoCompare;
                }
            }

            if (a1.getId() != null && a2.getId() != null) {
                return a1.getId().compareTo(a2.getId());
            }

            return 0;
        });

        return cabinetBatchInventoryList;
    }

    /**
     * 库位库存排序
     *
     * @param cabinetBatchInventoryList
     * @return
     */
    public static List<CabinetBatchInventory> sortCabinetInventoryNormal(CabinetBatchInventorySorterEntity cabinetBatchInventorySorterEntity) {
        List<CabinetBatchInventory> cabinetBatchInventoryList = cabinetBatchInventorySorterEntity.getCabinetBatchInventoryList();
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort(Comparator.comparing(CabinetBatchInventory::getQualityDate)
                .thenComparing(CabinetBatchInventory::getProduceDate)
                .thenComparing(CabinetBatchInventory::getCabinetCode)
                .thenComparing(CabinetBatchInventory::getBatchNo)
                .thenComparing(CabinetBatchInventory::getId))
        ;

        return cabinetBatchInventoryList;
    }

    public static List<CabinetBatchInventory> sortCabinetInventory(List<CabinetBatchInventory> cabinetBatchInventoryList ) {
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort(Comparator.comparing(CabinetBatchInventory::getQualityDate)
                .thenComparing(CabinetBatchInventory::getProduceDate)
                .thenComparing(CabinetBatchInventory::getCabinetCode)
                .thenComparing(CabinetBatchInventory::getBatchNo)
                .thenComparing(CabinetBatchInventory::getId))
        ;

        return cabinetBatchInventoryList;
    }

    public static List<CabinetBatchInventory> sortCabinetInventoryByBatchNo(List<CabinetBatchInventory> cabinetBatchInventoryList) {
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort(Comparator.comparing(CabinetBatchInventory::getQualityDate)
                .thenComparing(CabinetBatchInventory::getProduceDate)
                .thenComparing(CabinetBatchInventory::getBatchNo)
                .thenComparing(CabinetBatchInventory::getCabinetCode)
                .thenComparing(CabinetBatchInventory::getId))
        ;

        return cabinetBatchInventoryList;
    }

    public static List<CabinetBatchInventory> sortCabinetInventoryByBatchNo(CabinetBatchInventorySorterEntity cabinetBatchInventorySorterEntity) {
        List<CabinetBatchInventory> cabinetBatchInventoryList = cabinetBatchInventorySorterEntity.getCabinetBatchInventoryList();
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort(Comparator.comparing(CabinetBatchInventory::getQualityDate)
                .thenComparing(CabinetBatchInventory::getProduceDate)
                .thenComparing(CabinetBatchInventory::getBatchNo)
                .thenComparing(CabinetBatchInventory::getCabinetCode)
                .thenComparing(CabinetBatchInventory::getId))
        ;

        return cabinetBatchInventoryList;
    }


    /**
     * 库位库存排序
     *
     * @param cabinetBatchInventoryList
     * @return
     */
    public static List<CabinetBatchInventory> sortCabinetInventoryReversed(List<CabinetBatchInventory> cabinetBatchInventoryList) {
        if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
            return cabinetBatchInventoryList;
        }

        cabinetBatchInventoryList.sort(Comparator.comparing(CabinetBatchInventory::getQualityDate).reversed()
                .thenComparing(CabinetBatchInventory::getProduceDate).reversed()
                .thenComparing(CabinetBatchInventory::getCabinetCode).reversed()
                .thenComparing(CabinetBatchInventory::getBatchNo).reversed()
                .thenComparing(CabinetBatchInventory::getId).reversed());

        return cabinetBatchInventoryList;
    }
}
