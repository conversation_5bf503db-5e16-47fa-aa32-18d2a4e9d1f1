package net.summerfarm.wms.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.api.h5.goods.enums.SkuTypeEnums;
import net.summerfarm.wms.common.dto.SaasSku;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.manage.model.domain.StockTaskItem;
import net.summerfarm.wms.manage.model.domain.StockTaskItemCabinetOccupyDO;
import net.summerfarm.wms.manage.web.enums.SkuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskItemVO extends StockTaskItem implements SaasSku, Serializable {

    private static final long serialVersionUID = -5189456643916631882L;

    private String pdName;

    private String weight;

    private String pack;

    private String unit;

    private Integer status;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    private Integer categoryId;

    private Integer pdId;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime endTime;

    private Integer areaNo;

    private Integer outStoreNo;

    private List<StockTaskItemDetailVO> stockTaskItemDetailVOS;

    private Integer taskState;

    private LocalDateTime finishTime;

    private Integer storageLocation;

    @ApiModelProperty(value = "sku类型 0 自营 1 代仓")
    private Integer skuType;

    /**
     * sku出库数量总计
     */
    private Integer detailQuantity;

    private Integer categoryType;

    /**
     * 储存区域
     */
    private String storageArea;

    /**
     * 包装
     */
    private String packing;

    /**
     * 一级类目，鲜果、非鲜果
     */
    private String firstLevelCategory;

    /**
     * 二级类目
     */
    private String secondLevelCategory;

    /**
     * 商品性质
     */
    private Integer extType;

    private Integer characters;

    private String supplier;

    @ApiModelProperty(value = "重量")
    private BigDecimal weightNum;

    private String category;

    private String volume;

    private Long saasSkuId;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 库位ID
     */
    private Long cabinetId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /**
     * 采购退货库位锁定明细
     */
    @ApiModelProperty(value = "采购退货库位锁定明细")
    private List<StockTaskItemCabinetOccupyDO> cabinetOccupyDOList;

    /**
     * 国产还是进口
     */
    private Integer isDomestic;

    @ApiModelProperty(value = "实际生成拣货数量")
    private Integer actualGenPickQuantity;

    /**
     * 出库数量
     */
    private Integer outQuantity;

    /**
     * 其他属性
     */
    public String getOtherAttribute() {
        // 自营还是代仓还是POP
        String skuTypeStr = SkuTypeEnums.getNameByType(skuType);
        // 包装方式 packing
        // 国产还是进口
        String origin = Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic) ? "进口" : "国产";
        return StringUtils.joinWith("/", packing, skuTypeStr, origin);
    }

    public Integer getActualGenPickQuantity() {
        return actualGenPickQuantity == null ? 0 : actualGenPickQuantity;
    }

    public Integer getOutQuantity() {
        return getQuantity();
    }

    public Integer compareCategoryType() {
        if (Objects.isNull(categoryType)) {
            return 0;
        }
        return categoryType;
    }
}
