package net.summerfarm.wms.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.skushare.service.SkuShareCommandService;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.enums.WarehouseConfigKeyEnum;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.config.domainobject.WarehouseConfig;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 清理订单sku规格共享的定时任务
 *
 * @<AUTHOR>
 */
@Slf4j
@Component
public class ClearOrderSkuShareInventoryJob extends XianMuJavaProcessor {

    private static final String STATUS_PROCESSING = "processing";
    private static final String STATUS_FINISH = "finish";

    @Autowired
    private WarehouseConfigRepository warehouseConfigRepository;

    @Autowired
    private SkuShareCommandService skuShareService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("开始执行订单sku规格共享的定时清理任务, params:{}", context.getInstanceParameters());
        List<Integer> warehouseNos = Lists.newArrayList();
        String params = context.getInstanceParameters();
        if (StringUtils.isNotEmpty(params)) {
            // 清理指定的仓库，可用于还未到配置时间的仓库清理以及清理完成后再次清理等特殊场景
            warehouseNos = JSON.parseArray(params, Integer.class);
        }
        // 如果任务参数里有指定，则循环清理指定的仓库
        if (CollectionUtils.isNotEmpty(warehouseNos)) {
            warehouseNos.forEach(skuShareService::clearSkuShare);
            return new ProcessResult(true);
        }
        // 查询订单sku规格共享的定时清理配置
        WarehouseConfig warehouseConfig = new WarehouseConfig();
        warehouseConfig.setConfigKey(WarehouseConfigKeyEnum.CLEAR_ORDER_SKU_SHARE_TIME.getCode());
        List<WarehouseConfig> warehouseConfigList = warehouseConfigRepository.list(warehouseConfig);
        if (CollectionUtils.isEmpty(warehouseConfigList)) {
            log.info("没有订单sku规格共享的定时清理配置，任务结束, 配置查询参数:{}", JSON.toJSONString(warehouseConfig));
            return new ProcessResult(true);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String day = LocalDate.now().format(formatter);
        boolean allSuccess = true;
        for (WarehouseConfig config : warehouseConfigList) {
            // 忽略还未到执行时间的配置
            if (LocalTime.now().isBefore(LocalTime.parse(config.getConfigValue()))) {
                log.info("还未到仓库配置的订单sku规格共享清理时间，warehouseNo:{}, 配置的清理时间:{}", config.getWarehouseNo(), config.getConfigValue());
                continue;
            }
            // 清理订单sku规格共享的步骤：先通过key值判断当前仓库是否正在清理或者已经完成清理，如果没有则将key值设置为processing再进行清理，
            // 清理成功则将key值设置为finish，清理失败则删除key等待下次定时任务重试
            String key = RedisKeys.buildClearOrderSkuShareExKey(day, config.getWarehouseNo());
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                log.info("正在清理或者已完成清理该仓库的订单sku规格共享，warehouseNo:{}, 清理状态:{}", config.getWarehouseNo(), value);
                continue;
            }
            Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(key, STATUS_PROCESSING, 10L, TimeUnit.MINUTES);
            if (!Boolean.TRUE.equals(setIfAbsent)) {
                log.info("清理订单sku规格共享的任务设置状态失败，可能有别的任务正在清理，请稍后重试，warehouseNo:{}", config.getWarehouseNo());
                continue;
            }
            try {
                skuShareService.clearSkuShare(config.getWarehouseNo());
                redisTemplate.opsForValue().set(key, STATUS_FINISH, 24L, TimeUnit.HOURS);
            } catch (Exception ex) {
                log.error("清理订单sku规格共享失败", ex);
                redisTemplate.delete(key);
                allSuccess = false;
            }
        }

        log.info("结束执行订单sku规格共享的定时清理任务, allSuccess:{}", allSuccess);
        return new ProcessResult(allSuccess);
    }
}
