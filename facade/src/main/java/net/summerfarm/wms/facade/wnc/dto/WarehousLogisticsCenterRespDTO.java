package net.summerfarm.wms.facade.wnc.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/10/19 14:59<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehousLogisticsCenterRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 名称
     */
    private String storeName;

    /**
     * 配送中心状态：0、失效 1、有效
     */
    private Integer status;

    /**
     * 物流中心负责人
     */
    private Integer manageAdminId;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 是否支持提前截单：0、false 1、true
     */
    private Integer closeOrderType;

    /**
     * 同步库存使用仓编号
     */
    private Integer originStoreNo;

    /**
     * 销售出库完成时间
     */
    private LocalDateTime sotFinishTime;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 修改人adminId
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 截单时间
     */
    private String closeTime;

    /**
     * 更新截单时间
     */
    private String updateCloseTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 区域
     */
    private String region;

    /**
     * 城配仓照片
     */
    private String storePic;
}
