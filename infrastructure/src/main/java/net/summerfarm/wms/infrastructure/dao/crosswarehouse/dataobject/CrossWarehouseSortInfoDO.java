package net.summerfarm.wms.infrastructure.dao.crosswarehouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/10/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrossWarehouseSortInfoDO {

    private Long id;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * ofc传输过程中的编号
     */
    private String psoNo;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 总需分拣的数量
     */
    private Integer totalQuantity;

    /**
     * 生成的分拣数量
     */
    private Integer generateQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 供应商
     */
    private Long supplierId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * 初始数量
     */
    private Integer initQuantity;

    /**
     * 上架数量
     */
    private Integer shelveQuantity;


}
