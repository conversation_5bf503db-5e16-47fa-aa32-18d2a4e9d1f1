package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskProcess implements Serializable {

    private static final long serialVersionUID = 8173532407548764411L;

    private Integer id;

    @ApiModelProperty(value = "任务编号")
    private Integer stockTaskId;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addtime;

    @ApiModelProperty(value = "记录人")
    private String recorder;

    @ApiModelProperty(value = "是否货检:0否、1是")
    private Integer isInspect;

    private Long tenantId;

    /**
     * 出库单状态（1：初始化，2：库存处理中，3：库存处理完成，4:库存处理失败）
     */
    private Integer processStatus;

    /**
     * 仓库
     */
    private Integer warehouseNo;

    /**
     * 出库任务类型
     */
    private Integer stockTaskType;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 仓租户
     */
    private Long warehouseTenantId;
    /**
     * 仓库服务商
     */
    private String companyName;

    public StockTaskProcess(Integer stockTaskId, LocalDateTime addtime, String recorder) {
        this.stockTaskId = stockTaskId;
        this.addtime = addtime;
        this.recorder = recorder;
    }

    public StockTaskProcess(Integer stockTaskId, LocalDateTime addtime, String recorder, Long tenantId) {
        this.stockTaskId = stockTaskId;
        this.addtime = addtime;
        this.recorder = recorder;
        this.tenantId = tenantId;
    }
}
