package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventory;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventoryUpdate;
import net.summerfarm.wms.domain.inventory.domainobject.query.CabinetInventoryQuery;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetInventoryDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetInventoryUpdateDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetInventoryQueryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/28
 */
@Mapper
public interface CabinetInventoryConverter {

    CabinetInventoryConverter INSTANCE = Mappers.getMapper(CabinetInventoryConverter.class);

    CabinetInventoryDO convert(CabinetInventory cabinetInventory);

    CabinetInventoryQueryDO convert(CabinetInventoryQuery cabinetInventory);

    CabinetInventoryUpdateDO convert(CabinetInventoryUpdate cabinetInventory);

    CabinetInventory convert(CabinetInventoryDO cabinetInventoryDO);

    List<CabinetInventory> convertList(List<CabinetInventoryDO> cabinetInventoryDO);

}
