package net.summerfarm.wms.manage.model.input.batchcode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 批量打印批次码请求参数
 * @author: xdc
 * @date: 2024/1/24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBatchCodBatchPrintInput implements Serializable {
    private static final long serialVersionUID = -5488120632655498533L;

    /**
     * 库存仓编号
     */
    @NotNull(message = "请指定仓库")
    private Long warehouseNo;

    /**
     * 类型
     * 10-调拨入库, 11-采购入库, 13-拒收入库, 18-调拨回库, 19-退货入库, 20-缺货入库, 21-越仓入库, 22-拦截入库
     * 23-多出入库, 24-其他入库, 25-越库入库, 110-入库任务 120-转换任务
     */
    @NotNull(message = "请指定业务类型")
    private Integer bizType;

    /**
     * 任务操作id
     */
    @NotNull(message = "请指定业务id")
    private Long bizId;

    /**
     * 打印sku列表
     */
    @NotEmpty(message = "请指定打印sku列表")
    @Size(max = 1000, message = "最多支持1000个条码打印")
    public List<SkuBatchCodePrintInput> printSkuList;

}
