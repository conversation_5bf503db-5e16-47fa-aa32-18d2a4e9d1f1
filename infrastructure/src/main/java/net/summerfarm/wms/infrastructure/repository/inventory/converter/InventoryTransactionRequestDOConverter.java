package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.entity.InventoryTransactionRequest;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.InventoryTransactionRequestDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface InventoryTransactionRequestDOConverter {

    InventoryTransactionRequestDOConverter INSTANCE = Mappers.getMapper(InventoryTransactionRequestDOConverter.class);

    InventoryTransactionRequestDO convert(InventoryTransactionRequest inventoryTransactionRequest);

    List<InventoryTransactionRequestDO> convertList(List<InventoryTransactionRequest> inventoryTransactionRequestList);
}
