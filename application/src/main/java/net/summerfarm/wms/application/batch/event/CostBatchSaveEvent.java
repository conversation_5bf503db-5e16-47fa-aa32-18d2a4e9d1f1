package net.summerfarm.wms.application.batch.event;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.wms.common.event.BaseEvent;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class CostBatchSaveEvent extends BaseEvent<String> {

    private static final long serialVersionUID = -7955077501196110755L;
    String data;

    public CostBatchSaveEvent(String eventData) {
        super(eventData);
    }
}
