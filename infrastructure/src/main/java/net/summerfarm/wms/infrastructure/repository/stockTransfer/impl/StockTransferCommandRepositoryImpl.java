package net.summerfarm.wms.infrastructure.repository.stockTransfer.impl;

import net.summerfarm.wms.domain.stockTransfer.StockTransferCommandRepository;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferCreateParam;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferUpdateParam;
import net.summerfarm.wms.infrastructure.dao.stockTransfer.StockTransferDAO;
import net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferDO;
import net.summerfarm.wms.infrastructure.repository.stockTransfer.converter.StockTransferConverter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Repository
public class StockTransferCommandRepositoryImpl implements StockTransferCommandRepository {

    @Resource
    private StockTransferDAO stockTransferDAO;

    /**
     * 保存转换任务
     *
     * @param stockTransferCreateParam 转换任务保存参数
     */
    @Override
    public void insert(StockTransferCreateParam stockTransferCreateParam) {
        if (Objects.isNull(stockTransferCreateParam)) {
            return;
        }
        StockTransferDO stockTransferDO = StockTransferConverter.INSTANCE.convert(stockTransferCreateParam);
        stockTransferDAO.insertStockTransfer(stockTransferDO);
        stockTransferCreateParam.setId(stockTransferDO.getId());
    }

    /**
     * 修改转换任务
     *
     * @param stockTransferUpdateParam 转换任务修改参数
     */
    @Override
    public int updateStateById(StockTransferUpdateParam stockTransferUpdateParam) {
        if (Objects.isNull(stockTransferUpdateParam)) {
            return 0;
        }
        StockTransferDO stockTransferDO = StockTransferConverter.INSTANCE.convert(stockTransferUpdateParam);
        return stockTransferDAO.updateStateById(stockTransferDO);
    }
}
