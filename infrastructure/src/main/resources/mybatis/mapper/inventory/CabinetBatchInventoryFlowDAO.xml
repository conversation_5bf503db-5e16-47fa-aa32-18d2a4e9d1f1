<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.CabinetBatchInventoryFlowDAO">
    <resultMap id="CabinetBatchInventoryFlowMap"
               type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryFlowDO">
        <id property="id" column="id"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="sku" column="sku"/>
        <result property="cabinetCode" column="cabinet_code"/>
        <result property="cabinetBatchInventoryId" column="cabinet_batch_inventory_id"/>
        <result property="beforeChangeQuantity" column="before_change_quantity"/>
        <result property="changeQuantity" column="change_quantity"/>
        <result property="afterChangeQuantity" column="after_change_quantity"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderTypeName" column="order_type_name"/>
        <result property="internalNo" column="internal_no"/>
        <result property="produceDate" column="produce_date"/>
        <result property="qualityDate" column="quality_date"/>
        <result property="batchNo" column="batch_no"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createOperator" column="create_operator"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="ownerCode" column="owner_code"/>
        <result property="ownerName" column="owner_name"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="zoneCode" column="zone_code"/>
        <result property="zoneType" column="zone_type"/>
        <result property="containerCode" column="container_code"/>
        <result property="operatorType" column="operator_type"/>
        <result property="beforeChangeLockQuantity" column="before_change_lock_quantity"/>
        <result property="changeLockQuantity" column="change_lock_quantity"/>
        <result property="afterChangeLockQuantity" column="after_change_lock_quantity"/>
    </resultMap>

    <sql id="table_name">
        wms_cabinet_batch_inventory_flow
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `warehouse_no`, `sku`, `cabinet_code`, `cabinet_batch_inventory_id`, `before_change_quantity`,
        `change_quantity`, `after_change_quantity`, `order_no`, `order_type_name`, `internal_no`, `produce_date`,
        `quality_date`, `batch_no`, `create_time`, `update_time`, `create_operator`, `update_operator`, `owner_code`,
        `owner_name`, `tenant_id`
            , `zone_code`, `zone_type`, `container_code`, `operator_type`, `before_change_lock_quantity`, `change_lock_quantity`, `after_change_lock_quantity`
    </sql>

    <sql id="values_exclude_id">
        #{warehouseNo}, #{sku}, #{cabinetCode}, #{cabinetBatchInventoryId}, #{beforeChangeQuantity}, #{changeQuantity},
        #{afterChangeQuantity}, #{orderNo}, #{orderTypeName}, #{internalNo}, #{produceDate}, #{qualityDate}, #{batchNo},
        #{createTime}, #{updateTime}, #{createOperator}, #{updateOperator}, #{ownerCode}, #{ownerName}, #{tenantId}
            , #{zoneCode}, #{zoneType}, #{containerCode}, #{operatorType}, #{beforeChangeLockQuantity}, #{changeLockQuantity}, #{afterChangeLockQuantity}
    </sql>

    <sql id="query">
        <where>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                AND `warehouse_no` in
                <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                    #{warehouseNo1}
                </foreach>
            </if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="skuList != null and skuList.size() > 0">
                AND `sku` in
                <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                    #{sku1}
                </foreach>
            </if>
            <if test="cabinetCode != null">AND `cabinet_code` = #{cabinetCode}</if>
            <if test="cabinetBatchInventoryId != null">AND `cabinet_batch_inventory_id` = #{cabinetBatchInventoryId}
            </if>
            <if test="beforeChangeQuantity != null">AND `before_change_quantity` = #{beforeChangeQuantity}</if>
            <if test="changeQuantity != null">AND `change_quantity` = #{changeQuantity}</if>
            <if test="afterChangeQuantity != null">AND `after_change_quantity` = #{afterChangeQuantity}</if>
            <if test="orderNo != null">AND `order_no` = #{orderNo}</if>
            <if test="orderTypeName != null">AND `order_type_name` = #{orderTypeName}</if>
            <if test="internalNo != null">AND `internal_no` = #{internalNo}</if>
            <if test="produceDate != null">AND `produce_date` = #{produceDate}</if>
            <if test="qualityDate != null">AND `quality_date` = #{qualityDate}</if>
            <if test="batchNo != null">AND `batch_no` = #{batchNo}</if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="ownerCode != null">AND `owner_code` = #{ownerCode}</if>
            <if test="ownerName != null">AND `owner_name` = #{ownerName}</if>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="zoneCode != null">AND `zone_code` = #{zoneCode}</if>
            <if test="zoneType != null">AND `zone_type` = #{zoneType}</if>
            <if test="createTimeGe != null">AND `create_time` >= #{createTimeGe}</if>
            <if test="createTimeLt != null">AND `create_time` &lt; #{createTimeLt}</if>
            <if test="startTime != null">AND `create_time` >= #{startTime}</if>
            <if test="endTime != null">AND `create_time` &lt;= #{endTime}</if>
            <if test="containerCode != null">AND `container_code` = #{containerCode}</if>
            <if test="operatorType != null">AND `operator_type` = #{operatorType}</if>
            <if test="beforeChangeLockQuantity != null">AND `before_change_lock_quantity` = #{beforeChangeLockQuantity},</if>
            <if test="changeLockQuantity != null">AND `change_lock_quantity` = #{changeLockQuantity},</if>
            <if test="afterChangeLockQuantity != null">AND `after_change_lock_quantity` = #{afterChangeLockQuantity},</if>
        </where>
    </sql>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryFlowDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">`warehouse_no`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="cabinetCode != null">`cabinet_code`,</if>
            <if test="cabinetBatchInventoryId != null">`cabinet_batch_inventory_id`,</if>
            <if test="beforeChangeQuantity != null">`before_change_quantity`,</if>
            <if test="changeQuantity != null">`change_quantity`,</if>
            <if test="afterChangeQuantity != null">`after_change_quantity`,</if>
            <if test="orderNo != null">`order_no`,</if>
            <if test="orderTypeName != null">`order_type_name`,</if>
            <if test="internalNo != null">`internal_no`,</if>
            <if test="produceDate != null">`produce_date`,</if>
            <if test="qualityDate != null">`quality_date`,</if>
            <if test="batchNo != null">`batch_no`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="ownerCode != null">`owner_code`,</if>
            <if test="ownerName != null">`owner_name`,</if>
            <if test="tenantId != null">`tenant_id`,</if>
            <if test="zoneCode != null">`zone_code`,</if>
            <if test="zoneType != null">`zone_type`,</if>
            <if test="containerCode != null">`container_code`,</if>
            <if test="operatorType != null">`operator_type`,</if>
            <if test="beforeChangeLockQuantity != null">`before_change_lock_quantity`,</if>
            <if test="changeLockQuantity != null">`change_lock_quantity`,</if>
            <if test="afterChangeLockQuantity != null">`after_change_lock_quantity`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">#{warehouseNo},</if>
            <if test="sku != null">#{sku},</if>
            <if test="cabinetCode != null">#{cabinetCode},</if>
            <if test="cabinetBatchInventoryId != null">#{cabinetBatchInventoryId},</if>
            <if test="beforeChangeQuantity != null">#{beforeChangeQuantity},</if>
            <if test="changeQuantity != null">#{changeQuantity},</if>
            <if test="afterChangeQuantity != null">#{afterChangeQuantity},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderTypeName != null">#{orderTypeName},</if>
            <if test="internalNo != null">#{internalNo},</if>
            <if test="produceDate != null">#{produceDate},</if>
            <if test="qualityDate != null">#{qualityDate},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="ownerCode != null">#{ownerCode},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="zoneCode != null">#{zoneCode},</if>
            <if test="zoneType != null">#{zoneType},</if>
            <if test="containerCode != null">#{containerCode},</if>
            <if test="operatorType != null">#{operatorType},</if>
            <if test="beforeChangeLockQuantity != null">#{beforeChangeLockQuantity},</if>
            <if test="changeLockQuantity != null">#{changeLockQuantity},</if>
            <if test="afterChangeLockQuantity != null">#{afterChangeLockQuantity},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryFlowDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.warehouseNo}, #{i.sku}, #{i.cabinetCode}, #{i.cabinetBatchInventoryId}, #{i.beforeChangeQuantity},
            #{i.changeQuantity}, #{i.afterChangeQuantity}, #{i.orderNo}, #{i.orderTypeName}, #{i.internalNo},
            #{i.produceDate}, #{i.qualityDate}, #{i.batchNo}, #{i.createTime}, #{i.updateTime}, #{i.createOperator},
            #{i.updateOperator}, #{i.ownerCode}, #{i.ownerName}, #{i.tenantId}
                , #{i.zoneCode}, #{i.zoneType}, #{i.containerCode}, #{i.operatorType}
                , #{i.beforeChangeLockQuantity}, #{i.changeLockQuantity}, #{i.afterChangeLockQuantity})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryFlowDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="warehouseNo != null">`warehouse_no` = #{warehouseNo},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="cabinetCode != null">`cabinet_code` = #{cabinetCode},</if>
            <if test="cabinetBatchInventoryId != null">`cabinet_batch_inventory_id` = #{cabinetBatchInventoryId},</if>
            <if test="beforeChangeQuantity != null">`before_change_quantity` = #{beforeChangeQuantity},</if>
            <if test="changeQuantity != null">`change_quantity` = #{changeQuantity},</if>
            <if test="afterChangeQuantity != null">`after_change_quantity` = #{afterChangeQuantity},</if>
            <if test="orderNo != null">`order_no` = #{orderNo},</if>
            <if test="orderTypeName != null">`order_type_name` = #{orderTypeName},</if>
            <if test="internalNo != null">`internal_no` = #{internalNo},</if>
            <if test="produceDate != null">`produce_date` = #{produceDate},</if>
            <if test="qualityDate != null">`quality_date` = #{qualityDate},</if>
            <if test="batchNo != null">`batch_no` = #{batchNo},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="ownerCode != null">`owner_code` = #{ownerCode},</if>
            <if test="ownerName != null">`owner_name` = #{ownerName},</if>
            <if test="tenantId != null">`tenant_id` = #{tenantId},</if>
            <if test="zoneCode != null">`zone_code` = #{zoneCode},</if>
            <if test="zoneType != null">`zone_type` = #{zoneType},</if>
            <if test="containerCode != null">`container_code` = #{containerCode},</if>
            <if test="operatorType != null">`operator_type` = #{operatorType},</if>
            <if test="beforeChangeLockQuantity != null">`before_change_lock_quantity` = #{beforeChangeLockQuantity},</if>
            <if test="changeLockQuantity != null">`change_lock_quantity` = #{changeLockQuantity},</if>
            <if test="afterChangeLockQuantity != null">`after_change_lock_quantity` = #{afterChangeLockQuantity},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="CabinetBatchInventoryFlowMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="CabinetBatchInventoryFlowMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryFlowQueryDO"
            resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryFlowQueryDO"
            resultMap="CabinetBatchInventoryFlowMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryFlowQueryDO"
            resultMap="CabinetBatchInventoryFlowMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>


</mapper>
