package net.summerfarm.wms.manage.model.input.transfer;

import com.google.common.collect.Lists;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 转换任务列表查询
 * @author: xdc
 * @date: 2024/2/19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOpQueryInput implements Serializable {
    private static final long serialVersionUID = -8650464486886364918L;

    /**
     * 任务id
     * 查询单个
     */
    Long stockTransferItemId;

    /**
     * 任务ids
     * 批量查询
     */
    List<Long> stockTransferItemIds;

    public List<Long> getItemIds() {
        if (Objects.nonNull(stockTransferItemId)) {
            return Lists.newArrayList(stockTransferItemId);
        }
        return stockTransferItemIds;
    }
}