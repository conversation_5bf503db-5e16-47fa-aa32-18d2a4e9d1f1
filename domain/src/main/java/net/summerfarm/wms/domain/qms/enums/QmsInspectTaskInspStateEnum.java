package net.summerfarm.wms.domain.qms.enums;

import lombok.Getter;

/**
 * 货检状态，10-待分配 20-待进行 30-进行中 40进行中(存在异常操作) 50-已取消 60-已完成 70-异常完成
 *
 * @see net.summerfarm.wms.domain.mission.enums.MissionStateEnum
 */
@Getter
public enum QmsInspectTaskInspStateEnum {

    WAIT_ASSIGN(10, "待分配"),
    WAIT_EXE(20, "待执行"),
    EXE_ING(30, "执行中"),
    /**
     * 异常执行中
     * 存在异常操作变更为此状态
     * 前端展示异常执行中也为执行中
     */
    EX_EXE(40, "执行中(存在异常操作)"),
    CANCEL(50, "已取消"),
    FINISH(60, "已完成"),
    EX_FINISH(70, "异常完成"),

    ;
    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    QmsInspectTaskInspStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean equalsCode(Integer input){
        return this.code.equals(input);
    }
}
