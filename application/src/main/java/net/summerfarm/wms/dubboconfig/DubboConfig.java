package net.summerfarm.wms.dubboconfig;

import net.summerfarm.manage.client.admin.AdminProvider;
import net.summerfarm.manage.client.delivery.AfterDeliveryPatchProvider;
import net.summerfarm.manage.client.ding.DingProcessProvider;
import net.summerfarm.manage.client.inventory.InventoryWriteProvider;
import net.summerfarm.manage.client.msg.MsgAdminProvider;
import net.summerfarm.manage.client.purchase.PurchaseArrangeProvider;
import net.summerfarm.manage.client.purchase.PurchaseProvider;
import net.summerfarm.manage.client.purchase.StockArrangeProvider;
import net.summerfarm.manage.client.wms.GoodsCheckTaskProvider;
import net.summerfarm.manage.client.wms.WarehouseProvider;
import net.summerfarm.tms.client.cost.provider.TmsDeliveryCostQueryProvider;
import net.summerfarm.tms.client.delivery.provider.TmsLackApprovedQueryProvider;
import net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DubboConfig {

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private AdminProvider adminProvider;

    @DubboReference(check = false, timeout = 6000, protocol = "dubbo")
    private DingProcessProvider dingProcessProvider;

    @DubboReference(check = false, timeout = 6000, protocol = "dubbo")
    private TmsDistOrderQueryProvider tmsDistOrderQueryProvider;

    @DubboReference(check = false, timeout = 6000, protocol = "dubbo")
    private MsgAdminProvider msgAdminProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private PurchaseProvider purchaseProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private AfterDeliveryPatchProvider afterDeliveryPatchProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private StockArrangeProvider stockArrangeProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private PurchaseArrangeProvider purchaseArrangeProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private TmsDeliveryCostQueryProvider tmsDeliveryCostQueryProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private InventoryWriteProvider inventoryWriteProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private GoodsCheckTaskProvider goodsCheckTaskProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private WarehouseStorageFenceQueryProvider warehouseStorageFenceQueryProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private WarehouseProvider warehouseProvider;

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private TmsLackApprovedQueryProvider tmsLackApprovedQueryProvider;

    @Bean
    public AdminProvider adminProvider() {
        return adminProvider;
    }

    @Bean
    public TmsLackApprovedQueryProvider tmsLackApprovedQueryProvider() {
        return tmsLackApprovedQueryProvider;
    }

    @Bean
    public TmsDeliveryCostQueryProvider tmsDeliveryCostQueryProvider() {
        return tmsDeliveryCostQueryProvider;
    }

    @Bean
    public PurchaseArrangeProvider purchaseArrangeProvider() {
        return purchaseArrangeProvider;
    }

    @Bean
    public TmsDistOrderQueryProvider tmsDistOrderQueryProvider() {
        return tmsDistOrderQueryProvider;
    }

    @Bean
    public DingProcessProvider dingProcessProvider() {
        return dingProcessProvider;
    }

    @Bean
    public MsgAdminProvider msgAdminProvider() {
        return msgAdminProvider;
    }

    @Bean
    public PurchaseProvider purchaseProvider() {
        return purchaseProvider;
    }

    @Bean
    public AfterDeliveryPatchProvider afterDeliveryPatchProvider() {
        return afterDeliveryPatchProvider;
    }

    @Bean
    StockArrangeProvider stockArrangeProvider() {
        return stockArrangeProvider;
    }

    @Bean
    public InventoryWriteProvider inventoryWriteProvider() {
        return inventoryWriteProvider;
    }

    @Bean
    public GoodsCheckTaskProvider goodsCheckTaskProvider() {
        return goodsCheckTaskProvider;
    }

    @Bean
    public WarehouseStorageFenceQueryProvider warehouseStorageFenceQueryProvider() {
        return warehouseStorageFenceQueryProvider;
    }

    @Bean
    public WarehouseProvider warehouseProvider() {
        return warehouseProvider;
    }

}
