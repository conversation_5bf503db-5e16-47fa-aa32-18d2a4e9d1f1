package net.summerfarm.wms.infrastructure.repository.goods.enums;

import java.util.Objects;

/**
 * 仓库存储位置枚举
 * <NAME_EMAIL> on 2017/8/14.
 */
public enum StorageLocation {

    DEFAULT(0, "未分类"),
    FREEZING(1, "冷冻"),
    REFRIGERATION(2, "冷藏"),
    CONSTANT_TEMPERATURE(3, "常温"),
    DING_HUI(4, "顶汇大流通");

    private int id;

    private String type;

    StorageLocation(int id, String type) {
        this.id = id;
        this.type = type;
    }

    public static String getTypeById(int id) {
        StorageLocation[] values = StorageLocation.values();
        for (StorageLocation storageLocation : values) {
            if (storageLocation.getId() == id) {
                return storageLocation.getType();
            }
        }
        return DEFAULT.getType();
    }

    public static String queryTypeById(Integer id) {
        StorageLocation[] values = StorageLocation.values();
        for (StorageLocation storageLocation : values) {
            if (Objects.equals(storageLocation.getId(), id)) {
                return storageLocation.getType();
            }
        }
        return DEFAULT.getType();
    }

    public static Integer getIdByType(String type) {
        StorageLocation[] values = StorageLocation.values();
        for (StorageLocation storageLocation : values) {
            if (Objects.equals(storageLocation.getType(), type)) {
                return storageLocation.getId();
            }
        }
        return null;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
