package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.InventoryWMSInfo;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.InventoryWMSInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface InventoryWMSInfoConverter {

    InventoryWMSInfoConverter INSTANCE = Mappers.getMapper(InventoryWMSInfoConverter.class);

    InventoryWMSInfo convert(InventoryWMSInfoDO infoDO);

    List<InventoryWMSInfo> convertList(List<InventoryWMSInfoDO> infoDOList);
}
