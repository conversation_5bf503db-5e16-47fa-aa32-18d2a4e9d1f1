package net.summerfarm.wms.infrastructure.dao.outstore.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @Date 2023/5/4 21:52
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecheckOutStoreTaskDO {
    /**
     * 发货仓
     */
    private Integer warehouseNo;

    /**
     * 城配仓
     */
    private Integer storeNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 规格
     */
    private String weight;
    /**
     * 单位
     */
    private String unit;
    /**
     * 任务ID列表
     */
    private String stockTaskIdList;
    /**
     * 预计出库时间
     */
    private Date expectTime;
    /**
     * 波次出库数量
     */
    private Integer waveOutQuantity;
//    /**
//     * 多出入库数
//     */
//    private Integer returnQuantity;
//    /**
//     * 实际应发数量
//     */
//    private Integer actualQuantity;
    /**
     * 截单时间的出库任务id
     */
    private Integer maxTaskId;
}
