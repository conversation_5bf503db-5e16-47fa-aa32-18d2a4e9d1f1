package net.summerfarm.wms.infrastructure.repository.allocation.converter;

import net.summerfarm.wms.domain.allocation.domainobject.StockAllocationList;
import net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StockAllocationConvert {
    StockAllocationConvert INSTACNE = Mappers.getMapper(StockAllocationConvert.class);

    StockAllocationList convert(StockAllocationListDO stockAllocationListDO);
}
