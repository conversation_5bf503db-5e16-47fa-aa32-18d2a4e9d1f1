package net.summerfarm.wms.infrastructure.repository.bizLog;


import net.summerfarm.wms.infrastructure.converter.bizLog.WmsBizLogConverter;
import net.summerfarm.wms.domain.bizLog.repository.WmsBizLogQueryRepository;
import net.summerfarm.wms.domain.bizLog.entity.WmsBizLogEntity;
import net.summerfarm.wms.domain.bizLog.param.query.WmsBizLogQueryParam;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.infrastructure.dao.bizLog.WmsBizLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-04-10 11:06:13
* @version 1.0
*
*/
@Repository
public class WmsBizLogQueryRepositoryImpl implements WmsBizLogQueryRepository {

    @Autowired
    private WmsBizLogMapper wmsBizLogMapper;


    @Override
    public PageInfo<WmsBizLogEntity> getPage(WmsBizLogQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WmsBizLogEntity> entities = wmsBizLogMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WmsBizLogEntity selectById(Long id) {
        return WmsBizLogConverter.toWmsBizLogEntity(wmsBizLogMapper.selectById(id));
    }


    @Override
    public List<WmsBizLogEntity> selectByCondition(WmsBizLogQueryParam param) {
        return WmsBizLogConverter.toWmsBizLogEntityList(wmsBizLogMapper.selectByCondition(param));
    }

}