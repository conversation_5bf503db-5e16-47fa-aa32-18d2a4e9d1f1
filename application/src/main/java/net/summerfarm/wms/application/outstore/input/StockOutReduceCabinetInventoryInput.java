package net.summerfarm.wms.application.outstore.input;

import lombok.Data;
import net.summerfarm.wms.application.outstore.dto.StockOutReduceCabinetInventoryDetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3
 */
@Data
public class StockOutReduceCabinetInventoryInput implements Serializable {

    private static final long serialVersionUID = 8078296060690306418L;

    /**
     * 出库任务id
     */
    private Long stockTaskId;

    /**
     * 出库单id
     */
    private Long stockTaskProcessId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * 出库扣减库位库存明细
     */
    private List<StockOutReduceCabinetInventoryDetail> stockOutReduceCabinetInventoryDetails;

}
