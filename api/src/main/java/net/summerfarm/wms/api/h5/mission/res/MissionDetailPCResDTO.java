package net.summerfarm.wms.api.h5.mission.res;

import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionDetailPCResDTO implements Serializable {
    /**
     * 仓库号
     */
    Long warehouseNo;
    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 明细条目
     */
    List<MissionItemPCResDTO> items;

    /**
     * 波次号
     */
    String waveNo;

    /**
     * 业务单号
     */
    String sourceOrderNo;

    /**
     * 业务单号列表
     */
    List<String> sourceOrderNoList;

    /**
     * 业务类型
     */
    Integer bizType;

    /**
     * 业务类型列表
     */
    List<Integer> bizTypeList;

    /**
     * 任务状态
     */
    Integer state;

    /**
     * 操作人
     */
    String operatorName;

    /**
     * 创建时间
     */
    String createTime;

    /**
     * 完成时间
     */
    String finishTime;

    /**
     * 单据备注
     */
    String remark;

    public String getBizTypeListStr() {
        if (CollectionUtils.isEmpty(bizTypeList)) {
            return null;
        }
        return StringUtils.join(bizTypeList, ",");
    }

    public String getSourceOrderNoListStr() {
        if (CollectionUtils.isEmpty(sourceOrderNoList)) {
            return null;
        }
        return StringUtils.join(sourceOrderNoList, ",");
    }
}
