package net.summerfarm.wms.application.bizLog.controller.bizLog;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.bizLog.controller.bizLog.assembler.WmsBizLogAssembler;
import net.summerfarm.wms.application.bizLog.controller.bizLog.input.query.WmsBizLogQueryInput;
import net.summerfarm.wms.application.bizLog.controller.bizLog.vo.WmsBizLogVO;
import net.summerfarm.wms.application.bizLog.service.bizLog.WmsBizLogCommandService;
import net.summerfarm.wms.application.bizLog.service.bizLog.WmsBizLogQueryService;
import net.summerfarm.wms.domain.bizLog.entity.WmsBizLogEntity;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * wms业务操作日志表
 * @Description wms业务操作日志表功能模块
 * <AUTHOR>
 * @date 2024-04-10 11:06:13
 * @version 1.0
 */
@RestController
@RequestMapping(value="/wmsBizLog")
public class WmsBizLogController{

	@Autowired
	private WmsBizLogCommandService wmsBizLogCommandService;
	@Autowired
	private WmsBizLogQueryService wmsBizLogQueryService;


	/**
	 * wms业务操作日志表列表
	 * @return WmsBizLogVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<WmsBizLogVO>> getPage(@RequestBody WmsBizLogQueryInput input){
		PageInfo<WmsBizLogEntity> page = wmsBizLogQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, WmsBizLogAssembler::toWmsBizLogVO));
	}
}

