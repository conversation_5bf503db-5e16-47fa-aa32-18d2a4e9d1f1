package net.summerfarm.wms.infrastructure.repository.processingtask;

import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigQueryParam;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingMaterialConfigQueryRepository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingMaterialConfigMapper;
import net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingMaterialConfig;
import net.summerfarm.wms.infrastructure.repository.processingtask.converter.WmsProcessingMaterialConfigConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2025-01-10 18:59:23
* @version 1.0
*
*/
@Repository
public class ProcessingMaterialConfigQueryRepositoryImpl implements ProcessingMaterialConfigQueryRepository {

    @Autowired
    private WmsProcessingMaterialConfigMapper wmsProcessingMaterialConfigMapper;


    @Override
    public PageInfo<ProcessingMaterialConfigEntity> getPage(WmsProcessingMaterialConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ProcessingMaterialConfigEntity> entities = wmsProcessingMaterialConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ProcessingMaterialConfigEntity selectById(Long id) {
        return WmsProcessingMaterialConfigConverter.toWmsProcessingMaterialConfigEntity(wmsProcessingMaterialConfigMapper.selectById(id));
    }


    @Override
    public List<ProcessingMaterialConfigEntity> selectByCondition(WmsProcessingMaterialConfigQueryParam param) {
        return WmsProcessingMaterialConfigConverter.toWmsProcessingMaterialConfigEntityList(wmsProcessingMaterialConfigMapper.selectByCondition(param));
    }

    @Override
    public Map<Long, List<ProcessingMaterialConfigEntity>> mapByConfigIdList(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)){
            return new HashMap<>();
        }

        WmsProcessingMaterialConfigQueryParam param = new WmsProcessingMaterialConfigQueryParam();
        param.setProcessingConfigIdList(configIdList);
        param.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        List<WmsProcessingMaterialConfig> configList = wmsProcessingMaterialConfigMapper.selectByCondition(param);
        List<ProcessingMaterialConfigEntity> result = WmsProcessingMaterialConfigConverter
                .toWmsProcessingMaterialConfigEntityList(configList);

        return result.stream()
                .collect(Collectors.groupingBy(ProcessingMaterialConfigEntity::getProcessingConfigId));
    }

    @Override
    public Map<String, ProcessingMaterialConfigEntity> mapByConfigIdListGroupByMSku(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)){
            return new HashMap<>();
        }

        WmsProcessingMaterialConfigQueryParam param = new WmsProcessingMaterialConfigQueryParam();
        param.setProcessingConfigIdList(configIdList);
        param.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        List<WmsProcessingMaterialConfig> configList = wmsProcessingMaterialConfigMapper.selectByCondition(param);
        List<ProcessingMaterialConfigEntity> result = WmsProcessingMaterialConfigConverter
                .toWmsProcessingMaterialConfigEntityList(configList);

        return result.stream()
                .collect(Collectors.toMap(ProcessingMaterialConfigEntity::getMaterialSkuCode,
                        Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, ProcessingMaterialConfigEntity> mapByConfigIdGroupByMSku(Long configId) {
        if (configId == null){
            return null;
        }

        WmsProcessingMaterialConfigQueryParam param = new WmsProcessingMaterialConfigQueryParam();
        param.setProcessingConfigId(configId);
        param.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        List<WmsProcessingMaterialConfig> configList = wmsProcessingMaterialConfigMapper.selectByCondition(param);
        List<ProcessingMaterialConfigEntity> result = WmsProcessingMaterialConfigConverter
                .toWmsProcessingMaterialConfigEntityList(configList);

        return result.stream()
                .collect(Collectors.toMap(ProcessingMaterialConfigEntity::getMaterialSkuCode,
                        Function.identity(), (a, b) -> a));
    }

}