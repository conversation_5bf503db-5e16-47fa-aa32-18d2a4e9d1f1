package net.summerfarm.wms.manage.model.domain;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import net.summerfarm.common.util.StringUtils;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
public class ContactAddressRemark {

    /**
     * 自定义备注
     */
    private String customRemark;
    /**
     * 基础备注集合
     */
    private List<String> baseRemark;


    public String toJsonStr(ContactAddressRemark contactAddressRemark){
        return toJsonStr(contactAddressRemark);
    }

    public static  ContactAddressRemark initContactAddressRemark(String json){
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSONUtil.toBean(json,ContactAddressRemark.class);
    }

}
