<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <groupId>net.summerfarm</groupId>
    <artifactId>summerfarm-wms</artifactId>
    <version>0.0.1</version>

    <modules>
        <module>api</module>
        <module>common</module>
        <module>facade</module>
        <module>domain</module>
        <module>infrastructure</module>
        <module>application</module>
        <module>inbound</module>
        <module>starter</module>
        <module>manage</module>
    </modules>


    <packaging>pom</packaging>
    <properties>
        <wms.inbound.version>0.0.1</wms.inbound.version>
        <wms.manage.version>0.0.1</wms.manage.version>
        <wms.facade.version>0.0.1</wms.facade.version>
        <wms.start.version>0.0.1</wms.start.version>
        <wms.client.version>1.7.8-RELEASE</wms.client.version>
        <tms.client.version>1.1.7-RELEASE</tms.client.version>
        <tms.api.version>0.0.13</tms.api.version>
        <wms.domain.version>0.0.1</wms.domain.version>
        <wms.api.version>0.0.1</wms.api.version>
        <wms.common.version>0.0.1</wms.common.version>
        <wms.infrastructure.version>0.0.1</wms.infrastructure.version>
        <wms.application.version>0.0.1</wms.application.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <lombok.version>1.18.2</lombok.version>
        <mybatis.version>3.5.1</mybatis.version>
        <spring.mybatis.version>2.0.6</spring.mybatis.version>
        <spring.version>5.2.12.RELEASE</spring.version>
        <mysql.version>8.0.23</mysql.version>
        <manage.client.version>1.0.49-RELEASE</manage.client.version>
        <ofc.clent.version>1.6.7-RELEASE</ofc.clent.version>
        <smart-doc.version>2.5.3-xm</smart-doc.version>
        <xianmu-rocketmq-support.version>1.2.3</xianmu-rocketmq-support.version>
        <task.support.version>1.0.5</task.support.version>
        <erp-client.version>1.1.9-RELEASE</erp-client.version>
        <common-client.version>1.0.15-RELEASE</common-client.version>
        <usercenter-client.version>1.0.2</usercenter-client.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <wnc.client.version>1.4.1-RELEASE</wnc.client.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.1</mybatis-plus-generator.version>
        <open-platform-client.version>1.2-RELEASE</open-platform-client.version>
        <sentinel.version>1.0.1-RELEASE</sentinel.version>
        <goods-center-client.version>1.2.0-RELEASE</goods-center-client.version>
        <order-center-client.version>1.3.7-RELEASE</order-center-client.version>
        <cosfo-manage-client.version>1.4.3-RELEASE</cosfo-manage-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-starter</artifactId>
                <version>${wms.start.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-facade</artifactId>
                <version>${wms.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-inbound</artifactId>
                <version>${wms.inbound.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-manage</artifactId>
                <version>${wms.manage.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-domain</artifactId>
                <version>${wms.domain.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-api</artifactId>
                <version>${wms.api.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-common</artifactId>
                <version>${wms.common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-infrastructure</artifactId>
                <version>${wms.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>wms-application</artifactId>
                <version>${wms.application.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-mybatis-interceptor-support</artifactId>
                <version>1.0.6-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm.wms</groupId>
                <artifactId>summerfarm-wms-client</artifactId>
                <version>${wms.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>1.0.13-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${task.support.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.apache.dubbo</groupId>-->
            <!--                <artifactId>dubbo-registry-nacos</artifactId>-->
            <!--                <version>2.7.15</version>-->
            <!--            </dependency>-->

            <!-- validation -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>

            <!--boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.junit.vintage</groupId>
                        <artifactId>junit-vintage-engine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                </exclusions>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.1.0</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.16.4</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!--二方包-->
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <!--      根据实际版本修改，线上禁止SNAPSHOT版本     -->
                <version>1.1.12-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>erp-client</artifactId>
                <version>${erp-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.9</version>
            </dependency>

            <!--mybatis-->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.9</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-robot-util</artifactId>
                <version>1.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-common</artifactId>
                <version>1.4.10</version>
                <exclusions>
                    <exclusion>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                        <groupId>org.apache.rocketmq</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.xianmu.common</groupId>
                        <artifactId>xianmu-robot-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                    <!--<exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>-->
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>com.github.pagehelper</groupId>-->
            <!--                <artifactId>pagehelper</artifactId>-->
            <!--                <version>4.2.0</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.2.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-warehouse</artifactId>
                <version>1.3.5-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>redis.clients</groupId>
                        <artifactId>jedis</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                        <groupId>org.apache.rocketmq</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-dataformat-cbor</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-dataformat-smile</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-dataformat-yaml</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>net.manage.client</groupId>
                <artifactId>manage-client</artifactId>
                <version>${manage.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>ofc-client</artifactId>
                <version>${ofc.clent.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-client</artifactId>
                <version>${wnc.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>order-center-client</artifactId>
                <version>${order-center-client.version}</version>
            </dependency>

            <!-- MQ组件 -->
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-rocketmq-support.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-retryable-rocketmq-support</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>4.0.60</version>
            </dependency>
            <!-- authorization -->

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.version}</version>
            </dependency>
            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.14.9</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-sdk</artifactId>
                <version>1.1.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-dataformat-cbor</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-dataformat-smile</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-dataformat-yaml</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>1.0.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 日志组件 -->
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>1.0.14-RELEASE</version>
            </dependency>

            <!--QLExpress-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-client</artifactId>
                <version>${tms.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>tms-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-api</artifactId>
                <version>${tms.api.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>common-client</artifactId>
                <version>${common-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.xianmu.common</groupId>
                        <artifactId>xianmu-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>xianmu-download-support</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>1.0.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>summerfarm-common</artifactId>
                        <groupId>net.summerfarm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.12</version>
            </dependency>

            <!-- 开放平台 -->
            <dependency>
                <groupId>net.xianmu.open</groupId>
                <artifactId>open-platform-client</artifactId>
                <version>${open-platform-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-pms-client</artifactId>
                <version>1.5.8-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goods-center-client.version}</version>
            </dependency>

            <!-- 库存中心 -->
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>summerfarm-inventory-client</artifactId>
                <version>2.0.8-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>1.7.1</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo.summerfarm</groupId>
                <artifactId>saas-to-summerfarm</artifactId>
                <version>1.6.13-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!-- 外部 -->
            <dependency>
                <groupId>com.juepei</groupId>
                <artifactId>qimen-1.0.0-SHAPSHOT</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.juepei</groupId>
                <artifactId>qimen-RESOURCE-1.0.0-SHAPSHOT</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>jindie-sdk</artifactId>
                <version>1.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.13.1</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>2.13.1</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>2.13.1</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.2.0.Final</version>
                        </path>
                    </annotationProcessorPaths>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12.4</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>${smart-doc.version}</version>
                <configuration>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <include>com.cosfo.mall.*.controller:.*</include>
                        <include>com.github.shalousun:.*</include>
                    </includes>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>summerfarm-wms</projectName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--            自定义apijson处理插件-->
            <plugin>
                <groupId>xianmu.common</groupId>
                <artifactId>xianmu-maven-plugin</artifactId>
                <version>1.1.0</version>
            </plugin>
            <!--sonar-->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.3.0.603</version>
            </plugin>
        </plugins>
    </build>

</project>
