package net.summerfarm.wms.infrastructure.repository.outstore.impl;

import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.outstore.domainobject.WmsDamageStockItem;
import net.summerfarm.wms.domain.outstore.repository.WmsDamageStockItemRepository;
import net.summerfarm.wms.domain.outstore.valueObject.WmsDamageItemBatchValueObject;
import net.summerfarm.wms.infrastructure.dao.outstore.WmsDamageStockItemDAO;
import net.summerfarm.wms.infrastructure.dao.outstore.dataobject.WmsDamageStockItemDO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/28  17:33
 */
@Repository
public class WmsDamageStockItemRepositoryImpl implements WmsDamageStockItemRepository {

    @Resource
    WmsDamageStockItemDAO wmsDamageStockItemDAO;

    @Override
    public List<WmsDamageStockItem> selectByTaskNoAndType(String listNo, Integer type) {
        List<WmsDamageStockItemDO> wmsDamageStockItemDOS = wmsDamageStockItemDAO.selectByTaskNoAndType(listNo, type);
        List<WmsDamageStockItem> collect = wmsDamageStockItemDOS.stream().map(x -> {
            WmsDamageStockItem item = WmsDamageStockItem.builder().actualQuantity(x.getShouldQuantity())
                    .batch(x.getListNo())
                    .productionDate(x.getProductionDate())
                    .sku(x.getSku())
                    .qualityDate(x.getQualityDate()).build();
            return item;
        }).collect(Collectors.toList());
        return collect;

    }

    @Override
    public List<WmsDamageItemBatchValueObject> selectDamageBatchInfoByTaskNoAndType(List<String> taskNos, Integer type, String sku) {
        ExceptionUtil.checkAndThrow(type != null, "type不能为空");
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(taskNos), "taskNos不能为空");
        return wmsDamageStockItemDAO.selectDamageItemBatchByTaskNoAndType(taskNos, type, sku);
    }

    @Override
    public List<WmsDamageStockItem> selectByDamageId(Long damageId) {
        List<WmsDamageStockItemDO> wmsDamageStockItemDOList = wmsDamageStockItemDAO.selectByDamageId(damageId);
        if (CollectionUtils.isEmpty(wmsDamageStockItemDOList)) {
            return new ArrayList<>();
        }
        return wmsDamageStockItemDOList.stream().map(item -> WmsDamageStockItem.builder()
                .id(item.getId())
                .damageStockTaskId(item.getDamageStockTaskId())
                .sku(item.getSku())
                .listNo(item.getListNo())
                .qualityDate(item.getQualityDate())
                .productionDate(item.getProductionDate())
                .actualQuantity(item.getShouldQuantity())
                .quantity(item.getQuantity())
                .shouldQuantity(item.getShouldQuantity())
                .reason(item.getReason())
                .reasonType(item.getReasonType())
                .build()).collect(Collectors.toList());
    }

    @Override
    public Integer selectTaskStatusByTaskId(Long damageId) {
        return wmsDamageStockItemDAO.selectTaskStatusByTaskId(damageId);
    }

    @Override
    public Integer selectWarehouseNoByTaskId(Long damageId) {
        return wmsDamageStockItemDAO.selectWarehouseNoByTaskId(damageId);
    }

    @Override
    public Integer selectLockBatch(Integer warehouseNo, String batch, LocalDate qualityDate, String sku) {
        if (null == warehouseNo || StringUtils.isEmpty(batch) || null == qualityDate || StringUtils.isEmpty(sku)) {
            return 0;
        }
        return wmsDamageStockItemDAO.selectLockBatch(warehouseNo, batch, qualityDate, sku);
    }

}
