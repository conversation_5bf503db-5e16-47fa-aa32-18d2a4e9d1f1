package net.summerfarm.wms.application.acl.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.skucodetrace.inbound.input.command.FulfillmentOrderInfoInput;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceCommandService;
import net.summerfarm.wms.skucodetrace.SkuBatchCodeTraceCommandProvider;
import net.summerfarm.wms.skucodetrace.req.SkuBatchCodeTraceFulfillmentOrderInfoReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 唯一溯源码操作服务
 * date: 2025/2/24 10:31<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class SkuBatchCodeTraceCommandProviderImpl implements SkuBatchCodeTraceCommandProvider {

    @Resource
    private SkuBatchCodeTraceCommandService skuBatchCodeTraceCommandService;

    @Override
    public DubboResponse<Void> updateSkuBatchCodeTraceFulfillmentOrderInfo(@Valid List<SkuBatchCodeTraceFulfillmentOrderInfoReq> list) {
        if(CollectionUtils.isEmpty(list)){
            return DubboResponse.getOK();
        }

        List<FulfillmentOrderInfoInput> fulfillmentOrderInfoInputList = list.stream()
                .flatMap(item -> {
                    List<String> skuBatchTraceCodeList = item.getSkuBatchTraceCodeList();
                    return skuBatchTraceCodeList.stream()
                            .map(skuBatchTraceCode -> FulfillmentOrderInfoInput.builder()
                                    .orderNo(item.getOrderNo())
                                    .skuBatchTraceCode(skuBatchTraceCode)
                                    .fulfillmentNo(item.getFulfillmentNo())
                                    .merchantName(item.getMerchantName())
                                    .merchantId(item.getMerchantId())
                                    .deliveryTime(item.getDeliveryTime())
                                    .fulfillmentWay(item.getFulfillmentWay())
                                    .pathCode(item.getPathCode())
                                    .pathSequence(item.getPathSequence())
                                    .contactId(item.getContactId())
                                    .build());
                }).collect(Collectors.toList());

        skuBatchCodeTraceCommandService.updateSkuBatchCodeTraceFulfillmentOrderInfo(fulfillmentOrderInfoInputList);
        return DubboResponse.getOK();
    }
}
