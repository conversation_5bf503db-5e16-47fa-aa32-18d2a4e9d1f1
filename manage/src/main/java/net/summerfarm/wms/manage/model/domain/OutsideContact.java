package net.summerfarm.wms.manage.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutsideContact {

    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 品牌id
     */
    private Long tenantId;

    /**
     * 商户id
     */
    private Long storeId;

    /**
     * mname
     */
    private String mName;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 联系人
     */
    private String name;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * poi点位
     */
    private String poi;

    /**
     * 到仓库距离
     */
    private BigDecimal distance;

}
