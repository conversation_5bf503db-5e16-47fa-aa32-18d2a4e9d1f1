package net.summerfarm.wms.manage.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetBatchInventoryAddDetailFacadeReq implements Serializable {

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String cabinetCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Date produceDate;

    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    private Date qualityDate;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次编码")
    private String skuBatchCode;

    /**
     * 增加库存
     */
    @ApiModelProperty(value = "增加库存")
    private Integer addQuantity;
}