package net.summerfarm.wms.application.acl.provider.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.instore.StockTaskStorageProvider;
import net.summerfarm.wms.instore.dto.Res.BatchQueryStorageRes;
import net.summerfarm.wms.instore.dto.req.BatchQueryStorageReq;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import net.summerfarm.wms.starter.WmsApplication;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description
 * @Date 2023/2/16 11:51
 * @<AUTHOR>
 */
@SpringBootTest(classes = WmsApplication.class)
@Slf4j
class StockTaskStorageProviderImplTest {

    @Resource
    private StockTaskStorageProvider stockTaskStorageProvider;

    @Test
    void batchQueryStockTaskStorage() {
        BatchQueryStorageReq request = BatchQueryStorageReq.builder()
                .orderNo("01164999430709980")
                .type(StockStorageTypeEnums.AFTER_SALE_IN_NEW.getId())
                .build();
        DubboResponse<BatchQueryStorageRes> response = stockTaskStorageProvider.batchQueryStockTaskStorage(request);
        log.info("result:{}", JSONUtil.toJsonStr(response));
    }
}