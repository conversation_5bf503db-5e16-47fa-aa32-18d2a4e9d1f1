package net.summerfarm.wms.manage.web.req.skubatchcode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * @author: xdc
 * @date: 2024/1/24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBatchCodePrintCommand implements Serializable {
    private static final long serialVersionUID = -8417103188985476364L;

    /**
     * 库存仓编号
     */
    @NotNull(message = "请指定仓库")
    private Long warehouseNo;

    /**
     * 类型
     * 10-调拨入库, 11-采购入库, 13-拒收入库, 18-调拨回库, 19-退货入库, 20-缺货入库, 21-越仓入库, 22-拦截入库
     * 23-多出入库, 24-其他入库, 25-越库入库, 110-入库任务 120-转换任务
     */
    @NotNull(message = "请指定业务类型")
    private Integer bizType;

    /**
     * 任务操作id
     */
    @NotNull(message = "请指定业务id")
    private Long bizId;

    /**
     * sku编码
     */
    private String sku;

    /**
     * saasSkuId
     */
    private Long saasSkuId;

    /**
     * 本次需要打印次数
     */
    @NotNull(message = "请指定打印数量")
    private Integer printNumber;

    /**
     * 保质期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productionDate;

    /**
     * 批次号
     */
//    @NotEmpty(message = "请指定批次号")
    private String purchaseNo;

    /**
     * 货品查询唯一编码
     */
    private String skuBatchOnlyCode;

    /**
     * 入库单任务id
     */
    private Integer stockTaskProcessDetailId;

    /**
     * 采购预约单id
     */
    private Long stockArrangeId;
}
