package net.summerfarm.wms.manage.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.manage.web.service.StockTaskService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 越库出库任务创建
 * @author: dongcheng
 * @date: 2023/12/6
 */
@Component
@Slf4j
public class CrossOutStockTaskJob extends XianMuJavaProcessorV2 {

    @Resource
    private StockTaskService stockTaskService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("越库出库任务生成开始执行，{}", JSON.toJSONString(context));
        stockTaskService.saleAndAfterOutStockTaskForCross(context.getJobParameters());
        log.info("越库出库任务生成开始执行完成");
        return new ProcessResult(true);
    }

}
