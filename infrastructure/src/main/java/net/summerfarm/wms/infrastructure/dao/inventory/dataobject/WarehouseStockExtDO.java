package net.summerfarm.wms.infrastructure.dao.inventory.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2022/2/23  18:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseStockExtDO {

    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 库存仓编号
     */
    private Long warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 状态
     */
    private Integer status;


    public WarehouseStockExtDO(Long warehouseNo, String sku) {
        this.warehouseNo = warehouseNo;
        this.sku = sku;
    }


}
