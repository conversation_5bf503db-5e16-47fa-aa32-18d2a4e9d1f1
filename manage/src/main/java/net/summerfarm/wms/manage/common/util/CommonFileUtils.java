package net.summerfarm.wms.manage.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.RequestHolder;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Slf4j
public class CommonFileUtils {
    /**
     * 导出模板或报告文件
     *
     * @param response response
     * @param dic      文件路径
     * @param fileName 文件名
     */
    public static void exportFile(HttpServletResponse response, String dic, String fileName) {
        exportFile(response, dic, fileName, fileName);
    }

    /**
     * 导出模板或报告文件
     *
     * @param response response
     * @param dic      文件路径
     * @param originFileName 文件名
     * @param exportFileName 文件名
     */
    public static void exportFile(HttpServletResponse response, String dic, String originFileName, String exportFileName) {
        log.info("文件名   ："+dic + File.separator + originFileName);
        File file = new File(dic + File.separator + originFileName);
        if (!file.exists()) {
            throw new DefaultServiceException("文件不存在，请联系技术处理");
        }

        FileInputStream in = null;
        OutputStream out = null;
        try {
            in = FileUtils.openInputStream(file);
            out = response.getOutputStream();
            RequestHolder.getResponse().setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(exportFileName, "UTF-8"));

            int count = 0;
            byte[] by = new byte[1024];
            while ((count = in.read(by)) != -1) {
                out.write(by, 0, count);
            }
            out.flush();
        } catch (IOException e) {
            log.error("文件导出失败，", e);
            throw new DefaultServiceException("文件导出失败");
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("流关闭失败，", e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("流关闭失败，", e);
                }
            }
        }
    }

    /**
     * 生成excel文件到磁盘
     *
     * @param dir      目录
     * @param fileName 文件名
     * @param workbook workbook
     */
    public static void generateExcelFile(String dir, String fileName, Workbook workbook) {
        File report;
        OutputStream outputStream = null;
        try {
            String filePath = dir + File.separator + fileName;
            report = new File(filePath);
            if (!report.getParentFile().exists()) {
                boolean f = report.getParentFile().mkdirs();
                if (!f) {
                    throw new IOException("创建文件夹失败");
                }
            }
            boolean f = report.createNewFile();
            if (!f) {
                throw new IOException("创建文件失败");
            }

            outputStream = new FileOutputStream(report);
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("批量上架报告生成失败：", e);
            e.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流失败，", e);
            }
        }

    }
}

