package net.summerfarm.wms.facade.goods;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.GoodsCommonConfigTypeEnum;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.goods.client.provider.ProductsPropertyProvider;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.*;
import net.summerfarm.goods.client.resp.*;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.NumberUtils;
import net.summerfarm.wms.facade.goods.converter.GoodsInfoDTOConvert;
import net.summerfarm.wms.facade.goods.dto.*;
import net.summerfarm.goods.client.req.ProductSkuListReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class GoodsReadFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    @DubboReference
    private ProductsPropertyProvider productsPropertyProvider;

    @Resource
    private WarehouseStorageService warehouseStorageService;


    /**
     * 查询货品属性
     *
     * @param skuList
     * @return
     */
    public Map<String, GoodsPropertyDTO> mapGoodsPropertyBySkuList(List<String> skuList) {
        List<GoodsPropertyDTO> goodsPropertyDTOList = listGoodsPropertyBySkuList(skuList);
        return goodsPropertyDTOList.stream()
                .collect(Collectors.toMap(
                        GoodsPropertyDTO::getSku, Function.identity(),
                        (a, b) -> a));
    }

    /**
     * 查询货品属性
     *
     * @param skuList
     * @return
     */
    public List<GoodsPropertyDTO> listGoodsPropertyBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        ProductSkuBasicReq productSkuBasicReq = new ProductSkuBasicReq();
        productSkuBasicReq.setSkuList(skuList);
        DubboResponse<List<ProductSkuBasicResp>> listDubboResponse = productsSkuQueryProvider
                .selectSkuSubTypeList(productSkuBasicReq);
        if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
            log.error("请求货品服务发生异常 {}", skuList);
            throw new BizException("请求货品服务发生异常");
        }

        List<GoodsPropertyDTO> result = new ArrayList<>();
        List<ProductSkuBasicResp> productSkuBasicResps = listDubboResponse.getData();
        for (ProductSkuBasicResp productSkuBasicResp : productSkuBasicResps) {
            GoodsPropertyDTO goodsPropertyDTO = new GoodsPropertyDTO();

            goodsPropertyDTO.setSku(productSkuBasicResp.getSku());
            goodsPropertyDTO.setSubAgentType(productSkuBasicResp.getSubAgentType());
            goodsPropertyDTO.setIsSale2PurchaseGoods(
                    Integer.valueOf(1).equals(productSkuBasicResp.getSubAgentType()));

            result.add(goodsPropertyDTO);
        }

        return result;
    }


    /**
     * 查询货品属性
     * 废弃-货品接口不支持代仓搜索
     *
     * @param skuList
     * @return
     */
    @Deprecated
    public Map<String, GoodsInfoDTO> mapGoodsInfoBySkuList(Long tenantId, List<String> skuList) {
        List<GoodsInfoDTO> goodsPropertyDTOList = listGoodsInfoBySkuList(tenantId, skuList);
        return goodsPropertyDTOList.stream()
                .collect(Collectors.toMap(
                        GoodsInfoDTO::getSku, Function.identity(),
                        (a, b) -> a));
    }

    /**
     * 查询货品信息
     * 废弃-货品接口不支持代仓搜索
     *
     * @param skuList
     * @return
     */
    @Deprecated
    public List<GoodsInfoDTO> listGoodsInfoBySkuList(Long tenantId, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        ProductSkuListReq req = new ProductSkuListReq();
        req.setTenantId(tenantId);
        req.setSkuList(skuList);
        DubboResponse<List<ProductSkuDetailResp>> listDubboResponse = productsSkuQueryProvider
                .selectSkuDetailList(req);
        if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
            log.error("请求货品服务发生异常 {}", skuList);
            throw new BizException("请求货品服务发生异常");
        }

        List<GoodsInfoDTO> result = new ArrayList<>();
        List<ProductSkuDetailResp> respList = listDubboResponse.getData();
        for (ProductSkuDetailResp resp : respList) {
            result.add(GoodsInfoDTOConvert.convert(resp));
        }

        return result;
    }


    /**
     * 查询货品信息
     * @param tenantId
     * @param skuList
     * @return
     */
    public Map<String, GoodsInfoDTO> mapGoodsInfoByTidAndSkuList(Long tenantId, List<String> skuList) {
        List<GoodsInfoDTO> goodsInfoDTOs = listGoodsInfoByTidAndSkuList(tenantId, skuList);
        return goodsInfoDTOs.stream()
                .collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(), (a, b) -> a));
    }

    /**
     * 查询货品信息
     * @param tenantId
     * @param sku
     * @return
     */
    public GoodsInfoDTO findGoodsInfoBySkuList(Long tenantId, String sku) {
        if (StringUtils.isEmpty(sku) || tenantId == null){
            return null;
        }

        List<GoodsInfoDTO> list = listGoodsInfoByTidAndSkuList(tenantId, Collections.singletonList(sku));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 查询货品信息
     * @param tenantId
     * @param skuList
     * @return
     */
    public List<GoodsInfoDTO> listGoodsInfoByTidAndSkuList(Long tenantId, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList) || tenantId == null){
            return new ArrayList<>();
        }
        return listGoodsInfoBySkuListConvertWms(GoodsInfoQueryDTO.builder()
                .tenantId(tenantId)
                .skus(skuList)
                .build());
    }



    public Map<Long, GoodsInfoDTO> mapGoodsInfoByTidAndSaasSkuIdList(Long tenantId, List<Long> saasSkuIdList) {
        List<GoodsInfoDTO> goodsInfoDTOs = listGoodsInfoByTidAndSaasSkuIdList(tenantId, saasSkuIdList);
        return goodsInfoDTOs.stream()
                .collect(Collectors.toMap(GoodsInfoDTO::getSkuId, Function.identity(), (a, b) -> a));
    }
    public GoodsInfoDTO getGoodsInfoByTidAndSaasSkuIdList(Long tenantId, Long saasSkuId) {
        if (tenantId == null || saasSkuId == null){
            return null;
        }
        List<GoodsInfoDTO> goodsInfoDTOs = listGoodsInfoByTidAndSaasSkuIdList(tenantId,
                Arrays.asList(saasSkuId));
        return CollectionUtils.isEmpty(goodsInfoDTOs)? null : goodsInfoDTOs.get(0);
    }
    public List<GoodsInfoDTO> listGoodsInfoByTidAndSaasSkuIdList(Long tenantId, List<Long> saasSkuIdList) {
        if (CollectionUtils.isEmpty(saasSkuIdList) || tenantId == null){
            return new ArrayList<>();
        }
        return listGoodsInfoBySkuListConvertWms(GoodsInfoQueryDTO.builder()
                .tenantId(tenantId)
                .saasSkuIds(saasSkuIdList)
                .build());
    }

    public Map<Long, GoodsInfoDTO> mapGoodsInfoByTidAndPdIds(Long tenantId, List<Long> pdIds) {
        List<GoodsInfoDTO> goodsInfoDTOs = listGoodsInfoByTidAndPdIds(tenantId, pdIds);
        return goodsInfoDTOs.stream()
                .collect(Collectors.toMap(GoodsInfoDTO::getSpuId, Function.identity(), (a, b) -> a));
    }

    public List<GoodsInfoDTO> listGoodsInfoByTidAndPdIds(Long tenantId, List<Long> pdIds) {
        if (CollectionUtils.isEmpty(pdIds) || tenantId == null){
            return new ArrayList<>();
        }

        return listGoodsInfoBySkuListConvertWms(GoodsInfoQueryDTO.builder()
                .tenantId(tenantId)
                .pdIds(pdIds)
                .build());
    }

    /**
     * 通过skus查询鲜沐品及鲜沐代仓品
     * while true写法是为了方便业务代码中不出现丑陋代码，但是列表元素长度重点关注！！！
     *
     * @param goodsInfoQueryDTO goodsInfoQueryDTO
     * @return goods
     */
    public List<GoodsInfoDTO> listGoodsInfoBySkuListConvertWms(GoodsInfoQueryDTO goodsInfoQueryDTO) {
        if (CollectionUtils.isEmpty(Optional.ofNullable(goodsInfoQueryDTO.getSkus()).orElse(Lists.newArrayList())
                .stream()
                .filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
                .collect(Collectors.toList())) &&
                CollectionUtils.isEmpty(Optional.ofNullable(goodsInfoQueryDTO.getSaasSkuIds()).orElse(Lists.newArrayList())
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())) &&
                CollectionUtils.isEmpty(Optional.ofNullable(goodsInfoQueryDTO.getPdIds()).orElse(Lists.newArrayList())
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())) &&
                StringUtils.isEmpty(goodsInfoQueryDTO.getPdName())) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(goodsInfoQueryDTO.getWarehouseNo())) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(goodsInfoQueryDTO.getWarehouseNo().intValue());
            goodsInfoQueryDTO.setTenantId(Objects.nonNull(warehouseStorageCenter) ? warehouseStorageCenter.getTenantId() : goodsInfoQueryDTO.getTenantId());
        }
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setSkuIds(goodsInfoQueryDTO.getSaasSkuIds());
        productSkuPageQueryReq.setSkuList(goodsInfoQueryDTO.getSkus());
        List<String> customSpuCodeList = Optional.ofNullable(goodsInfoQueryDTO.getPdIds())
                .orElse(Lists.newArrayList()).stream().map(String::valueOf)
                .filter(s -> !StringUtils.isEmpty(s))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customSpuCodeList)) {
            productSkuPageQueryReq.setCustomSpuCodeList(customSpuCodeList);
        }
        productSkuPageQueryReq.setTitle(goodsInfoQueryDTO.getPdName());
        productSkuPageQueryReq.setTenantId(goodsInfoQueryDTO.getTenantId());
        productSkuPageQueryReq.setPageSize(200);
        productSkuPageQueryReq.setIsSearchXmAgent(true);
        List<ProductSkuDetailResp> res = Lists.newArrayList();
        int pageNum = NumberUtils.INTEGER_ONE;
        boolean hasNextPage;
        do {
            productSkuPageQueryReq.setPageIndex(pageNum);
            DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
            if (Objects.isNull(pageInfoDubboResponse.getData())) {
                break;
            }
            List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            res.addAll(list);
            pageNum++;
            hasNextPage = pageInfoDubboResponse.getData().isHasNextPage();
        } while (hasNextPage);

        return res.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList());
    }

    /**
     * 通过skus查询鲜沐品及鲜沐代仓品
     * while true写法，列表元素长度重点关注！！！
     *
     * @param skus skus
     * @return goods
     */
    public List<GoodsInfoDTO> listGoodsInfoBySkus(Long warehouseNo, List<String> skus, Long tenantId, List<Long> saasSkuIds) {
        if (CollectionUtils.isEmpty(skus) && CollectionUtils.isEmpty(saasSkuIds)) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(warehouseNo)) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo.intValue());
            tenantId = Objects.nonNull(warehouseStorageCenter) ? warehouseStorageCenter.getTenantId() : tenantId;
        }
        if (Objects.isNull(tenantId)) {
            tenantId = WmsConstant.XIANMU_TENANT_ID;
        }
        List<ProductSkuDetailResp> res = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(skus)) {
            // skus按200个分片
            List<List<String>> partitionSkuList = Lists.partition(skus, 200);
            for (List<String> skuList : partitionSkuList) {
                ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
                productSkuPageQueryReq.setSkuList(skuList);
                productSkuPageQueryReq.setTenantId(tenantId);
                productSkuPageQueryReq.setPageSize(200);
                productSkuPageQueryReq.setIsSearchXmAgent(true);
                int pageNum = NumberUtils.INTEGER_ONE;
                while (true) {
                    productSkuPageQueryReq.setPageIndex(pageNum);
                    DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
                    if (Objects.isNull(pageInfoDubboResponse.getData())) {
                        break;
                    }
                    List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    res.addAll(list);
                    if (!pageInfoDubboResponse.getData().isHasNextPage()){
                        break;
                    }
                    pageNum++;
                }
            }
        } else if (!CollectionUtils.isEmpty(saasSkuIds)) {
            // saasSkuIds按200个分片
            List<List<Long>> partitionSaasSkuIdList = Lists.partition(saasSkuIds, 200);
            for (List<Long> saasSkuIdList : partitionSaasSkuIdList) {
                ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
                productSkuPageQueryReq.setSkuIds(saasSkuIdList);
                productSkuPageQueryReq.setTenantId(tenantId);
                productSkuPageQueryReq.setPageSize(200);
                productSkuPageQueryReq.setIsSearchXmAgent(true);
                int pageNum = NumberUtils.INTEGER_ONE;
                while (true) {
                    productSkuPageQueryReq.setPageIndex(pageNum);
                    DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
                    if (Objects.isNull(pageInfoDubboResponse.getData())) {
                        break;
                    }
                    List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    res.addAll(list);
                    if (!pageInfoDubboResponse.getData().isHasNextPage()){
                        break;
                    }
                    pageNum++;
                }
            }
        }
        return res.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList());
    }

    /**
     * 筛选sku 符合体积，重量条件
     * 当skus和pdIds都为空的情况慎用！！内存警告
     * while true写法，列表元素长度重点关注！！！
     *
     * @param warehouseNo  仓库号
     * @param skus         sku列表，非必填
     * @param pdIds        pdId列表,非必填
     * @param isNullVolume 体积是否为空, 必填
     * @param isNullWeight 重量是否为空, 必填
     * @return skus
     */
    public List<String> filterSkusByVolumeAndWeight(Long warehouseNo, List<String> skus, List<Long> pdIds,
                                                    Boolean isNullVolume, Boolean isNullWeight) {
        if (Objects.isNull(isNullVolume) &&
                Objects.isNull(isNullWeight)) {
            return Lists.newArrayList();
        }
        Long tenantId = WmsConstant.XIANMU_TENANT_ID;
        if (Objects.nonNull(warehouseNo)) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo.intValue());
            tenantId = Objects.nonNull(warehouseStorageCenter) ? warehouseStorageCenter.getTenantId() : tenantId;
        }
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setSkuList(skus);
        productSkuPageQueryReq.setCustomSpuCodeList(Optional.ofNullable(pdIds)
                .orElse(Lists.newArrayList()).stream().map(String::valueOf).collect(Collectors.toList()));
        productSkuPageQueryReq.setTenantId(tenantId);
        productSkuPageQueryReq.setPageSize(200);
        productSkuPageQueryReq.setIsSearchXmAgent(true);
//        productSkuPageQueryReq.setIsNullVolume(isNullVolume);
//        productSkuPageQueryReq.setIsNullWeight(isNullWeight);
        List<String> res = Lists.newArrayList();
        int pageNum = NumberUtils.INTEGER_ONE;
        while (true) {
            productSkuPageQueryReq.setPageIndex(pageNum);
            DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
            if (Objects.isNull(pageInfoDubboResponse.getData())) {
                break;
            }
            List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            res.addAll(list.stream().map(ProductSkuDetailResp::getSku).collect(Collectors.toList()));
            pageNum++;
        }
        return res;
    }


    /**
     * 通过sku名称查询鲜沐品及鲜沐代仓品，限制200条
     * @param warehouseNo
     * @param pdName
     * @param tenantId
     * @return
     */
    public List<GoodsInfoDTO> listGoodsInfoByPdNameLimit200(Long warehouseNo, String pdName, Long tenantId) {
        if (StringUtils.isEmpty(pdName)) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(warehouseNo)) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo.intValue());
            tenantId = Objects.nonNull(warehouseStorageCenter) ? warehouseStorageCenter.getTenantId() : tenantId;
        }
        if (Objects.isNull(tenantId)) {
            tenantId = WmsConstant.XIANMU_TENANT_ID;
        }
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setTitle(pdName);
        productSkuPageQueryReq.setTenantId(tenantId);
        productSkuPageQueryReq.setPageSize(200);
        productSkuPageQueryReq.setIsSearchXmAgent(true);
        List<ProductSkuDetailResp> res = Lists.newArrayList();
        int pageNum = NumberUtils.INTEGER_ONE;
//        while (true) {
            productSkuPageQueryReq.setPageIndex(pageNum);
            DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
            if (Objects.isNull(pageInfoDubboResponse.getData())) {
//                break;
                return Lists.newArrayList();
            }
            List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
            if (CollectionUtils.isEmpty(list)) {
//                break;
                return Lists.newArrayList();
            }
            res.addAll(list);
//            pageNum++;
//        }
        return res.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList());
    }

    /**
     * 通过skus查询鲜沐品及鲜沐代仓品
     * while true写法，列表元素长度重点关注！！！
     *
     * @param pdIds pdIds
     * @return goods
     */
    public List<GoodsInfoDTO> listGoodsInfoByPdIds(Long warehouseNo, List<Long> pdIds, Long tenantId) {
        if (CollectionUtils.isEmpty(Optional.ofNullable(pdIds).orElse(Lists.newArrayList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList()))) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(warehouseNo)) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo.intValue());
            tenantId = Objects.nonNull(warehouseStorageCenter) ? warehouseStorageCenter.getTenantId() : tenantId;
        }
        if (Objects.isNull(tenantId)) {
            tenantId = WmsConstant.XIANMU_TENANT_ID;
        }
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setCustomSpuCodeList(pdIds.stream().map(String::valueOf).collect(Collectors.toList()));
        productSkuPageQueryReq.setTenantId(tenantId);
        productSkuPageQueryReq.setPageSize(200);
        productSkuPageQueryReq.setIsSearchXmAgent(true);
        List<ProductSkuDetailResp> res = Lists.newArrayList();
        int pageNum = NumberUtils.INTEGER_ONE;
        while (true) {
            productSkuPageQueryReq.setPageIndex(pageNum);
            DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
            if (Objects.isNull(pageInfoDubboResponse.getData())) {
                break;
            }
            List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            res.addAll(list);
            pageNum++;
        }
        return res.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList());
    }

    /**
     * 分页查询货品信息
     *
     * @param pageGoodsInfoDTO 分页参数必填，仓库号和租户id选填一个，其余参数非必填
     * @return goods
     */
    public PageInfo<GoodsInfoDTO> pageGoodsInfo(PageGoodsInfoDTO pageGoodsInfoDTO) {
        if (Objects.isNull(pageGoodsInfoDTO.getPageIndex()) || Objects.isNull(pageGoodsInfoDTO.getPageSize())) {
            throw new BizException("分页查询货品分页参数不可为空");
        }
        if (Objects.nonNull(pageGoodsInfoDTO.getWarehouseNo())) {
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(pageGoodsInfoDTO.getWarehouseNo().intValue());
            pageGoodsInfoDTO.setTenantId(Objects.nonNull(warehouseStorageCenter) ?
                    warehouseStorageCenter.getTenantId() : pageGoodsInfoDTO.getTenantId());
        }
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setCustomSpuCodeList(Optional.ofNullable(pageGoodsInfoDTO.getPdIds()).orElse(Lists.newArrayList())
                .stream().map(String::valueOf).collect(Collectors.toList()));
        productSkuPageQueryReq.setTenantId(pageGoodsInfoDTO.getTenantId());
        productSkuPageQueryReq.setPageSize(pageGoodsInfoDTO.getPageSize());
        productSkuPageQueryReq.setIsSearchXmAgent(true);
        productSkuPageQueryReq.setPageIndex(pageGoodsInfoDTO.getPageIndex());
        DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);

        List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();

        PageInfo<GoodsInfoDTO> res = new PageInfo<>(list.stream().map(GoodsInfoDTOConvert::convertXM).collect(Collectors.toList()));
        res.setTotal(pageInfoDubboResponse.getData().getTotal());
        return res;
    }

    /**
     * 通过pdIds和tenantId查询货品map
     *
     * @param pdIds
     * @param tenantId
     * @return
     */
    public Map<String, GoodsInfoDTO> mapGoodsInfoByPdIds(Long warehouseNo, List<Long> pdIds, Long tenantId) {
        if (CollectionUtils.isEmpty(Optional.ofNullable(pdIds).orElse(Lists.newArrayList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList()))) {
            return Maps.newHashMap();
        }
        List<GoodsInfoDTO> goodsInfoDTOS = listGoodsInfoByPdIds(warehouseNo, pdIds, tenantId);
        return goodsInfoDTOS.stream().collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 通过pdIds和tenantId查询货品map
     *
     * @param skus
     * @param tenantId
     * @return
     */
    public Map<String, GoodsInfoDTO> mapGoodsInfoBySkus(Long warehouseNo, List<String> skus, Long tenantId) {
        if (CollectionUtils.isEmpty(skus)) {
            return Maps.newHashMap();
        }
        List<GoodsInfoDTO> goodsInfoDTOS = listGoodsInfoBySkus(warehouseNo, skus, tenantId, null);
        return goodsInfoDTOS.stream().collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 查询货品扩展属性
     *
     * @param skus
     * @return
     */
    public Map<String, ProductsPropertyValueDTO> mapGoodsProperty(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)){
            return Maps.newHashMap();
        }
        PropertyValueQueryReq propertyValueQueryReq = new PropertyValueQueryReq();
        propertyValueQueryReq.setSkuList(skus);
        DubboResponse<List<ProductsPropertyValueResp>> listDubboResponse = productsPropertyProvider.selectPropertyValueList(propertyValueQueryReq);
        List<ProductsPropertyValueResp> data = listDubboResponse.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, String>> propertyMap = data.stream().filter(item -> Objects.nonNull(item.getProductsPropertyValue()))
                .collect(Collectors.groupingBy(ProductsPropertyValueResp::getSku,
                        Collectors.toMap(ProductsPropertyValueResp::getProductsPropertyName,
                                ProductsPropertyValueResp::getProductsPropertyValue, (o1, o2) -> o1)));

        return data.stream().map(item -> {
            String warnTime = propertyMap.getOrDefault(item.getSku(), Maps.newHashMap()).get(ProductsPropertyEnum.WARN_TIME.getContent());
            return ProductsPropertyValueDTO.builder()
                    .sku(item.getSku())
                    .warnTime(StringUtils.isEmpty(warnTime) ? null : Integer.valueOf(warnTime))
                    .build();
        }).collect(Collectors.toMap(ProductsPropertyValueDTO::getSku, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 查询货品属性
     * @param name 属性名称
     * @param type 属性集合 ：0=关键实性 1=销售属性
     * @return 结果
     */
    public ProductsPropertyDTO findProductsProperty(String name,Integer type) {
        if(StringUtils.isEmpty(name) || type == null){
            return null;
        }
        ProductsPropertyQueryReq queryReq = new ProductsPropertyQueryReq();
        queryReq.setName(name);
        queryReq.setTypeIds(Lists.newArrayList(type));
        DubboResponse<List<ProductsPropertyResp>> resp = productsPropertyProvider.selectPropertyList(queryReq);
        if (Objects.isNull(resp) || Objects.isNull(resp.getData()) || CollectionUtils.isEmpty(resp.getData())) {
            return null;
        }
        List<ProductsPropertyResp> data = resp.getData();
        ProductsPropertyResp productsPropertyResp = data.get(0);

        ProductsPropertyDTO dto = new ProductsPropertyDTO();
        dto.setId(productsPropertyResp.getId());
        dto.setName(productsPropertyResp.getName());
        dto.setType(productsPropertyResp.getType());
        dto.setFormatType(productsPropertyResp.getFormatType());
        dto.setFormatStr(productsPropertyResp.getFormatStr());
        dto.setStatus(productsPropertyResp.getStatus());
        dto.setCreator(productsPropertyResp.getCreator());
        dto.setCreateTime(productsPropertyResp.getCreateTime());

        return dto;
    }

    /**
     * 查询货品属性值
     * @param pdId pdId
     * @param sku SKU
     * @param productsPropertyId 属性ID
     * @return 结果
     */
    public ProductsPropertyValueDTO findProductsPropertyValue(Long pdId,String sku, Integer productsPropertyId){
        if(StringUtils.isEmpty(sku)){
            return null;
        }
        PropertyValueQueryReq req = new PropertyValueQueryReq();
        req.setXmSpuIds(Collections.singletonList(pdId));
        req.setSkuList(Collections.singletonList(sku));
        req.setPropertyIds(Collections.singletonList(productsPropertyId));
        DubboResponse<List<ProductsPropertyValueResp>> resp = productsPropertyProvider.selectPropertyValueList(req);
        if (Objects.isNull(resp) || Objects.isNull(resp.getData()) || CollectionUtils.isEmpty(resp.getData())) {
            return null;
        }
        List<ProductsPropertyValueResp> data = resp.getData();
        ProductsPropertyValueResp respOneData = data.get(0);

        ProductsPropertyValueDTO dto = new ProductsPropertyValueDTO();
        dto.setSku(respOneData.getSku());
        if(respOneData.getXmSpuId() != null){
            dto.setPdId(respOneData.getXmSpuId().intValue());
        }
        dto.setProductsPropertyId(respOneData.getProductsPropertyId());
        dto.setProductsPropertyValue(respOneData.getProductsPropertyValue());

        return dto;
    }

    /**
     * 获取耗材以及类目
     * @return
     */
    public String getMaterialFirstCategory(){
        GoodsCommonConfigQueryReq req = new GoodsCommonConfigQueryReq();
        req.setConfigType(GoodsCommonConfigTypeEnum.FIRST_CATEGORY_CONSUMABLES.getValue());
        DubboResponse<GoodsCommonConfigResp> resp = productsSkuQueryProvider.queryGoodsCommonConfig(req);
        if (Objects.isNull(resp) || Objects.isNull(resp.getData())) {
            return null;
        }
        GoodsCommonConfigResp data = resp.getData();
        if (StringUtils.isEmpty(data.getConfigValue())) {
            return null;
        }
        return data.getConfigValue();
    }


}
