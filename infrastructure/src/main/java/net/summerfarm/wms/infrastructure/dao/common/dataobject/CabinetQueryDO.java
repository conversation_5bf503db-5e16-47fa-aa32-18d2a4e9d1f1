package net.summerfarm.wms.infrastructure.dao.common.dataobject;

import lombok.*;
import net.summerfarm.wms.infrastructure.dao.base.dataobject.BaseQueryDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 库位查询DO
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class CabinetQueryDO extends BaseQueryDO implements Serializable {

    private static final long serialVersionUID = 95500924118657283L;

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 库位类型（1：高位货架，2：轻型货架，3：地堆）
     */
    private Integer cabinetType;

    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    private Integer purpose;

    /**
     * 长（单位：m）
     */
    private Double length;

    /**
     * 宽（单位：m）
     */
    private Double width;

    /**
     * 高（单位：m）
     */
    private Double high;

    /**
     * 所属库区id
     */
    private Long zoneId;

    /**
     * 所属库区编码
     */
    private String zoneCode;

    /**
     * 所属库区名称
     */
    private String zoneName;

    /**
     * 是否允许混放sku（0：不允许，1：允许）
     */
    private Integer allowMixedSku;

    /**
     * 允许sku混放数量
     */
    private Integer allowMixedSkuQuantity;

    /**
     * 是否允许混放效期（0：不允许，1：允许）
     */
    private Integer allowMixedPeriod;

    /**
     * 允许效期混放数量
     */
    private Integer allowMixedPeriodQuantity;

    /**
     * 是否允许混放批次（0：不允许，1：允许）
     */
    private Integer allowMixedBatch;

    /**
     * 允许批次混放数量
     */
    private Integer allowMixedBatchQuantity;

    /**
     * 库位顺序
     */
    private Integer sequence;

    /**
     * 库位状态（0：禁用，1：启用）
     */
    private Integer cabinetStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 承重量（单位：kg）
     */
    private Double loadWeight;

    /**
     * 初始化库位标签（0：非系统初始化库位，1：系统初始化库位）
     */
    private Integer initOption;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 库位编码集合
     */
    private List<String> cabinetCodes;

    /**
     * 库位编码模糊
     */
    private String cabinetCodeLike;

    /**
     * 库位类型集合（1：高位货架，2：轻型货架，3：地堆）
     */
    private List<Integer> cabinetTypes;

    /**
     * 库位属性集合（1：拣货区，2：存储区，3：暂存区）
     */
    private List<Integer> purposes;

    /**
     * idGt
     */
    private Long idGt;

}
