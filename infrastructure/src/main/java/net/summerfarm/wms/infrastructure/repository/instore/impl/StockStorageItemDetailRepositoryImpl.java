package net.summerfarm.wms.infrastructure.repository.instore.impl;

import com.google.common.collect.Lists;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItemDetail;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemDetailRepository;
import net.summerfarm.wms.infrastructure.dao.instore.StockStorageItemDetailDAO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO;
import net.summerfarm.wms.infrastructure.repository.instore.converter.StockStorageItemDetailConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/16  14:39
 */
@Repository
public class StockStorageItemDetailRepositoryImpl implements StockStorageItemDetailRepository {

    @Resource
    StockStorageItemDetailDAO stockStorageItemDetailDAO;

    @Override
    public void saveStockStorageItemDetail(StockStorageItemDetail stockStorageItemDetail) {
        StockStorageItemDetailDO detailDO = StockStorageItemDetailConverter.INSTANCE.convert(stockStorageItemDetail);
        stockStorageItemDetailDAO.insertDetailDO(detailDO);
    }

    @Override
    public void saveBatchStockStorageItemDetail(List<StockStorageItemDetail> storageItemDetails) {
        List<StockStorageItemDetailDO> saveBatchDetailDO =
                storageItemDetails.stream().map(StockStorageItemDetailConverter.INSTANCE::convert).collect(Collectors.toList());
        stockStorageItemDetailDAO.insertBatch(saveBatchDetailDO);
        return;
    }

    @Override
    public List<StockStorageItemDetail> selectItemDetail(StockStorageItemDetail stockStorageItemDetail) {

        StockStorageItemDetailDO detailDO = StockStorageItemDetailConverter.INSTANCE.convert(stockStorageItemDetail);
        List<StockStorageItemDetailDO> stockStorageItemDetailDOS = stockStorageItemDetailDAO.selectByDetailDO(detailDO);
        List<StockStorageItemDetail> stockStorageItemDetailList =
                stockStorageItemDetailDOS.stream().map(StockStorageItemDetailConverter.INSTANCE::convert).collect(Collectors.toList());
        return stockStorageItemDetailList;
    }

    @Override
    public List<StockStorageItemDetail> selectItemDetailBatch(List<Long> itemDetailBatch) {
        if (CollectionUtils.isEmpty(itemDetailBatch)) {
            return Lists.newArrayList();
        }
        List<StockStorageItemDetailDO> stockStorageItemDetailDOS = stockStorageItemDetailDAO.selectByDetailBatchDO(itemDetailBatch);
        List<StockStorageItemDetail> stockStorageItemDetailList =
                stockStorageItemDetailDOS.stream().map(StockStorageItemDetailConverter.INSTANCE::convert).collect(Collectors.toList());
        return stockStorageItemDetailList;
    }

    @Override
    public void updateItemDetail(StockStorageItemDetail stockStorageItemDetail) {
        StockStorageItemDetailDO detailDO = StockStorageItemDetailConverter.INSTANCE.convert(stockStorageItemDetail);
        stockStorageItemDetailDAO.updateItemDetail(detailDO);
    }

    @Override
    public void changeItemDetailQuantity(StockStorageItemDetail stockStorageItemDetail) {
        StockStorageItemDetailDO detailDO = StockStorageItemDetailConverter.INSTANCE.convert(stockStorageItemDetail);
        stockStorageItemDetailDAO.updateItemDetailQuantityAndContainerNo(detailDO);
    }

    @Override
    public void updateItemDetailAddTime(StockStorageItemDetail stockStorageItemDetail) {
        StockStorageItemDetailDO detailDO = StockStorageItemDetailConverter.INSTANCE.convert(stockStorageItemDetail);

        stockStorageItemDetailDAO.updateItemDetailAddTime(detailDO);
    }

}
