package net.summerfarm.wms.domain.processingtask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.param.ProcessingConfigQueryParam;

import java.util.List;
import java.util.Map;

public interface ProcessingConfigRepository {


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProcessingConfig queryById(Long id);


    /**
     * 统计总行数
     *
     * @param processingConfig 查询条件
     * @return 总行数
     */
    long count(ProcessingConfig processingConfig);

    /**
     * 更新
     * @param updateProcessingConfig 加工规则配置id
     */
    void update(ProcessingConfig updateProcessingConfig);

    /**
     * 新增数据
     *
     * @param processingConfig 实例对象
     * @return 影响行数
     */
    Long insert(ProcessingConfig processingConfig);

    /**
     * 查询规则
     * @param warehouseNo
     * @param materialSkuCode
     * @param productSkuCode
     * @return
     */
    /**
     * 根据库存仓编码和SKU编码查询
     * @param warehouseNo 库存仓编码
     * @param productSkuCode 成品SKU编码
     * @param type 加工类型
     * @return 查询结果
     */
    ProcessingConfig queryByWarehouseAndSkuCodeAndType(Integer warehouseNo, String productSkuCode, Integer type);

    /**
     * 查询规则列表
     * @param warehouseNo 仓库编码
     * @param productSkuCode 成品sku集合
     * @param type 加工类型
     * @return 规则列表
     */
    List<ProcessingConfig> listByWarehouseAndProductSkuCodeAndType(Integer warehouseNo, String productSkuCode, Integer type);

    /**
     * 查询规则列表map
     * @param warehouseNo 仓库编码
     * @param productSkuCode 成品sku集合
     * @param type 加工类型
     * @return 规则列表map
     */
    Map<String, List<ProcessingConfig>> mapByWarehouseAndProductSkuCodeAndType(Integer warehouseNo, String productSkuCode, Integer type);


    /**
     * 查询规则列表
     * @param warehouseNo 仓库编码
     * @param productSkuCodeList 成品sku集合
     * @param type 加工类型
     * @return 规则列表
     */
    List<ProcessingConfig> listByWarehouseAndProductSkuCodeListAndType(Integer warehouseNo, List<String> productSkuCodeList, Integer type);

    /**
     * 查询规则列表map
     * @param warehouseNo 仓库编码
     * @param productSkuCodeList 成品sku集合
     * @param type 加工类型
     * @return 规则列表
     */
    Map<String, List<ProcessingConfig>> mapByWarehouseAndProductSkuCodeListAndType(Integer warehouseNo, List<String> productSkuCodeList, Integer type);

    /**
     * 分页查询
     * @param processingConfig 查询条件
     * @return 查询结果
     */
    PageInfo<ProcessingConfig> queryAll(ProcessingConfigQueryParam queryParam, Integer pageIndex, Integer pageSize);

    /**
     * 根据加工规格配置id作废
     *
     * @param processingConfigId 加工规格配置id
     * @param operatorName
     * @return 操作结果
     */
    Long updateInvalidByProcessingConfigId(Long processingConfigId, String operatorName);

    /**
     * 根据加工规则编码进行查询
     * @param processingConfigCode 加工规则编码
     * @return 查询结果
     */
    ProcessingConfig queryByProcessingConfigCode(String processingConfigCode);
}
