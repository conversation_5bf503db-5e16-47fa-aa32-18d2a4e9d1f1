package net.summerfarm.wms.domain.safeinventorylock.repository;

import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;

/**
 * 安全库存锁定表命令仓储接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface WmsSafeInventoryLockCommandRepository {

    /**
     * 插入记录
     * @param param 命令参数
     * @return 实体
     */
    WmsSafeInventoryLockEntity insertSelective(WmsSafeInventoryLockCommandParam param);

    /**
     * 根据ID更新记录
     * @param param 命令参数
     * @return 更新行数
     */
    int updateSelectiveById(WmsSafeInventoryLockCommandParam param);

    /**
     * 根据ID删除记录
     * @param id 主键ID
     * @return 删除行数
     */
    int remove(Long id);

    /**
     * 根据锁定编号更新锁定状态
     * @param id 锁定编号
     * @param lockStatus 锁定状态
     * @return 更新行数
     */
    int updateLockStatusByLockNo(Long id, Integer lockStatus);

    /**
     * 根据锁定编号更新锁定数量
     * @param id id
     * @param unlockQuantity 锁定数量
     * @param updateOperator 更新人
     * @return 更新行数
     */
    int updateLockQuantityById(Long id, Integer unlockQuantity, String updateOperator, Integer oldLockQuantity);
}
