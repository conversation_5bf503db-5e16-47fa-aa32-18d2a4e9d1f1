package net.summerfarm.wms.infrastructure.repository.stockTransfer.impl;

import net.summerfarm.wms.domain.stockTransfer.StockTransferItemCommandRepository;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferItemCreateParam;
import net.summerfarm.wms.infrastructure.dao.stockTransfer.StockTransferItemDAO;
import net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.wms.infrastructure.repository.stockTransfer.converter.StockTransferConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Repository
public class StockTransferItemCommandRepositoryImpl implements StockTransferItemCommandRepository {

    @Resource
    private StockTransferItemDAO stockTransferItemDAO;

    @Override
    public void batchInsert(List<StockTransferItemCreateParam> transferItemCreateParamList) {
        if (CollectionUtils.isEmpty(transferItemCreateParamList)) {
            return;
        }
        List<StockTransferItemDO> transferItemDOList = transferItemCreateParamList.stream()
                .map(StockTransferConverter.INSTANCE::convert).collect(Collectors.toList());
        stockTransferItemDAO.batchInsert(transferItemDOList);
    }
}
