package net.summerfarm.wms.infrastructure.dao.skushare.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wms.infrastructure.dao.skushare.entity.InventorySkuShareTransferDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 规格共享转换表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-08
 */
@Mapper
public interface InventorySkuShareTransferDAO extends BaseMapper<InventorySkuShareTransferDO> {

    Integer updateSkuShare(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku, @Param("remainingTransferOutQuantityChange") Integer remainingTransferOutQuantityChange,
                           @Param("remainingTransferInQuantityChange") Integer remainingTransferInQuantityChange, @Param("operator") String operator);

}
