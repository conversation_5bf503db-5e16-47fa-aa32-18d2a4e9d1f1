package net.summerfarm.wms.manage.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2023/9/13 14:30
 * @<AUTHOR>
 */
@Data
public class SkuBaseInfoDTO {

    private Long invId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 商品id
     */
    private Long pdId;
    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * 规格
     */
    private String weight;


    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    private String packaging;
    private Integer storageArea;
    private Integer categoryId;
    /**
     * 类型 0 自营 1 代仓
     */
    private Integer pdAttribute;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
     */
    private Integer outdated;

    /**
     * 所属大客户ID
     */
    private Integer adminId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku图片
     */
    private String skuPic;

    private Integer isDomestic;
}
