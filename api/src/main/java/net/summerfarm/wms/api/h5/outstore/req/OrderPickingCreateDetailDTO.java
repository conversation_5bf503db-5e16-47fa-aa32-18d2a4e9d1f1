package net.summerfarm.wms.api.h5.outstore.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.util.DateUtil;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 拣货生成明细
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderPickingCreateDetailDTO implements Serializable {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 来源id(如出库任务编号)
     */
    String sourceId;

    /**
     * 来源类型（如销售出库、补货出库、出样出库）
     */
    Integer sourceType;

    /**
     * 来源单号（如订单号）-没有时可不传
     */
    String sourceOrderNo;

    /**
     * sku
     */
    @NotNull(message = "sku不可为空")
    String sku;

    /**
     * 库位
     */
    @NotNull(message = "库位不可为空")
    String cabinetNo;

    /**
     * 库区
     */
    @NotNull(message = "库区不可为空")
    String zone;

    /**
     * 生产日期
     */
    @NotNull(message = "生产日期不可为空")
    LocalDate produceDate;

    /**
     * 保质期
     */
    @NotNull(message = "保质期不可为空")
    LocalDate qualityDate;

    /**
     * 应拣数量
     */
    @NotNull(message = "数量不可为空")
    Integer quantity;

    /**
     * 类目类型
     */
    Integer categoryType;
    /**
     * 自营-代仓
     */
    Integer skuType;
    /**
     * 国产|进口
     */
    Integer origin;
    /**
     * 规格
     */
    String specification;
    /**
     * 包装
     */
    String packaging;
    /**
     * 温区
     */
    String temperature;
    /**
     * 商品名称
     */
    String pdName;

    /**
     * 预计出库时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    Date expectTime;

    /**
     * 目标库存仓库
     * 调拨需要此字段
     */
    Long targetWarehouseNo;

    public String dupKey() {
        return sku + produceDate + qualityDate + cabinetNo;
    }

}
