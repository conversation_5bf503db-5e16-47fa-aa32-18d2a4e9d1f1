package net.summerfarm.wms.domain.skushare.domainService;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.OpenSaleEnum;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.areasku.AreaSkuRepository;
import net.summerfarm.wms.domain.areasku.domainobject.AreaSku;
import net.summerfarm.wms.domain.config.domainobject.ConversionSkuQuantity;
import net.summerfarm.wms.domain.config.repository.ConversionSkuQuantityRepository;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.SkuShareRate;
import net.summerfarm.wms.facade.wnc.FenceQueryFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SkuShareRateCalculator {

    @Autowired
    private ConversionSkuQuantityRepository conversionSkuQuantityRepository;

    @Autowired
    private WarehouseInventoryService warehouseInventoryService;

    @Autowired
    private AreaSkuRepository areaSkuRepository;

    @Autowired
    private AreaStoreRepository areaStoreRepository;

    @Autowired
    private FenceQueryFacade fenceQueryFacade;

    /**
     * 计算共享比例
     *
     * @param warehouseNo     仓库号
     * @param sharedSku       共享的sku（转出的sku）
     * @param transferRateMap 转换配置，key为转入的sku，value为sku转换比例（格式为整数比，形如3:7）
     * @return 注意：返回的列表里包含共享sku（转出sku）的数据
     */
    public List<SkuShareRate> calculate(Integer warehouseNo, String sharedSku, Map<String, String> transferRateMap) {
        List<String> allSku = Lists.newArrayList();
        allSku.add(sharedSku);
        allSku.addAll(transferRateMap.keySet());
        // 获取所有sku的历史七天峰值销量
        Map<String, Integer> saleCountMap = getHistorySaleCount(warehouseNo, allSku);
        // 获取所有sku的上架状态
        Map<String, Boolean> onSaleStatusMap = Maps.newHashMap();
        allSku.forEach(sku -> onSaleStatusMap.put(sku, getSkuOnSaleStatus(warehouseNo, sku)));
        // 获取sku的仓库库存
        Map<String, Integer> quantityMap = getStockQuantity(warehouseNo, allSku);
        // 为了方便计算将sharedSku也添加到转换比例map，值为1:1
        transferRateMap.put(sharedSku, "1:1");
        // 计算规格共享比例
        Map<String, String> shareRateMap = doCalculate(allSku, saleCountMap, onSaleStatusMap, quantityMap, transferRateMap);
        // 组装返回结果
        List<SkuShareRate> skuShareRates = Lists.newArrayList();
        for (String sku : allSku) {
            SkuShareRate skuShareRate = SkuShareRate.builder()
                    .transferOutSku(sharedSku)
                    .transferInSku(sku)
                    .sevenDayPeakSales(saleCountMap.get(sku))
                    .onSale(onSaleStatusMap.get(sku))
                    .quantity(quantityMap.get(sku))
                    .shareRate(shareRateMap.get(sku))
                    .transferRate(transferRateMap.get(sku))
                    .build();
            skuShareRates.add(skuShareRate);
        }
        return skuShareRates;
    }

    /**
     * 列表中的共享比例之和是否小于等于1
     *
     * @param shareRates
     * @return
     */
    public boolean sumOfShareRatesLeOne(List<String> shareRates) {
        if (CollectionUtils.isEmpty(shareRates)) {
            return true;
        }
        // 例如sku共享比例分别为：1:5、1:6和5:8，则共享比例之和为
        // (1 * 24 + 1 * 20 + 5 * 15) / 120 < 1
        long lcm = 1L;
        for (String shareRate : shareRates) {
            String[] shareRateSplit = shareRate.split(":");
            lcm = lcm(lcm, Integer.parseInt(shareRateSplit[1]));
        }
        long numerator = 0L;
        for (String shareRate : shareRates) {
            String[] shareRateSplit = shareRate.split(":");
            numerator += Integer.parseInt(shareRateSplit[0]) * lcm / Integer.parseInt(shareRateSplit[1]);
        }
        return numerator <= lcm;
    }

    private Map<String, Integer> getHistorySaleCount(Integer warehouseNo, List<String> skus) {
        Date yesterday = Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<ConversionSkuQuantity> conversionSkuQuantities = conversionSkuQuantityRepository.selectList(warehouseNo, skus, yesterday);
        Map<String, Integer> saleCountMap = conversionSkuQuantities.stream().collect(Collectors.toMap(ConversionSkuQuantity::getSku,
                x -> x.getMaxSaleSeven() != null ? x.getMaxSaleSeven().intValue() : NumberUtils.INTEGER_ZERO, (v1, v2) -> v1));
        for (String sku : skus) {
            if (!saleCountMap.containsKey(sku)) {
                saleCountMap.put(sku, NumberUtils.INTEGER_ZERO);
            }
        }
        return saleCountMap;
    }

    private boolean getSkuOnSaleStatus(Integer warehouseNo, String sku) {
        WarehouseInventoryMappingVO condition = new WarehouseInventoryMappingVO();
        condition.setWarehouseNo(warehouseNo);
        condition.setSku(sku);
        List<WarehouseInventoryMappingVO> warehouseInventoryMappingVOS = warehouseInventoryService.selectVo(condition);
        if (CollectionUtils.isEmpty(warehouseInventoryMappingVOS)) {
            log.info("当前库存仓无任何城配仓映射信息(未查询到任何数据)，warehouseNo:{}，sku:{}", warehouseNo, sku);
            return false;
        }
        List<Integer> storeNos = warehouseInventoryMappingVOS.stream().map(WarehouseInventoryMappingVO::getStoreNo)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeNos)) {
            log.info("当前库存仓无任何城配仓映射信息(数据非空过滤后为空集)，warehouseNo:{}，sku:{}", warehouseNo, sku);
            return false;
        }
        List<Integer> areaNoSet = fenceQueryFacade.queryFenceListByStoreNoList(storeNos);
        if (CollectionUtils.isEmpty(areaNoSet)) {
            log.info("当前库存仓无任何运营城市，warehouseNo:{}，storeNos:{}", warehouseNo, storeNos);
            return false;
        }
        List<AreaSku> areaSkuList = areaSkuRepository.selectBySkuAndAreaNos(sku, Lists.newArrayList(areaNoSet));
        if (CollectionUtils.isEmpty(areaSkuList)) {
            log.info("查询城市商品数据为空，sku:{}，areaNoSet:{}", sku, areaNoSet);
            return false;
        }
        List<Boolean> onSaleList = areaSkuList.stream().map(AreaSku::getOnSale).collect(Collectors.toList());
        List<Integer> openSaleList = areaSkuList.stream().map(AreaSku::getOpenSale).collect(Collectors.toList());
        if (onSaleList.contains(Boolean.TRUE)) {
            return true;
        }
        if (openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON.ordinal()) ||
                openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON_FOREVER.ordinal())) {
            return true;
        }
        return false;
    }

    private Map<String, Integer> getStockQuantity(Integer warehouseNo, List<String> skus) {
        List<AreaStore> areaStores = areaStoreRepository.listSkuStockBySku(warehouseNo.longValue(), skus);
        Map<String, Integer> quantityMap = areaStores.stream().collect(Collectors.toMap(AreaStore::getSku, AreaStore::getQuantity, (v1, v2) -> v1));
        for (String sku : skus) {
            if (!quantityMap.containsKey(sku)) {
                quantityMap.put(sku, NumberUtils.INTEGER_ZERO);
            }
        }
        return quantityMap;
    }

    private Map<String, String> doCalculate(List<String> skus, Map<String, Integer> saleCountMap, Map<String, Boolean> onSaleStatusMap, Map<String, Integer> quantityMap, Map<String, String> transferRateMap) {
        // 所有sku的历史七天峰值销量都为0则上架的sku等比例共享，未上架的sku不共享
        boolean allSkuHaveNoSales = saleCountMap.values().stream().allMatch(x -> x == 0);
        if (allSkuHaveNoSales) {
            Map<String, String> shareRateMap = Maps.newHashMap();
            long onSaleSkuCount = onSaleStatusMap.values().stream().filter(status -> status).count();
            for (String sku : skus) {
                if (onSaleStatusMap.get(sku)) {
                    shareRateMap.put(sku, getReducedFractionRate(1L, onSaleSkuCount));
                } else {
                    shareRateMap.put(sku, getReducedFractionRate(0L, skus.size()));
                }
            }
            return shareRateMap;
        }
        // 将所有sku转换成相同规格再计算历史七天峰值销量比。例如sku转换比例为：a:a=1:1, a:b=2:5, a:c=3:4，
        // a、b、c的历史七天峰值销量分别为10、20、30，且a、b、c均为上架状态，则a的历史七天峰值销量比为：
        // (10 * 5 * 4) / (10 * 5 * 4 + 20 * 2 * 4 + 30 * 3 * 5)
        long product = 1L;
        for (Map.Entry<String, String> entry : transferRateMap.entrySet()) {
            String[] rateSplit = entry.getValue().split(":");
            product *= Integer.parseInt(rateSplit[1]);
        }
        long sum = 0L;
        for (Map.Entry<String, Integer> entry : saleCountMap.entrySet()) {
            String sku = entry.getKey();
            String[] rateSplit = transferRateMap.get(sku).split(":");
            Integer saleCount = entry.getValue();
            sum += saleCount * Integer.parseInt(rateSplit[0]) * product / Integer.parseInt(rateSplit[1]);
        }

        Map<String, String> shareRateMap = Maps.newHashMap();
        for (String sku : skus) {
            int saleCount = saleCountMap.get(sku);
            // 历史七天峰值销量为0则共享比例也为0
            if (saleCount == 0) {
                shareRateMap.put(sku, getReducedFractionRate(0, sum));
                continue;
            }
            // 仓库库存足够（仓库库存大于等于历史其他峰值销量的一半）或者sku未上架时不进行共享
            int quantity = quantityMap.get(sku) != null ? quantityMap.get(sku) : 0;
            if (quantity >= saleCount / 2F || !onSaleStatusMap.get(sku)) {
                shareRateMap.put(sku, getReducedFractionRate(0, sum));
            } else {
                // 仓库库存不足且sku已上架时按照历史七天峰值销量比进行共享
                String[] rateSplit = transferRateMap.get(sku).split(":");
                long numerator = saleCount * Integer.parseInt(rateSplit[0]) * product / Integer.parseInt(rateSplit[1]);
                shareRateMap.put(sku, getReducedFractionRate(numerator, sum));
            }
        }
        return shareRateMap;
    }

    /**
     * 返回约分后的比例
     *
     * @param numerator
     * @param denominator
     * @return
     */
    private String getReducedFractionRate(long numerator, long denominator) {
        if (numerator == 0) {
            return numerator + ":" + denominator;
        }
        long gcd = gcd(numerator, denominator);
        return numerator / gcd + ":" + denominator / gcd;
    }

    private long gcd(long a, long b) {
        if (b == 0) {
            return a;
        }
        return gcd(b, a % b);
    }

    private long lcm(long a, long b) {
        return (a * b) / gcd(a, b);
    }

}
