package net.summerfarm.inbound;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.base.PageRes;
import net.summerfarm.wms.api.h5.instore.InBoundOrderCommandService;
import net.summerfarm.wms.api.h5.instore.InBoundOrderQueryService;
import net.summerfarm.wms.api.h5.instore.dto.req.*;
import net.summerfarm.wms.api.h5.instore.dto.res.InBoundOrderDetailResDTO;
import net.summerfarm.wms.api.h5.instore.dto.res.InBoundOrderListResDTO;
import net.summerfarm.wms.api.h5.instore.dto.res.InBoundOrderResDTO;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.api.h5.prove.ProveQueryService;
import net.summerfarm.wms.api.h5.prove.dto.req.ProveQuery;
import net.summerfarm.wms.api.h5.prove.dto.res.ProveResDTO;
import net.summerfarm.wms.api.h5.prove.enums.ProveBizTypeEnum;
import net.summerfarm.wms.application.common.InitInboundExcelDealService;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.data.InBoundOrderDataController;
import net.summerfarm.wms.data.dto.SyncDTO;
import net.summerfarm.wms.starter.WmsApplication;
import net.summerfarm.wms.web.controller.instore.InBoundOrderController;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class InBoundTest {

    @Resource
    private InBoundOrderCommandService inBoundOrderCommandService;

    @Resource
    private InBoundOrderController inBoundOrderController;

    @Resource
    private InBoundOrderQueryService inBoundOrderQueryService;

    @Resource
    private ProveQueryService proveQueryService;

    @Resource
    private InBoundOrderDataController inBoundOrderDataController;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private InitInboundExcelDealService initInboundExcelDealService;

    @Test
    public void redisTest(){
        System.out.println( redisUtil.get("1"));
    }

    @Test
    public void syncTest(){
        inBoundOrderDataController.sync(SyncDTO.builder().build());
    }

    @Test
    public void listTest() {
        PageRes<InBoundOrderListResDTO> inBoundOrderListResDTOS =
                inBoundOrderQueryService.pageInBoundOrder(InBoundOrderListQuery.builder().pageIndex(1)
                                .tenantId(1L)
                                .cargoOwner("XIANMU")
//                                .receivingContainer("CONTAINER001")
                        .pageSize(10).id(267L).build());
        List<InBoundOrderListResDTO> content = inBoundOrderListResDTOS.getContent();
        log.info("入库单列表查询:{}", JSON.toJSONString(content));
    }

    @Test
    public void orderTest(){
        InBoundOrderResDTO inBoundOrderResDTO = inBoundOrderQueryService.queryInBoundOrder(InBoundOrderQuery.builder()
                .id(267L)
                .build());
        log.info("入库单查询:{}", JSON.toJSONString(inBoundOrderResDTO));

    }

    @Test
    public void detailTest(){
        List<InBoundOrderDetailResDTO> inBoundOrderDetailResDTOS = inBoundOrderQueryService.queryInBoundOrderDetail(InBoundOrderQuery.builder()
                .id(267L)
                .build());
        log.info("入库单详情查询:{}", JSON.toJSONString(inBoundOrderDetailResDTOS));

    }

    @Test
    public void proveTest(){
        ProveResDTO proveResDTO = proveQueryService.queryProve(ProveQuery.builder().sourceId(String.valueOf(20006)).type(ProveBizTypeEnum.PURCHASE_IN.getCode()).build());
        log.info("证明详情查询:{}", JSON.toJSONString(proveResDTO));

    }

    @Test
    public void createTest() {
        InBoundOrderCommand build = InBoundOrderCommand.builder()
                .warehouseNo(1L)
                .operator("4581")
                .stockStorageTaskId(15L)
                //.type(InBoundOrderEnum.PURCHASE_IN.getCode())
                .orderDetails(Lists.newArrayList(InBoundOrderDetailReqDTO.builder()
                        .packaging("测试包装4")
                        .pdName("测试商品4")
                        .produceAt(System.currentTimeMillis())
                        .prove(ProveReqDTO.builder()
                                .qualityInspectionReport("test/i46mzgsbk2jjepb1n.png")
                                .isComplete(0)
                                .shelfLife(System.currentTimeMillis())
                                .sku("168606274223")
                                .produceAt(System.currentTimeMillis())
                                .purchaseNo("20221111458132010")
                                .build())
                        //.purchaseNo("20221111458132010")
                        .shelfLife(System.currentTimeMillis())
                        .sku("168606274223")
                        .specification("测试规格4")
                        .stockNum(10)
                        .supplier("测试D供应商")
                        .temperature(0)
                        .build()))
                .build();
        inBoundOrderController.createOrder(build);
    }

    @Test
    public void inboundTimeTest() {
        InBoundOrderListQuery query = InBoundOrderListQuery.builder()
                .pageIndex(1)
                .pageSize(10)
                .inboundStartTime("2023-05-01 00:00:00")
                .inboundEndTime("2023-05-10 23:59:59")
                .tenantId(2L)
                .build();

        CommonResult<PageRes<InBoundOrderListResDTO>> result = inBoundOrderController.pageOrder(query);
        log.info(JSON.toJSONString(result));
    }

    @Test
    public void initUploadTest() {
        initInboundExcelDealService.dealOss("test-app-temp:front_file/sass-manage/test/oss/期初库存导入模版_副本-65aa327f26fb4d01b4548fb516904bff.xlsx", 197L);
    }

}
