package net.summerfarm.wms.manage.model.domain;

import lombok.Data;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/28
 */
@Data
public class AdminDataPermission implements Serializable {
    private static final long serialVersionUID = -4232025431837134493L;
    private Integer id;

    private Integer adminId;

    private String permissionValue;

    private String permissionName;

    private String type;

    private LocalDateTime addtime;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;


    public AdminDataPermission(){}

    public AdminDataPermission(WarehouseStorageCenter center){
        this.permissionValue = String.valueOf(center.getWarehouseNo());
        this.permissionName = center.getWarehouseName();
        this.type = "0";

    }
}

