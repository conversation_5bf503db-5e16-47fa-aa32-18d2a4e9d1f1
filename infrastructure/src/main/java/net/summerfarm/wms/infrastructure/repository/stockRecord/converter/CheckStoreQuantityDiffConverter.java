package net.summerfarm.wms.infrastructure.repository.stockRecord.converter;

import net.summerfarm.wms.domain.StoreRecord.domainobject.CheckStoreQuantityDiff;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CheckStoreQuantityDiffDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CheckStoreQuantityDiffConverter {

    CheckStoreQuantityDiffConverter INSTANCE = Mappers.getMapper(CheckStoreQuantityDiffConverter.class);


    /**
     * 转换
     * @param diffDO
     * @return
     */
    CheckStoreQuantityDiff convert(CheckStoreQuantityDiffDO diffDO);

    /**
     * 转换
     * @param diffDOList
     * @return
     */
    List<CheckStoreQuantityDiff> convertList(List<CheckStoreQuantityDiffDO> diffDOList);
}
