package net.summerfarm.wms.application.skushare.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryCommand;
import net.summerfarm.wms.application.skushare.dto.*;
import net.summerfarm.wms.application.skushare.factory.OrderSkuShareFactory;
import net.summerfarm.wms.application.skushare.service.SkuShareCommandService;
import net.summerfarm.wms.application.skushare.service.SkuShareMsgService;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.config.domainobject.ConversionSkuConfig;
import net.summerfarm.wms.domain.config.repository.ConversionSkuConfigRepository;
import net.summerfarm.wms.domain.skushare.domainService.SkuShareDomainService;
import net.summerfarm.wms.domain.skushare.domainService.SkuShareRateCalculator;
import net.summerfarm.wms.domain.skushare.domainobject.OrderSkuShare;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.OrderSkuShareOutOrderQuery;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.SkuShareRate;
import net.summerfarm.wms.domain.skushare.factory.OrderSkuShareQueryFactory;
import net.summerfarm.wms.domain.skushare.repository.OrderSkuShareRepository;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sku规格共享功能实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SkuShareCommandServiceImpl implements SkuShareCommandService {

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SkuShareDomainService skuShareDomainService;
    @Autowired
    private SkuShareMsgService skuShareMsgService;
    @Autowired
    private ConversionSkuConfigRepository conversionSkuConfigRepository;
    @Autowired
    private OrderSkuShareRepository orderSkuShareRepository;
    @Autowired
    private SkuShareRateCalculator skuShareRateCalculator;
    @Autowired
    private OrderSkuShareFactory orderSkuShareFactory;
    @Autowired
    private OrderSkuShareQueryFactory orderSkuShareQueryFactory;
    @Autowired
    private AreaStoreRepository areaStoreRepository;
    @Autowired
    private SafeInventoryCommandService safeInventoryCommandService;
    @Autowired
    private RedisUtil redisUtil;


    @Override
    public void addSkuShare(@Valid AddSkuShareCommand addSkuShareCommand) {
        String lockKey = getOrderSkuShareLockKey(addSkuShareCommand.getOutOrderNo(), addSkuShareCommand.getOutOrderType().getType());
        if (!redisUtil.tryLock(lockKey, 3000L, TimeUnit.MILLISECONDS, 3000L)) {
            throw new BizException(ErrorCodeNew.REDIS_LOCK_ERROR);
        }
        try {
            transactionTemplate.execute(status -> {
                try {
                    doAddSkuShare(addSkuShareCommand);
                    return true;
                } catch (Exception e) {
                    log.error("新增sku规格共享失败，addSkuShareCommand:{}", JSON.toJSONString(addSkuShareCommand), e);
                    throw e;
                }
            });
        } finally {
            redisUtil.unlock(lockKey);
        }
    }

    @Override
    public void cancelSkuShare(@Valid CancelSkuShareCommand cancelSkuShareCommand) {
        String lockKey = getOrderSkuShareLockKey(cancelSkuShareCommand.getOutOrderNo(), cancelSkuShareCommand.getOutOrderType().getType());
        if (!redisUtil.tryLock(lockKey, 3000L, TimeUnit.MILLISECONDS, 3000L)) {
            throw new BizException(ErrorCodeNew.REDIS_LOCK_ERROR);
        }
        try {
            transactionTemplate.execute(status -> {
                try {
                    doCancelSkuShare(cancelSkuShareCommand);
                    return true;
                } catch (Exception e) {
                    log.error("取消sku规格共享失败，cancelSkuShareCommand:{}", JSON.toJSONString(cancelSkuShareCommand), e);
                    throw e;
                }
            });
        } finally {
            redisUtil.unlock(lockKey);
        }
    }

    @Override
    public void updateSkuShareTotalQuantity(@Valid UpdateSkuShareTotalQuantityCommand updateSkuShareTotalQuantityCommand) {
        String lockKey = getOrderSkuShareLockKey(updateSkuShareTotalQuantityCommand.getOutOrderNo(), updateSkuShareTotalQuantityCommand.getOutOrderType().getType());
        if (!redisUtil.tryLock(lockKey, 3000L, TimeUnit.MILLISECONDS, 3000L)) {
            throw new BizException(ErrorCodeNew.REDIS_LOCK_ERROR);
        }
        try {
            transactionTemplate.execute(status -> {
                try {
                    doUpdateSkuShareTotalQuantity(updateSkuShareTotalQuantityCommand);
                    return true;
                } catch (Exception e) {
                    log.error("更新sku规格共享数量失败，updateSkuShareTotalQuantityCommand:{}", JSON.toJSONString(updateSkuShareTotalQuantityCommand), e);
                    throw e;
                }
            });
        } finally {
            redisUtil.unlock(lockKey);
        }
    }

    @Override
    public void clearSkuShare(@NotNull Integer warehouseNo) {
        log.info("开始订单sku规格共享清零，仓库号：{}", warehouseNo);
        List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForClear(warehouseNo);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            log.info("该仓库下没有订单sku规格共享需要清零，warehouseNo:{}", warehouseNo);
            return;
        }
        // 逐个清零订单sku规格共享
        orderSkuShares.forEach(this::clearSkuShare);
    }

    @Override
    @OperatorAnnotation
    public Map<String, Integer> checkModifySkuShare(@Valid ModifySkuShareCommand modifySkuShareCommand) {
        // 构建更新订单sku规格共享比例的列表
        List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForModifySkuShare(modifySkuShareCommand);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            log.info("该订单该sku无状态正常的规格共享记录, outOrderNo:{}, warehouseNo:{}, sharedSku:{}", modifySkuShareCommand.getOutOrderNo(), modifySkuShareCommand.getWarehouseNo(), modifySkuShareCommand.getTransferOutSku());
            return Maps.newHashMap();
        }
        if (!validateUpdatedSkuShareRates(orderSkuShares)) {
            throw new BizException("修改后的sku规格共享比例之和大于1");
        }
        return skuShareDomainService.checkModifySkuShare(orderSkuShares);
    }

    @Override
    @OperatorAnnotation
    public void modifySkuShare(@Valid ModifySkuShareCommand modifySkuShareCommand) {
        String lockKey = getOrderSkuShareLockKey(modifySkuShareCommand.getOutOrderNo(), modifySkuShareCommand.getOutOrderType().getType());
        if (!redisUtil.tryLock(lockKey, 3000L, TimeUnit.MILLISECONDS, 500L)) {
            throw new BizException(ErrorCodeNew.REDIS_LOCK_ERROR);
        }
        try {
            transactionTemplate.execute(status -> {
                try {
                    doModifySkuShare(modifySkuShareCommand);
                    return true;
                } catch (Exception e) {
                    log.error("更新sku规格共享比例失败，modifySkuShareCommand:{}", JSON.toJSONString(modifySkuShareCommand), e);
                    throw e;
                }
            });
        } finally {
            redisUtil.unlock(lockKey);
        }
    }

    private void doModifySkuShare(ModifySkuShareCommand modifySkuShareCommand) {
        // 构建更新订单sku规格共享比例的列表
        List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForModifySkuShare(modifySkuShareCommand);
        if (CollectionUtils.isEmpty(orderSkuShares)) {
            log.info("该订单该sku无状态正常的规格共享记录, outOrderNo:{}, warehouseNo:{}, sharedSku:{}", modifySkuShareCommand.getOutOrderNo(), modifySkuShareCommand.getWarehouseNo(), modifySkuShareCommand.getTransferOutSku());
            return;
        }
        if (!validateUpdatedSkuShareRates(orderSkuShares)) {
            throw new BizException("修改后的sku规格共享比例之和大于1");
        }
        for (OrderSkuShare orderSkuShare : orderSkuShares) {
            if (orderSkuShare.getUpdateValue() != null && StringUtils.isNotEmpty(orderSkuShare.getUpdateValue().getUpdatedShareRate())) {
                // 只有修改了共享比例的sku才需要更新
                skuShareDomainService.modifySkuShare(orderSkuShare);
            }
        }
    }

    private boolean validateUpdatedSkuShareRates(List<OrderSkuShare> orderSkuShares) {
        List<String> skuShareRates = orderSkuShares.stream().map(x -> {
            if (x.getUpdateValue() != null && StringUtils.isNotEmpty(x.getUpdateValue().getUpdatedShareRate())) {
                return x.getUpdateValue().getUpdatedShareRate();
            }
            return x.getShareRate();
        }).collect(Collectors.toList());
        return skuShareRateCalculator.sumOfShareRatesLeOne(skuShareRates);
    }

    private void doAddSkuShare(AddSkuShareCommand addSkuShareCommand) {
        addSkuShareCommand.getDetails().sort(Comparator.comparing(AddSkuShareDetail::getSharedSku));
        // 逐个sku处理
        for (AddSkuShareDetail detail : addSkuShareCommand.getDetails()) {
            // 获取规格共享的sku转换配置
            List<ConversionSkuConfig> conversionSkuConfigs = getConversionSkuConfigs(detail.getWarehouseNo(), detail.getSharedSku());
            if (CollectionUtils.isEmpty(conversionSkuConfigs)) {
                log.info("该订单该sku不支持规格共享, outOrderNo:{}, warehouseNo:{}, sku:{}", addSkuShareCommand.getOutOrderNo(), detail.getWarehouseNo(), detail.getSharedSku());
                continue;
            }
            // 计算sku规格共享比例
            Map<String, String> transferRateMap = conversionSkuConfigs.stream().collect(Collectors.toMap(ConversionSkuConfig::getInSku, ConversionSkuConfig::getRates, (v1, v2) -> v1));
            List<SkuShareRate> skuShareRates = skuShareRateCalculator.calculate(detail.getWarehouseNo(), detail.getSharedSku(), transferRateMap);
            log.info("订单sku规格共享比例：{}, outOrderNo:{}", JSON.toJSONString(skuShareRates), addSkuShareCommand.getOutOrderNo());
            // 构建订单sku规格共享列表
            List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForAdd(addSkuShareCommand, detail, skuShareRates);
            // 逐个创建订单sku规格共享
            List<OrderSkuShare> sendSkuShareCostList = Lists.newArrayList();
            for (OrderSkuShare orderSkuShare : orderSkuShares) {
                // 幂等处理，判断是否有同订单同sku且状态正常的规格共享记录，有则直接返回
                OrderSkuShareOutOrderQuery query = orderSkuShareQueryFactory.createOutOrderQuery(orderSkuShare);
                List<OrderSkuShare> orderSkuSharesInDb = orderSkuShareRepository.queryOrderSkuShare(query);
                if (CollectionUtils.isNotEmpty(orderSkuSharesInDb)) {
                    log.info("已存在相同的规格共享记录, outOrderNo:{}, transferOutSku:{}, transferInSku:{}", orderSkuShare.getOutOrderNo(),
                            orderSkuShare.getTransferOutSku(), orderSkuShare.getTransferInSku());
                    continue;
                }
                // 没有再创建
                skuShareDomainService.createOrderSkuShare(orderSkuShare);
                if (orderSkuShare.getRemainingTransferInQuantity() != null && orderSkuShare.getRemainingTransferInQuantity() > 0) {
                    // 有规格共享待转入时发送规格共享成本消息
                    sendSkuShareCostList.add(orderSkuShare);
                }
            }
            skuShareMsgService.sendSkuShareCostMsg(sendSkuShareCostList);
        }
    }

    private void doCancelSkuShare(CancelSkuShareCommand cancelSkuShareCommand) {
        cancelSkuShareCommand.getDetails().sort(Comparator.comparing(CancelSkuShareDetail::getSharedSku));
        // 逐个sku处理
        for (CancelSkuShareDetail detail : cancelSkuShareCommand.getDetails()) {
            // 构建取消的订单sku规格共享列表
            List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForCancel(cancelSkuShareCommand, detail);
            if (CollectionUtils.isEmpty(orderSkuShares)) {
                log.info("该订单该sku无状态正常的规格共享记录不需要取消, outOrderNo:{}, warehouseNo:{}, sharedSku:{}", cancelSkuShareCommand.getOutOrderNo(), detail.getWarehouseNo(), detail.getSharedSku());
                continue;
            }
            // 逐个取消订单sku规格共享
            for (OrderSkuShare orderSkuShare : orderSkuShares) {
                skuShareDomainService.cancelOrderSkuShare(orderSkuShare);
            }
        }
    }

    private void doUpdateSkuShareTotalQuantity(UpdateSkuShareTotalQuantityCommand updateSkuShareTotalQuantityCommand) {
        updateSkuShareTotalQuantityCommand.getDetails().sort(Comparator.comparing(UpdateSkuShareTotalQuantityDetail::getSharedSku));
        // 逐个sku处理
        for (UpdateSkuShareTotalQuantityDetail detail : updateSkuShareTotalQuantityCommand.getDetails()) {
            // 构建更新订单sku规格共享数量的列表
            List<OrderSkuShare> orderSkuShares = orderSkuShareFactory.buildOrderSkuShareForUpdateTotalQuantity(updateSkuShareTotalQuantityCommand, detail);
            if (CollectionUtils.isEmpty(orderSkuShares)) {
                log.info("该订单该sku无状态正常的规格共享记录, outOrderNo:{}, warehouseNo:{}, sharedSku:{}", updateSkuShareTotalQuantityCommand.getOutOrderNo(), detail.getWarehouseNo(), detail.getSharedSku());
                continue;
            }
            // 逐个更新订单sku规格共享的总数量
            for (OrderSkuShare orderSkuShare : orderSkuShares) {
                skuShareDomainService.updateOrderSkuShareTotalQuantity(orderSkuShare);
            }
        }
    }

    private void clearSkuShare(OrderSkuShare orderSkuShare) {
        String lockKey = getOrderSkuShareLockKey(orderSkuShare.getOutOrderNo(), orderSkuShare.getOutOrderType());
        if (!redisUtil.tryLock(lockKey, 3000L, TimeUnit.MILLISECONDS, 3000L)) {
            throw new BizException(ErrorCodeNew.REDIS_LOCK_ERROR);
        }
        try {
            transactionTemplate.execute(status -> {
                try {
                    skuShareDomainService.clearOrderSkuShare(orderSkuShare);
                    lockSafeQuantityIfNeeded(orderSkuShare);
                    return true;
                } catch (Exception e) {
                    log.error("sku规格共享清零失败，outOrderNo:{}, outOrderType:{}", orderSkuShare.getOutOrderNo(), orderSkuShare.getOutOrderType(), e);
                    throw e;
                }
            });
        } finally {
            redisUtil.unlock(lockKey);
        }
    }

    private List<ConversionSkuConfig> getConversionSkuConfigs(Integer warehouseNo, String sku) {
        List<ConversionSkuConfig> configs = conversionSkuConfigRepository.selectList(warehouseNo, sku);
        // 过滤掉转换比例为空的配置
        List<ConversionSkuConfig> ratesNotEmptyConfigs = configs.stream().filter(x -> StringUtils.isNotEmpty(x.getRates())).collect(Collectors.toList());
        return ratesNotEmptyConfigs;
    }

    private String getOrderSkuShareLockKey(String outOrderNo, Integer outOrderType) {
        return RedisKeys.buildOrderSkuShareLockKey(outOrderNo, outOrderType);
    }

    private void lockSafeQuantityIfNeeded(OrderSkuShare orderSkuShare) {
        try {
            // 规格共享清零后，如果转入sku超卖且转出sku有可用库存，则锁定转出sku的安全库存留待后续规格转换，
            // 锁定数量=MIN(转出sku可用库存, 超卖数量换算成的转出sku数量)
            List<AreaStore> areaStores = areaStoreRepository.listSkuStockBySku(orderSkuShare.getWarehouseNo().longValue(),
                    Lists.newArrayList(orderSkuShare.getTransferInSku(), orderSkuShare.getTransferOutSku()));
            Map<String, AreaStore> areaStoreMap = areaStores.stream().collect(Collectors.toMap(AreaStore::getSku, Function.identity(), (v1, v2) -> v1));
            AreaStore inSkuAreaStore = areaStoreMap.get(orderSkuShare.getTransferInSku());
            // 库存数量 - 冻结库存数 - 安全库存数量 < 0，则认为超卖
            int inSkuAvailableQuantity = inSkuAreaStore.getQuantity() - inSkuAreaStore.getLockQuantity() - inSkuAreaStore.getSafeQuantity();
            if (inSkuAvailableQuantity >= 0) {
                return;
            }
            String[] transferRateSplit = orderSkuShare.getTransferRate().split(":");
            int needTransferOutQuantity = new BigDecimal(-inSkuAvailableQuantity).multiply(new BigDecimal(transferRateSplit[0]))
                    .divide(new BigDecimal(transferRateSplit[1]), RoundingMode.CEILING).intValue();
            AreaStore outSkuAreaStore = areaStoreMap.get(orderSkuShare.getTransferOutSku());
            int outSkuAvailableQuantity = outSkuAreaStore.getQuantity() - outSkuAreaStore.getLockQuantity() - outSkuAreaStore.getSafeQuantity();
            // 转出sku需要锁定的安全库存数量
            int lockSafeQuantity = Math.min(outSkuAvailableQuantity, needTransferOutQuantity);
            if (lockSafeQuantity <= 0) {
                log.info("规格共享清零后，转入sku已超卖但转出sku无可用库存，转入sku:{}，转出ku:{}，转入sku超卖数量:{}，转出sku可用库存数量:{}",
                        orderSkuShare.getTransferInSku(), orderSkuShare.getTransferOutSku(), -inSkuAvailableQuantity, outSkuAvailableQuantity);
                return;
            }
            SafeInventoryCommand safeInventoryCommand = SafeInventoryCommand.builder()
                    .warehouseNo(orderSkuShare.getWarehouseNo().longValue())
                    .sku(orderSkuShare.getTransferOutSku())
                    .safeQuantity(outSkuAreaStore.getSafeQuantity() + lockSafeQuantity)
                    .remark("小规格库存已售，库存待转入小规格数量")
                    .systemExe(true)
                    .tenantId(outSkuAreaStore.getTenantId())
                    .build();
            safeInventoryCommandService.updateSafeInventory(safeInventoryCommand);
        } catch (Exception ex) {
            log.error("规格共享清零后，按需锁定转出sku安全库存失败，orderSkuShare:{}", JSON.toJSONString(orderSkuShare), ex);
        }
    }

}
