package net.summerfarm.wms.facade.openapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * @Date 2023/10/23 14:46
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInPartDTO implements Serializable {


    private static final long serialVersionUID = -4552294315820340390L;
    /**
     * 幂等单号
     */
    private String idempotentNo;
    /**
     * 采购单号
     */
    private String outPurchaseNo;
    /**
     * 入库仓
     */
    private Integer inWarehouseNo;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 详情
     */
    private List<InBoundOrderDetailDTO> detailList;
}
