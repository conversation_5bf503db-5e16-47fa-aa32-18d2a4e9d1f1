package net.summerfarm.wms.infrastructure.dao.instore.dataobject;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/11/18  16:36
 */
@Data
public class StockTaskAbnormalRecordDO {

    private Integer id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 任务条目id
     */
    private Integer stockTaskId;

    /**
     * sku
     */
    private String sku;
    /**
     * 异常数量
     */
    private Integer quantity;
    /**
     * 原因：0少货 1拒收
     */
    private Integer reasonType;

    /**
     * 操作人
     */
    private Integer createAdminId;

    /**
     * 任务条目id
     */
    private Integer StockStorageItemId;
}
