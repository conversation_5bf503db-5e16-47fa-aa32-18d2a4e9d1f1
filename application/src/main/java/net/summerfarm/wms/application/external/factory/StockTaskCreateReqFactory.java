package net.summerfarm.wms.application.external.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.input.WarehouseExternalRouteCommandInput;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.allocation.StockAllocationRepository;
import net.summerfarm.wms.domain.allocation.domainobject.StockAllocationList;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.purchase.domainobject.AllocationOrderItem;
import net.summerfarm.wms.domain.purchase.domainobject.AllocationOrderItemPeriod;
import net.summerfarm.wms.domain.stocktask.PurchaseBackDetailRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskNoticeOrderRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.PurchasesBackDetail;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItem;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.pms.SrmQueryFacade;
import net.summerfarm.wms.facade.pms.dto.SupplierInfoDTO;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.summerfarm.wms.facade.wnc.WarehouseStorageFacade;
import net.summerfarm.wms.facade.wnc.dto.WarehousLogisticsCenterRespDTO;
import net.summerfarm.wms.facade.wnc.dto.WarehouseLogisticsQueryReqDTO;
import net.summerfarm.wms.facade.wnc.dto.WarehouseStorageDTO;
import net.summerfarm.wms.openapi.stockout.xm.req.ReceiverInfo;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateItem;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateNoticeReq;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 出库任务创建请求工厂
 * <AUTHOR>
 * @Date 2024/5/23 16:38
 * @Version 1.0
 */
@Component
@Slf4j
public class StockTaskCreateReqFactory {

    @Autowired
    private WarehouseStorageFacade warehouseStorageFacade;

    @Autowired
    private WarehouseLogisticsFacade warehouseLogisticsFacade;

    @Autowired
    private StockTaskNoticeOrderRepository stockTaskNoticeOrderRepository;

    @Autowired
    private StockAllocationRepository stockAllocationRepository;

    @Autowired
    private StockTaskRepository stockTaskRepository;

    @Autowired
    private PurchaseBackDetailRepository purchaseBackDetailRepository;

    @Autowired
    private SrmQueryFacade srmQueryFacade;

    @Resource
    private GoodsReadFacade goodsReadFacade;


    /**
     * 构建出库通知单创建请求
     * @param stockTask 出库任务
     * @param stockTaskItemList 出库任务明细
     * @param warehouseExternalRoute 仓库外部路由
     * @return 出库通知单创建请求
     */
    public WarehouseExternalRouteCommandInput<StockOutCreateNoticeReq> buildStockOutCreateNoticeCommand(StockTask stockTask, List<StockTaskItem> stockTaskItemList, WarehouseExternalRouteEntity warehouseExternalRoute) {
        WarehouseExternalRouteCommandInput<StockOutCreateNoticeReq> warehouseExternalRouteCommandInput = new WarehouseExternalRouteCommandInput<>();
        warehouseExternalRouteCommandInput.setIdempotentNo(stockTask.getId().toString());
        warehouseExternalRouteCommandInput.setWarehouseNo(stockTask.getAreaNo());
        warehouseExternalRouteCommandInput.setTenantId(stockTask.getTenantId());
        warehouseExternalRouteCommandInput.setWarehouseTenantId(warehouseExternalRoute.getWarehouseTenantId());
        warehouseExternalRouteCommandInput.setOrderType(stockTask.getType());
        warehouseExternalRouteCommandInput.setAbilityCode(warehouseExternalRoute.getAbilityCode());
        warehouseExternalRouteCommandInput.setAbilityId(warehouseExternalRoute.getAbilityId());
        warehouseExternalRouteCommandInput.setExternalData(buildStockOutCreateNoticeReq(stockTask, stockTaskItemList, warehouseExternalRoute));
        return warehouseExternalRouteCommandInput;
    }

    private StockOutCreateNoticeReq buildStockOutCreateNoticeReq(StockTask stockTask, List<StockTaskItem> stockTaskItemList, WarehouseExternalRouteEntity warehouseExternalRoute) {
        StockOutCreateNoticeReq stockOutCreateNoticeReq = new StockOutCreateNoticeReq();
        stockOutCreateNoticeReq.setIdempotentNo(stockTask.getId().toString());
        stockOutCreateNoticeReq.setStockOutNo(stockTask.getId().toString());
        stockOutCreateNoticeReq.setOutWarehouseNo(null != warehouseExternalRoute && !StringUtils.isBlank(warehouseExternalRoute.getExternalWarehouseNo()) ? warehouseExternalRoute.getExternalWarehouseNo() : stockTask.getAreaNo().toString());
        stockOutCreateNoticeReq.setOrderType(stockTask.getType());
        stockOutCreateNoticeReq.setCreateTime(DateUtil.formatDate(new Date()));
        stockOutCreateNoticeReq.setExpectTime(DateUtil.formatDate(stockTask.getExpectTime()));
        List<StockOutCreateItem> stockOutCreateItems = new ArrayList<>();

        ReceiverInfo receiverInfo = new ReceiverInfo();

        // 销售出库\样品出库\补货出库任务 的收货人信息取城配仓的
        List<Integer> saleOutReqOrderTypeList = Arrays.asList(StockTaskTypeEnum.SALE_OUT.getId(),
                StockTaskTypeEnum.DEMO_OUT.getId(),
                StockTaskTypeEnum.SUPPLY_AGAIN_TASK.getId(),
                StockTaskTypeEnum.CROSS_OUT.getId());
        if (saleOutReqOrderTypeList.contains(stockTask.getType())) {
            // 销售出库任务 的收货人信息取城配仓的
            receiverInfo = this.buildReceiverInfoByStoreNo(stockTask.getOutStoreNo());
        }
        if (StockTaskTypeEnum.OWN_SALE_OUT_TASK.getId() == stockTask.getType()
                || StockTaskTypeEnum.DEMO_SELF_OUT.getId() == stockTask.getType()){
            // 自提销售出库任务 和 样品自提出库任务 的收货人信息取出库通知单的
            receiverInfo = this.buildReceiverInfoByStockTaskId(stockTask.getId());
        }
        // 调拨出库单独封装
        if (StockTaskTypeEnum.STORE_ALLOCATION_OUT.getId() == stockTask.getType()) {
            // 调拨出库任务 的收货人信息取目标仓库的
            StockAllocationList allocation = stockAllocationRepository.findAllocation(stockTask.getTaskNo());
            if (null != allocation) {
                // 调拨出库任务 的 预计出库时间 取调拨单的 预计出库时间
                stockOutCreateNoticeReq.setExpectTime(DateUtil.formatDate(allocation.getExpectOutTime()));
                receiverInfo = this.buildReceiverInfoByWarehouseNo(allocation.getInStore());
            }
            List<AllocationOrderItem> allocationOrderItemList = stockTaskRepository.queryStockTaskItemByAllocate(stockTask.getTaskNo());
            for (AllocationOrderItem allocationOrderItem : allocationOrderItemList) {
                StockOutCreateItem stockOutCreateItem = new StockOutCreateItem();
                stockOutCreateItem.setSkuCode(allocationOrderItem.getSku());
                if (allocationOrderItem.getOutQuantity() <= 0){
                    log.error("\n 调拨明细里存在数量为0的sku >>> {} >>> {} \n", stockTask.getTaskNo(), allocationOrderItem.getSku());
                    continue;
                }
                stockOutCreateItem.setPlanQuantity(allocationOrderItem.getOutQuantity());
                stockOutCreateItem.setDetailRemark(this.buildDetailRemark(allocationOrderItem.getItemPeriodList()));
                stockOutCreateItems.add(stockOutCreateItem);
            }
            stockOutCreateNoticeReq.setReceiverInfo(receiverInfo);
            stockOutCreateNoticeReq.setStockOutItems(stockOutCreateItems);
            return stockOutCreateNoticeReq;
        }
        // 采退出库单独封装
        if (StockTaskTypeEnum.PURCHASES_BACK.getId() == stockTask.getType()) {
            List<PurchasesBackDetail> purchasesBackDetailList = purchaseBackDetailRepository.selectByNo(stockTask.getTaskNo());
            if (CollectionUtils.isEmpty(purchasesBackDetailList)) {
                throw new BizException("采退任务明细为空，采退任务编号：" + stockTask.getTaskNo());
            }
            // 获取商品信息
            List<String> skuList = purchasesBackDetailList.stream().map(PurchasesBackDetail::getSku).distinct().collect(Collectors.toList());
            Map<String, GoodsInfoDTO> skuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(stockTask.getTenantId(), skuList);
            // 采退出库任务 的收货人信息取供应商的
            stockOutCreateNoticeReq.setReceiverInfo(this.buildReceiverInfoBySupplierId(purchasesBackDetailList.get(0).getSupplierId()));
            for (PurchasesBackDetail purchasesBackDetail : purchasesBackDetailList) {
                StockOutCreateItem stockOutCreateItem = new StockOutCreateItem();
//                stockOutCreateItem.setBatchCode(purchasesBackDetail.getBatch());
                stockOutCreateItem.setProductDate(DateUtil.formatYmd(purchasesBackDetail.getProductionDate()));
                stockOutCreateItem.setExpireDate(DateUtil.formatYmd(purchasesBackDetail.getQualityDate()));
                // saas自营仓补充自有编码
                if (stockTask.getTenantId() > WmsConstant.XIANMU_TENANT_ID) {
                    GoodsInfoDTO goodsInfoDTO = skuGoodsInfoDTOMap.get(purchasesBackDetail.getSku());
                    if (null == goodsInfoDTO) {
                        throw new BizException("sku：" + purchasesBackDetail.getSku() + "查询商品信息为空");
                    }
                    if (StringUtils.isBlank(goodsInfoDTO.getCustomSkuCode())) {
                        throw new BizException("sku：" + purchasesBackDetail.getSku() + "未维护货品自有编码");
                    }
                    stockOutCreateItem.setSkuCode(purchasesBackDetail.getSku());
                    stockOutCreateItem.setCustomerSkuCode(goodsInfoDTO.getCustomSkuCode());
                } else {
                    stockOutCreateItem.setSkuCode(purchasesBackDetail.getSku());
                }
                stockOutCreateItem.setPlanQuantity(purchasesBackDetail.getOutQuantity());
                stockOutCreateItems.add(stockOutCreateItem);
            }
            stockOutCreateNoticeReq.setStockOutItems(stockOutCreateItems);
            // saas自营仓补充自有编码
            if (stockTask.getTenantId() > WmsConstant.XIANMU_TENANT_ID) {
                for (StockTaskItem stockTaskItem : stockTaskItemList) {
                    GoodsInfoDTO goodsInfoDTO = skuGoodsInfoDTOMap.get(stockTaskItem.getSku());
                    if (null != goodsInfoDTO && !StringUtils.isBlank(goodsInfoDTO.getCustomSkuCode())) {
                        stockTaskRepository.updateCustomerSkuCode(goodsInfoDTO.getCustomSkuCode(), stockTaskItem.getId().longValue());
                    }
                }
            }
            return stockOutCreateNoticeReq;
        }

        stockOutCreateNoticeReq.setReceiverInfo(receiverInfo);
        for (StockTaskItem stockTaskItem : stockTaskItemList) {
            StockOutCreateItem stockOutCreateItem = new StockOutCreateItem();
            stockOutCreateItem.setSkuCode(stockTaskItem.getSku());
            stockOutCreateItem.setPlanQuantity(stockTaskItem.getQuantity());
            stockOutCreateItems.add(stockOutCreateItem);
        }
        stockOutCreateNoticeReq.setStockOutItems(stockOutCreateItems);
        return stockOutCreateNoticeReq;
    }

    private String buildDetailRemark(List<AllocationOrderItemPeriod> itemPeriodList) {
        if (CollectionUtils.isEmpty(itemPeriodList)) {
            return null;
        }
        StringBuilder detailRemark = new StringBuilder();
        detailRemark.append("生产日期指定以下区间：");
        for (AllocationOrderItemPeriod itemPeriod : itemPeriodList) {
            detailRemark.append("生产日期：").append(DateUtil.formatYmd(itemPeriod.getProducePeriodStartTime())).append(" ~ ").append(DateUtil.formatYmd(itemPeriod.getProducePeriodEndTime())).append("；");
        }
        return detailRemark.toString();
    }

    private ReceiverInfo buildReceiverInfoByStockTaskId(Long id) {
        List<StockTaskNoticeOrder> stockTaskNoticeOrderList = stockTaskNoticeOrderRepository.findByStockTaskId(id.intValue());
        if (CollectionUtils.isEmpty(stockTaskNoticeOrderList)) {
            log.error("出库通知单信息为空，出库任务编号：{}", id);
            return null;
        }
        StockTaskNoticeOrder stockTaskNoticeOrder = stockTaskNoticeOrderList.get(0);
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setShopName(stockTaskNoticeOrder.getShopName());
        receiverInfo.setShopNo(null != stockTaskNoticeOrder.getShopId() ? stockTaskNoticeOrder.getShopId().toString() : null);
        receiverInfo.setProvince(stockTaskNoticeOrder.getProvince());
        receiverInfo.setCity(stockTaskNoticeOrder.getCity());
        receiverInfo.setArea(stockTaskNoticeOrder.getArea());
        receiverInfo.setDetailAddress(stockTaskNoticeOrder.getDetailAddress());
        receiverInfo.setReceiver(stockTaskNoticeOrder.getReceiver());
        receiverInfo.setPhone(stockTaskNoticeOrder.getPhone());
        return receiverInfo;
    }

    private ReceiverInfo buildReceiverInfoByStoreNo(Integer outStoreNo) {
        WarehouseLogisticsQueryReqDTO warehouseLogisticsQueryReqDTO = new WarehouseLogisticsQueryReqDTO();
        warehouseLogisticsQueryReqDTO.setStoreNo(outStoreNo);
        List<WarehousLogisticsCenterRespDTO> warehouseLogisticsInfoList = warehouseLogisticsFacade.queryWarehouseLogisticsList(warehouseLogisticsQueryReqDTO);
        if (CollectionUtils.isEmpty(warehouseLogisticsInfoList)) {
            log.error("城配仓信息为空，城配仓编号：{}", outStoreNo);
            return null;
        }
        WarehousLogisticsCenterRespDTO storeInfo = warehouseLogisticsInfoList.get(0);
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setStoreNo(storeInfo.getStoreNo());
        receiverInfo.setStoreName(storeInfo.getStoreName());
        receiverInfo.setReceiver(storeInfo.getPersonContact());
        receiverInfo.setPhone(storeInfo.getPhone());
//        receiverInfo.setProvince(storeInfo);
//        receiverInfo.setCity();
//        receiverInfo.setArea();
        receiverInfo.setDetailAddress(storeInfo.getAddress());
//        receiverInfo.setRemark();
        return receiverInfo;
    }

    /**
     * 根据仓库编号构建收货人信息
     * @param warehouseNo 仓库编号
     * @return 收货人信息
     */
    private ReceiverInfo buildReceiverInfoByWarehouseNo(Integer warehouseNo){
        WarehouseStorageDTO warehouseStorageDTO = warehouseStorageFacade.queryWarehouseStorage(warehouseNo);
        if (null == warehouseStorageDTO) {
            log.error("仓库信息为空，仓库编号：{}", warehouseNo);
            return null;
        }
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setTargetWarehouseNo(warehouseStorageDTO.getWarehouseNo());
        receiverInfo.setTargetWarehouseName(warehouseStorageDTO.getWarehouseName());
        receiverInfo.setReceiver(warehouseStorageDTO.getPersonContact());
        receiverInfo.setPhone(warehouseStorageDTO.getPhone());
        receiverInfo.setDetailAddress(warehouseStorageDTO.getAddress());
        return receiverInfo;
    }

    /**
     * 根据供应商编号构建收货人信息
     * @param supplierId 供应商编号
     * @return 收货人信息
     */
    private ReceiverInfo buildReceiverInfoBySupplierId(Integer supplierId) {
        try {
            Map<Integer, SupplierInfoDTO> supplierInfoDTOMap = srmQueryFacade.querySupplierInfoMap(Collections.singletonList(supplierId));
            if (CollectionUtils.isEmpty(supplierInfoDTOMap)) {
                log.error("供应商信息为空，供应商编号：{}", supplierId);
                return null;
            }
            SupplierInfoDTO supplierInfoDTO = supplierInfoDTOMap.get(supplierId);
            if (null == supplierInfoDTO) {
                log.error("供应商信息为空，供应商编号：{}", supplierId);
                return null;
            }
            ReceiverInfo receiverInfo = new ReceiverInfo();
            receiverInfo.setReceiver(supplierInfoDTO.getSupplierName());
            receiverInfo.setShopName(supplierInfoDTO.getSupplierName());
            return receiverInfo;
        } catch (Exception e) {
            log.error("构建收货人信息异常，供应商编号：{}", supplierId, e);
            return null;
        }
    }
}
