package net.summerfarm.wms.api.h5.crosswarehouse.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 越库分拣明细退单售后对象
 * @author: xdc
 * @date: 2024/3/12
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrossWarehouseSortAfterSaleCommandInput implements Serializable {
    private static final long serialVersionUID = 9165546926438578284L;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 采购供应单号
     */
    private String psoNo;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 退单数量
     */
    private Integer quantity;

}
