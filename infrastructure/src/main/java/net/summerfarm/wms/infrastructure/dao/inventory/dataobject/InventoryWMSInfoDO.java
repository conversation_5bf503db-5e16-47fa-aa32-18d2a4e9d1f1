package net.summerfarm.wms.infrastructure.dao.inventory.dataobject;

import lombok.Data;

import java.io.Serializable;

/**
 * sku的wms信息
 * <AUTHOR>
 * @Create 2020-11-16
 */
@Data
public class InventoryWMSInfoDO implements Serializable {

    /** sku */
    private String sku;

    /** 一级类目，鲜果、非鲜果 */
    private String firstLevelCategory;

    /** 二级类目 */
    private String secondLevelCategory;

    /** 储存区域 */
    private String storageArea;

    /** 包装方式 */
    private String packing;

    /** 储存区域 */
    private Integer storageLocation;

    /** 类目类型 */
    private Integer categoryType;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    public String getSku() {
        return this.sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getFirstLevelCategory() {
        return this.firstLevelCategory;
    }

    public void setFirstLevelCategory(String firstLevelCategory) {
        this.firstLevelCategory = firstLevelCategory;
    }

    public String getSecondLevelCategory() {
        return this.secondLevelCategory;
    }

    public void setSecondLevelCategory(String secondLevelCategory) {
        this.secondLevelCategory = secondLevelCategory;
    }

    public String getStorageArea() {
        return this.storageArea;
    }

    public void setStorageArea(String storageArea) {
        this.storageArea = storageArea;
    }

    public String getPacking() {
        return this.packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }

    public Integer getStorageLocation() {
        return this.storageLocation;
    }

    public void setStorageLocation(Integer storageLocation) {
        this.storageLocation = storageLocation;
    }

    public Integer getCategoryType() {
        return this.categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }
}
