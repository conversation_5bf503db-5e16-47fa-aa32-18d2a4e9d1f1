package net.summerfarm.wms.domain.allocation.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockAllocationList {

    private Integer id;

    private String listNo;

    private Integer createAdmin;

    private String createAdminName;

    /**
     * 审核人admin_id
     */
    private Integer auditAdmin;

    /**
     * 审核人
     */
    private String auditAdminName;

    /**
     * 调出仓
     */
    private Integer outStore;

    /**
     * 调出仓名
     */
    private String outStoreName;

    /**
     * 调入仓
     */
    private Integer inStore;

    /**
     * 调入仓名
     */
    private String inStoreName;

    /**
     * 入库仓库负责人admin_id
     */
    private Integer inStoreAdmin;

    /**
     * 入库仓库负责人
     */
    private String inStoreAdminName;

    /**
     * 出库仓库负责人admin_id
     */
    private Integer outStoreAdmin;

    /**
     * 出库仓库负责人
     */
    private String outStoreAdminName;

    /**
     * 调出仓负责人确认,0待确认，1通过2不通过
     */
    private Integer outStatus;

    /**
     * 调入仓负责人确认,0待确认，1通过2不通过
     */
    private Integer inStatus;

    /**
     * 出库时间
     */
    private LocalDateTime outTime;

    /**
     * 期望出库时间
     */
    private LocalDateTime expectOutTime;

    /**
     * 期望入库时间
     */
    private LocalDateTime expectTime;

    /**
     * 调拨单状态
     */
    private Integer status;

    /**
     * 到货时间
     */
    private LocalDateTime inTime;

    /**
     * 运输方式
     */
    private Integer transport;

    /**
     * 物流单号
     */
    private String trackingNo;

    /**
     * 添加时间
     */
    private LocalDateTime addtime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;

    /**
     * "单据类型:1:手动创建,2:自动创建,3手动创建(自动计算)"
     */
    private Integer orderType;

    /**
     * 调拨计划单号
     */
    private String planListNo;

    /**
     * 是否次日达：0、是  1、不是 （空表示次日达）
     */
    private Integer nextDayArrive;

    /**
     * 调拨计划id
     */
    private Long planListId;

    public StockAllocationList(Integer inStore, Integer outStore, LocalDateTime addtime) {
        this.inStore = inStore;
        this.outStore = outStore;
        this.addtime = addtime;
    }

    public StockAllocationList(String listNo, Integer createAdmin, String createAdminName, Integer auditAdmin, String auditAdminName, Integer outStore, Integer inStore, Integer status, LocalDateTime addtime) {
        this.listNo = listNo;
        this.createAdmin = createAdmin;
        this.auditAdmin = auditAdmin;
        this.auditAdminName = auditAdminName;
        this.outStore = outStore;
        this.inStore = inStore;
        this.status = status;
        this.addtime = addtime;
        this.createAdminName = createAdminName;
    }
}