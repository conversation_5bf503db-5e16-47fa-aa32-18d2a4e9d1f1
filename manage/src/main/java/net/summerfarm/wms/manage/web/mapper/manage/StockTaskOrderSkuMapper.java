package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StockTaskOrderSkuDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Repository
public interface StockTaskOrderSkuMapper {

    /**
     * 根据任务ID查询任务订单明细列表
     *
     * <AUTHOR>
     * @date 2023/2/17 11:17
     * @param stockTaskId 任务ID
     * @return java.util.List<StockTaskOrderSkuDO> 任务订单明细列表
     */
    List<StockTaskOrderSkuDO> selectListByTaskId(@Param("stockTaskId") Integer stockTaskId);

    /**
     * 根据任务ID和sku列表查询任务订单明细列表
     *
     * <AUTHOR>
     * @date 2023/2/6 18:02
     * @param stockTaskId 任务编码
     * @param skuList sku列表
     * @return java.util.List<net.summerfarm.model.domain.StockTaskOrderSkuDO>
     */
    List<StockTaskOrderSkuDO> selectListByTaskIdAndSkuList(@Param("stockTaskId") Integer stockTaskId, @Param("skuList") List<String> skuList);

    /**
     * 根据任务ID和sku查询任务订单明细列表
     *
     * <AUTHOR>
     * @date 2023/2/8 17:14
     * @param stockTaskId 任务ID
     * @param sku sku编码
     * @return java.util.List<net.summerfarm.model.domain.StockTaskOrderSkuDO>
     */
    List<StockTaskOrderSkuDO> selectListByTaskIdAndSku(@Param("stockTaskId") Integer stockTaskId, @Param("sku") String sku);

    /**
     * 更新实出数量
     *
     * <AUTHOR>
     * @date 2023/2/8 18:24
     * @param id 主键ID
     * @param actualQuantity 实际出数量
     * @param oldActualQuantity 旧的实出数量
     */
    int updateActualQuantityById(@Param("id") Long id, @Param("actualQuantity") Integer actualQuantity, @Param("oldActualQuantity") Integer oldActualQuantity);

    /**
     * 更新实出数量
     *
     * @param id                  主键ID
     * @param abnormalQuantity    缺出数量
     * @param oldAbnormalQuantity 旧的缺出数量
     * <AUTHOR>
     * @date 2023/2/8 18:24
     */
    int updateAbnormalQuantityById(@Param("id") Long id, @Param("abnormalQuantity") Integer abnormalQuantity, @Param("oldAbnormalQuantity") Integer oldAbnormalQuantity);

    /**
     * 插入出库任务订单明细
     *
     * <AUTHOR>
     * @Date 2023/1/9 17:31
     * @param record 出库任务订单明细对象
     * @return StockTaskOrderSkuDO
     **/
    int insert(StockTaskOrderSkuDO record);

    /**
     * 插入出库任务订单明细
     *
     * <AUTHOR>
     * @Date 2023/1/9 17:31
     * @param recordList 出库任务订单明细对象列表
     * @return StockTaskOrderSkuDO
     **/
    int batchInsert(@Param("recordList") List<StockTaskOrderSkuDO> recordList);

    /**
     * 根据外部订单号查询出库任务信息
     *
     * <AUTHOR>
     * @date 2023/5/22 13:38
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param outOrderNoList 订单编号列表
     * @return java.util.List<String>
     */
    List<String> selectOutOrderNoByOutOrderNoList(@Param("warehouseNo") Integer warehouseNo,
                                                  @Param("storeNo") Integer storeNo,
                                                  @Param("deliveryDate") String deliveryDate,
                                                  @Param("outOrderNoList") List<String> outOrderNoList,
                                                  @Param("taskType") Integer taskType);

    /**
     * 获取出库任务外部订单明细
     *
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param outOrderNoList
     * @return
     */
    List<StockTaskOrderSkuDO> selectListNoByOutOrderNoList(@Param("warehouseNo") Integer warehouseNo,
                                                           @Param("storeNo") Integer storeNo,
                                                           @Param("deliveryDate") String deliveryDate,
                                                           @Param("outOrderNoList") List<String> outOrderNoList,
                                                           @Param("taskType") Integer taskType);

    /**
     * 获取未取消的出库任务外部单号
     *
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @return
     */
    List<String> selectOutOrderNoByNotCancel(@Param("warehouseNo") Integer warehouseNo,
                                             @Param("storeNo") Integer storeNo,
                                             @Param("deliveryDate") String deliveryDate,
                                             @Param("taskType") Integer taskType);
}
