package net.summerfarm.wms.manage.web.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.manage.model.domain.Inventory;

import java.util.List;

/**
 * @Description
 * @Date 2023/11/20 11:46
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaStoreQueryInput {

    /**
     * size
     */
    private Integer pageSize;

    /**
     * nu,
     */
    private Integer pageNum;

    /**
     * sku
     */
    private List<Inventory> skuList;

    /**
     * 仓库
     */
    private List<Long> warehouseNoList;

    /**
     * 是否售罄
     */
    private Boolean saleOut;
}
