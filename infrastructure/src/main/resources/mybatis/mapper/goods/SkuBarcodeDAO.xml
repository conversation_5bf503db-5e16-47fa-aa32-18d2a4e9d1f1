<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.goods.SkuBarcodeDAO">
    <resultMap id="SkuBarcodeMap" type="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sku" column="sku"/>
        <result property="barcode" column="barcode"/>
        <result property="status" column="status"/>
        <result property="createOperator" column="create_operator"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="table_name">
        wms_sku_barcode
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `create_time`
        , `update_time`, `sku`, `barcode`, `status`, `create_operator`, `update_operator`, `tenant_id`
    </sql>

    <sql id="values_exclude_id">
        #{createTime}
        ,
        #{updateTime},
        #{sku},
        #{barcode},
        #{status},
        #{createOperator},
        #{updateOperator},
        #{tenantId}
    </sql>

    <sql id="query">
        <where>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="barcode != null">AND `barcode` = #{barcode}</if>
            <if test="status != null">AND `status` = #{status}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
        </where>
    </sql>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="barcode != null">`barcode`,</if>
            <if test="status != null">`status`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="tenantId != null">`tenant_id`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sku != null">#{sku},</if>
            <if test="barcode != null">#{barcode},</if>
            <if test="status != null">#{status},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="tenantId != null">#{tenantId},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        `status` = VALUES(status)
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.createTime}, #{i.updateTime}, #{i.sku}, #{i.barcode}, #{i.status}, #{i.createOperator},
            #{i.updateOperator}, #{i.tenantId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        `status` = VALUES(status),
        `update_operator` = VALUES(update_operator),
        `update_time` = VALUES(update_time)
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="barcode != null">`barcode` = #{barcode},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="tenantId != null">`tenant_id` = #{tenantId},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatusBySku" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="status != null">`status` = #{status},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            update_time = now()
        </set>
        WHERE sku = #{sku}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="SkuBarcodeMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SkuBarcodeMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO"
            resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO"
            resultMap="SkuBarcodeMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO"
            resultMap="SkuBarcodeMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <select id="listBySkus" resultMap="SkuBarcodeMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where sku in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and status = 1
    </select>

    <update id="updateStatusBySkuList">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="status != null">`status` = #{status},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            update_time = now()
        </set>
        WHERE sku in
        <foreach collection="skus" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and tenant_id = #{tenantId}
    </update>
</mapper>
