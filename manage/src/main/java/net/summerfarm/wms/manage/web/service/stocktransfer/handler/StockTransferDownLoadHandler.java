package net.summerfarm.wms.manage.web.service.stocktransfer.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.filedown.handler.FileDownLoadHandler;
import net.summerfarm.wms.api.h5.filedown.req.FileDownloadAsyncQuery;
import net.summerfarm.wms.api.h5.stocktransfer.req.StockTransferDownQuery;
import net.summerfarm.wms.domain.download.enums.WmsFileDownloadMessageType;
import net.summerfarm.wms.manage.web.service.stocktransfer.StockTransferQueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 转换任务导出执行器
 *
 * @author: xdc
 * @date: 2024/2/22
 **/
@Component
@Slf4j
public class StockTransferDownLoadHandler implements FileDownLoadHandler {

    @Resource
    private StockTransferQueryService stockTransferQueryService;

    /**
     * 转换任务导出
     *
     * @param fileDownloadType 文件导出类型
     * @return 返回转换任务导出
     */
    @Override
    public Boolean support(Integer fileDownloadType) {
        return WmsFileDownloadMessageType.STOCK_TRANSFER_DOWNLOAD.equalsCode(fileDownloadType);
    }

    @Override
    public void asyncExport(FileDownloadAsyncQuery fileDownloadAsyncQuery) {
        log.info("转换任务导出:{}", JSON.toJSONString(fileDownloadAsyncQuery) + ":" + fileDownloadAsyncQuery.getOperator());
        StockTransferDownQuery exportInfoReq = JSONObject.parseObject(fileDownloadAsyncQuery.getDataJson(), StockTransferDownQuery.class);
        Long fileId = fileDownloadAsyncQuery.getFileId();
        String fileName = fileDownloadAsyncQuery.getFileName();
        stockTransferQueryService.exportStockTransferHandler(exportInfoReq, fileId, fileName, fileDownloadAsyncQuery.getExpireDay());
        log.info("转换任务导出-结束");
    }
}
