package net.summerfarm.wms.manage.web.service.strategy.stockout.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3
 */
@Data
public class StockOutReduceBatchInventoryDetail implements Serializable {

    private static final long serialVersionUID = 3413140118684981279L;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次
     */
    private String batch;

    /**
     * 应出数量
     */
    private Integer shouldQuantity;

    /**
     * 出库数量
     */
    private Integer quantity;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;
}
