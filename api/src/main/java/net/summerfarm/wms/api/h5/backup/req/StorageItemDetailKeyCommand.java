package net.summerfarm.wms.api.h5.backup.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/20
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class StorageItemDetailKeyCommand implements Serializable {

    private static final long serialVersionUID = -4476039089863228428L;

    private String id;
}
