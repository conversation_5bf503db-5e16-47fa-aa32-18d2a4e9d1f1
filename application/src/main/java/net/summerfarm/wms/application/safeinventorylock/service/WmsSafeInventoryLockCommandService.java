package net.summerfarm.wms.application.safeinventorylock.service;

import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryUnlockInput;
import net.xianmu.common.result.CommonResult;

/**
 * 安全库存锁定表命令服务接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface WmsSafeInventoryLockCommandService {

    /**
     * 锁定库存
     * @param input 锁定输入
     * @return 锁定结果
     */
    CommonResult<Boolean> lock(WmsSafeInventoryLockInput input);

    /**
     * 释放锁定
     * @param input 释放锁定输入
     * @return 释放结果
     */
    CommonResult<Boolean> unlock(WmsSafeInventoryUnlockInput input);


}
