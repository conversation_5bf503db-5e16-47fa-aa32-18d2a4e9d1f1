package net.summerfarm.wms.application.inventory.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.areaStore.AreaStoreService;
import net.summerfarm.wms.domain.transaction.AreaStoreInventoryTransactionRepository;
import net.summerfarm.wms.domain.transaction.domainobject.AreaStoreInventoryTransaction;
import net.summerfarm.wms.domain.transaction.enums.TransactionStateEnum;
import net.summerfarm.wms.inventory.enums.AreaStoreInventoryTypeEnum;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AreaStoreInventoryReqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class AreaStoreInventoryChangeListener {

    @Resource
    private AreaStoreService areaStoreService;

    @Resource
    private AreaStoreInventoryTransactionRepository areaStoreInventoryTransactionRepository;

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_ONLINE_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_ONLINE_CG, maxReconsumeTimes = 5)
    public class OnlineInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理虚拟库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.ONLINE);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到虚拟库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                throw new BizException("未查询到虚拟库存事务消息");
            }
            String changeType = areaStoreInventoryReqDTO.getType();
            if (StringUtils.isNotEmpty(areaStoreInventoryReqDTO.getSubType())) {
                changeType = areaStoreInventoryReqDTO.getSubType();
            }
            try {
                areaStoreService.updateOnlineStockByWarehouseNo(areaStoreInventoryReqDTO.getDiffChange(), areaStoreInventoryReqDTO.getDiffStock()
                        , areaStoreInventoryReqDTO.getSku(), areaStoreInventoryReqDTO.getWarehouseNo(),
                        changeType, areaStoreInventoryReqDTO.getBizId(), areaStoreInventoryReqDTO.getOperator());
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.FINISH.getCode());
            } catch (Exception e) {
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.ERROR.getCode());
                throw e;
            } finally {
                areaStoreInventoryTransactionRepository.changeAreaStoreInventoryTransaction(areaStoreInventoryTransaction);
            }
        }
    }

    private AreaStoreInventoryTransaction getNormalTransaction(AreaStoreInventoryReqDTO req, AreaStoreInventoryTypeEnum typeEnum) {
        return areaStoreInventoryTransactionRepository.findAreaStoreInventoryNormalTransaction(req.getWarehouseNo(),
                req.getBizId(), req.getType(), typeEnum.getDesc(), req.getSku());
    }

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_ROAD_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_ROAD_CG, maxReconsumeTimes = 5)
    public class RoadInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理在途库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.ROAD);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到在途库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                throw new BizException("未查询到在途库存事务消息");
            }
            String changeType = areaStoreInventoryReqDTO.getType();
            if (StringUtils.isNotEmpty(areaStoreInventoryReqDTO.getSubType())) {
                changeType = areaStoreInventoryReqDTO.getSubType();
            }
            try {
                areaStoreService.updateRoadStockByStoreNo(areaStoreInventoryReqDTO.getDiffStock(),
                        areaStoreInventoryReqDTO.getSku(), areaStoreInventoryReqDTO.getWarehouseNo(),
                        changeType, areaStoreInventoryReqDTO.getBizId(), areaStoreInventoryReqDTO.getOperator());
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.FINISH.getCode());
            } catch (Exception e) {
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.ERROR.getCode());
                throw e;
            } finally {
                areaStoreInventoryTransactionRepository.changeAreaStoreInventoryTransaction(areaStoreInventoryTransaction);
            }
        }
    }

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_LOCK_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_LOCK_CG, maxReconsumeTimes = 5)
    public class LockInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理冻结库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));

            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.LOCK);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到冻结库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                throw new BizException("未查询到冻结库存事务消息");
            }
            String changeType = areaStoreInventoryReqDTO.getType();
            if (StringUtils.isNotEmpty(areaStoreInventoryReqDTO.getSubType())) {
                changeType = areaStoreInventoryReqDTO.getSubType();
            }
            try {
                areaStoreService.updateLockStockByWarehouseNo(areaStoreInventoryReqDTO.getDiffStock(),
                        areaStoreInventoryReqDTO.getSku(), areaStoreInventoryReqDTO.getWarehouseNo(),
                        changeType, areaStoreInventoryReqDTO.getBizId(), areaStoreInventoryReqDTO.getOperator());
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.FINISH.getCode());
            } catch (Exception e) {
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.ERROR.getCode());
                throw e;
            } finally {
                areaStoreInventoryTransactionRepository.changeAreaStoreInventoryTransaction(areaStoreInventoryTransaction);
            }
        }
    }

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_SALE_LOCK_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_SALE_LOCK_CG, maxReconsumeTimes = 5)
    public class SaleLockInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理销售冻结库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));

            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.SALE_LOCK);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到销售冻结库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                throw new BizException("未查询到销售冻结库存事务消息");
            }
            String changeType = areaStoreInventoryReqDTO.getType();
            if (StringUtils.isNotEmpty(areaStoreInventoryReqDTO.getSubType())) {
                changeType = areaStoreInventoryReqDTO.getSubType();
            }
            try {
                areaStoreService.updateLockStockByWarehouseNo(areaStoreInventoryReqDTO.getDiffStock(),
                        areaStoreInventoryReqDTO.getSku(), areaStoreInventoryReqDTO.getWarehouseNo(),
                        changeType, areaStoreInventoryReqDTO.getBizId(), areaStoreInventoryReqDTO.getOperator());
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.FINISH.getCode());
            } catch (Exception e) {
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.ERROR.getCode());
                throw e;
            } finally {
                areaStoreInventoryTransactionRepository.changeAreaStoreInventoryTransaction(areaStoreInventoryTransaction);
            }
        }
    }

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_STORE_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_STORE_CG, maxReconsumeTimes = 5)
    public class StoreInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理仓库库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));

            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.STORE);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到仓库库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                throw new BizException("未查询到仓库库存事务消息");
            }
            String changeType = areaStoreInventoryReqDTO.getType();
            if (StringUtils.isNotEmpty(areaStoreInventoryReqDTO.getSubType())) {
                changeType = areaStoreInventoryReqDTO.getSubType();
            }
            try {
                areaStoreService.updateStoreStockByWarehouseNo(areaStoreInventoryReqDTO.getDiffStock(),
                        areaStoreInventoryReqDTO.getSku(), areaStoreInventoryReqDTO.getWarehouseNo(),
                        changeType, areaStoreInventoryReqDTO.getBizId(), areaStoreInventoryReqDTO.getOperator());
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.FINISH.getCode());
            } catch (Exception e) {
                areaStoreInventoryTransaction.setTransactionState(TransactionStateEnum.ERROR.getCode());
                throw e;
            } finally {
                areaStoreInventoryTransactionRepository.changeAreaStoreInventoryTransaction(areaStoreInventoryTransaction);
            }
        }
    }

    @MqListener(topic = WmsConstant.AREA_STORE_INVENTORY_TOPIC, tag = WmsConstant.AREA_STORE_INVENTORY_SAFE_TAG,
            consumerGroup = WmsConstant.AREA_STORE_INVENTORY_SAFE_CG, maxReconsumeTimes = 5)
    public class SafeInventoryListener extends AbstractMqListener<AreaStoreInventoryReqDTO> {

        @Override
        public void process(AreaStoreInventoryReqDTO areaStoreInventoryReqDTO) {
            log.info("开始处理安全库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));

            AreaStoreInventoryTransaction areaStoreInventoryTransaction = getNormalTransaction(areaStoreInventoryReqDTO, AreaStoreInventoryTypeEnum.SAFE);
            if (Objects.isNull(areaStoreInventoryTransaction)) {
                log.warn("未查询到安全库存事务消息:{}", JSON.toJSONString(areaStoreInventoryReqDTO));
                return;
            }
            return;
        }
    }
}
