<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionOperatorDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="cancel_time" jdbcType="BIGINT" property="cancelTime"/>
        <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, mission_no, cancel_time, operator_id, operator_name,
    `state`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_operator
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByMissionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_operator
        where mission_no = #{missionNo}
    </select>
    <select id="selectByMissionNoAndState" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_operator
        where mission_no = #{missionNo}
        and state in
        <foreach collection="states" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_operator
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorDO"
            useGeneratedKeys="true">
        insert into wms_mission_operator (create_time, update_time, mission_no,
                                          cancel_time, operator_id, operator_name,
                                          `state`)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{missionNo,jdbcType=VARCHAR},
                #{cancelTime,jdbcType=BIGINT}, #{operatorId,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR},
                #{state,jdbcType=INTEGER})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorDO"
            useGeneratedKeys="true">
        insert IGNORE into wms_mission_operator (mission_no,
        cancel_time, operator_id, operator_name,
        `state`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.missionNo,jdbcType=VARCHAR},
            #{item.cancelTime,jdbcType=BIGINT}, #{item.operatorId,jdbcType=VARCHAR},
            #{item.operatorName,jdbcType=VARCHAR},
            #{item.state,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorDO"
            useGeneratedKeys="true">
        insert ignore into wms_mission_operator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="state != null">
                `state`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorDO">
        update wms_mission_operator
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByOperator"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionOperatorUpdateDO">
        update wms_mission_operator
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
        </set>
        where mission_no = #{missionNo} and operator_name = #{operatorName} and operator_id = #{operatorId}
        <if test="cancelTimeCond != null">
            and `cancel_time` = #{cancelTimeCond}
        </if>
    </update>
</mapper>