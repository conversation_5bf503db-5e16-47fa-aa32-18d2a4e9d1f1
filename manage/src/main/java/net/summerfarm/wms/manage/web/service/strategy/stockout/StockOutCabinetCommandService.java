package net.summerfarm.wms.manage.web.service.strategy.stockout;

import net.summerfarm.wms.api.h5.stocktask.dto.req.DiffPickingMoveDTO;
import net.summerfarm.wms.manage.model.domain.StockTask;
import net.summerfarm.wms.manage.model.domain.StockTaskItemCabinetOccupyDO;
import net.summerfarm.wms.manage.web.service.strategy.stockout.input.StockOutCabinetCommandInput;
import net.summerfarm.wms.manage.web.service.strategy.stockout.input.StockOutReduceCabinetInventoryInput;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8
 */
public interface StockOutCabinetCommandService {

    /**
     * 处理出库任务拣货映射
     * @param stockOutCabinetCommandInput
     */
    List<Long> handleMissionTaskItemMapping(StockOutCabinetCommandInput stockOutCabinetCommandInput, Set<String> missionNoSet);

    /**
     * 处理出库差异库存冻结
     * @param stockOutCabinetCommandInput
     * @param missionTaskItemMappingUpdateIdList
     */
    void handleInventoryLockWithDiffOut(StockOutCabinetCommandInput stockOutCabinetCommandInput, List<Long> missionTaskItemMappingUpdateIdList);

    /**
     * 缺拣或缺出释放差异库位库存占用
     * @param stockOutReduceCabinetInventoryInput
     * @param stockTaskItemCabinetOccupyDOS
     * @param stockTask
     */
    void releaseCabinetInventoryLockWithDiffOut(StockOutReduceCabinetInventoryInput stockOutReduceCabinetInventoryInput, List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS, StockTask stockTask, List<DiffPickingMoveDTO> diffPickingMoveDTOList);
}
