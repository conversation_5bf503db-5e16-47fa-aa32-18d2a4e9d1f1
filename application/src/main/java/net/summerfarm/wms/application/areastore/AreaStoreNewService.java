package net.summerfarm.wms.application.areastore;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreQuery;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreSafeStockUpdateDto;
import net.summerfarm.wms.application.areastore.dto.req.AreaStoreSkuQuery;
import net.summerfarm.wms.application.areastore.dto.res.AreaStoreDTO;
import net.summerfarm.wms.application.areastore.dto.res.AreaStoreSkuRes;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/9/4
 * 迁移接口
 * AreaStoreService 看目录结构不太对
 */
public interface AreaStoreNewService {

    /**
     * 查询有库存的仓列表
     *
     * @param areaStoreQuery 查询条件内容
     * @return 返回查询到的条件
     **/
    List<AreaStoreDTO> selectAreaStockList(AreaStoreQuery areaStoreQuery);

    /**
     * 查询库存商品列表
     */
    PageInfo<AreaStoreSkuRes> selectAreaStockSkuList(AreaStoreSkuQuery areaStoreQuery);

    /**
     * 更新安全库存数量
     * @param updateDto
     * @return
     */
    CommonResult<String> updateSafeStock(AreaStoreSafeStockUpdateDto updateDto);

}
