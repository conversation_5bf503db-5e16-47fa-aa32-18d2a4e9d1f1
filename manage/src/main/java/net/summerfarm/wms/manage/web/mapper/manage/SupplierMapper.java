package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.Supplier;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Repository
public interface SupplierMapper {

    Supplier selectByPrimaryKey(@Param("id") Integer id);

    List<Supplier> selectAll();

    Supplier selectByName(@Param("name") String name);

    List<Supplier> selectByPrimaryKeyList(List<Integer> ids);
}
