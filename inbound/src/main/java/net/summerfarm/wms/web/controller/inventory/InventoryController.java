package net.summerfarm.wms.web.controller.inventory;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.wms.api.h5.inventory.dto.InventoryQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.enums.StorageLocationEnum;
import net.summerfarm.wms.api.h5.inventory.dto.req.InventoryQuery;
import net.summerfarm.wms.api.h5.inventory.dto.res.InventoryRelationDTO;
import net.summerfarm.wms.api.h5.inventory.dto.input.InventoryPageInput;
import net.summerfarm.wms.api.h5.inventory.dto.input.InventoryQueryInput;
import net.summerfarm.wms.web.controller.inventory.vo.InventoryVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * sku信息
 * @author: dongcheng
 * @date: 2023/9/5
 */
@RestController
@RequestMapping(value = "/inventory")
public class InventoryController {

    @Resource
    private InventoryQueryService inventoryQueryService;

    /**
     * sku详情信息
     *
     * @param query 查询sku详情信息
     * @return 返回查询的
     */
    @PostMapping(value = "/skus/sku")
    public CommonResult<InventoryRelationDTO> selectBySku(@RequestBody InventoryQuery query){
        InventoryRelationDTO inventory = inventoryQueryService.selectBySku(query);
        return CommonResult.ok(inventory);
    }

    /**
     * 查询使用中的商品信息列表
     *
     * @param query 查询sku详情信息
     * @return 返回查询的
     */
    @PostMapping(value = "/sku-list")
    public CommonResult<List<InventoryVO>> selectInventoryBySku(@RequestBody @Valid InventoryQueryInput query){
        List<InventoryRelationDTO> relationList = inventoryQueryService.selectInventoryBySku(query);
        List<InventoryVO> inventoryList = convert(relationList, query.getWarehouseNo());
        return CommonResult.ok(inventoryList);
    }

    /**
     * 分页查询使用中的商品信息列表
     *
     * @param query 查询sku详情信息
     * @return 返回查询的
     */
    @PostMapping(value = "/sku-page")
    public CommonResult<PageInfo<InventoryVO>> pageInventoryBySku(@RequestBody @Valid InventoryPageInput query){
        PageInfo<InventoryRelationDTO> pageInfo = inventoryQueryService.pageInventoryBySku(query);
        List<InventoryRelationDTO> relationList = pageInfo.getList();
        List<InventoryVO> inventoryList = convert(relationList, query.getWarehouseNo());

        PageInfo<InventoryVO> respPage = new PageInfo<>();
        respPage.setPages(pageInfo.getPages());
        respPage.setTotal(pageInfo.getTotal());
        respPage.setPageNum(pageInfo.getPageNum());
        respPage.setPageSize(pageInfo.getPageSize());
        respPage.setList(inventoryList);
        return CommonResult.ok(respPage);
    }

    public List<InventoryVO> convert(List<InventoryRelationDTO> list, Integer warehouseNo) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(relation -> {
            return InventoryVO.builder()
                    .sku(relation.getSku())
                    .warehouseNo(warehouseNo)
                    .pdId(relation.getPdId())
                    .packing(relation.getUnit())
                    .pdName(relation.getPdName())
                    .skuType(relation.getType())
                    .skuSubType(relation.getSkuSubType())
                    .skuPic(relation.getSkuPic())
                    .spuPic(relation.getPicturePath())
                    .isDomestic(relation.getIsDomestic())
                    .qualityTime(relation.getQualityTime())
                    .qualityTimeUnit(relation.getQualityTimeUnit())
                    .qualityTimeType(relation.getQualityTimeType())
                    .specification(relation.getWeight())
                    .weight(relation.getWeight())
                    .quantity(relation.getQuantity())
                    .safeQuantity(relation.getSafeQuantity())
                    .lockQuantity(relation.getLockQuantity())
                    .onlineQuantity(relation.getOnlineQuantity())
                    .storageLocation(Objects.isNull(relation.getStorageLocation()) ? "" : StorageLocationEnum.getDescByCode(Integer.valueOf(relation.getStorageLocation())))
                    .categoryId(relation.getCategoryId())
                    .categoryType(relation.getCategoryType())
                    .weightNum(relation.getWeightNum())
                    .build();
        }).collect(Collectors.toList());
    }
}
