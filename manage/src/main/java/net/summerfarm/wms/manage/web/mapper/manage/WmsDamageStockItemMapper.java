package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.StoreRecord;
import net.summerfarm.wms.manage.model.domain.WmsDamageStockItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Repository
public interface WmsDamageStockItemMapper {

    /**
     * 查询调拨货损出库条目
     * @param listNo
     * @param type
     * @return
     */
    List<WmsDamageStockItem> selectByTaskNoAndType(@Param("taskNo") String listNo, @Param("type") Integer type);

    /**
     * 查询货损冻结批次数量
     * @param select
     * @return
     */
    Integer selectLockBatch(StoreRecord select);

    int insertSelective(WmsDamageStockItem record);

    /**
     * 根据货损出库ID删除
     * @param id
     */
    void deleteByTaskId(Long id);

    int deleteByPrimaryKey(Long id);

    int insert(WmsDamageStockItem record);

    /**
     * 根据货损出库ID查看
     * @param id
     * @return
     */
    List<WmsDamageStockItem> selectByDamageStockTaskId(Long id);

}
