package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.SampleSku;
import net.summerfarm.wms.manage.model.domain.SampleApply;
import net.summerfarm.wms.manage.model.domain.StockTaskPick;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Repository
public interface SampleApplyMapper {
    SampleApply selectSampleById(Integer sampleId);

    List<SampleSku> selectByDeliveryTime(@Param("areaNo") Integer areaNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("trustStoreNo") Integer trustStoreNo);

    /**
     * 获取出样捡货信息
     */
    List<StockTaskPick> selectSampleByType(@Param("areaNo") Integer areaNo, @Param("deliveryTime") LocalDate deliveryTime,
                                           @Param("trustStoreNo") Integer trustStoreNo, @Param("closeTime") String closeTime);
}
