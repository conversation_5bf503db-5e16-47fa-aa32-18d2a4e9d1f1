package net.summerfarm.wms.common.constant;

public class WmsConstant {
    public static final Integer PAGE_SIZE = 20;
    public static final Integer PARTIAL_SIZE = 100;
    public static final Integer PARTITION_SIZE = 200;
    public static final String SYSTEM = "系统";
    public static final String SYSTEM_ID = "0";
    public static final Long SYSTEM_ID_LONG = 0L;
    public static final String SPLIT = ":";
    public static final String SPLIT_D = ",";
    public static final String SPLIT_U = "_";
    public static final String MONTH = "month";
    public static final String SPLIT_CABINET_NO = "-";
    public static final Integer BATCH_SIZE_LIMIT = 50;
    public static final Integer PAGE_INIT = 1000;
    public static final Integer STOCK_OUT_PARTITION_SIZE = 20;

    public static final String NULL = "null";

    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";

    // config key
    public static final String CODE_URL_PREFIX = "CODE_URL";

    // MQ
    // 统一gid
    public static final String MQ_WMS_GID ="GID_wms";
    // 外部gid
    public static final String MQ_WMS_EXTERNAL_GID ="GID_wms_external";
    // 顺序消息gid
    public static final String MQ_WMS_ORDERLY_GID ="GID_wms_common_orderly";
    // 库存
    public static final String AREA_STORE_INVENTORY_PG = "GID_wms_area_store_inventory_p";
    public static final String WMS_CONSUMER_AREA_STORE_INVENTORY_CG = "wms_area_store_inventory_c";
    // 在途可售
    public static final String TOPIC_INVENTORY_ORDERLY = "topic_wms_inventory_orderly";
    public static final String TAG_INVENTORY_ORDER_ROAD_SALE = "tag_wms_inventory_road_sale";
    // sku规格共享
    public static final String INVENTORY_ORDER_SKU_SHARE_CONSUMER_GROUP = "GID_wms_inventory_order_sku_share";

    public static final String INVENTORY_APPLY_ROAD_SALE_CONSUMER_GROUP = "GID_wms_inventory_apply_road_sale";

    public static final String INVENTORY_UPDATE_ROAD_SALE_CONSUMER_GROUP = "GID_wms_orderly_backup";

    public static final String INVENTORY_IN_STORE_ROAD_SALE_CONSUMER_GROUP = "GID_wms_inventory_in_store_road_sale";

    public static final String INVENTORY_ABNORMAL_ROAD_SALE_CONSUMER_GROUP = "GID_wms_inventory_abnormal_road_sale";

    public static final String INVENTORY_CANCEL_ROAD_SALE_CONSUMER_GROUP = "GID_wms_inventory_cancel_road_sale";


    public final static String TOPIC_WMS_STOCK_TASK = "topic_wms_stock_task";
    public final static String TAG_COST_BATCH_RELATION = "tag_wms_cost_batch_create";
    // TMS完成排线
    public static final String TMS_PATH = "topic_tms_path";

    // 事务消息topic
    public static final String AREA_STORE_INVENTORY_TOPIC = "topic_wms_area_store_inventory";
    public static final String AREA_STORE_INVENTORY_ROAD_TAG = "tag_wms_inventory_road";
    public static final String AREA_STORE_INVENTORY_ONLINE_TAG = "tag_wms_inventory_online";
    public static final String CABINET_INVENTORY_ADD_TAG = "tag_wms_cabinet_inventory_add";
    public static final String AUTO_SHELVING_TAG = "tag_wms_auto_shelving_add";
    public static final String CABINET_INVENTORY_REDUCE_TAG = "tag_wms_cabinet_inventory_reduce";
    public static final String CABINET_INVENTORY_MOVE_TAG = "tag_wms_cabinet_inventory_move";
    public static final String AREA_STORE_INVENTORY_STORE_TAG = "tag_wms_inventory_store";
    public static final String AREA_STORE_INVENTORY_SAFE_TAG = "tag_wms_inventory_safe";
    public static final String AREA_STORE_INVENTORY_LOCK_TAG = "tag_wms_inventory_lock";
    public static final String AREA_STORE_INVENTORY_SALE_LOCK_TAG = "tag_wms_inventory_sale_lock";
    public static final String AREA_STORE_INVENTORY_ROAD_CG = "GID_wms_area_store_inventory_road";
    public static final String AREA_STORE_INVENTORY_ONLINE_CG = "GID_wms_area_store_inventory_online";
    public static final String CABINET_INVENTORY_ADD_CG = "GID_wms_area_store_inventory_cabinet_add";
    public static final String CABINET_INVENTORY_REDUCE_CG = "GID_wms_area_store_inventory_cabinet_reduce";
    public static final String AREA_STORE_INVENTORY_STORE_CG = "GID_wms_area_store_inventory_store";
    public static final String AREA_STORE_INVENTORY_SAFE_CG = "GID_wms_area_store_inventory_safe";
    public static final String AREA_STORE_INVENTORY_LOCK_CG = "GID_wms_area_store_inventory_lock";
    public static final String AREA_STORE_INVENTORY_SALE_LOCK_CG = "GID_wms_area_store_inventory_sale_lock";
    public static final String AUTO_SHELVING_CG = "GID_wms_auto_shelving_cg";
    public static final String PICK_CREATE_GID = "GID_wms_picking_create";
    public static final String PICK_FINISH_GID = "GID_wms_picking_finish";
    public static final String PICK_COMMIT_GID = "GID_wms_picking_commit";
    public static final String PICKING_AUTO_GID = "GID_wms_picking_auto";

    public static final String STOCK_OUT_COMMON_INVENTORY_PROCESS_GID = "GID_wms_stock_out_common_inventory_process";

    public static final String STOCKTAKING_EXE_GID = "GID_wms_stocktaking_exe";
    public static final String STOCKTAKING_CREATE_GID = "GID_wms_stocktaking_create";
    public static final String STOCKTAKING_CREATE_TAG = "tag_wms_stocktaking_create";

    public static final String MQ_TOPIC ="wms-list";
    public static final String MQ_EXTERNAL_TOPIC ="topic_wms_external";
    public static final String PICKING_MISSION_CREATE_TAG = "tag_wms_picking_create";
    public static final String PICKING_MISSION_COMMIT_TAG = "tag_wms_picking_commit";
    public static final String MISSION_FINISH_TAG = "tag_wms_mission_finish";
    public static final String PICKING_AUTO_TAG = "tag_wms_picking_auto";
    public static final String PURCHASE_BACK_LOCK = "tag_purchase_back_lock_cabinet_batch";
    public static final String ORDER_UPDATE_TAG = "tag_order_update_out_store";

    public static final String DIFF_PICKING_MOVE_TAG = "tag_wms_diff_picking_move";
    public static final String DIFF_PICKING_MOVE_GID = "GID_wms_diff_picking_move";

    public static final String CONTAINER_RELEASE_PICK_TAG = "tag_wms_container_release_pick";
    public static final String CONTAINER_RELEASE_PICK_GID = "GID_wms_container_release_pick";

    // 入库任务
    public static final String STOCK_STORAGE_TASK_CREATE_TAG = "tag_wms_stock_storage_task_create";
    public static final String IN_BOUND_BATCH_CREATE_TAG = "tag_in_bound_batch_create";
    public static final String IN_BOUND_PROVE_CREATE_TAG = "tag_in_bound_prove_create";
    public static final String IN_BOUND_SKU_CODE_CREATE_TAG = "tag_in_bound_sku_code_create";
    public static final String STOCK_STORAGE_TASK_CREATE_CG = "GID_wms_stock_storage_task_create_cg";
    public static final String IN_BOUND_OPERATOR_CG = "GID_in_bound_operator_cg";
    //入库任务自动完成
    public static final String IN_BOUND_AUTO_COMPLETE_TAG = "tag_in_bound_auto_complete_tag";
    //入库溯源码唯一码生成
    public static final String SKU_BATCH_CODE_TRACE_CREATE_TAG = "tag_sku_batch_code_trace_create_tag";
    // 城配仓完成排线
    public static final String TAG_DELIVERY_STORE_COMPLETE_PATH = "tag_delivery_store_complete_path";

    // 出库任务
    // 货损任务创建
    public static final String STOCK_DAMAGE_CREATE = "tag_stock_damage_create";

    // 盘点任务
    public static final String STOCKTAKING_FINISH_TAG = "tag_wms_stocktaking_finish";
    public static final String STOCKTAKING_EXE_TAG = "tag_wms_stocktaking_exe";
    public static final String ONSALE_STOCKTAKING_EXE_TAG = "tag_wms_onsale_stocktaking_exe";

    // 销售库存
    public static final String SALE_INVENTORY_RELEASE = "tag_wms_sale_inventory_release";

    // 出库通用库存处理流程
    public static final String STOCK_OUT_COMMON_INVENTORY_PROCESS = "tag_wms_stock_out_common_inventory_process";
    // 出库通用库存失败处理流程
    public static final String STOCK_OUT_COMMON_INVENTORY_FAILED_PROCESS = "tag_wms_stock_out_common_inventory_failed_process";
    // 调拨出库后置关闭流程处理流程
    public static final String STOCK_OUT_ALLOCATION_POST_CLOSE_PROCESS = "tag_wms_stock_out_allocation_post_close_process";
    // 出库通用库存处理校验流程
    public static final String STOCK_OUT_COMMON_INVENTORY_RECHECK_PROCESS = "tag_wms_stock_out_common_inventory_recheck_process";

    // 外部路由出库调用
    public static final String EXTERNAL_ROUTE_STOCK_OUT_INVOKE = "tag_wms_external_route_stock_out_invoke";

    // 外部路由出库调用
    public static final String EXTERNAL_ROUTE_STOCK_OUT_CALLBACK_INVOKE = "tag_wms_external_route_stock_out_callback_invoke";

    // 外部路由出库调用
    public static final String EXTERNAL_ROUTE_PURCHASE_OUT_BILL_INVOKE = "tag_wms_external_route_purchase_out_bill_invoke";

    // 外部路由入库调用
    public static final String EXTERNAL_ROUTE_PURCHASE_IN_BILL_INVOKE = "tag_wms_external_route_purchase_in_bill_invoke";

    // 外部路由入库调用
    public static final String EXTERNAL_ROUTE_STOCK_IN_CALLBACK_INVOKE = "tag_wms_external_route_stock_in_callback_invoke";
    // 履约通知异常订单巡检完成
    public static final String TAG_COMPLETE_ABNORMAL_ORDER_INSPECT = "tag_complete_abnormal_order_inspect";
    // 调拨入库初始化调拨在途批次库存
    public static final String TAG_ALLOCATION_INIT_ROAD_COST_BATCH = "tag_allocation_init_road_cost_batch";
    // 调拨货损/调拨回库更新调拨在途批次库存
    public static final String TAG_ALLOCATION_ABNORMAL_UPDATE_ROAD_COST_BATCH = "tag_allocation_abnormal_update_road_cost_batch";
    // 调拨入库更新调拨在途批次库存
    public static final String TAG_ALLOCATION_IN_UPDATE_ROAD_COST_BATCH = "tag_allocation_in_update_road_cost_batch";
    // 拦截入库任务生成
    public static final String INTERCEPT_STOCK_TASK_STORAGE_CREATE = "tag_wms_intercept_stock_task_storage_create";
    // 入库通知单生成回告
    public static final String STOCK_TASK_STORAGE_NOTICE_CREATE_CALLBACK = "tag_create_storage_notice_order";

    // 外部路由盘点调用
    public static final String EXTERNAL_ROUTE_STOCK_TAKING_INVOKE = "tag_wms_external_route_stock_taking_invoke";

    // 外部路由盘点提交
    public static final String EXTERNAL_ROUTE_STOCK_TAKING_COMMIT_INVOKE = "tag_wms_external_route_stock_taking_commit_invoke";

    // 外部路由盘点回告调用
    public static final String EXTERNAL_ROUTE_STOCK_TAKING_CALLBACK_INVOKE = "tag_wms_external_route_stock_taking_callback_invoke";

    // 外部路由盘点取消调用
    public static final String EXTERNAL_ROUTE_STOCK_TAKING_CANCEL_INVOKE = "tag_wms_external_route_stock_taking_cancel_invoke";

    // 外部路由货损调用
    public static final String EXTERNAL_ROUTE_STOCK_DAMAGE_INVOKE = "tag_wms_external_route_stock_damage_invoke";

    // 外部路由货损提交
    public static final String EXTERNAL_ROUTE_STOCK_DAMAGE_COMMIT_INVOKE = "tag_wms_external_route_stock_damage_commit_invoke";

    // 外部路由货损取消
    public static final String EXTERNAL_ROUTE_STOCK_DAMAGE_CANCEL_INVOKE = "tag_wms_external_route_stock_damage_cancel_invoke";

    // 外部路由货损回告调用
    public static final String EXTERNAL_ROUTE_STOCK_DAMAGE_CALLBACK_INVOKE = "tag_wms_external_route_stock_damage_callback_invoke";

    // 外部路由转换调用
    public static final String EXTERNAL_ROUTE_STOCK_TRANSFER_INVOKE = "tag_wms_external_route_stock_transfer_invoke";

    // 外部路由转换回告调用
    public static final String EXTERNAL_ROUTE_STOCK_TRANSFER_CALLBACK_INVOKE = "tag_wms_external_route_stock_transfer_callback_invoke";

    /**
     * 物料任务调用
     */
    public static final String WMS_MATERIAL_TASK_NOTICE = "tag_wms_material_task_notice";

    /**
     * 鲜沐tenantId
     */
    public static final Long XIANMU_TENANT_ID = 1L;

    /**
     * 鲜沐仓库服务商
     */
    public static final String XIANMU_COMPANY_NAME = "杭州鲜沐科技有限公司";

    /**
     * 默认容器编码
     */
    public static final String DEFAULT_CONTAINER_CODE = "MR00001";

    public static final String DEFAULT_ZONE = "MR01";

    public static final String TRUE = "TRUE";
    public static final String FALSE = "FALSE";

    public static final String STOCK_IN_NOTICE_BATCH_PRE = "IB";
    public static final String INVENTORY_LOCK_PRE = "SD";

    public static final Integer MAX_FIELD_VALUE_LENGTH_10000 = 10000;
    public static final Integer MAX_FIELD_VALUE_LENGTH_5000 = 5000;
    public static final Integer MAX_FIELD_VALUE_LENGTH_3000 = 3000;
    public static final Integer MAX_FIELD_VALUE_LENGTH_1000 = 1000;

    public static Long getTenantId(Long tenantId){
        if (tenantId == null){
            return XIANMU_TENANT_ID;
        };

        return tenantId;
    }

    /**
     * 是否鲜沐租户
     * @param tenantId
     * @return
     */
    public static boolean isXm(Long tenantId) {
        if (tenantId != null && tenantId > XIANMU_TENANT_ID) {
            return false;
        }
        return true;
    }
}
