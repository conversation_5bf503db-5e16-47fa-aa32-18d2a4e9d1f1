package net.summerfarm.wms.common.util;

/**
 * @author: dongcheng
 * @date: 2023/9/1
 */
public class NumberUtils {

    /**
     * Double类型 0.25
     */
    public static final Double DOUBLE_ZERO_POINT_TWO_FIVE = Double.valueOf(0.25d);

    /**
     * Double类型 0.6
     */
    public static final Double DOUBLE_ZERO_POINT_SIX = Double.valueOf(0.6d);

    /**
     * Double类型的0.67
     */
    public static final Double DOUBLE_ZERO_POINT_SIX_SEVEN = Double.valueOf(0.67d);

    /**
     * Double类型0.9
     */
    public static final Double DOUBLE_ZERO_POINT_NINE = Double.valueOf(0.9d);

    /**
     * Double类型1.2
     */
    public static final Double DOUBLE_ONE_POINT_TWO = Double.valueOf(1.2d);

    /**
     * Double类型1
     */
    public static final Double DOUBLE_ONE = Double.valueOf(1.0d);

    /**
     * Double类型9
     */
    public static final Double DOUBLE_NINE = Double.valueOf(9.0d);

    /**
     * Integer类型0
     */
    public static Integer INTEGER_ZERO = Integer.valueOf(0);

    /**
     * Integer类型1
     */
    public static Integer INTEGER_ONE = Integer.valueOf(1);

    /**
     * Integer类型2
     */
    public static Integer INTEGER_TWO = Integer.valueOf(2);

    /**
     * Integer类型3
     */
    public static Integer INTEGER_THREE = Integer.valueOf(3);
    /**
     * Integer类型5
     */
    public static Integer INTEGER_FIVE = Integer.valueOf(5);

    /**
     * Integer类型7
     */
    public static Integer INTEGER_SEVEN = Integer.valueOf(7);

    /**
     * Integer类型14
     */
    public static Integer INTEGER_FOURTEEN = Integer.valueOf(14);

    /**
     * Integer类型30
     */
    public static Integer INTEGER_THIRTY = Integer.valueOf(30);

    /**
     * Integer类型50
     */
    public static Integer INTEGER_FIFTY = Integer.valueOf(50);

    /**
     * Integer类型100
     */
    public static Integer INTEGER_ONE_HUNDREDS = Integer.valueOf(100);

    /**
     * Integer类型1000
     */
    public static Integer INTEGER_ONE_THOUSAND = Integer.valueOf(1000);

    /**
     * Integer类型200
     */
    public static Integer INTEGER_TWO_HUNDREDS = Integer.valueOf(200);

}
