package net.summerfarm.wms.application.mission.factory;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.cabinet.CabinetStockQueryService;
import net.summerfarm.wms.api.h5.cabinet.req.CabinetCheckQuery;
import net.summerfarm.wms.api.h5.mission.moveCabinet.enums.MoveCabinetTypeEnum;
import net.summerfarm.wms.api.h5.mission.moveCabinet.req.MoveCabinetMissionCommand;
import net.summerfarm.wms.api.h5.mission.req.MissionAssignCommand;
import net.summerfarm.wms.api.h5.mission.req.MissionDetailReceiveCommand;
import net.summerfarm.wms.api.h5.mission.req.MissionOperatorReqDTO;
import net.summerfarm.wms.api.h5.mission.req.MissionReceiveCommand;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingCommitInfoReqDTO;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingMissionCommitCommand;
import net.summerfarm.wms.application.mission.bizobject.ShelvingMissionCreateDTO;
import net.summerfarm.wms.application.mission.bizobject.ShelvingMissionDetailDTO;
import net.summerfarm.wms.application.sku.enums.StorageLocation;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.admin.domainobject.AdminOperator;
import net.summerfarm.wms.domain.crosswarehouse.repository.CrossWarehouseSortInfoRepository;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryRepository;
import net.summerfarm.wms.domain.mission.delegate.MissionQueryDelegate;
import net.summerfarm.wms.domain.mission.domainobject.*;
import net.summerfarm.wms.domain.mission.enums.ExecuteUnitStateEnum;
import net.summerfarm.wms.domain.mission.enums.MissionCarrierTypeEnum;
import net.summerfarm.wms.domain.mission.enums.MissionStateEnum;
import net.summerfarm.wms.domain.mission.enums.MissionTypeEnum;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.mission.util.MissionNoFactory;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.sku.SkuBarcodeRepository;
import net.summerfarm.wms.domain.sku.domainobject.Sku;
import net.summerfarm.wms.domain.sku.domainobject.SkuBarcode;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MissionFactory {

    @Resource
    private MissionRepository missionRepository;

    @Resource
    private MissionNoFactory missionNoFactory;

    @Resource
    private MissionQueryDelegate missionQueryDelegate;

    @Resource
    private ProductRepository productRepository;

    @Resource
    private CabinetBatchInventoryRepository cabinetBatchInventoryRepository;

    @Resource
    private SkuBarcodeRepository skuBarcodeRepository;

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;

    @Resource
    private CabinetStockQueryService cabinetStockQueryService;

    /**
     * 由入库任务触发的上架生成
     *
     * @param shelvingMissionCreateDTO s
     * @return r
     */
    public Mission createMissionForShelving(ShelvingMissionCreateDTO shelvingMissionCreateDTO) {
        // 上架任务
        Mission mission = Mission.builder()
                .missionNo(missionNoFactory.createMissionNo(MissionTypeEnum.SHELVING.getCode()))
                .warehouseNo(shelvingMissionCreateDTO.getWarehouseNo())
                .state(MissionStateEnum.WAIT_ASSIGN.getCode())
                .pMissionNo(shelvingMissionCreateDTO.getPMissionNo())
                .pMissionType(shelvingMissionCreateDTO.getPMissionType())
                .cancelTime(0L)
                .creatorName(shelvingMissionCreateDTO.getOperatorName())
                .creatorId(shelvingMissionCreateDTO.getOperatorId())
                .missionType(MissionTypeEnum.SHELVING.getCode())
                .missionQueryDelegate(missionQueryDelegate)
                .tenantId(shelvingMissionCreateDTO.getTenantId())
                .build();

        MissionSourceProperty sourceProperty = MissionSourceProperty.builder()
                .missionType(mission.getMissionType())
                .missionNo(mission.getMissionNo())
                .sourceType(shelvingMissionCreateDTO.getSourceType())
                .sourceOrderNo(shelvingMissionCreateDTO.getSourceOrderNo())
                .warehouseNo(mission.getWarehouseNo())
                .build();

        List<String> skus = shelvingMissionCreateDTO.getDetails().stream().map(ShelvingMissionDetailDTO::getSku).collect(Collectors.toList());
        List<Product> products = productRepository.findProductsFromGoodsCenter(QueryProduct.builder().warehouseNo(shelvingMissionCreateDTO.getWarehouseNo()).skus(skus).build());
        Map<String, Product> productMap = products.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));
        List<SkuBarcode> skuBarcodes = skuBarcodeRepository.listBySkus(skus);
        Map<String, List<String>> barCodeMap = skuBarcodes.stream().collect(Collectors.groupingBy(SkuBarcode::getSku,
                Collectors.mapping(SkuBarcode::getBarcode, Collectors.toList())));

        // 执行单元
        ExecuteUnit unit = ExecuteUnit.builder()
                .missionNo(mission.getMissionNo())
                .cancelTime(0L)
                .unitNo(missionNoFactory.createUnitNo())
                .missionType(mission.getMissionType())
                .sourceType(shelvingMissionCreateDTO.getSourceType())
                .state(ExecuteUnitStateEnum.EXE.getCode())
                .sourceNo(shelvingMissionCreateDTO.getSourceOrderNo())
                .carrierCode(shelvingMissionCreateDTO.getContainerNo())
                .carrierType(MissionCarrierTypeEnum.CONTAINER.getCode())
                .warehouseNo(mission.getWarehouseNo())
                .build();
        if (shelvingMissionCreateDTO.getIsPda() != null && shelvingMissionCreateDTO.getIsPda()) {
            StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(
                    Long.parseLong(shelvingMissionCreateDTO.getPMissionNo()));
            if (stockTaskStorage.getState() != StockTaskStorageStateEnum.FINISH.getId()) {
                unit.setState(ExecuteUnitStateEnum.INIT.getCode());
            }
        }

        // 任务明细扩展
        List<MissionDetail> details = shelvingMissionCreateDTO.getDetails().stream()
                .map(item -> {
                    MissionDetailExtend extend = null;
                    Product product1 = productMap.get(item.getSku());
                    if (Objects.nonNull(product1)) {
                        Sku skuInfo = Sku.builder()
                                .sku(item.getSku())
                                .pdName(product1.getPdName())
                                .cargoType(Objects.equals(4, product1.getCategoryType()) ? "鲜果" : "标品")
                                .packaging(product1.getPackaging())
                                .specification(product1.getSpecification())
                                .pic(product1.getPic())
                                .temperature(StorageLocation.getTypeById(product1.getTemperature()))
                                .barcode(String.join(",", barCodeMap.getOrDefault(item.getSku(), Lists.newArrayList())))
                                .build();
                        extend = MissionDetailExtend.builder()
                                .missionNo(mission.getMissionNo())
                                .sku(item.getSku())
                                .extend(JSON.toJSONString(skuInfo))
                                .warehouseNo(mission.getWarehouseNo())
                                .build();
                    }
                    // 任务明细
                    return MissionDetail.builder()
                            .shouldInQuantity(item.getShouldInQuantity())
                            .warehouseNo(mission.getWarehouseNo())
                            .sku(item.getSku())
                            .unitNo(unit.getUnitNo())
                            .shelfLife(item.getShelfLife())
                            .produceTime(item.getProduceTime())
                            .missionNo(mission.getMissionNo())
                            .exeQuantity(0)
                            .batchNo(item.getBatchNo())
                            .extend(extend)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());

        mission.setMissionDetails(details);
        mission.setExecuteUnits(Lists.newArrayList(unit));
        mission.setSourceProperty(Lists.newArrayList(sourceProperty));
        return mission;

    }

    public List<MissionDetail> createShelvingMissionDetailForAppend(ShelvingMissionCreateDTO dto, Mission mission) {
        List<String> skus = dto.getDetails().stream().map(ShelvingMissionDetailDTO::getSku).collect(Collectors.toList());
        List<Product> products = productRepository.findProductsFromGoodsCenter(QueryProduct.builder().warehouseNo(mission.getWarehouseNo()).skus(skus).build());
        Map<String, Product> productMap = products.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (o1, o2) -> o1));
        List<SkuBarcode> skuBarcodes = skuBarcodeRepository.listBySkus(skus);
        Map<String, String> barCodeMap = skuBarcodes.stream().collect(Collectors.toMap(SkuBarcode::getSku, SkuBarcode::getBarcode, (o1, o2) -> o1));

        ExecuteUnit executeUnit = mission.getExecuteUnits().get(NumberUtils.INTEGER_ZERO);

        return dto.getDetails().stream()
                .map(item -> {
                    MissionDetailExtend extend = null;
                    Product product1 = productMap.get(item.getSku());
                    if (Objects.nonNull(product1)) {
                        Sku skuInfo = Sku.builder()
                                .sku(item.getSku())
                                .pdName(product1.getPdName())
                                .cargoType(Objects.equals(4, product1.getCategoryType()) ? "鲜果" : "标品")
                                .packaging(product1.getPackaging())
                                .specification(product1.getSpecification())
                                .temperature(StorageLocation.getTypeById(product1.getTemperature()))
                                .barcode(barCodeMap.get(item.getSku()))
                                .build();
                        extend = MissionDetailExtend.builder()
                                .missionNo(mission.getMissionNo())
                                .sku(item.getSku())
                                .extend(JSON.toJSONString(skuInfo))
                                .warehouseNo(mission.getWarehouseNo())
                                .build();
                    }
                    // 任务明细
                    return MissionDetail.builder()
                            .shouldInQuantity(item.getShouldInQuantity())
                            .warehouseNo(mission.getWarehouseNo())
                            .sku(item.getSku())
                            .unitNo(executeUnit.getUnitNo())
                            .shelfLife(item.getShelfLife())
                            .produceTime(item.getProduceTime())
                            .missionNo(mission.getMissionNo())
                            .exeQuantity(0)
                            .batchNo(item.getBatchNo())
                            .tenantId(mission.getTenantId())
                            .extend(extend)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Mission createMissionForMoveCabinet(MoveCabinetMissionCommand command) {
        // 移库任务
        Integer sourceType = null;
        if (StringUtils.isNotEmpty(command.getSku()) && command.getTargetCabinetNo() != null) {
            sourceType = MoveCabinetTypeEnum.ONE_STEP.getCode();
        }
        Mission mission = Mission.builder()
                .missionNo(missionNoFactory.createMissionNo(MissionTypeEnum.MOVE_CABINET.getCode()))
                .warehouseNo(command.getWarehouseNo())
                .state(MissionStateEnum.WAIT_ASSIGN.getCode())
                .pMissionNo(null)
                .pMissionType(null)
                .cancelTime(0L)
                .tenantId(command.getTenantId())
                .creatorName(command.getOperatorName())
                .creatorId(command.getOperatorId())
                .missionType(MissionTypeEnum.MOVE_CABINET.getCode())
                .syncExeInventory(command.getSyncExeInventory())
                .missionQueryDelegate(missionQueryDelegate)
                .build();

        MissionSourceProperty sourceProperty = MissionSourceProperty.builder()
                .missionType(mission.getMissionType())
                .missionNo(mission.getMissionNo())
                .sourceType(sourceType)
                .warehouseNo(mission.getWarehouseNo())
                .build();

        mission.setSourceProperty(Lists.newArrayList(sourceProperty));

        // 执行单元
        ExecuteUnit unit = ExecuteUnit.builder()
                .missionNo(mission.getMissionNo())
                .cancelTime(0L)
                .unitNo(missionNoFactory.createUnitNo())
                .missionType(mission.getMissionType())
                .sourceType(null)
                .state(ExecuteUnitStateEnum.EXE.getCode())
                .sourceNo(null)
                .tenantId(command.getTenantId())
                .carrierCode(command.getMoveCabinetNo())
                .carrierType(MissionCarrierTypeEnum.CABINET.getCode())
                .warehouseNo(mission.getWarehouseNo())
                .build();

        // 任务明细扩展
        MissionDetailExtend extend = null;
        List<Product> products = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                .warehouseNo(mission.getWarehouseNo())
                .skus(Lists.newArrayList(command.getSku()))
                .build());
        List<SkuBarcode> skuBarcodes = skuBarcodeRepository.listBySkus(Lists.newArrayList(command.getSku()));
        Map<String, String> barCodeMap = skuBarcodes.stream().collect(Collectors.toMap(SkuBarcode::getSku, SkuBarcode::getBarcode, (o1, o2) -> o1));

        if (CollectionUtils.isNotEmpty(products)) {
            Product product = products.get(NumberUtils.INTEGER_ZERO);
            Sku skuInfo = Sku.builder()
                    .sku(command.getSku())
                    .pdName(product.getPdName())
                    .cargoType(Objects.equals(4, product.getCategoryType()) ? "鲜果" : "标品")
                    .packaging(product.getPackaging())
                    .specification(product.getSpecification())
                    .temperature(StorageLocation.getTypeById(product.getTemperature()))
                    .barcode(barCodeMap.get(command.getSku()))
                    .build();
            extend = MissionDetailExtend.builder()
                    .missionNo(mission.getMissionNo())
                    .sku(command.getSku())
                    .extend(JSON.toJSONString(skuInfo))
                    .warehouseNo(mission.getWarehouseNo())
                    .build();
        }

        // 任务明细
        List<CabinetBatchInventory> cabinetBatchInventories = cabinetBatchInventoryRepository.listByWnoAndSkuAndCabinet(command.getWarehouseNo().intValue(), Lists.newArrayList(command.getSku()),
                Lists.newArrayList(command.getMoveCabinetNo()), null, Lists.newArrayList(DateUtil.toDate(command.getProduceTime())),
                Lists.newArrayList(DateUtil.toDate(command.getShelfLife())), false);
        log.info("移库查询出来的库位库存数据:{}", cabinetBatchInventories);
        if (CollectionUtils.isEmpty(cabinetBatchInventories)) {
            throw new BizException(ErrorCodeNew.CABINET_BATCH_QUERY_EX);
        }

        ArrayList<MissionDetail> missionDetails = Lists.newArrayList();
        ArrayList<SubmitDetail> submitDetails = Lists.newArrayList();
        AtomicReference<Integer> assNum = new AtomicReference<>(command.getQuantity());
        MissionDetailExtend finalExtend = extend;
        List<String> errorMsgList = new ArrayList<>();
        cabinetBatchInventories.stream().forEach(detail -> {
            if (assNum.get() <= 0) {
                return;
            }
            try {
                if (detail.getAvailableQuantity() <= assNum.get()) {
                    // 提交明细
                    buildMoveCabinetSubmitDetail(command, mission, unit, submitDetails, detail, detail.getAvailableQuantity());
                    buildMoveCabinetMissionDetail(command, mission, missionDetails, finalExtend, detail, detail.getAvailableQuantity(),
                            unit.getUnitNo());
                }
                if (detail.getAvailableQuantity() > assNum.get()) {
                    buildMoveCabinetSubmitDetail(command, mission, unit, submitDetails, detail, assNum.get());
                    buildMoveCabinetMissionDetail(command, mission, missionDetails, finalExtend, detail, assNum.get(), unit.getUnitNo());
                }
                assNum.set(assNum.get() - detail.getAvailableQuantity());
            } catch (BizException e) {
                log.info("移库分配批次校验过滤", e);
                errorMsgList.add(e.getMessage());
            }
        });
        if (assNum.get() > 0) {
            throw new BizException("库位校验不通过,请检查:" +
                    errorMsgList.stream().collect(Collectors.joining(",")));
        }
        log.info("移库提交明细:{}", mission.getSubmitDetails());


        mission.setSubmitDetails(submitDetails);
        mission.setExecuteUnits(Lists.newArrayList(unit));
        mission.setMissionDetails(missionDetails);
        return mission;
    }

    private void buildMoveCabinetSubmitDetail(MoveCabinetMissionCommand command, Mission mission, ExecuteUnit unit, ArrayList<SubmitDetail> submitDetails, CabinetBatchInventory detail, Integer integer) {
        // 提交明细
        SubmitDetail submitDetail = SubmitDetail.builder()
                .warehouseNo(mission.getWarehouseNo())
                .unitNo(unit.getUnitNo())
                .targetCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                .targetCarrierCode(command.getTargetCabinetNo().trim())
                .sourceCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                .sourceCarrierCode(command.getMoveCabinetNo().trim())
                .sku(command.getSku())
                .shelfLife(command.getShelfLife())
                .quantity(integer)
                .produceTime(command.getProduceTime())
                .opreatorName(command.getOperatorName())
                .cargoOwner(null)
                .batchNo(detail.getBatchNo())
                .missionNo(mission.getMissionNo())
                .operatorId(command.getOperatorId())
                .missionType(mission.getMissionType())
                .tenantId(command.getTenantId())
                .build();

        cabinetStockQueryService.checkCabinet(CabinetCheckQuery.builder()
                .cabinetNo(submitDetail.getTargetCarrierCode())
                .sku(submitDetail.getSku())
                .batch(submitDetail.getBatchNo())
                .produceDate(submitDetail.getProduceTime())
                .qualityDate(submitDetail.getShelfLife())
                .warehouseNo(submitDetail.getWarehouseNo())
                .build());

        submitDetails.add(submitDetail);
    }

    private void buildMoveCabinetMissionDetail(MoveCabinetMissionCommand command, Mission mission, ArrayList<MissionDetail> res,
                                               MissionDetailExtend finalExtend, CabinetBatchInventory detail, Integer shouldInQuantity,
                                               String unitNo) {
        MissionDetail missionDetail = MissionDetail.builder()
                .shouldInQuantity(shouldInQuantity)
                .warehouseNo(mission.getWarehouseNo())
                .sku(command.getSku())
                .unitNo(unitNo)
                .shelfLife(command.getShelfLife())
                .produceTime(command.getProduceTime())
                .missionNo(mission.getMissionNo())
                .cabinetNo(command.getMoveCabinetNo().trim())
                .exeQuantity(0)
                .tenantId(command.getTenantId())
                .batchNo(detail.getBatchNo())
                .extend(finalExtend)
                .build();
        res.add(missionDetail);
    }

    public static List<AdminOperator> createOperatorForAssign(MissionAssignCommand missionAssignCommand) {
        return missionAssignCommand.getOperators().stream()
                .map(item -> AdminOperator.builder()
                        .adminId(item.getOperatorId().toString())
                        .name(item.getOperatorName())
                        .build()).collect(Collectors.toList());
    }

    public static AdminOperator createOperatorForReceive(MissionReceiveCommand command) {
        return AdminOperator.builder()
                .adminId(command.getOperator().getOperatorId().toString())
                .name(command.getOperator().getOperatorName())
                .build();
    }

    public static AdminOperator createOperatorForReceive(MissionDetailReceiveCommand command) {
        return AdminOperator.builder()
                .adminId(command.getOperator().getOperatorId().toString())
                .name(command.getOperator().getOperatorName())
                .build();
    }

    public static List<AdminOperator> createCancelOperator(List<MissionOperatorReqDTO> req) {
        return req.stream()
                .map(item -> AdminOperator.builder()
                        .adminId(item.getOperatorId().toString())
                        .name(item.getOperatorName())
                        .build()).collect(Collectors.toList());
    }

    public static List<AdminOperator> createChangeOperator(List<MissionOperatorReqDTO> req) {
        return req.stream()
                .map(item -> AdminOperator.builder()
                        .adminId(item.getOperatorId().toString())
                        .name(item.getOperatorName())
                        .build()).collect(Collectors.toList());
    }

    public Mission createMissionForCommit(ShelvingMissionCommitCommand command) {
        Mission mission = missionRepository.findMission(command.getMissionNo());
        List<ExecuteUnit> executeUnits = mission.getExecuteUnits();
        Map<String, String> unitMap = executeUnits.stream().collect(Collectors.toMap(ExecuteUnit::getCarrierCode, ExecuteUnit::getUnitNo, (o1, o2) -> o1));
        boolean allHaveBatch = command.getCommitInfo().stream().allMatch(item -> StringUtils.isNotEmpty(item.getBatch()));
        Map<String, List<MissionDetail>> collect = Maps.newHashMap();
        if (!allHaveBatch) {
            List<MissionDetail> missionDetail = missionRepository.findMissionDetailForStocktaking(command.getMissionNo());
            collect = missionDetail.stream().collect(Collectors.groupingBy(MissionDetail::pcDuplicateKey));
        }
        Map<String, List<MissionDetail>> finalCollect = collect;
        List<SubmitDetail> submitDetails = command.getCommitInfo().stream()
                .map(item -> {
                    if (StringUtils.isNotEmpty(item.getBatch())) {
                        SubmitDetail build = SubmitDetail.builder()
                                .missionNo(mission.getMissionNo())
                                .missionType(mission.getMissionType())
                                .operatorId(AdminUtil.getCurrentLoginAdminIdV2().toString())
                                // 没有需要查询
                                .batchNo(item.getBatch())
                                .cargoOwner(null)
                                .opreatorName(AdminUtil.getCurrentLoginAdminNameV2())
                                .produceTime(item.getProduceTime())
                                .quantity(item.getDealNum())
                                .shelfLife(item.getShelfLife())
                                .sku(item.getSku())
                                .sourceCarrierCode(StringUtils.isBlank(item.getSourceContainerNo()) ? null : item.getSourceContainerNo().trim())
                                .sourceCarrierType(MissionCarrierTypeEnum.CONTAINER.getCode())
                                .targetCarrierCode(StringUtils.isBlank(item.getTargetCabinetNo()) ? null : item.getTargetCabinetNo().trim())
                                .targetCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                                .unitNo(unitMap.get(item.getSourceContainerNo()))
                                .warehouseNo(mission.getWarehouseNo())
                                .build();
                        return Lists.newArrayList(build);
                    }
                    // 查询批次
                    List<MissionDetail> need = finalCollect.getOrDefault(item.pcDuplicateKey(), Lists.newArrayList());
                    // 剩余分配数量
                    ArrayList<SubmitDetail> res = Lists.newArrayList();
                    AtomicReference<Integer> assNum = new AtomicReference<>(item.getDealNum());
                    need.forEach(detail -> {
                        if (assNum.get() <= 0) {
                            return;
                        }
                        if (detail.waitExeNum() <= 0) {
                            return;
                        }
                        try {
                            if (detail.waitExeNum() <= assNum.get()) {
                                buildSubmit(mission, unitMap, item, res, detail, detail.waitExeNum());
                            }
                            if (detail.waitExeNum() > assNum.get()) {
                                buildSubmit(mission, unitMap, item, res, detail, assNum.get());
                            }
                            assNum.set(assNum.get() - detail.waitExeNum());
                        } catch (BizException e) {
                            log.info("上架分配批次校验过滤", e);
                        }
                    });
                    if (assNum.get() > 0) {
                        throw new BizException("上架提交失败,请检查库位校验规则以及库位库存");
                    }
                    return res;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        mission.setSubmitDetails(submitDetails);

        return mission;
    }



    private void buildSubmit(Mission mission, Map<String, String> unitMap, ShelvingCommitInfoReqDTO item, ArrayList<SubmitDetail> res, MissionDetail detail, Integer integer) {
        SubmitDetail submitDetail = SubmitDetail.builder()
                .missionNo(mission.getMissionNo())
                .missionType(mission.getMissionType())
                .operatorId(AdminUtil.getCurrentLoginAdminIdV2().toString())
                .batchNo(detail.getBatchNo())
                .cargoOwner(null)
                .opreatorName(AdminUtil.getCurrentLoginAdminNameV2())
                .produceTime(item.getProduceTime())
                .quantity(integer)
                .shelfLife(item.getShelfLife())
                .sku(item.getSku())
                .sourceCarrierCode(item.getSourceContainerNo())
                .sourceCarrierType(MissionCarrierTypeEnum.CONTAINER.getCode())
                .targetCarrierCode(item.getTargetCabinetNo())
                .targetCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                .unitNo(unitMap.get(item.getSourceContainerNo()))
                .warehouseNo(mission.getWarehouseNo())
                .tenantId(mission.getTenantId())
                .build();
        cabinetStockQueryService.checkCabinet(CabinetCheckQuery.builder()
                .cabinetNo(submitDetail.getTargetCarrierCode())
                .sku(submitDetail.getSku())
                .batch(submitDetail.getBatchNo())
                .produceDate(submitDetail.getProduceTime())
                .qualityDate(submitDetail.getShelfLife())
                .warehouseNo(submitDetail.getWarehouseNo())
                .build());
        res.add(submitDetail);
    }






}
