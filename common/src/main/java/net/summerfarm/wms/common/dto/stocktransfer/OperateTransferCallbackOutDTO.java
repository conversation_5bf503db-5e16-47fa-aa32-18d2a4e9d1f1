package net.summerfarm.wms.common.dto.stocktransfer;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/30 15:15
 * @Version 1.0
 */
@Data
public class OperateTransferCallbackOutDTO {

    private String transferOutSku;

    private String transferOutProduceDate;

    private String transferOutExpireDate;

    private Integer transferOutQuantity;

    public static OperateTransferCallbackOutDTO createByUniqueKey(String uniqueKey, Integer quantity) {
        String[] split = uniqueKey.split("#");
        OperateTransferCallbackOutDTO operateTransferCallbackDTO = new OperateTransferCallbackOutDTO();
        operateTransferCallbackDTO.setTransferOutSku(split[0]);
        operateTransferCallbackDTO.setTransferOutProduceDate(split[1]);
        operateTransferCallbackDTO.setTransferOutExpireDate(split[2]);
        operateTransferCallbackDTO.setTransferOutQuantity(quantity);
        return operateTransferCallbackDTO;
    }
}
