package net.summerfarm.wms.manage.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@Data
@AllArgsConstructor
public class WarehouseStockExt {

    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 状态
     */
    private Integer status;


    public WarehouseStockExt(){}

    public WarehouseStockExt(Integer warehouseNo,String sku){
        this.warehouseNo = warehouseNo;
        this.sku = sku;
    }


}
