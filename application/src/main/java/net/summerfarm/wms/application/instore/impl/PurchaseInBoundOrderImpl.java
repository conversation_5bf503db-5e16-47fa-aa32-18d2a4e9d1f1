package net.summerfarm.wms.application.instore.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderCommand;
import net.summerfarm.wms.api.h5.instore.dto.req.InBoundOrderDetailReqDTO;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.domain.acl.mq.external.event.WarehouseExternalRoutePurchaseInBillEvent;
import net.summerfarm.wms.application.instore.InBoundOrderTemplate;
import net.summerfarm.wms.application.instore.dto.CheckDTO;
import net.summerfarm.wms.application.instore.factory.InBoundOrderFactory;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.BooleanEnum;
import net.summerfarm.wms.common.enums.QualityTimeUnitEnum;
import net.summerfarm.wms.common.enums.SystemSourceEnum;
import net.summerfarm.wms.common.exceptions.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.mqUtil.MqUtil;
import net.summerfarm.wms.common.mqUtil.base.enums.MType;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalRouteQueryRepository;
import net.summerfarm.wms.domain.instore.domainobject.*;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.enums.StockStoragePurchaseModeEnums;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskAbnormalRecordRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import net.summerfarm.wms.domain.stock.domainobject.Stock;
import net.summerfarm.wms.domain.transaction.domainobject.AreaStoreInventoryTransaction;
import net.summerfarm.wms.domain.transaction.enums.TransactionStateEnum;
import net.summerfarm.wms.facade.openapi.OutPurchaseFacade;
import net.summerfarm.wms.facade.openapi.dto.PurchaseInPartDTO;
import net.summerfarm.wms.facade.product.GoodsCheckTaskFacade;
import net.summerfarm.wms.facade.product.dto.CreateGoodsCheckTaskDTO;
import net.summerfarm.wms.facade.product.dto.ProductQualityTimeTypeDTO;
import net.summerfarm.wms.facade.purchase.StockArrangeFacade;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import net.summerfarm.wms.inventory.enums.AreaStoreInventoryTypeEnum;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AreaStoreInventoryReqDTO;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
public class PurchaseInBoundOrderImpl extends InBoundOrderTemplate {
    @Resource
    StockArrangeFacade stockArrangeFacade;
    @Resource
    StockTaskAbnormalRecordRepository stockTaskAbnormalRecordRepository;
    @Resource
    StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    InBoundOrderQueryRepository inBoundOrderQueryRepository;
    @Resource
    StockStorageItemRepository stockStorageItemRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private GoodsCheckTaskFacade goodsCheckTaskFacade;
    @Resource
    private MqUtil mqUtil;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private InBoundOrderMsgService inBoundOrderMsgService;
    @Resource
    private OutPurchaseFacade outPurchaseFacade;
    @Resource
    private InBoundOrderFactory inBoundOrderFactory;
    @Resource
    private WarehouseExternalRouteQueryRepository warehouseExternalRouteQueryRepository;

    @Override
    public List<Integer> getTypes() {
        return Lists.newArrayList(InBoundOrderEnum.PURCHASE_IN.getCode());
    }

    @Override
    public void stockChange(Stock stock) {
        // 外部系统来源不操作在途库存
        if (!Objects.equals(stock.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            stock.updateRoadStock();
        }
        stock.updateStoreStock();
        // 预提类型不处理虚拟库存
        if (StockStoragePurchaseModeEnums.PRE_ORDERED_GOODS.getCode().equals(stock.getPurchaseMode())) {
            return;
        }

        if (stock.getSync() != null && NumberUtils.INTEGER_ONE.equals(stock.getSync())) {
            stock.updateOnlineStock();
        }
    }

    @Override
    public void sendStockChangeMq(Stock stock) {
        AreaStoreInventoryReqDTO inventory = AreaStoreInventoryReqDTO.builder()
                .sku(stock.getSku())
                .bizId(stock.getBizId())
                .diffChange(stock.getDiffChange())
                .warehouseNo(stock.getWarehouseNo())
                .diffStock(stock.getDiffStock())
                .operator(stock.getOperator())
                .type(Objects.nonNull(stock.getType()) ? stock.getType().getTypeName() : null)
                .build();

        mqProducer.sendTransaction(WmsConstant.AREA_STORE_INVENTORY_PG, WmsConstant.AREA_STORE_INVENTORY_TOPIC,
                WmsConstant.AREA_STORE_INVENTORY_STORE_TAG, inventory);

        // 外部系统不处理在途库存
        if (!Objects.equals(stock.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            inventory.setDiffStock(-stock.getDiffStock());
            mqProducer.sendTransaction(WmsConstant.AREA_STORE_INVENTORY_PG, WmsConstant.AREA_STORE_INVENTORY_TOPIC,
                    WmsConstant.AREA_STORE_INVENTORY_ROAD_TAG, inventory);
        }
        InBoundOrder inBoundOrder = inBoundOrderQueryRepository.findInBoundOrder(Long.valueOf(stock.getBizId()));
        if (inBoundOrder == null) {
            throw new net.xianmu.common.exception.BizException("入库单不存在");
        }
        // 获取入库任务id
        Long stockStorageTaskId = inBoundOrder.getStockStorageTaskId();
        // 查询入库任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageTaskId);
        Integer purchaseMode = stockTaskStorage.getPurchaseMode();
        // 预提类型不处理虚拟库存 加仓库库存 扣减在途库存
        if (StockStoragePurchaseModeEnums.PRE_ORDERED_GOODS.getCode().equals(purchaseMode)) {
            return;
        }

        if (stock.getSync() != null && NumberUtils.INTEGER_ONE.equals(stock.getSync())) {
            inventory.setDiffStock(stock.getDiffStock());
            mqProducer.sendTransaction(WmsConstant.AREA_STORE_INVENTORY_PG, WmsConstant.AREA_STORE_INVENTORY_TOPIC,
                    WmsConstant.AREA_STORE_INVENTORY_ONLINE_TAG, inventory);
        }

    }

    @Override
    public void buildStockTransaction(Stock stock, List<AreaStoreInventoryTransaction> transactions) {
        transactions.add(buildTs(stock, AreaStoreInventoryTypeEnum.STORE.getDesc()));
        transactions.add(buildTs(stock, AreaStoreInventoryTypeEnum.ROAD.getDesc()));

        InBoundOrder inBoundOrder = inBoundOrderQueryRepository.findInBoundOrder(Long.valueOf(stock.getBizId()));
        if (inBoundOrder == null) {
            throw new net.xianmu.common.exception.BizException("入库单不存在");
        }
        // 获取入库任务id
        Long stockStorageTaskId = inBoundOrder.getStockStorageTaskId();
        // 查询入库任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageTaskId);
        Integer purchaseMode = stockTaskStorage.getPurchaseMode();
        // 预提类型不处理虚拟库存
        if (StockStoragePurchaseModeEnums.PRE_ORDERED_GOODS.getCode().equals(purchaseMode)) {
            return;
        }

        if (stock.getSync() != null && NumberUtils.INTEGER_ONE.equals(stock.getSync())) {
            transactions.add(buildTs(stock, AreaStoreInventoryTypeEnum.ONLINE.getDesc()));
        }

    }

    private AreaStoreInventoryTransaction buildTs(Stock stock, String inventoryType) {
        return AreaStoreInventoryTransaction.builder()
                .bizId(stock.getBizId())
                .bizType(stock.getType().getTypeName())
                .inventoryType(inventoryType)
                .warehouseNo(stock.getWarehouseNo())
                .transactionState(TransactionStateEnum.DOING.getCode())
                .sku(stock.getSku())
                .param(JSON.toJSONString(stock))
                .build();
    }

    @Override
    public void checkExternal(InBoundOrderCommand command) {
        Long stockStorageTaskId = command.getStockStorageTaskId();
        //stockTaskAbnormalRecordRepository.select(stockStorageTaskId, null);

        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageTaskId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskStorage), "未查询到对应入库任务");
        if (Objects.equals(stockTaskStorage.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            return;
        }
        Boolean StockArrangeState = stockArrangeFacade.queryStockArrangeState(stockTaskStorage.getStockTaskId());
        if (Boolean.FALSE.equals(StockArrangeState)) {
            // todo ct
            throw new BizException("预约单状态以取消不可入库");
        }


    }

    @Override
    public void checkBaseInfo(InBoundOrderCommand command) {
        //越仓入库，调拨回库不校验
        List<Integer> skipCheckType = Arrays.asList(InBoundOrderEnum.SKIP_STORE_IN.getCode(),
                InBoundOrderEnum.ALLOCATION_ABNORMAL_IN.getCode());
        if (skipCheckType.contains(command.getType())) {
            return;
        }

        boolean check = command.getOrderDetails().stream().allMatch(item -> Objects.nonNull(item.getSku()) &&
                Objects.nonNull(item.getProduceAt()) &&
                Objects.nonNull(item.getShelfLife()) &&
                Objects.nonNull(item.getPurchaseNo()));
        if (!check && !CheckDTO.notNeedCheckElement.contains(command.getType())) {
            throw new BizException(ErrorCode.PARAM_ERROR.getCode(), "sku,批次号以及效期不可为空");
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());

        if (Objects.isNull(stockTaskStorage)) {
            throw new BizException("未查询到入库任务信息");
        }

        if (!Objects.equals(stockTaskStorage.getState(), StockTaskStorageStateEnum.NORMAL.getId())) {
            throw new BizException("该任务状态下不允许操作入库单");
        }
        List<InBoundOrderDetailReqDTO> orderDetails = command.getOrderDetails();
        Long stockStorageTaskId = command.getStockStorageTaskId();
        Map<String, List<InBoundOrderDetailReqDTO>> skuMap = orderDetails.stream().collect(groupingBy(InBoundOrderDetailReqDTO::getSku));

        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockStorageTaskId);
        if (CollectionUtils.isEmpty(stockStorageItems)) {
            throw new BizException("无入库任务信息");
        }
        //异常操作 旧任务id 异常操作需要重构 todo
        List<StockTaskAbnormalRecord> abnormalRecords = stockTaskAbnormalRecordRepository.select(stockTaskStorage.getStockTaskId(), null);
        Map<String, List<StockTaskAbnormalRecord>> abnormalRecordMap = abnormalRecords.stream().collect(Collectors.groupingBy(StockTaskAbnormalRecord::getSku));
        Map<String, List<StockStorageItem>> skuStockStorageItemMap = stockStorageItems.stream().collect(groupingBy(StockStorageItem::getSku));
        List<InBoundOrderDetail> inBoundOrderDetail =
                inBoundOrderQueryRepository.findInBoundOrderDetailByTaskId(InBoundTaskId.builder().taskId(stockStorageTaskId).build());

        Map<String, List<InBoundOrderDetail>> skuDetail = inBoundOrderDetail.stream().collect(groupingBy(InBoundOrderDetail::getSku));
        skuMap.forEach((sku, reqDTOList) -> {
            //操作入库数量
            int stockNumSum = reqDTOList.stream().mapToInt(InBoundOrderDetailReqDTO::getStockNum).sum();
            List<InBoundOrderDetail> inBoundOrderDetails = skuDetail.get(sku);
            //已入数量
            int inStockSum = CollectionUtils.isEmpty(inBoundOrderDetails) ? 0 : inBoundOrderDetails.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
            //异常操作
            List<StockTaskAbnormalRecord> recordList = abnormalRecordMap.get(sku);
            int abnormalRecordSum = CollectionUtils.isEmpty(recordList) ? 0 : recordList.stream().mapToInt(StockTaskAbnormalRecord::getQuantity).sum();
            List<StockStorageItem> stockStorageItemList = skuStockStorageItemMap.get(sku);
            //总数量
            StockStorageItem storageItem = stockStorageItemList.stream().findFirst().orElseThrow(() -> new BizException("入库任务异常"));
            Integer quantity = storageItem.getQuantity();
            if (stockNumSum + inStockSum + abnormalRecordSum > quantity) {
                log.warn("入库数量异常,sku:{},入库数量:{},已入库数量:{},异常操作数量:{},总数量:{}", sku, stockNumSum, inStockSum, abnormalRecordSum, quantity);
                // err
                throw new BizException("入库数量异常,请刷新页面确认数量");
            }
        });
    }

    @Override
    public void preHandlerSource(InBoundOrderCommand command) {

    }

    @Override
    public void noticeExternal(InBoundOrderCommand command) {

        // 发送采购入库通知
        if (Objects.equals(command.getSystemSource(), SystemSourceEnum.EXTERNAL.getCode())) {
            this.handleNoticePurchaseInPart(command.getInBoundOrderIdList());
        } else {
            command.getInBoundOrderIdList().forEach(orderId -> inBoundOrderMsgService.sendInBoundOrderMsg(orderId));
        }

        // 获取外部路由配置
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(command.getWarehouseNo().intValue())
                .abilityCode(ExternalCodeConstant.PURCHASE_IN_BILL_NOTICE)
                .orderType(StockTaskTypeEnum.PURCHASE_IN.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        if (null != warehouseExternalRoute) {
            log.info("入库任务：{}，入库单：{}，已配置单据推送外部路由，发送推送调用消息", command.getStockStorageTaskId(), !CollectionUtils.isEmpty(command.getInBoundOrderIdList()) ? command.getInBoundOrderIdList().get(0) : null);
            mqProducer.send(WmsConstant.MQ_EXTERNAL_TOPIC, WmsConstant.EXTERNAL_ROUTE_PURCHASE_IN_BILL_INVOKE, WarehouseExternalRoutePurchaseInBillEvent.builder()
                    .idempotentNo(!CollectionUtils.isEmpty(command.getInBoundOrderIdList()) ? command.getInBoundOrderIdList().get(0).toString() : null)
                    .warehouseExternalRouteId(warehouseExternalRoute.getId())
                    .stockStorageTaskId(command.getStockStorageTaskId())
                    .inboundOrderId(!CollectionUtils.isEmpty(command.getInBoundOrderIdList()) ? command.getInBoundOrderIdList().get(0) : null)
                    .build());
        }

        if (null != command.getTenantId() && command.getTenantId() > WmsConstant.XIANMU_TENANT_ID) {
            log.info("非鲜沐仓过滤返回，入库任务id：{}", command.getStockStorageTaskId());
            return;
        }
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(command.getStockStorageTaskId());
        if((Objects.equals(stockTaskStorage.getPurchaseMode(), StockStoragePurchaseModeEnums.POP_PURCHASE.getCode())
                || Objects.equals(stockTaskStorage.getPurchaseMode(), StockStoragePurchaseModeEnums.POP_PURCHASE_T2.getCode()))
                && Objects.equals(stockTaskStorage.getType(), StockStorageTypeEnums.PURCHASE_IN.getId())){
            log.info("非采购入库类型，非POP采购模式无需变更商品保质期类型、商品校验任务,入库任务Id:{}",stockTaskStorage.getId());
            return;
        }
        // 获取商品保质期限
        List<String> skuList = command.getOrderDetails().stream().map(InBoundOrderDetailReqDTO::getSku).distinct().collect(Collectors.toList());
        List<Product> productList = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                .warehouseNo(stockTaskStorage.getInWarehouseNo().longValue()).tenantId(stockTaskStorage.getTenantId()).skus(skuList).build());
        if (CollectionUtils.isEmpty(productList)) {
            log.info("根据sku查询spu为空，sku：{}", JSON.toJSONString(skuList));
            return;
        }
        // 保质期类型不同则通知商品变更保质期类型
        List<ProductQualityTimeTypeDTO> productQualityTimeTypeDTOList = new ArrayList<>();
        Map<String, Integer> skuQualityTypeMap = command.getOrderDetails().stream()
                .filter(item -> null != item.getQualityTimeType())
                .collect(Collectors.toMap(InBoundOrderDetailReqDTO::getSku, InBoundOrderDetailReqDTO::getQualityTimeType, (a, b) -> a));
        productList.forEach(item -> {
            if (null == item.getQualityTimeType() || !item.getQualityTimeType().equals(skuQualityTypeMap.get(item.getSku()))) {
                ProductQualityTimeTypeDTO productQualityTimeTypeDTO = new ProductQualityTimeTypeDTO();
                productQualityTimeTypeDTO.setSku(item.getSku());
                productQualityTimeTypeDTO.setQualityTimeType(skuQualityTypeMap.get(item.getSku()));
                if (null != productQualityTimeTypeDTO.getQualityTimeType()) {
                    productQualityTimeTypeDTOList.add(productQualityTimeTypeDTO);
                }
            }
        });
        // 发送mq到wms-list
        if (!CollectionUtils.isEmpty(productQualityTimeTypeDTOList)) {
            List<List<ProductQualityTimeTypeDTO>> partitionList = Lists.partition(productQualityTimeTypeDTOList, WmsConstant.BATCH_SIZE_LIMIT);
            partitionList.forEach(list -> {
                try {
                    mqUtil.sendMqData(Global.MQ_TOPIC, list, MType.CHANGE_QUALITY_TIME_TYPE.name());
                } catch (Exception e) {
                    log.error("变更商品保质期类型发送消息失败，{}", JSON.toJSONString(list), e);
                }
            });
        }
        Map<String, Product> skuProductMap = productList.stream().collect(Collectors.toMap(Product::getSku, Function.identity(), (a, b) -> a));
        // 入库保质期时长和商品侧对比，存在差异创建商品校验任务
        List<CreateGoodsCheckTaskDTO> createGoodsCheckTaskDTOList = new ArrayList<>();
        command.getOrderDetails().forEach(item -> {
            Product product = skuProductMap.get(item.getSku());
            if (null == product) {
                return;
            }
            if (Boolean.FALSE.equals(equalsQualityTime(item.getProduceAt(), item.getShelfLife(), product.getEffectiveTime(), product.getTimeUnit()))) {
                log.info("入库计算保质期时长存在差异，warehouseNo{}，sku{}，produceAt{}，shelfLife{}，effectiveTime{}，timeUnit{}，qualityTimeType{}", command.getWarehouseNo(), item.getSku(), item.getProduceAt(), item.getShelfLife(), product.getEffectiveTime(), product.getTimeUnit(), item.getQualityTimeType());
                CreateGoodsCheckTaskDTO createGoodsCheckTaskDTO = new CreateGoodsCheckTaskDTO();
                createGoodsCheckTaskDTO.setWarehouseNo(command.getWarehouseNo().intValue());
                createGoodsCheckTaskDTO.setSku(item.getSku());
                createGoodsCheckTaskDTO.setOperator("系统—效期校验");
                createGoodsCheckTaskDTOList.add(createGoodsCheckTaskDTO);
            }
        });
        if (!CollectionUtils.isEmpty(createGoodsCheckTaskDTOList)) {
            try {
                goodsCheckTaskFacade.createGoodsCheckTask(createGoodsCheckTaskDTOList);
            } catch (Exception e) {
                log.error("创建商品校验任务异常", e);
            }
        }
    }

    /**
     * 判断保质期时长是否相同
     *
     * @param startTimestamp
     * @param endTimestamp
     * @param effectiveTime
     * @param timeUnit
     * @return
     */
    private Boolean equalsQualityTime(Long startTimestamp, Long endTimestamp, Integer effectiveTime, String timeUnit) {
        if (null == startTimestamp || null == endTimestamp || null == effectiveTime || StringUtils.isBlank(timeUnit)) {
            return false;
        }
        // 单位为月
        if (QualityTimeUnitEnum.MONTH.getCode().equals(timeUnit)) {
            // 判断期间有多少个月，开始日期的天大于结束日期的天则减去一个月
            Long betweenMonth = DateUtil.getBetweenMonth(startTimestamp, endTimestamp, false);
            if (!effectiveTime.equals(betweenMonth.intValue())) {
                return false;
            }
            // 判断结束日期大于开始日期时，天数是否一致
            Integer startDay = DateUtil.getDayOfMonth(startTimestamp);
            Integer endDay = DateUtil.getDayOfMonth(endTimestamp);
            if (!startDay.equals(endDay)) {
                return false;
            }
            return true;
            // 单位为天
        } else if (QualityTimeUnitEnum.DAY.getCode().equals(timeUnit)) {
            // 判断期间有多少天
            Long betweenDay = DateUtil.getBetweenDay(startTimestamp, endTimestamp, false);
            if (!effectiveTime.equals(betweenDay.intValue())) {
                return false;
            }
            return true;
        }
        // 其他单位返回
        return false;
    }

    private void handleNoticePurchaseInPart(List<Long> inBoundOrderIdList) {
        try {
            inBoundOrderIdList.forEach(orderId -> {
                if (Objects.isNull(orderId)) {
                    return;
                }
                InBoundOrder inBoundOrder = inBoundOrderQueryRepository.findInBoundOrder(orderId);
                ExceptionUtil.checkAndThrow(Objects.nonNull(inBoundOrder), "未查询到入库单信息");
                List<InBoundOrderDetail> detailList = inBoundOrderQueryRepository.findInBoundOrderDetail(orderId);
                ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(detailList), "未查询到入库单明细信息");
                inBoundOrder.setInBoundOrderDetails(detailList);
                StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(inBoundOrder.getStockStorageTaskId());
                ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskStorage), "未查询到入库任务信息");
                PurchaseInPartDTO inPartDTO = inBoundOrderFactory.buildPurchaseInPartDTO(inBoundOrder, stockTaskStorage);
                outPurchaseFacade.noticePurchaseInPart(inPartDTO, stockTaskStorage.getTenantId());
            });
        } catch (Exception e) {
            log.error("外部采购单，部分入库回传异常，入库单编码:{}", inBoundOrderIdList, e);
        }
    }
}
