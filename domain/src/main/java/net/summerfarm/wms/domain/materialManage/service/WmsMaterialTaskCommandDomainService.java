package net.summerfarm.wms.domain.materialManage.service;

import net.summerfarm.wms.common.enums.GeneratorDocTypeEnum;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.StoreRecord.enums.OtherStockChangeType;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.inventory.domainService.WmsInventoryAggregationDomianSerivce;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.Inventory4AutoPeriodOperatorAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.InventoryDetail4AutoPeriodOperatorAggregate;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialTaskReceiveAggregate;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialTaskRestoreAggregate;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskCommandRepository;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskDetailCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Title: 物料任务领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Service
public class WmsMaterialTaskCommandDomainService {


    @Autowired
    private WmsMaterialTaskCommandRepository wmsMaterialTaskCommandRepository;
    @Autowired
    private WmsMaterialTaskDetailCommandRepository wmsMaterialTaskDetailCommandRepository;
    @Autowired
    private WmsInventoryAggregationDomianSerivce wmsInventoryAggregationDomianSerivce;
    @Autowired
    private SerialNumberGenerator serialNumberGenerator;


    @Transactional(rollbackFor = Exception.class)
    public WmsMaterialTaskEntity receive(WmsMaterialTaskReceiveAggregate receiveAggregate) {
        String taskCode = serialNumberGenerator.createDocCode(GeneratorDocTypeEnum.MATERAIL_RECEIVE);
        receiveAggregate.getTaskCommandParam().setMaterialTaskCode(taskCode);
        for (WmsMaterialTaskDetailCommandParam detailCommandParam : receiveAggregate.getTaskDetailCommandParam()) {
            detailCommandParam.setMaterialTaskCode(taskCode);
        }

        // 任务操作
        WmsMaterialTaskEntity wmsMaterialTaskEntity = wmsMaterialTaskCommandRepository
                .insertSelective(receiveAggregate.getTaskCommandParam());

        wmsMaterialTaskDetailCommandRepository.batchInsertSelective(
                receiveAggregate.getTaskDetailCommandParam());


        // 库存操作
        List<InventoryDetail4AutoPeriodOperatorAggregate> aggregateDetailList = new ArrayList<>();
        for (WmsMaterialTaskDetailCommandParam detailCommandParam : receiveAggregate.getTaskDetailCommandParam()) {
            aggregateDetailList.add(InventoryDetail4AutoPeriodOperatorAggregate.builder()
                    .sku(detailCommandParam.getMaterialSku())
                    .quantity(detailCommandParam.getQuantity())
                    .build());
        }
        Inventory4AutoPeriodOperatorAggregate aggregate = Inventory4AutoPeriodOperatorAggregate.builder()
                .tenantId(receiveAggregate.getTaskCommandParam().getTenantId())
                .warehouseNo(receiveAggregate.getTaskCommandParam().getWarehouseNo())
                .bizId(wmsMaterialTaskEntity.getId())
                .bizNo(taskCode)
                .otherStockChangeType(OtherStockChangeType.MATERIAL_TASK_RECEIVE)
                .cabinetInventoryChangeTypeEnum(CabinetInventoryChangeTypeEnum.MATERIAL_TASK_RECEIVE)
                .operationType(OperationType.MATERIAL_TASK_RECEIVE)
                .operationName(receiveAggregate.getTaskCommandParam().getCreator())
                .detailList(aggregateDetailList)
                .build();
        wmsInventoryAggregationDomianSerivce.decreaseInventory4AutoPeriod(aggregate);
        return wmsMaterialTaskEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public WmsMaterialTaskEntity restore(WmsMaterialTaskRestoreAggregate restoreAggregate) {
        String taskCode = serialNumberGenerator.createDocCode(GeneratorDocTypeEnum.MATERAIL_RESTORE);
        restoreAggregate.getTaskCommandParam().setMaterialTaskCode(taskCode);
        for (WmsMaterialTaskDetailCommandParam detailCommandParam : restoreAggregate.getTaskDetailCommandParam()) {
            detailCommandParam.setMaterialTaskCode(taskCode);
        }

        // 任务操作
        WmsMaterialTaskEntity wmsMaterialTaskEntity = wmsMaterialTaskCommandRepository
                .insertSelective(restoreAggregate.getTaskCommandParam());

        wmsMaterialTaskDetailCommandRepository.batchInsertSelective(
                restoreAggregate.getTaskDetailCommandParam());

        // 库存操作
        List<InventoryDetail4AutoPeriodOperatorAggregate> aggregateDetailList = new ArrayList<>();
        for (WmsMaterialTaskDetailCommandParam detailCommandParam : restoreAggregate.getTaskDetailCommandParam()) {
            aggregateDetailList.add(InventoryDetail4AutoPeriodOperatorAggregate.builder()
                    .sku(detailCommandParam.getMaterialSku())
                    .quantity(detailCommandParam.getQuantity())
                    .build());
        }
        Inventory4AutoPeriodOperatorAggregate aggregate = Inventory4AutoPeriodOperatorAggregate.builder()
                .tenantId(restoreAggregate.getTaskCommandParam().getTenantId())
                .warehouseNo(restoreAggregate.getTaskCommandParam().getWarehouseNo())
                .bizId(wmsMaterialTaskEntity.getId())
                .bizNo(taskCode)
                .otherStockChangeType(OtherStockChangeType.MATERIAL_TASK_RESTORE)
                .cabinetInventoryChangeTypeEnum(CabinetInventoryChangeTypeEnum.MATERIAL_TASK_RESTORE)
                .operationType(OperationType.MATERIAL_TASK_RESTORE)
                .operationName(restoreAggregate.getTaskCommandParam().getCreator())
                .detailList(aggregateDetailList)
                .build();
        wmsInventoryAggregationDomianSerivce.increaseInventory4AutoPeriod(aggregate);

        return wmsMaterialTaskEntity;

    }

}
