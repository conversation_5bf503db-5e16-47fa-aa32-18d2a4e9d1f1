package net.summerfarm.wms.domain.initconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @description 自动转换限制配置
 * @date 2023/11/15
 */
@NacosConfigurationProperties(prefix = "online.auto.transfer", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Configuration
@Data
public class OnlineAutoTransferConfig {

    /**
     * 限制生成时间 17：30
     */
    private String holdTime;

}
