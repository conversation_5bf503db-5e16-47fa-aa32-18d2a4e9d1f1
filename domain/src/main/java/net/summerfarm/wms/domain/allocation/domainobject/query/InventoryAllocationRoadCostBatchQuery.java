package net.summerfarm.wms.domain.allocation.domainobject.query;

import lombok.Data;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-08-08 16:51:15
 */
@Data
public class InventoryAllocationRoadCostBatchQuery {
    /**
     * 调拨单号
     */
    private String listNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

}