package net.summerfarm.wms.application.common.enums;

/**
 * SaaS文件下载类型
 *
 * <AUTHOR>
 */
public enum SaaSFileDownloadTypeEnum {
    SHELF_LIFE_WARNING(60, "保质期预警报表"),
    STORE_MATURITY_WARNING(61, "存货周转预警报表"),
    SAFE_STOCK_WARNING(79, "安全库存预警报表"),

    STOCK_TASK_STORAGE(28, "入库任务导出"),

    STORE_RECORD_SUMMARY(30, "出入库汇总表导出"),

    SAAS_STOCK_TAKING(40, "盘点库存导出"),


    ;

    private Integer type;
    private String desc;

    public Integer getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    private SaaSFileDownloadTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
