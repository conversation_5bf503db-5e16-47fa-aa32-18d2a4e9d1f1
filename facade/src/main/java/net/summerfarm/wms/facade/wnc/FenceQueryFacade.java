package net.summerfarm.wms.facade.wnc;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.wnc.converter.DeliveryFenceConverter;
import net.summerfarm.wms.facade.wnc.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.DeliveryFenceByCityQueryReq;
import net.summerfarm.wnc.client.req.fence.FenceQueryReq;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.fence.FenceResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 围栏查询服务
 */
@Slf4j
@Component
public class FenceQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    public static final Integer MAX_CITY_NUM = 200;

    /**
     * 根据城市名称查询围栏信息
     * @param cityNames 城市名称
     * @return 围栏信息
     */
    public List<DeliveryFenceDTO> queryFenceCity(List<String> cityNames) {
        List<DeliveryFenceDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(cityNames)) {
            log.warn("invoke FenceQueryFacade.queryFenceCity error:cityNames is empty");
            return list;
        }
        //先去重
        cityNames = cityNames.stream().distinct().collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(cityNames, MAX_CITY_NUM);
        partition.forEach(subList -> {
            try {
                DeliveryFenceByCityQueryReq cityQueryReq = new DeliveryFenceByCityQueryReq();
                cityQueryReq.setCityNames(subList);
                DubboResponse<List<DeliveryFenceResp>> dubboResponse =
                        deliveryFenceQueryProvider.batchQueryDeliveryFenceByCity(cityQueryReq);
                if (dubboResponse == null ||!dubboResponse.isSuccess()) {
                    log.warn("invoke FenceQueryFacade.queryFenceCity error:{}", dubboResponse);
                    return;
                }
                list.addAll(DeliveryFenceConverter.toDeliveryFenceDTOList(dubboResponse.getData()));
            } catch (Exception e) {
                //不做处理，保证查询成功的数据依旧有效
                log.warn("invoke FenceQueryFacade.queryFenceCity error:{}", Throwables.getStackTraceAsString(e));
            }
        });
        return list;
    }


    /**
     * 根据城配仓编号查询运营区域编号
     * @param storeNoList 城配仓编号
     * @return 运营区域编号集合
     */
    public List<Integer> queryFenceListByStoreNoList(List<Integer> storeNoList){
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }

        List<DeliveryFenceDTO> fenceDTOS = this.queryEffectiveFenceDTOListByStoreNoList(storeNoList);

        if (!CollectionUtils.isEmpty(fenceDTOS)) {
            return fenceDTOS.stream().map(DeliveryFenceDTO::getAreaNo).distinct().collect(Collectors.toList());
        } else {
            log.error("queryFenceListWithArea param:{}", storeNoList);
        }

        return Collections.emptyList();
    }


    /**
     * 根据城配仓编号集合查询有效的围栏
     * @param storeNoList 城配仓编号集合
     * @return 围栏信息
     */
    public List<DeliveryFenceDTO> queryEffectiveFenceDTOListByStoreNoList(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return Collections.emptyList();
        }
        FenceQueryReq req = new FenceQueryReq();
        req.setStoreNos(storeNoList);
        req.setStatus(0);

        DubboResponse<List<FenceResp>> resp = deliveryFenceQueryProvider.queryFenceListWithArea(req);
        if (resp == null ||!resp.isSuccess()) {
            throw new BizException("调用仓网查询城配仓围栏信息异常");
        }

        List<FenceResp> fenceResps = resp.getData();
        if(CollectionUtils.isEmpty(fenceResps)){
            return Collections.emptyList();
        }
        return fenceResps.stream().map(DeliveryFenceConverter::toDeliveryFenceDTO).collect(Collectors.toList());
    }
}
