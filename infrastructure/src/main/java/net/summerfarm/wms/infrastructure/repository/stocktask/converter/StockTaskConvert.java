package net.summerfarm.wms.infrastructure.repository.stocktask.converter;

import net.summerfarm.wms.domain.stocktask.domainobject.*;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskItemDO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskItemDetailDO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskProcessDO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskProcessDetailDO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.WmsStockTaskItemCabinetOccupyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface StockTaskConvert {
    StockTaskConvert INSTANCE = Mappers.getMapper(StockTaskConvert.class);

    StockTask convert(StockTaskDO stockTaskDO);

    List<StockTask> convertList(List<StockTaskDO> stockTaskDOList);

    StockTaskDO convert2StockTaskDO(StockTask stockTask);

    StockTaskProcess convert(StockTaskProcessDO stockTaskProcessDO);

    StockTaskProcessDetail convert(StockTaskProcessDetailDO stockTaskProcessDetailDO);

    StockTaskItem convert(StockTaskItemDO stockTaskItemDO);

    StockTaskItemDO convert2DO(StockTaskItem stockTaskItem);

    StockTaskItemDetail convert(StockTaskItemDetailDO stockTaskItemDO);

    StockTaskItemCabinetOccupy convert2StockTaskItemCabinetOccupy(WmsStockTaskItemCabinetOccupyDO cabinetOccupyDO);

}
