package net.summerfarm.wms.infrastructure.repository.skushare.converter;

import net.summerfarm.wms.domain.skushare.domainobject.SkuShare;
import net.summerfarm.wms.infrastructure.dao.skushare.entity.InventorySkuShareTransferDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SkuShareConvert {

    SkuShareConvert INSTANCE = Mappers.getMapper(SkuShareConvert.class);

    InventorySkuShareTransferDO convert(SkuShare skuShare);

    SkuShare convert(InventorySkuShareTransferDO skuShareTransferDO);

    List<SkuShare> convert(List<InventorySkuShareTransferDO> skuShareTransferDOS);

}
