package net.summerfarm.wms.common.proxy.model;

/**
 *
 */
public class CglibRetryModel {

    /**
     * id
     */
    private Long id;

    /**
     * 在Spring上下文中的类名
     */
    private Class beanClass;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 参数类型数组
     */
    private Class[] parameterTypes;

    /**
     * 参数值数组
     */
    private Object[] parameterValue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Class getBeanClass() {
        return beanClass;
    }

    public void setBeanClass(Class beanClass) {
        this.beanClass = beanClass;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public Class[] getParameterTypes() {
        return parameterTypes;
    }

    public void setParameterTypes(Class[] parameterTypes) {
        this.parameterTypes = parameterTypes;
    }

    public Object[] getParameterValue() {
        return parameterValue;
    }

    public void setParameterValue(Object[] parameterValue) {
        this.parameterValue = parameterValue;
    }

    public static final class Builder {
        private Long id;
        private Class className;
        private String methodName;
        private Class[] parameterTypes;
        private Object[] parameterValue;

        private Builder() {
        }

        public static Builder aCglibRetryModel() {
            return new Builder();
        }

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder className(Class className) {
            this.className = className;
            return this;
        }

        public Builder methodName(String methodName) {
            this.methodName = methodName;
            return this;
        }

        public Builder parameterTypes(Class... parameterTypes) {
            this.parameterTypes = parameterTypes;
            return this;
        }

        public Builder parameterValue(Object... parameterValue) {
            this.parameterValue = parameterValue;
            return this;
        }

        public CglibRetryModel build() {
            CglibRetryModel cglibRetryModel = new CglibRetryModel();
            cglibRetryModel.setId(id);
            cglibRetryModel.setBeanClass(className);
            cglibRetryModel.setMethodName(methodName);
            cglibRetryModel.setParameterTypes(parameterTypes);
            cglibRetryModel.setParameterValue(parameterValue);
            return cglibRetryModel;
        }
    }
}

