package net.summerfarm.external;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.handler.ww.WWExternalAbilityHandler;
import net.summerfarm.wms.manage.web.service.StockTransferBizService;
import net.summerfarm.wms.openapi.transfer.xm.req.TransferCallbackReq;
import net.summerfarm.wms.starter.WmsApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class ExternalTransferTest {

    @Autowired
    private WWExternalAbilityHandler wwExternalAbilityHandler;



    @Test
    public void testTransferCallback() {
        String jsonData = "{\n" +
                "    \"stockTransferItems\": [\n" +
                "        {\n" +
                "            \"transferInQuantity\": 10,\n" +
                "            \"transferInSku\": \"18650638828\",\n" +
                "            \"transferOutItemDetails\": [\n" +
                "                {\n" +
                "                    \"transferOutQuantity\": 10,\n" +
                "                    \"transferOutProductDate\": \"2022-01-28\",\n" +
                "                    \"transferOutExpireDate\": \"2022-02-07\",\n" +
                "                    \"transferOutSku\": \"18650638304\",\n" +
                "                    \"transferOutBatch\": \"0128202289905001\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"transferOutQuantity\": 10,\n" +
                "            \"transferRatio\": \"1:1\",\n" +
                "            \"transferOutSku\": \"18650638304\",\n" +
                "            \"transferInItemDetails\": [\n" +
                "                {\n" +
                "                    \"transferInQuantity\": 10,\n" +
                "                    \"transferInBatch\": \"0120240812466123\",\n" +
                "                    \"transferInProductDate\": \"2022-01-28\",\n" +
                "                    \"transferInSku\": \"18650638828\",\n" +
                "                    \"transferInExpireDate\": \"2022-02-07\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"finishTime\": \"2024-08-12 10:55:18\",\n" +
                "    \"idempotentNo\": \"7233\",\n" +
                "    \"stockTransferNo\": \"7233\",\n" +
                "    \"transferPurpose\": \"拆包\",\n" +
                "    \"transferType\": 1,\n" +
                "    \"warehouseNo\": \"1\"\n" +
                "}";
        TransferCallbackReq transferCallbackReq = JSON.parseObject(jsonData, TransferCallbackReq.class);
        wwExternalAbilityHandler.stockTransferCallback(transferCallbackReq);
    }

    @Autowired
    private StockTransferBizService stockTransferBizService;

    @Test
    public void testTransferInItemOnly() {
        stockTransferBizService.operateStockTransferInItemOnly(7235L);
    }


}
