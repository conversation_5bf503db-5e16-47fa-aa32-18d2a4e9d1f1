package net.summerfarm.wms.manage.web.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.facade.wnc.FenceQueryFacade;
import net.summerfarm.wms.manage.web.service.FenceService;
import net.summerfarm.wms.manage.model.domain.AreaStore;
import net.summerfarm.wms.manage.model.domain.PurchasesConfig;
import net.summerfarm.wms.manage.web.mapper.manage.AreaStoreMapper;
import net.summerfarm.wms.manage.web.mapper.manage.PurchasesConfigMapper;
import net.summerfarm.wms.manage.web.service.PurchasesConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/23
 */
@Service
@Slf4j
public class PurchasesConfigServiceImpl implements PurchasesConfigService {

    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private WarehouseStorageService storageService;
    @Resource
    private PurchasesConfigMapper purchasesConfigMapper;
    @Resource
    private FenceQueryFacade fenceQueryFacade;
    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void msgArrival(Integer storeNo,String sku){
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        if(mapping == null){
            return;
        }
        WarehouseStorageCenter storageCenter = storageService.selectByWarehouseNo(mapping.getWarehouseNo());

        PurchasesConfig select = new PurchasesConfig();
        select.setType(storageCenter.getType());
        select.setAreaManageId(storageCenter.getAreaManageId());
        select.setSku(sku);
        select.setAreaNo(storeNo);
        PurchasesConfig purchasesConfig = purchasesConfigMapper.selectOne(select);
        if (purchasesConfig == null){ //未配置预警信息
            return;
        }
        List<Integer> areaNos = fenceQueryFacade.queryFenceListByStoreNoList(Collections.singletonList(storeNo));
        AreaStore areaStoreSelect = new AreaStore();
        Integer quantity = 0;
        Integer lockQuantity = 0;
        Integer roadQuantity = 0;
        Integer safeQuantity = 0;

        for (Integer areaNo: areaNos){
            areaStoreSelect.setAreaNo(areaNo);
            areaStoreSelect.setSku(purchasesConfig.getSku());
            AreaStore areaStore = areaStoreMapper.selectOne(areaStoreSelect);
            if (areaStore != null){
                quantity += areaStore.getQuantity();
                lockQuantity += areaStore.getLockQuantity();
                roadQuantity += areaStore.getRoadQuantity();
                safeQuantity += areaStore.getSafeQuantity();
            }
        }

        if ((quantity-lockQuantity-safeQuantity+roadQuantity) < purchasesConfig.getSafeLevel()){
            if (purchasesConfig.getStatus() == 0){//未处于预警状态
                PurchasesConfig update = new PurchasesConfig();
                update.setId(purchasesConfig.getId());
                update.setStatus(1);
                update.setUpdateTime(LocalDateTime.now());
                purchasesConfigMapper.update(update);

//                handleMsg(purchasesConfig);
            }
        }else {
            PurchasesConfig update = new PurchasesConfig();
            update.setId(purchasesConfig.getId());
            update.setStatus(0);
            update.setUpdateTime(LocalDateTime.now());
            purchasesConfigMapper.update(update);
        }
    }

}
