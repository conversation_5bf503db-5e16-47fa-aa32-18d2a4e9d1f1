package net.summerfarm.wms.application.skushare.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * 新增sku规格共享详情
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddSkuShareDetail implements Serializable {

    private static final long serialVersionUID = -6393422599018637050L;

    /**
     * 仓库编号
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;
    /**
     * 共享的sku编码
     */
    @NotBlank(message = "共享的sku编码不能为空")
    private String sharedSku;
    /**
     * 共享的总数量
     */
    @NotNull(message = "共享的总数量不能为空")
    @PositiveOrZero(message = "共享的总数量不能小于0")
    private Integer sharedTotalQuantity;
}
