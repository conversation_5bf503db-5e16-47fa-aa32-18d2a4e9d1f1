package net.summerfarm.wms.api.h5.stocktask.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.SkuProductRangeReq;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskCabinetOccupyOneKeyCommand implements Serializable {

    /**
     * 出库任务编码
     */
    private Long stockTaskId;
    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 出库任务明细编码
     */
    private Long stockTaskItemId;

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 库位编码，可空
     */
    @ApiModelProperty(value = "库位编码")
    private String cabinetCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 生产日期大于等于
     */
    @ApiModelProperty(value = "生产日期大于等于")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDateGe;


    /**
     * 生产日期小于等于
     */
    @ApiModelProperty(value = "生产日期小于等于")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDateLe;

    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qualityDate;


    /**
     * 保质期大于等于
     */
    @ApiModelProperty(value = "保质期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qualityDateGe;


    /**
     * 保质期小于
     */
    @ApiModelProperty(value = "保质期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qualityDateLt;

    /**
     * 占用库存
     */
    @ApiModelProperty(value = "占用库存")
    private Integer occupyQuantity;

    /**
     * sku批次吗
     */
    @ApiModelProperty(value = "sku批次吗")
    private String skuBatchCode;

    /**
     * 生产日期范围列表
     */
    @ApiModelProperty(value = "生产日期范围列表")
    private List<SkuProductRangeReq> skuProductRangeInputs;

    /**
     * 出库任务类型
     */
    private Integer stockTaskType;

    /**
     * 是否出库任务接口锁定
     */
    private Boolean outWebLock = false;
}
