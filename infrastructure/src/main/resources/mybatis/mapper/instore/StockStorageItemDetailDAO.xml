<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.instore.StockStorageItemDetailDAO">

    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="purchase_no" property="purchaseNo"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="stock_storage_item_id" property="stockStorageItemId"/>
        <result column="should_in_quantity" property="actualInQuantity"/>
        <result column="actual_in_quantity" property="shouldQuantity"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="receiving_container" property="receivingContainer"/>
        <result column="specify" property="specify"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,stock_storage_item_id, actual_in_quantity , purchase_no, quality_date,production_date,tenant_id,receiving_container,
should_in_quantity,specify
    </sql>

    <insert id="insertDetailDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        insert into stock_storage_item_detail (stock_storage_item_id, actual_in_quantity, purchase_no, quality_date,
        production_date, tenant_id, receiving_container,
        <if test="actualInQuantity != null">
            should_in_quantity,
        </if>
        <if test="specify != null">
            specify
        </if>
        )
        VALUE (#{stockStorageItemId}, #{shouldQuantity}, #{purchaseNo}, #{qualityDate}, #{productionDate},
        #{tenantId},
        #{receivingContainer},
        <if test="actualInQuantity != null">
            #{actualInQuantity},
        </if>
        <if test="specify != null">
            #{specify}
        </if>)
    </insert>

    <select id="selectByDetailDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO"
            resultMap="BaseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_storage_item_detail
        where stock_storage_item_id = #{stockStorageItemId}
    </select>

    <insert id="insertBatch"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        insert into stock_storage_item_detail (stock_storage_item_id,actual_in_quantity , purchase_no,
        quality_date,production_date, tenant_id,receiving_container,should_in_quantity, specify)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.stockStorageItemId}, #{item.shouldQuantity}, #{item.purchaseNo}, #{item.qualityDate},
            #{item.productionDate}, #{item.tenantId},#{item.receivingContainer},#{item.actualInQuantity}, #{item.specify})
        </foreach>
    </insert>

    <select id="selectByDetailBatchDO" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="BASE_COLUMN"/>
        from stock_storage_item_detail
        where stock_storage_item_id in
        <foreach collection="list" index="index" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>

    <update id="updateItemDetail"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        update stock_storage_item_detail
        set should_in_quantity = #{actualInQuantity}
        where id = #{id}
    </update>

    <update id="updateItemDetailQuantityAndContainerNo"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        update stock_storage_item_detail
        <set>
            <if test="actualInQuantity != null">
                should_in_quantity = #{actualInQuantity},
            </if>
            <if test="receivingContainer != null">
                receiving_container = #{receivingContainer}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateItemDetailAddTime"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockStorageItemDetailDO">
        update stock_storage_item_detail
        set create_time = #{createTime}
        where stock_storage_item_id = #{stockStorageItemId}
    </update>
</mapper>