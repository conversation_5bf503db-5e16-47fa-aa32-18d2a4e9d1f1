package net.summerfarm.wms.application.batch.inbound.controller.batch.vo;

import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.batch.entity.SkuPurchaseBatchEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class SkuCostBatchVO implements Serializable {

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 保质期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productDate;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * 批次库存
     */
    private Integer quantity;

    /**
     * 来源批次列表
     */
    private List<SkuPurchaseBatchEntity> originBatchList;

    /**
     * 来源批次供应商列表
     */
    private List<SkuPurchaseBatchSourceVO> originBatchSupplierList;
}
