package net.summerfarm.wms.infrastructure.dao.stockTask;

import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 为了数据迁移
 */
public interface StockTaskDAO {

    int insert(StockTaskDO stockTask);

    int update(StockTaskDO stockTask);

    int updateState(@Param("id") Long id, @Param("state") Integer state);

    StockTaskDO selectByPrimaryKey(Integer id);

    StockTaskDO selectOne(@Param("taskNo") String taskNo, @Param("type") Integer type);

    StockTaskDO selectById(Long id);

    List<StockTaskDO> selectByIds(@Param("ids") List<Long> ids);

    List<StockTaskDO> listByTypes(@Param("types") List<Integer> types, @Param("maxId") Long maxId, @Param("minId") Long minId,
                                  @Param("id") Long id);


    /**
     * 初始化后面删除
     *
     * @param taskNo
     * @return
     */
    String queryName(String taskNo);


    List<StockTaskDO> listByWareNoAndInventoryLocked500(@Param("warehouseNo") Integer warehouseNo,
                                                        @Param("typeList") List<Integer> typeList,
                                                        @Param("deliveryDateStart") LocalDate deliveryDateStart,
                                                        @Param("deliveryDateEnd") LocalDate deliveryDateEnd,
                                                        @Param("addTimeStart") LocalDate addTimeStart);

    /**
     * 部分完成出库任务数量
     *
     * @param warehouseNo
     * @return
     */
    Long countPartFinishedTask(@Param("warehouseNo") Integer warehouseNo);

    int updateInventoryLocked(@Param("stockTaskId") Integer stockTaskId, @Param("inventoryLocked") Integer inventoryLocked);

    /**
     * 更新预计出库时间
     *
     * @param stockTaskDO 任务对象
     * @return int 更新数量
     * <AUTHOR>
     * @date 2023/8/4 14:01
     */
    int updateExpectTime(StockTaskDO stockTaskDO);

    int updateExternalWarehouseNo(StockTaskDO stockTaskDO);

    Long countUnFinishTask(@Param("warehouseNo") Integer warehouseNo, @Param("skuList") List<String> skuList);

    List<StockTaskDO> selectListByTaskNo(@Param("taskNoList") List<String> taskNoList, @Param("type") Integer type);

    StockTaskDO selectByWarehouseNoAndStoreNo(@Param("warehouseNo") Long warehouseNo, @Param("storeNo") Long storeNo);


    StockTaskDO selectOneByTaskNo(@Param("taskNo") String taskNo, @Param("type") Integer type);

    /**
     * 接口迁移：根据库存仓&预计出入库时间查询任务列表
     *
     * <AUTHOR>
     * @date 2024/2/28 11:57
     * @param expectStartTime 预计开始时间
     * @param expectEndTime 预计结束时间
     * @param warehouseNo 库存仓
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTask>
     */
    List<StockTaskDO> selectExpectTask(@Param("expectStartTime") LocalDateTime expectStartTime, @Param("expectEndTime") LocalDateTime expectEndTime, @Param("areaNo") Integer warehouseNo);

    /**
     * 统计越库出库任务数量
     *
     * @param type        类型
     * @param warehouseNo 仓库编号 必填
     * @param expectTime  期望出库时间 必填
     * @param storeNo     城配仓
     * @return 返回越库出库任务数量
     */
    Long countCrossTaskByType(@Param("type") Integer type, @Param("warehouseNo") Integer warehouseNo,
                              @Param("storeNo") Integer storeNo,
                              @Param("expectTime") LocalDateTime expectTime);

    /**
     * 批量查询出库任务
     * @param type 出入库类型
     * @param expectTime 预期出(入)库时间
     * @param state 出入库进展
     * @param outboundCategory 出库分类
     * @return 结果
     */
    List<StockTaskDO> queryByTypeAndExpectTimeAndState(@Param("type") Integer type,
                                                       @Param("expectTime") LocalDate expectTime,
                                                       @Param("state") Integer state,
                                                       @Param("outboundCategory") Integer outboundCategory);

    /**
     * 查询出库任务的任务面板 - 数量
     * @param warehouseNo 仓库编号
     * @param stateList 任务状态
     * @param typeList 任务类型
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(@Param("warehouseNo") Integer warehouseNo,
                                                @Param("stateList") List<Integer> stateList,
                                                @Param("typeList") List<Integer> typeList,
                                                @Param("createTimeStart") LocalDateTime createTimeStart,
                                                @Param("createTimeEnd") LocalDateTime createTimeEnd);

    /**
     * 查询出库任务的任务面板 - 未完成任务
     * @param warehouseNo 仓库编号
     * @param stateList 任务状态
     * @param typeList 任务类型
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    List<Long> queryTaskPanelNotFinishTask(@Param("warehouseNo") Integer warehouseNo,
                                           @Param("stateList") List<Integer> stateList,
                                          @Param("typeList") List<Integer> typeList,
                                          @Param("createTimeStart") LocalDateTime createTimeStart,
                                          @Param("createTimeEnd") LocalDateTime createTimeEnd);
    /**
     * 查询出库任务的任务面板 - 未及时完成任务
     * @param warehouseNo 仓库编号
     * @param stateList 任务状态
     * @param typeList 任务类型
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    List<Long> queryTaskPanelNotFinishInTimeTask(@Param("warehouseNo") Integer warehouseNo,
                                                 @Param("stateList") List<Integer> stateList,
                                                  @Param("typeList") List<Integer> typeList,
                                                  @Param("createTimeStart") LocalDateTime createTimeStart,
                                                  @Param("createTimeEnd") LocalDateTime createTimeEnd);

    /**
     * 查询需要推送金蝶的销售出库任务
     * @param warehouseNo
     * @return
     */
    List<Long> queryOutTaskNeedNoticeKingdee(@Param("warehouseNo") Integer warehouseNo, @Param("createTimeStart") LocalDateTime createTimeStart, @Param("createTimeEnd") LocalDateTime createTimeEnd);

    /**
     * 查询需要推送金蝶的采购出库任务
     * @param warehouseNo
     * @return
     */
    List<Long> queryPurchaseOutTaskNeedNoticeKingdee(@Param("warehouseNo") Integer warehouseNo, @Param("createTimeStart") LocalDateTime createTimeStart, @Param("createTimeEnd") LocalDateTime createTimeEnd);

}
