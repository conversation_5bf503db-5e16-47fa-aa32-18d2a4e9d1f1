package net.summerfarm.wms.api.h5.stocktaking.dto.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description
 * @Date 2023/6/28 17:50
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchValidityPeriodResp {

    /**
     * 仓编号
     */
    private Integer warehouseNo;
    /**
     * sku
     */
    private String sku;
    /**
     * saas sku
     */
    private Long saasSkuId;
    /**
     * 采购单号
     */
    private String purchaseNo;
    /**
     * 效期列表
     */
    private List<ValidityPeriodDTO> validityPeriodList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidityPeriodDTO {

        /**
         * 保质期
         */
        private LocalDate qualityDate;

        /**
         * 生产日期
         */
        private LocalDate productionDate;
        /**
         * 库存数量
         */
        private Integer quantity;
        /**
         * 库存成本
         */
        private BigDecimal cost;

    }

}
