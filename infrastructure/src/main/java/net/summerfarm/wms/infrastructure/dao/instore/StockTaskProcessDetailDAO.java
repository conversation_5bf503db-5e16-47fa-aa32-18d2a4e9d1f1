package net.summerfarm.wms.infrastructure.dao.instore;

import net.summerfarm.wms.infrastructure.dao.instore.dataobject.ProcessWithDetailIdDO;
import net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskProcessDetailDO;

import java.util.List;

@Deprecated
public interface StockTaskProcessDetailDAO {
    int insert(StockTaskProcessDetailDO processDetail);
    void batchInsert(List<StockTaskProcessDetailDO> processDetail);
    int update(ProcessWithDetailIdDO processWithDetailIdDO);
    List<StockTaskProcessDetailDO> listByProcessId(Long processId);
    List<StockTaskProcessDetailDO> listByProcessIds(List<Long> processIds);
}
