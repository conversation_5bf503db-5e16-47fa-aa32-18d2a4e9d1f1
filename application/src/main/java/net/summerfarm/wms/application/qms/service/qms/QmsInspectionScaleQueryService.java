package net.summerfarm.wms.application.qms.service.qms;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.qms.inbound.controller.qms.input.query.QmsInspectionScaleQueryInput;
import net.summerfarm.wms.domain.qms.entity.QmsInspectionScaleEntity;

/**
 *
 * @date 2024-04-02 09:34:21
 * @version 1.0
 *
 */
public interface QmsInspectionScaleQueryService {

    /**
     * @description: 新增
     * @return QmsInspectionScaleEntity
     **/
    PageInfo<QmsInspectionScaleEntity> getPage(QmsInspectionScaleQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    QmsInspectionScaleEntity getDetail(Long id);

}