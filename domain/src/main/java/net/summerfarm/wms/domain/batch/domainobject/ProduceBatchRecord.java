package net.summerfarm.wms.domain.batch.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ct
 * create at:  2022/11/11  15:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProduceBatchRecord {


    /**
     * 记录id
     */
    private Integer id;

    /**
     * sku
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 生产批次id
     */
    private Long warehouseProduceBatchId;

    /**
     * 操作数量
     */
    private Integer quantity;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 更新类型
     */
    private Integer type;

    /**
     * 更新人
     */
    private String recorder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
}
