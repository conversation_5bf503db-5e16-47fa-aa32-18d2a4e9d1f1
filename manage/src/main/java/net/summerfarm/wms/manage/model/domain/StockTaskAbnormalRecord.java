package net.summerfarm.wms.manage.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/11/30
 */
@Data
@Accessors(chain = true)
public class StockTaskAbnormalRecord {

    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
    /**
     * 任务条目id
     */
    private Integer stockTaskId;

    private String sku;
    /**
     * 异常数量
     */
    private Integer quantity;
    /**
     * 原因：0少货 1拒收
     */
    private Integer reasonType;

    private Integer createAdminId;


}
