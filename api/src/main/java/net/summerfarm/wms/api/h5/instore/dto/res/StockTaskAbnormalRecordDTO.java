package net.summerfarm.wms.api.h5.instore.dto.res;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2022/11/18  16:14
 * 异常操作
 */
@Data
@Builder
public class StockTaskAbnormalRecordDTO {

    /**
     * 异常数量
     */
    private Integer quantity;
    /**
     * 原因：0少货 1拒收 3,货损 4 回库
     */
    private Integer reasonType;

    /**
     * 采购单
     */
    private String purchaseNo;

    private LocalDate shelfLife;

    /**
     * 生产日期
     */
    private LocalDate produceAt;

}
