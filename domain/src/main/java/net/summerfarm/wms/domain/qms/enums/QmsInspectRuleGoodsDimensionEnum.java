package net.summerfarm.wms.domain.qms.enums;

import lombok.Getter;

/**
 * 维度sku/spu, 1-sku,2-spu
 */
@Getter
public enum QmsInspectRuleGoodsDimensionEnum {

    SKU(1, "sku"),
    SPU(2, "spu"),

    ;
    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    QmsInspectRuleGoodsDimensionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean equalsCode(Integer input){
        return this.code.equals(input);
    }
}
