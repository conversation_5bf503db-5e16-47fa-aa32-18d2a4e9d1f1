package net.summerfarm.wms.application.safeinventorylock.assembler;

import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.query.WmsSafeInventoryLockQueryInput;
import net.summerfarm.wms.application.safeinventorylock.vo.WmsSafeInventoryLockVO;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.enums.LockStatusEnum;
import net.summerfarm.wms.domain.safeinventorylock.enums.LockTypeEnum;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam;

import java.time.LocalDateTime;

/**
 * 安全库存锁定表转换器
 * <AUTHOR>
 * @date 2025-07-30
 */
public class WmsSafeInventoryLockAssembler {

    /**
     * QueryInput转QueryParam
     */
    public static WmsSafeInventoryLockQueryParam toWmsSafeInventoryLockQueryParam(WmsSafeInventoryLockQueryInput input) {
        if (input == null) {
            return null;
        }
        WmsSafeInventoryLockQueryParam param = new WmsSafeInventoryLockQueryParam();
        param.setPageIndex(input.getPageIndex());
        param.setPageSize(input.getPageSize());
        param.setId(input.getId());
        param.setWarehouseNo(input.getWarehouseNo());
        param.setWarehouseNos(input.getWarehouseNos());
        param.setSku(input.getSku());
        param.setSkus(input.getSkus());
        param.setBatchNo(input.getBatchNo());
        param.setProduceDate(input.getProduceDate());
        param.setQualityDate(input.getQualityDate());
        param.setCabinetCode(input.getCabinetCode());
        param.setLockNo(input.getLockNo());
        param.setLockNos(input.getLockNos());
        param.setLockType(input.getLockType());
        param.setLockTypes(input.getLockTypes());
        param.setLockStatus(input.getLockStatus());
        param.setLockStatuses(input.getLockStatuses());
        param.setLockReason(input.getLockReason());
        param.setCreateOperator(input.getCreateOperator());
        param.setUpdateOperator(input.getUpdateOperator());
        param.setTenantId(input.getTenantId());
        param.setCreateTimeStart(input.getCreateTimeStart());
        param.setCreateTimeEnd(input.getCreateTimeEnd());
        param.setUpdateTimeStart(input.getUpdateTimeStart());
        param.setUpdateTimeEnd(input.getUpdateTimeEnd());
        return param;
    }


    /**
     * LockInput转CommandParam
     */
    public static WmsSafeInventoryLockCommandParam toWmsSafeInventoryLockCommandParam(WmsSafeInventoryLockInput input, String operator, String lockNo) {
        if (input == null) {
            return null;
        }
        return WmsSafeInventoryLockCommandParam.builder()
                .warehouseNo(input.getWarehouseNo())
                .sku(input.getSku())
                .batchNo(input.getBatchNo())
                .produceDate(input.getProduceDate())
                .qualityDate(input.getQualityDate())
                .cabinetCode(input.getCabinetCode())
                .lockNo(lockNo)
                .initQuantity(input.getLockQuantity())
                .lockQuantity(input.getLockQuantity())
                .lockType(input.getLockType())
                .lockStatus(LockStatusEnum.LOCKED.getCode())
                .lockReason(input.getLockReason())
                .createOperator(operator)
                .updateOperator(operator)
                .tenantId(input.getTenantId())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    /**
     * Entity转VO
     */
    public static WmsSafeInventoryLockVO toWmsSafeInventoryLockVO(WmsSafeInventoryLockEntity entity) {
        if (entity == null) {
            return null;
        }
        WmsSafeInventoryLockVO vo = new WmsSafeInventoryLockVO();
        vo.setId(entity.getId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setWarehouseNo(entity.getWarehouseNo());
        vo.setSku(entity.getSku());
        vo.setBatchNo(entity.getBatchNo());
        vo.setProduceDate(entity.getProduceDate());
        vo.setQualityDate(entity.getQualityDate());
        vo.setCabinetCode(entity.getCabinetCode());
        vo.setLockNo(entity.getLockNo());
        vo.setInitQuantity(entity.getInitQuantity());
        vo.setLockQuantity(entity.getLockQuantity());
        vo.setLockType(entity.getLockType());
        vo.setLockStatus(entity.getLockStatus());
        vo.setLockReason(entity.getLockReason());
        vo.setCreateOperator(entity.getCreateOperator());
        vo.setUpdateOperator(entity.getUpdateOperator());
        vo.setTenantId(entity.getTenantId());

        // 设置枚举描述
        LockTypeEnum lockTypeEnum = LockTypeEnum.getByCode(entity.getLockType());
        if (lockTypeEnum != null) {
            vo.setLockTypeDesc(lockTypeEnum.getDesc());
        }

        LockStatusEnum lockStatusEnum = LockStatusEnum.getByCode(entity.getLockStatus());
        if (lockStatusEnum != null) {
            vo.setLockStatusDesc(lockStatusEnum.getDesc());
        }

        return vo;
    }

}
