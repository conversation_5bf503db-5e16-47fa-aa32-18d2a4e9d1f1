package net.summerfarm.wms.facade.tms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 配送委托单状态
 */
@Getter
@AllArgsConstructor
public enum DistOrderCityStatusEnum {
    TO_BE_WIRED(10, "待排线"),
    IN_WIRED(11, "排线中"),
    CLOSED(18, "已关闭"),
    CANCEL_BEFORE_WIRED(19, "完成排线前订单取消"),
    TO_BE_PICKED(20, "待捡货"),
    IN_DELIVERY(30, "配送中"),
    DELIVERY_ERROR(38, "配送异常"),
    CANCEL_AFTER_WIRED(39, "完成排线后订单取消"),
    COMPLETE_DELIVERY(40, "已完成"),
    REJECT(41, "拒收"),
    COMPLETE_DELIVERY_SHORT(42, "配送完成但缺货");

    private final Integer code;
    private final String name;

}
