package net.summerfarm.wms.domain.materialManage.repository;


import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskCommandParam;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialTaskCommandRepository {

    WmsMaterialTaskEntity insertSelective(WmsMaterialTaskCommandParam param);

    int updateSelectiveById(WmsMaterialTaskCommandParam param);

    int remove(Long id);

}