package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/12/1
 */
@Data
public class StockArrangeItemDetail implements Serializable {

    private Integer id;

    @ApiModelProperty(value = "预约单条目id")
    private Integer stockArrangeItemId;

    @ApiModelProperty(value = "预约单id")
    private Integer stockArrangeId;

    @ApiModelProperty(value = "任务id")
    private Integer stockTaskId;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "预约数量")
    private Integer arrQuantity;

    @ApiModelProperty(value = "到货数量")
    private Integer quantity;

    @ApiModelProperty(value = "货位编号")
    private String glNo;

    @ApiModelProperty(value = "保质期")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    @ApiModelProperty(value = "生产日期")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productionDate;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "发起时间")
    private LocalDateTime createTime;

    /**
     * 批次到货数量
     */
    private Integer batchQuantity;

    private String checkReport;
}
