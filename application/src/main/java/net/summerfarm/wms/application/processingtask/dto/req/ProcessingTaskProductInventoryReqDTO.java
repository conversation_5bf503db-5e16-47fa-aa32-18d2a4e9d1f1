package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProcessingTaskProductInventoryReqDTO implements Serializable {


    /**
     * 成品SKU编码
     */
    private String productSkuCode;
//
//    /**
//     * 成品采购批次
//     */
//    private String purchaseBatchCode;

    /**
     * 成品生产日期
     */
    private String purchaseBatchProductionDate;
//
//    /**
//     * 成品保质期
//     */
//    private String purchaseBatchQualityDate;

    /**
     * 成品提交数量
     */
    private Integer submitQuantity;

    public static ProcessingTaskProductInventoryReqDTO build(
            String productSkuCode,
//            String purchaseBatchCode,
            String purchaseBatchProductionDate,
//            String purchaseBatchQualityDate,
            Integer submitQuantity
    ){
        ProcessingTaskProductInventoryReqDTO reqDTO = new ProcessingTaskProductInventoryReqDTO();
        reqDTO.setProductSkuCode(productSkuCode);
//        reqDTO.setPurchaseBatchCode(purchaseBatchCode);
        reqDTO.setPurchaseBatchProductionDate(purchaseBatchProductionDate);
//        reqDTO.setPurchaseBatchQualityDate(purchaseBatchQualityDate);
        reqDTO.setSubmitQuantity(submitQuantity);
        return reqDTO;
    }
}
