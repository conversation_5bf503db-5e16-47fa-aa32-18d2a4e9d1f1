package net.summerfarm.wms.domain.stocktask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.domain.purchase.domainobject.AllocationOrderItem;
import net.summerfarm.wms.domain.stocktask.domainobject.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2022/11/28  13:28
 */
@Repository
public interface StockTaskRepository {

    void updateState(StockTask stockTask);

    void updateStateNotUpdateTime(StockTask stockTask);

    void saveProcessExt(StockTaskProcessExpand stockTaskProcessExpand);

    Long findStockTaskProcessId(Long inBoundOrderId);

    StockTaskProcess findProcess(Long inBoundOrderId);

    List<StockTaskProcess> findTaskProcess(Long taskId, String sku, String purchaseNo, LocalDate produceAt);

    /**
     * 外部来源单号查询出库任务
     *
     * @param listNo l
     * @return r
     */
    StockTask findOutStockTask(String listNo);

    StockTask findOutStockTask(Long id);

    List<StockTask> findOutStockTaskList(List<Long> ids);

    List<StockTaskProcessDetail> findProcessDetail(Long processId);

    Long saveStockTask(StockTask stockTask);

    Long insertStockTask(StockTask stockTask);


    List<StockTaskItem> queryStockTaskItem(Integer taskId);

    List<StockTaskItem> queryStockTaskItem(List<Integer> taskIds);

    List<StockTaskItem> queryStockTaskItem(Integer taskId, List<String> skus);

    Integer queryStockTaskItemQuantity(Integer stockTaskId, String sku);

    /**
     * 详情id
     *
     * @param itemTaskId
     * @return
     */
    List<StockTaskItemDetail> queryStockTaskItemDetail(List<Integer> itemTaskId);

    /**
     * 查询出库任务item详情
     *
     * @param taskId
     * @param sku
     * @param produceDate
     * @param qualityDate
     * @return
     */
    StockTaskItemDetail queryDetailByTaskIdAndSku(Integer taskId, String sku, LocalDate produceDate, LocalDate qualityDate);

    /**
     * 查询未出库的数量
     * @param warehouseNo 仓库号
     * @param sku sku
     * @return 未出库的数量
     */
    Integer queryWaitOutNumByType(Long warehouseNo, String sku, LocalDate expectTime);

    /**
     * 查询未出库的数量
     *
     * @param warehouseNo 仓库号
     * @param skus skus
     * @return 未出库的数量
     * @return map<Sku, num></>
     */
    Map<String, Integer> mapWaitOutNumByType(Long warehouseNo, List<String> skus, LocalDate expectTime);

    /**
     * 调拨单id查询数量信息
     */
    List<AllocationOrderItem> queryStockTaskItemByAllocate(String listNo);

    /**
     * 调拨出库数量
     *
     * @param listNo
     * @param sku
     * @return
     */
    Integer queryStockTaskItemQuantityByAllocate(String listNo, String sku);

    /**
     * 创建出库任务明细
     *
     * @param stockTaskItem
     * <AUTHOR>
     * @date 2023/4/21 15:18
     */
    Integer createStockTaskItem(StockTaskItem stockTaskItem);

    /**
     * 根据库存仓和库存冻结状态查询出库任务列表
     *
     * @param warehouseNo     库存仓
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTask>
     * <AUTHOR>
     * @date 2023/5/19 15:27
     */
    List<StockTask> listByWareNoAndInventoryLocked500(Integer warehouseNo,
                                                      LocalDate deliveryDateStart,
                                                      LocalDate deliveryDateEnd,
                                                      LocalDate addTimeStart,
                                                      List<Integer> typeList);

    /**
     * 新增出库任务明细库位库存占用信息
     *
     * <AUTHOR>
     * @date 2023/5/22 10:44
     */
    int createStockTaskItemCabinetOccupy(StockTaskItemCabinetOccupy stockTaskItemCabinetOccupy);

    /**
     * 根据出库任务编码查询出库任务信息
     *
     * @param stockTaskId 出库任务编码
     * @return net.summerfarm.wms.domain.stocktask.domainobject.StockTask
     * <AUTHOR>
     * @date 2023/5/22 11:25
     */
    StockTask queryStockTaskById(Integer stockTaskId);

    /**
     * 部分完成出库任务数量
     *
     * @param warehouseNo
     * @return
     */
    Long countPartFinishedTask(Integer warehouseNo);

    /**
     * 锁定出库任务库存冻结状态
     *
     * @param stockTaskId
     * <AUTHOR>
     * @date 2023/5/25 13:31
     */
    void updateInventoryLocked(Integer stockTaskId, Integer inventoryLocked);

    StockTaskItem queryByTaskIdAndSku(String sku, Integer taskId);

    /**
     * 更新预计出库时间
     *
     * <AUTHOR>
     * @date 2023/8/4 13:57
     * @param stockTask
     */
    void updateExpectTime(StockTask stockTask);

    /**
     * 更新外部仓编码
     * @param stockTask
     */
    void updateExternalWarehouseNo(StockTask stockTask);

    /**
     * 根据外部单号查询出库任务
     *
     * <AUTHOR>
     * @date 2023/8/4 14:09
     * @param outOrderNo 外部单号
     * @param type 类型
     * @return net.summerfarm.wms.domain.stocktask.domainobject.StockTask
     */
    StockTask selectByTaskNo(String outOrderNo, Integer type);

    /**
     * 根据单号列表查询出库人任务
     *
     * <AUTHOR>
     * @date 2023/11/6 13:47
     */
    List<StockTask> selectListByTaskNo(List<String> outOrderNoList, Integer type);

    /**
     * 根据库存仓和城配仓查询未完成的最近的一个出库任务
     * @param warehouseNo 仓库号
     * @param storeNo 城配仓
     * @return 出库任务
     */
    StockTask selectByWarehouseNoAndStoreNo(Long warehouseNo, Long storeNo);

    Boolean hasUnFinishTask(Integer warehouseNo, List<String> skuList);

    void cancel(StockTask stockTask);

    /**
     * 统计越库出库任务数量
     *
     * @param type        任务类型 必填
     * @param warehouseNo 仓库编号 必填
     * @param expectTime  期望出库时间 必填
     * @param storeNo     城配仓
     * @return 返回越库出库任务数量
     */
    Long countCrossTaskByType(Integer type, Integer warehouseNo, Integer storeNo, LocalDateTime expectTime);

    List<StockTask> selectExpectTask(LocalDateTime expectStartTime, LocalDateTime expectEndTime, Integer areaNo);

    BigDecimal countCapacity(List<Integer> stockTaskIdList);

    /**
     * 批量查询出库任务
     * @param type 出入库类型
     * @param expectTime 预期出(入)库时间
     * @param state 出入库进展
     * @param outboundCategory 出库分类
     * @return 结果
     */
    List<StockTask> queryList(Integer type, LocalDate expectTime, Integer state, Integer outboundCategory);


    /**
     * 查询出库任务的任务面板 - 数量
     * @param warehouseNo 仓库编号
     * @param stateList 任务状态
     * @param typeList 任务类型
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo,
                                                LocalDateTime createTimeStart,
                                                LocalDateTime createTimeEnd,
                                                List<Integer> stateList,
                                                List<Integer> typeList
                                                );
    PageInfo<String> pagePanelWindowData(Integer panelQueryTypeEnum,
                                         Integer pageNum,
                                         Integer pageSize,
                                         Integer warehouseNo,
                                         LocalDateTime createTimeStart,
                                         LocalDateTime createTimeEnd,
                                         List<Integer> stateList,
                                         List<Integer> typeList);

    Integer updateCustomerSkuCode(String customerSkuCode, Long id);

    /**
     * 查询需要推送金蝶的销售出库任务
     * @param warehouseNo
     * @return
     */
    List<Long> queryOutTaskNeedNoticeKingdee(Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd);

    /**
     * 查询需要推送金蝶的采退出库任务
     * @param warehouseNo
     * @return
     */
    List<Long> queryPurchaseOutTaskNeedNoticeKingdee(Integer warehouseNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd);
}
