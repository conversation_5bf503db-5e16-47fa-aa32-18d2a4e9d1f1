<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.SkuShareChangeRecordMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.manage.model.domain.SkuShareChangeRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo"/>
        <result column="new_remaining_transfer_out_quantity" jdbcType="INTEGER"
                property="newRemainingTransferOutQuantity"/>
        <result column="old_remaining_transfer_out_quantity" jdbcType="INTEGER"
                property="oldRemainingTransferOutQuantity"/>
        <result column="new_remaining_transfer_in_quantity" jdbcType="INTEGER"
                property="newRemainingTransferInQuantity"/>
        <result column="old_remaining_transfer_in_quantity" jdbcType="INTEGER"
                property="oldRemainingTransferInQuantity"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="record_type_desc" jdbcType="VARCHAR" property="recordTypeDesc"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , warehouse_no, sku, out_order_no, new_remaining_transfer_out_quantity, old_remaining_transfer_out_quantity,
    new_remaining_transfer_in_quantity, old_remaining_transfer_in_quantity, record_type,
    record_type_desc, remark, create_time, update_time, creator, operator
    </sql>

    <insert id="insertSelective" parameterType="net.summerfarm.wms.manage.model.domain.SkuShareChangeRecordDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into inventory_sku_share_transfer_change_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="outOrderNo != null">
                out_order_no,
            </if>
            <if test="newRemainingTransferOutQuantity != null">
                new_remaining_transfer_out_quantity,
            </if>
            <if test="oldRemainingTransferOutQuantity != null">
                old_remaining_transfer_out_quantity,
            </if>
            <if test="newRemainingTransferInQuantity != null">
                new_remaining_transfer_in_quantity,
            </if>
            <if test="oldRemainingTransferInQuantity != null">
                old_remaining_transfer_in_quantity,
            </if>
            <if test="recordType != null">
                record_type,
            </if>
            <if test="recordTypeDesc != null">
                record_type_desc,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="outOrderNo != null">
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="newRemainingTransferOutQuantity != null">
                #{newRemainingTransferOutQuantity,jdbcType=INTEGER},
            </if>
            <if test="oldRemainingTransferOutQuantity != null">
                #{oldRemainingTransferOutQuantity,jdbcType=INTEGER},
            </if>
            <if test="newRemainingTransferInQuantity != null">
                #{newRemainingTransferInQuantity,jdbcType=INTEGER},
            </if>
            <if test="oldRemainingTransferInQuantity != null">
                #{oldRemainingTransferInQuantity,jdbcType=INTEGER},
            </if>
            <if test="recordType != null">
                #{recordType,jdbcType=INTEGER},
            </if>
            <if test="recordTypeDesc != null">
                #{recordTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>