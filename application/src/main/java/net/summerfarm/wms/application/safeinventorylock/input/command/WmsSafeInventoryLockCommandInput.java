package net.summerfarm.wms.application.safeinventorylock.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 安全库存锁定表命令输入
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
public class WmsSafeInventoryLockCommandInput implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码不能为空")
    private Integer warehouseNo;

    /**
     * sku
     */
    @NotBlank(message = "SKU不能为空")
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 初始化数量
     */
    @NotNull(message = "初始化数量不能为空")
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    @NotNull(message = "锁定数量不能为空")
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    @NotNull(message = "锁定类型不能为空")
    private Integer lockType;

    /**
     * 锁定状态，1：锁定 0：释放
     */
    private Integer lockStatus;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;
}
