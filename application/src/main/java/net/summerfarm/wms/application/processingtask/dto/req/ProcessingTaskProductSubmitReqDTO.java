package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskProductSubmitReqDTO implements Serializable {

    /**
     * 加工成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 加工规格提交列表
     */
    private List<ProcessingTaskProductSpecReqDTO> processingSpecList;
}
