package net.summerfarm.wms.api.h5.pickuptask.dto.res;

import lombok.Data;
import net.summerfarm.wms.api.h5.goods.enums.SkuTypeEnums;
import net.summerfarm.wms.api.h5.pickuptask.dto.req.PickUpTaskProductSpecDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 投线任务查询Res对象
 * <AUTHOR>
 * @date 2023/02/18
 */
@Data
public class PickUpTaskQueryResDTO {

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 城配仓编码
     */
    private Integer storeNo;

    /**
     * 客户名称
     */
    private String adminName;

    /**
     * 客户id
     */
    private Integer adminId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * SKU单位
     */
    private String skuUnit;

    /**
     * SKU重量
     */
    private BigDecimal skuWeight;

    /**
     * SKU规格描述
     */
    private String skuSpecDesc;

    /**
     * SKU温区
     */
    private String storageLocation;

    /**
     * SKU类型 0自营 1代仓
     */
    private String skuType;

    /**
     * 配送时间
     */
    private String deliveryTime;

    /**
     * 配送类型
     */
    private Boolean deliveryType;

    /**
     * 投线类型
     */
    private Integer pickUpType;

    /**
     * 包装
     */
    private String packing;

    /**
     * 进口/国产
     */
    private Integer isDomestic;

    /**
     * 成品规格list
     */
    List<PickUpTaskProductSpecDTO> productSpecList;



    public String getOtherAttribute() {
        // 包装方式 packing
        // 国产还是进口
        String origin = Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic) ? "进口" : "国产";
        return StringUtils.joinWith("/", packing, skuType, origin);
    }
}
