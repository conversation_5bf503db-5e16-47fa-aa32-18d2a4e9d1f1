package net.summerfarm.wms.infrastructure.repository.skucodetrace;

import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceCommandRepository;
import net.summerfarm.wms.infrastructure.dao.skucode.SkuBatchCodeTraceDAO;
import net.summerfarm.wms.infrastructure.dao.skucode.SkuBatchCodeTraceWeightLogDAO;
import net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeTrace;
import net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeTraceWeightLog;
import net.summerfarm.wms.infrastructure.repository.skucodetrace.converter.SkuBatchCodeTraceConverter;
import net.summerfarm.wms.infrastructure.repository.skucodetrace.converter.SkuBatchCodeTraceWeightLogConverter;
import net.summerfarm.wms.infrastructure.util.MybatisPlusUtil;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 溯源码操作实现类<br/>
 * date: 2024/8/9 10:39<br/>
 *
 * <AUTHOR> />
 */
@Repository
public class SkuBatchCodeTraceCommandRepositoryImpl implements SkuBatchCodeTraceCommandRepository {

    @Resource
    private SkuBatchCodeTraceDAO skuBatchCodeTraceDAO;
    @Resource
    private SkuBatchCodeTraceWeightLogDAO skuBatchCodeTraceWeightLogDAO;

    @Override
    public List<SkuBatchCodeTraceEntity> batchSave(List<SkuBatchCodeTraceEntity> createTraceEntityList) {
        if(CollectionUtils.isEmpty(createTraceEntityList)){
            return createTraceEntityList;
        }
        List<SkuBatchCodeTrace> skuBatchCodeTraces = createTraceEntityList.stream().map(SkuBatchCodeTraceConverter::entity2Model).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(skuBatchCodeTraces, SkuBatchCodeTrace.class);
        return skuBatchCodeTraces.stream().map(SkuBatchCodeTraceConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchUpdate(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities) {
        if(CollectionUtils.isEmpty(skuBatchCodeTraceEntities)){
            return;
        }
        // 按照ID排序
        skuBatchCodeTraceEntities.sort(Comparator.comparing(SkuBatchCodeTraceEntity::getId));
        List<SkuBatchCodeTrace> skuBatchCodeTraces = skuBatchCodeTraceEntities.stream().map(SkuBatchCodeTraceConverter::entity2Model).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(skuBatchCodeTraces, SkuBatchCodeTrace.class);
    }

    @Override
    public void updateWeight(SkuBatchCodeTraceEntity skuBatchCodeTraceEntity, BigDecimal weight, String weightUserName, LocalDateTime weightDateTime) {
        int count = skuBatchCodeTraceDAO.updateWeight(skuBatchCodeTraceEntity.getId() ,
                weight, SkuBatchCodeTraceEnums.State.HAVE_WEIGHT.getValue(),weightUserName, weightDateTime);
        if (count <= 0){
            throw new BizException("更新重量发生并发请重试");
        }

        // 插入重量日志
        SkuBatchCodeTraceWeightLog log = SkuBatchCodeTraceWeightLogConverter.convert(
                skuBatchCodeTraceEntity, weight, weightUserName, weightDateTime);
        skuBatchCodeTraceWeightLogDAO.insert(log);
    }
}
