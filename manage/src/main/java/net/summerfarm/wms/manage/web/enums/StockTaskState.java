package net.summerfarm.wms.manage.web.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
public enum StockTaskState {

    WAIT_IN_OUT(0,"待入(出)库"),
    PART_IN_OUT(1,"部分入(出)库"),
    FINISH(2,"完成入(出)库"),
    CANCEL(3,"任务取消"),
    CLOSE(4,"任务关闭"),
    OUTBOUND_ING(5,"出库中"),
    ;

    private final int id;

    private final String state;

    StockTaskState(int id,String state){
        this.id = id;
        this.state = state;
    }

    public int getId(){
        return id;
    }

    public String getState(){
        return state;
    }

    public static List<Integer> getCompletionStatus() {
        return Lists.newArrayList(StockTaskState.FINISH.getId(), StockTaskState.CANCEL.getId(), StockTaskState.CLOSE.getId());
    }

}
