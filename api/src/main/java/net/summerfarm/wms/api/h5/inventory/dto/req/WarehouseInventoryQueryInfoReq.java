package net.summerfarm.wms.api.h5.inventory.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WarehouseInventoryQueryInfoReq implements Serializable {

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private Long warehouseNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 商品spuId
     */
    @ApiModelProperty(value = "商品spuId")
    private Long pdId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 是否售罄，1为无库存，0为有库存
     */
    @ApiModelProperty(value = "是否售罄，1为无库存，0为有库存")
    private Integer saleOut;

    /**
     * 有无库存，1为有库存，0为无库存
     */
    @ApiModelProperty(value = "有无库存，1为有库存，0为无库存")
    private Integer quantityGtZero;
}
