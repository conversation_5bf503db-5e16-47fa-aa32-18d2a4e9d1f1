package net.summerfarm.wms.application.instore.converter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wms.api.h5.instore.enums.InBoundOrderEnum;
import net.summerfarm.wms.domain.instore.enums.InBoundOrderType;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum InBoundOrderTypeConvert {
    /**
     * 枚举转换
     */
    STORE_ALLOCATION_IN(InBoundOrderType.STORE_ALLOCATION_IN, InBoundOrderEnum.STORE_ALLOCATION_IN),
    PURCHASE(InBoundOrderType.PURCHASE_IN, InBoundOrderEnum.PURCHASE_IN),
    RETURN_IN(InBoundOrderType.RETURN_IN, InBoundOrderEnum.RETURN_IN),
    ALLOCATION_ABNORMAL_IN(InBoundOrderType.ALLOCATION_ABNORMAL_IN, InBoundOrderEnum.ALLOCATION_ABNORMAL_IN),
    AFTER_SALE_IN_NEW(InBoundOrderType.AFTER_SALE_IN_NEW, InBoundOrderEnum.AFTER_SALE_IN_NEW),
    LACK_GOODS_APPROVED(InBoundOrderType.LACK_GOODS_APPROVED, InBoundOrderEnum.LACK_GOODS_APPROVED),
    SKIP_STORE_IN(InBoundOrderType.SKIP_STORE_IN, InBoundOrderEnum.SKIP_STORE_IN),
    STOP_STORE_IN(InBoundOrderType.STOP_STORE_IN, InBoundOrderEnum.STOP_STORE_IN),
    OUT_MORE_IN(InBoundOrderType.OUT_MORE_IN, InBoundOrderEnum.OUT_MORE_IN),
    OTHER_IN(InBoundOrderType.OTHER_IN, InBoundOrderEnum.OTHER_IN),
    CROSS_WAREHOUSE_IN(InBoundOrderType.CROSS_WAREHOUSE_IN, InBoundOrderEnum.CROSS_WAREHOUSE_IN),
    INIT_IN(InBoundOrderType.INIT_IN, InBoundOrderEnum.INIT_IN),
    UNKNOWN(InBoundOrderType.UNKNOWN, InBoundOrderEnum.UNKNOWN),
    ;

    public static InBoundOrderTypeConvert convert(InBoundOrderEnum inBoundOrderEnum) {
        return Arrays.stream(InBoundOrderTypeConvert.values())
                .filter(o -> o.getInBoundOrderEnum().equals(inBoundOrderEnum))
                .findFirst().orElse(InBoundOrderTypeConvert.UNKNOWN);
    }

    public static Integer convert(Integer param) {
        return Arrays.stream(InBoundOrderTypeConvert.values())
                .filter(o -> o.getInBoundOrderType().getCode().equals(param))
                .findFirst().orElse(InBoundOrderTypeConvert.UNKNOWN).getInBoundOrderEnum().getCode();
    }

    InBoundOrderType inBoundOrderType;
    InBoundOrderEnum inBoundOrderEnum;
}
