# 安全库存锁定释放功能测试说明

## 功能概述
在 `WmsSafeInventoryLockCommandServiceImpl` 的 `unlock` 方法中增加了批量释放逻辑：

### 新增功能
当入参 `id` 为空时，按照入参中的仓库+SKU所有未释放的锁定记录，按照本次释放数量依次释放。

### 分配逻辑
1. **优先释放库存锁定类型的记录**（LockTypeEnum.INVENTORY_LOCK = 1）
2. **当库存锁定类型的锁定记录数量不满足本次释放数量时**，剩余数量在批次锁定类型记录中按锁定编号顺序依次释放（LockTypeEnum.BATCH_LOCK = 2）
3. **如果所有记录释放后仍有剩余数量**，返回错误

## 主要修改内容

### 1. 修改 unlock 方法
- 增加了判断逻辑：当 `input.getId()` 为空时执行批量释放
- 保持原有的单个记录释放逻辑不变

### 2. 新增方法
- `executeBatchUnlock`: 批量释放的主要逻辑
- `processInventoryLockRecords`: 处理库存锁定类型记录
- `processBatchLockRecords`: 处理批次锁定类型记录（按锁定编号排序）
- `unlockRecord`: 释放单个锁定记录的通用方法
- `unlockSingleRecord`: 原有单个记录释放逻辑的封装

### 3. 修改输入参数
- 移除了 `WmsSafeInventoryUnlockInput.id` 字段的 `@NotNull` 注解
- 更新了注释说明支持批量释放

## 测试场景

### 场景1：单个记录释放（原有功能）
```json
{
  "id": 123,
  "warehouseNo": 1001,
  "sku": "SKU001",
  "unlockQuantity": 10,
  "lockType": 1,
  "unlockReason": "测试释放"
}
```

### 场景2：批量释放 - 库存锁定充足
```json
{
  "id": null,
  "warehouseNo": 1001,
  "sku": "SKU001",
  "unlockQuantity": 50,
  "lockType": 1,
  "unlockReason": "批量释放测试"
}
```
假设有库存锁定记录总量 >= 50，应该优先从库存锁定记录中释放。

### 场景3：批量释放 - 需要释放批次锁定
```json
{
  "id": null,
  "warehouseNo": 1001,
  "sku": "SKU001",
  "unlockQuantity": 100,
  "lockType": 1,
  "unlockReason": "批量释放测试"
}
```
假设库存锁定记录总量为30，批次锁定记录总量为80，应该：
1. 先释放30个库存锁定
2. 再按锁定编号顺序释放70个批次锁定

### 场景4：批量释放 - 数量不足
```json
{
  "id": null,
  "warehouseNo": 1001,
  "sku": "SKU001",
  "unlockQuantity": 200,
  "lockType": 1,
  "unlockReason": "批量释放测试"
}
```
假设所有锁定记录总量为150，应该返回错误："释放失败，所有锁定记录释放后仍有剩余数量：50"

## 注意事项
1. 批量释放时会按照锁定类型优先级处理
2. 批次锁定记录按锁定编号（lockNo）升序排列
3. 每个记录的释放都会调用相应的库存释放逻辑
4. 支持部分释放，如果记录的锁定数量大于需要释放的数量，只释放需要的部分
5. 完全释放的记录会更新状态为已释放（LockStatusEnum.RELEASED）
