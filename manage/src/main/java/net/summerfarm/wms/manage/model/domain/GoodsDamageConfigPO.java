package net.summerfarm.wms.manage.model.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GoodsDamageConfigPO implements Serializable {
    private static final long serialVersionUID = 4427159043023640163L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 类型值
     */
    private Integer configValue;

    /**
     * 描述
     */
    private String configDesc;

    /**
     * 证明类型 1-图片 2-文本框
     */
    private Integer proveType;

    /**
     * 启用状态 1-启用 2-停用
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;

    /**
     * 最新版本号
     */
    private Integer lastVer;

}
