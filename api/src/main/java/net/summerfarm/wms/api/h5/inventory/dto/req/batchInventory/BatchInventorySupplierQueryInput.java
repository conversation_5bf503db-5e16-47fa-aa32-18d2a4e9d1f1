package net.summerfarm.wms.api.h5.inventory.dto.req.batchInventory;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BatchInventorySupplierQueryInput implements Serializable {

    /**
     * 货品sku
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;
}
