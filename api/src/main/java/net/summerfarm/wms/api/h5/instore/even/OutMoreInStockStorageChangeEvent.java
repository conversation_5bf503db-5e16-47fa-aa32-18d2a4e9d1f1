package net.summerfarm.wms.api.h5.instore.even;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2024/1/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OutMoreInStockStorageChangeEvent implements Serializable {
    private static final long serialVersionUID = -3626233883570102330L;

    /**
     * 出库任务id
     */
    private Integer stockTaskId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;
}
