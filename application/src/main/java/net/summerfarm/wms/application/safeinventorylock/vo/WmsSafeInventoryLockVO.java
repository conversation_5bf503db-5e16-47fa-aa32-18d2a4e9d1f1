package net.summerfarm.wms.application.safeinventorylock.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 安全库存锁定表VO
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
public class WmsSafeInventoryLockVO implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 包装
     */
    private String packaging;

    /**
     * 类目类型
     */
    private Integer categoryType;

    /**
     * sku 归属类型
     */
    private Integer skuType;

    /**
     * 是否国产（0：不是，1：是）
     */
    private Integer isDomestic;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 初始化数量
     */
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定类型描述
     */
    private String lockTypeDesc;

    /**
     * 锁定状态，1：锁定 0：释放
     */
    private Integer lockStatus;

    /**
     * 锁定状态描述
     */
    private String lockStatusDesc;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;
}
