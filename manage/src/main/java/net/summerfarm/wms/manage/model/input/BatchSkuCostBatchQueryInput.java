package net.summerfarm.wms.manage.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2024/1/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchSkuCostBatchQueryInput implements Serializable {
    private static final long serialVersionUID = 8854399426397453004L;

    /**
     * sku批次查询列表
     */
    private List<SkuCostBatchQueryInput> skuCostBatchQueryInputList;
}
