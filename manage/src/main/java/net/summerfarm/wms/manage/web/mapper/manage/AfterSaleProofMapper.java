package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.vo.RejectSaleNeedStockVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Repository
public interface AfterSaleProofMapper {


    List<RejectSaleNeedStockVo> getRejectOrderInfo(@Param("time") LocalDateTime time);
}
