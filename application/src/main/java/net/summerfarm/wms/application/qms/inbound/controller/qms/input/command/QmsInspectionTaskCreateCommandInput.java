package net.summerfarm.wms.application.qms.inbound.controller.qms.input.command;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-04-02 15:05:02
 * @version 1.0
 *
 */
@Data
public class QmsInspectionTaskCreateCommandInput implements Serializable{

	/**
	 * 仓库编号
	 */
	private Long warehouseNo;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 生产日期
	 */
	private LocalDate produceTime;

	/**
	 * 保质期
	 */
	private LocalDate shelfLife;

	/**
	 * 库位编号
	 */
	private String cabinetNo;

	/**
	 * 供应商id
	 */
	private Long supplierId;

	/**
	 * 供应商名称
	 */
	private String supplierName;

	/**
	 * 货检类型，91-到货货检，92-库内巡检，93-成品货检；
	 */
	private Integer inspType;

}