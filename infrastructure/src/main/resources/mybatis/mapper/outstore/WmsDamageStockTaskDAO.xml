<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.outstore.WmsDamageStockTaskDAO">

    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.outstore.dataobject.WmsDamageStockTaskDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="stock_task_id" jdbcType="INTEGER" property="stockTaskId" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="creater" jdbcType="VARCHAR" property="creater" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="tenant_id" jdbcType="INTEGER" property="tenantId" />
    </resultMap>

    <sql id="Base_Column_List">
        id, create_time, update_time, stock_task_id, warehouse_no, `status`, creater, updater,
    `type`, tenant_id
    </sql>

    <select id="queryTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
    SELECT COUNT(DISTINCT wdst.id) AS notFinishTaskNum,
               COUNT(CASE
                     WHEN
                        wdst.creater = #{creator,jdbcType=VARCHAR}
                        AND IFNULL(wdst.update_time, NOW()) >= DATE(wdst.create_time) + INTERVAL 1 DAY
                     THEN wdst.id
                     ELSE NULL
                     END) AS notFinishInTimeTaskNum
        FROM wms_damage_stock_task wdst
        WHERE wdst.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wdst.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wdst.status in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
        AND wdst.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
    </select>

     <select id="queryTaskPanelNotFinishTask" resultType="java.lang.Long">
        SELECT DISTINCT wdst.id
        FROM wms_damage_stock_task wdst
        WHERE wdst.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wdst.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND wdst.status in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
          AND wdst.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
    </select>

     <select id="queryTaskPanelNotFinishInTimeTask" resultType="java.lang.Long">
        SELECT DISTINCT wdst.id AS taskId
        FROM wms_damage_stock_task wdst
        WHERE wdst.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wdst.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wdst.creater = #{creator,jdbcType=VARCHAR}
        AND wdst.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
         AND wdst.status in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
        AND IFNULL(wdst.update_time, NOW()) >= DATE(wdst.create_time) + INTERVAL 1 DAY
    </select>


</mapper>