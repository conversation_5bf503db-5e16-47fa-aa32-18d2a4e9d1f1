package net.summerfarm.wms.api.h5.stocktaking.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;

/**
 * 上架情况枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SaleStateEnum {
    /**
     * 上下架情况
     */
    ONLINE(1, "上架"),
    OFFLINE(0, "下架"),
    ;

    public static SaleStateEnum convert(Integer param) {
        return Arrays.stream(SaleStateEnum.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElseThrow(() -> new BizException("未知类型"));
    }

    Integer code;
    String desc;
}
