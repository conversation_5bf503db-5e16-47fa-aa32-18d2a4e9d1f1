package net.summerfarm.wms.domain.areaStore.domainobject;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class WarehouseAndSyncBySkuCode {
    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * sku码
     */
    private String skuCode;

    /**
     * 库存同步状态
     */
    private Integer sync;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;
    /**
     * 在途库存
     */
    private Integer roadQuantity;

    /**
     * 冻结库存
     */
    private Integer lockQuantity;
    /**
     * 销售冻结库存
     */
    private Integer saleLockQuantity;
    /**
     * 安全库存
     */
    private Integer safeQuantity;
    /**
     * 可用库存
     */
    private Integer enabledQuantity;

    /**
     * 调拨在途库存
     */
    private Integer allocationRoadQuantity;
    /**
     * 采购在途库存
     */
    private Integer purchaseRoadQuantity;
    /**
     * 调拨在途货值
     */
    private BigDecimal allocationRoadGoodsValue;

    public Integer getPurchaseRoadQuantity() {
        if (this.getRoadQuantity() == null || this.getAllocationRoadQuantity() == null){
            return this.getRoadQuantity();
        }
        return this.getRoadQuantity() - this.getAllocationRoadQuantity();
    }

    public Integer getEnabledQuantity() {
        if (this.getQuantity() == null || this.getLockQuantity() == null || this.getSafeQuantity() == null){
            return onlineQuantity;
        }

        return this.getQuantity() - this.getLockQuantity() - this.getSafeQuantity();
    }
}
