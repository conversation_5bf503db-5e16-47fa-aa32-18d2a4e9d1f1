package net.summerfarm.outstore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.mission.shelving.ShelvingMissionCommandService;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingCommitInfoReqDTO;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingMissionCommitCommand;
import net.summerfarm.ofc.client.common.message.wms.FulfillmentStockNoticeItemMessage;
import net.summerfarm.ofc.client.common.message.wms.FulfillmentStockNoticeMessage;
import net.summerfarm.ofc.client.common.message.wms.StockNoticeCancelMessage;
import net.summerfarm.ofc.client.common.message.wms.StockNoticeChangeMessage;
import net.summerfarm.ofc.client.enums.GoodsSupplyOrderEnum;
import net.summerfarm.wms.api.h5.mission.shelving.ShelvingMissionCommandService;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingCommitInfoReqDTO;
import net.summerfarm.wms.api.h5.mission.shelving.req.ShelvingMissionCommitCommand;
import net.summerfarm.wms.api.h5.stocktask.StockTaskCabinetOccupyCommandService;
import net.summerfarm.wms.api.h5.stocktask.StockTaskCommandService;
import net.summerfarm.wms.api.h5.stocktask.dto.req.OrderSelfStockTaskCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCabinetOccupyCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCabinetOccupyOneKeyCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.req.StockTaskCreateCommand;
import net.summerfarm.wms.api.h5.stocktask.dto.resp.StockTaskCabinetOccupyResp;
import net.summerfarm.wms.application.acl.mq.StockTaskNoticeOrderCancelListener;
import net.summerfarm.wms.application.acl.mq.StockTaskNoticeOrderChangeListener;
import net.summerfarm.wms.application.acl.mq.StockTaskNoticeOrderOrderlyListener;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.domain.inventory.domainobject.UpdateBatchInventoryLockRelation;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryLockCommandRepository;
import net.summerfarm.wms.domain.mission.util.MissionNoFactory;
import net.summerfarm.wms.inventory.WmsInventoryQueryProvider;
import net.summerfarm.wms.inventory.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AvailableInventory4ACBatchQueryReq;
import net.summerfarm.wms.inventory.req.areaStoreInventory.AvailableInventory4ACQueryReq;
import net.summerfarm.wms.inventory.resp.AvailableInventory4ACResp;
import net.summerfarm.wms.outstore.dto.OutStoreNoticeOrderPreCanCelReq;
import net.summerfarm.wms.outstore.provider.OutStockNoticeCommandProvider;
import net.summerfarm.wms.scheduler.AutoOccupyCabinetInventoryJob;
import net.summerfarm.wms.starter.WmsApplication;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.rocketmq.support.util.MessageExtUtil;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @Date 2023/4/12 15:02
 * @<AUTHOR>
 */
@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class StockTaskTest {

    @Resource
    private StockTaskCommandService stockTaskCommandService;
    @Resource
    private AutoOccupyCabinetInventoryJob autoOccupyCabinetInventoryJob;

    @Resource
    private StockTaskCabinetOccupyCommandService cabinetOccupyCommandService;
    @Resource
    private StockTaskNoticeOrderOrderlyListener stockTaskNoticeOrderOrderlyListener;
    @Resource
    private StockTaskNoticeOrderChangeListener stockTaskNoticeOrderChangeListener;
    @Resource
    private StockTaskNoticeOrderCancelListener stockTaskNoticeOrderCancelListener;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WmsInventoryQueryProvider wmsInventoryQueryProvider;
    @Resource
    private OutStockNoticeCommandProvider outStockNoticeCommandProvider;
    @Resource
    private CabinetBatchInventoryLockCommandRepository cabinetBatchInventoryLockCommandRepository;
    @Resource
    private MissionNoFactory missionNoFactory;
    @Resource
    private RedisTemplate redisTemplate;

    @Test
    public void queryAvailableInventory4AC() {
        LocalDate date = LocalDate.now();
        String dateStr4redis = DateUtil.formatDateYmd(date);
        StringBuilder key = new StringBuilder("JH").append(dateStr4redis);

        // 生成value
        for(int i = 50; i >=0; i--) {
            System.out.println(key.toString());
            Long value = redisTemplate.opsForValue().increment(key.toString());
            System.out.println(value);
        }
        long l = System.currentTimeMillis();
        cabinetBatchInventoryLockCommandRepository.updateInsert(UpdateBatchInventoryLockRelation.builder()
                .bizId("121212")
                .bizType(CabinetInventoryOperationType.TRANSFER_OUT.getId())
                .changeQuantity(-1)
                .produceDate(DateUtil.toDate(1709258400000L))
                .sku("111111")
                .qualityDate(DateUtil.toDate(1709258400000L))
                .batch("112222111")
                .warehouseNo(3L)
//                .cabinetNo()
                .bizName(CabinetInventoryChangeTypeEnum.PURCHASES_BACK_OUT.getTypeName())
                .build());
        log.info("执行时间:{}", System.currentTimeMillis() - l);
//        DubboResponse<AvailableInventory4ACResp> availableInventory4ACRespDubboResponse = wmsInventoryQueryProvider.queryAvailableInventory4AC(AvailableInventory4ACQueryReq.builder()
//                .warehouseNo(1L).sku("**********").build());
//        System.out.println(JSON.toJSONString(availableInventory4ACRespDubboResponse.getData()));
//        outStockNoticeCommandProvider.preCancelOutStoreNoticeOrder(OutStoreNoticeOrderPreCanCelReq.builder()
//                .warehouseNo(1L)
//                .goodsSupplyNo("GSN2024010801")
//                .build());
//        DubboResponse<List<AvailableInventory4ACResp>> listDubboResponse = wmsInventoryQueryProvider.listAvailableInventory4AC(AvailableInventory4ACBatchQueryReq.builder()
//                .warehouseNo(373L)
//                .skus(Lists.newArrayList("718033632580"))
//                .build());
//        System.out.println(JSON.toJSONString(listDubboResponse.getData()));

//        DubboResponse<AvailableInventory4ACResp> availableInventory4ACRespDubboResponse = wmsInventoryQueryProvider.queryAvailableInventory4AC(AvailableInventory4ACQueryReq.builder()
//                .warehouseNo(373L)
//                .sku("718033632580")
//                .build());
//        System.out.println(JSON.toJSONString(availableInventory4ACRespDubboResponse.getData()));

    }

    @Test
    public void changeNotice() {
        FulfillmentStockNoticeItemMessage item = new FulfillmentStockNoticeItemMessage();
        item.setSku("596400414682");
        item.setSkuName("测试");
        item.setQuantity(1);


        StockNoticeChangeMessage stockNoticeChangeMessage = new StockNoticeChangeMessage();
        stockNoticeChangeMessage.setGoodsSupplyNo("GSN2024010801");
        stockNoticeChangeMessage.setModifyType(1);
        stockNoticeChangeMessage.setAbnormalInBoundItemList(Lists.newArrayList(item));

        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId("msg2024010801Test");
        MessageExtUtil.setMessageExt(messageExt);
        stockTaskNoticeOrderChangeListener.process(stockNoticeChangeMessage);
    }

    @Test
    public void cancelNotice() {


        StockNoticeCancelMessage stockNoticeChangeMessage = new StockNoticeCancelMessage();
        stockNoticeChangeMessage.setGoodsSupplyNo("GSN2024010801");
        stockNoticeChangeMessage.setWarehouseNo(1L);


        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId("msg2024010801cancelTest");
        MessageExtUtil.setMessageExt(messageExt);
        stockTaskNoticeOrderCancelListener.process(stockNoticeChangeMessage);
    }

    @Resource
    private ShelvingMissionCommandService shelvingMissionCommandService;

    @Test
    public void mqOnceTest() {
//        String msg2024010801Test1 = redisUtil.get(RedisKeys.buildMqOnceLockKey("msg2024010801Test"));
//        System.out.println(msg2024010801Test1);
//        Boolean msg2024010801Test = redisUtil.setIfAbsent(RedisKeys.buildMqOnceLockKey("msg2024010801Test"),
//                Boolean.TRUE.toString(),3000L, TimeUnit.MILLISECONDS);
//        System.out.println(msg2024010801Test);
        redisUtil.del(RedisKeys.buildMqOnceLockKey("msg2024010801Test"));
    }

    @Test
    public void testNoticeCreate() {
        FulfillmentStockNoticeItemMessage item = new FulfillmentStockNoticeItemMessage();
        item.setSku("596400414682");
        item.setQuantity(10);
        item.setSkuName("测试");

        FulfillmentStockNoticeMessage fulfillmentStockNoticeMessage = new FulfillmentStockNoticeMessage();
        fulfillmentStockNoticeMessage.setWarehouseNo(1L);
        fulfillmentStockNoticeMessage.setAutoCreateStockTask(false);
        fulfillmentStockNoticeMessage.setExpectArriveTime(LocalDateTime.now());
        fulfillmentStockNoticeMessage.setGoodsSupplyNo("GSN2024010801");
        fulfillmentStockNoticeMessage.setOrderNo("T2024010801");
        fulfillmentStockNoticeMessage.setType(GoodsSupplyOrderEnum.NORMAL.getValue());
        fulfillmentStockNoticeMessage.setStoreNo(1L);
        fulfillmentStockNoticeMessage.setShopId(1L);
        fulfillmentStockNoticeMessage.setSupplyMode(0);
        fulfillmentStockNoticeMessage.setTenantId(1L);
        fulfillmentStockNoticeMessage.setItemList(Lists.newArrayList(item));
        stockTaskNoticeOrderOrderlyListener.process(fulfillmentStockNoticeMessage);
    }

    @Test
    public void testCreateStockTask() throws InterruptedException {
//        StockTaskCreateCommand stockTaskCommand = StockTaskCreateCommand.builder()
//                .noticeOrderIdList(Lists.newArrayList(9L, 13L))
//                .build();
//        Long stockTaskId = stockTaskCommandService.createStockTask(stockTaskCommand);
//        log.info("stockTaskId:{}", stockTaskId);
        Thread thread = new Thread(this::shelvingCommit);
        Thread thread1 = new Thread(this::shelvingCommit);
        Thread thread2 = new Thread(this::shelvingCommit);
        Thread thread3 = new Thread(this::shelvingCommit);
        Thread thread4 = new Thread(this::shelvingCommit);
        thread.start();
        thread1.start();
        thread2.start();
        thread3.start();
        thread4.start();
        Thread.sleep(10000L);
    }

    private void shelvingCommit() {
        shelvingMissionCommandService.commit(ShelvingMissionCommitCommand.builder()
                .warehouseNo(2L)
                .operatorName("戴浩冕")
                .operatorId(4581L)
                .commitInfo(Lists.newArrayList(
                        ShelvingCommitInfoReqDTO.builder()
                                .shelfLife(DateUtil.parseLocalDate("2024-12-23"))
                                .produceTime(DateUtil.parseLocalDate("2024-01-23"))
                                .dealNum(20)
                                .sku("592742817046")
                                .sourceContainerNo("SH20976")
                                .targetCabinetNo("A87-20-20-20")
                                .warehouseNo(2L)
                                .build()
                ))
                .missionNo("SJ20240123000011")
                .build());
    }

    @Test
    public void testOrderSelfStockTask() {
        List<OrderSelfStockTaskCommand.OrderSelfStockTaskDetail> detailList = Lists.newArrayList();
        detailList.add(OrderSelfStockTaskCommand.OrderSelfStockTaskDetail.builder()
                .sku("858022218175")
                .quantity(1).build());
        OrderSelfStockTaskCommand orderSelfStockTaskCommand = OrderSelfStockTaskCommand.builder()
                .orderNo("SXS001")
                .orderType(1)
                .deliveryTime(LocalDateTime.now())
                .tenantId(1L)
                .storeNo(1)
                .detailList(detailList)
                .build();
        stockTaskCommandService.orderSelfStockTask(orderSelfStockTaskCommand);
    }

    @Test
    public void testAutoOccupy() throws Exception {
        autoOccupyCabinetInventoryJob.processResult(null);
    }

    @Test
    public void testoccupyCabinetInventoryForNormalTask() throws Exception {
        Integer stockTaskId = 202145;
        cabinetOccupyCommandService.occupyCabinetInventoryForNormalTask(stockTaskId);
    }

    @Test
    public void testoccupyCabinetInventoryForAllocateTask() throws Exception {
        Integer stockTaskId = 203365;
        cabinetOccupyCommandService.occupyCabinetInventoryForAllocateTask(stockTaskId);
    }

    @Test
    public void testoccupyCabinetInventoryForPurchaseBackTask() throws Exception {
        Integer stockTaskId = 5748;
        cabinetOccupyCommandService.occupyCabinetInventoryForPurchaseBackTask(stockTaskId);
    }


    @Test
    public void testqueryStockTaskCabinetOccupy() throws Exception {
        Integer stockTaskId = 206993;
        StockTaskCabinetOccupyResp result = cabinetOccupyCommandService.queryStockTaskCabinetOccupy(stockTaskId);
        System.out.println("result: " + JSONObject.toJSONString(result));
    }

    @Test
    public void teststockTaskOccupyCabinetInventory() throws Exception {
        StockTaskCabinetOccupyCommand occupyCommand = new StockTaskCabinetOccupyCommand();
        occupyCommand.setWarehouseNo(1);
        occupyCommand.setStockTaskId(205647);
        occupyCommand.setStockTaskItemId(63248L);
        occupyCommand.setSku("283717641243");

        List<StockTaskCabinetOccupyCommand.SkuCabinetCommand> skuCabinetCommandList = new ArrayList<>();
        StockTaskCabinetOccupyCommand.SkuCabinetCommand skuCabinetCommand = new StockTaskCabinetOccupyCommand.SkuCabinetCommand();

//        StockTaskCabinetOccupyCommand.SkuCabinetInput current = new StockTaskCabinetOccupyCommand.SkuCabinetInput();
//        current.setCabinetCode("JH01");
//        current.setQualityDate(DateUtil.toLocalDate(DateUtil.parseStringToYmd("2023-09-22")));
//        current.setProductionDate(DateUtil.toLocalDate(DateUtil.parseStringToYmd("2023-06-14")));
//        current.setSkuProductRangeInputs(Arrays.asList(
//                StockTaskCabinetOccupyCommand.SkuProductRangeInput.builder()
//                        .produceDateGe(DateUtil.toDate(DateUtil.parseStringToYmd("2023-06-07")))
//                        .produceDateLe(DateUtil.toDate(DateUtil.parseStringToYmd("2023-06-30")))
//                        .build()
//        ));
////        current.setSkuBatchCode("20230225463191001");
//        current.setActualOutQuantity(1);
//        skuCabinetCommand.setCurrentSkuCabinetCommand(current);

        StockTaskCabinetOccupyCommand.SkuCabinetInput original = new StockTaskCabinetOccupyCommand.SkuCabinetInput();
        original.setCabinetCode("H99-01-02-01");
        original.setQualityDate(DateUtil.toLocalDate(DateUtil.parseStringToYmd("2024-05-27")));
        original.setProductionDate(DateUtil.toLocalDate(DateUtil.parseStringToYmd("2023-06-02")));
        original.setActualOutQuantity(1);
        skuCabinetCommand.setOriginalSkuCabinetCommand(original);
//
        skuCabinetCommandList.add(skuCabinetCommand);
        occupyCommand.setSkuCabinetCommandList(skuCabinetCommandList);
        System.out.println("req: " + JSONObject.toJSONString(occupyCommand));


        String a = "{\"stockTaskId\":206212,\"sku\":\"1029570672251\",\"warehouseNo\":1,\"skuCabinetCommandList\":[{\"currentSkuCabinetCommand\":{\"productionDate\":\"2023-06-19\",\"actualOutQuantity\":1,\"qualityDate\":\"2024-06-13\",\"cabinetCode\":\"B01-01-01-01\",\"skuProductRangeInputs\":[{\"produceDateLe\":\"2023-06-30\",\"produceDateGe\":\"2023-06-01\"}]}}],\"stockTaskItemId\":4405}";
        occupyCommand = JSONObject.parseObject(a, StockTaskCabinetOccupyCommand.class);
        Boolean result = cabinetOccupyCommandService.stockTaskOccupyCabinetInventory(occupyCommand);
        System.out.println("result: " + JSONObject.toJSONString(result));
    }


    @Test
    public void teststockTaskOccupyCabinetInventoryOneKey() throws Exception {
        StockTaskCabinetOccupyOneKeyCommand occupyCommand = new StockTaskCabinetOccupyOneKeyCommand();
        occupyCommand.setWarehouseNo(1);
        occupyCommand.setStockTaskId(205647L);
        occupyCommand.setStockTaskItemId(63250L);
        occupyCommand.setSku("974268224283");

        occupyCommand.setOccupyQuantity(1);
        occupyCommand.setPageIndex(1);
        occupyCommand.setPageSize(500);


        String a = "{\"pageIndex\":1,\"pageSize\":50,\"sku\":\"1003145218416\",\"warehouseNo\":2,\"stockTaskItemId\":5524,\"stockTaskId\":207699,\"occupyQuantity\":2,\"qualityDate\":\"2024-05-27\",\"productionDate\":\"2023-06-02\",\"skuBatchCode\":\"20230629462673004\"}";
        occupyCommand = JSONObject.parseObject(a, StockTaskCabinetOccupyOneKeyCommand.class);
        Boolean result = cabinetOccupyCommandService.stockTaskOccupyCabinetInventoryOneKey(occupyCommand);
        System.out.println("result: " + JSONObject.toJSONString(result));
    }
}
