package net.summerfarm.wms.api.h5.outstore.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * 拣货pda列表查询
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderPickingListPdaQuery implements Serializable {
    private static final long serialVersionUID = -2693093767110685929L;
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 城配仓
     */
    Long storeNo;

    /**
     * 城配仓
     */
    List<Long> storeNoList;

    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 分页条目
     */
    Integer pageIndex;

    /**
     * 分页数量
     */
    Integer pageSize;
}
