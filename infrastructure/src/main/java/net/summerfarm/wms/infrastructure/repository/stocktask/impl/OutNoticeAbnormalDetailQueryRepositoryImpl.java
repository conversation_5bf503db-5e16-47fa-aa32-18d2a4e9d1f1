package net.summerfarm.wms.infrastructure.repository.stocktask.impl;

import net.summerfarm.wms.domain.stocktask.OutNoticeAbnormalDetailQueryRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderAbnormalDetail;
import net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskNoticeOrderAbnormalDetailDAO;
import net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO;
import net.summerfarm.wms.infrastructure.repository.stocktask.converter.StockTaskNoticeOrderAbnormalDetailConvert;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class OutNoticeAbnormalDetailQueryRepositoryImpl implements OutNoticeAbnormalDetailQueryRepository {

    @Resource
    private StockTaskNoticeOrderAbnormalDetailDAO stockTaskNoticeOrderAbnormalDetailDAO;

    @Override
    public List<StockTaskNoticeOrderAbnormalDetail> queryNoticeOrderAbnormal(String goodsSupplyNo, List<String> sku) {
        if (StringUtils.isEmpty(goodsSupplyNo)) {
            throw new BizException("货品供应单不可为空");
        }
        List<StockTaskNoticeOrderAbnormalDetailDO> stockTaskNoticeOrderAbnormalDetailDOS =
                stockTaskNoticeOrderAbnormalDetailDAO.queryNoticeOrderAbnormal(goodsSupplyNo, sku);

        return stockTaskNoticeOrderAbnormalDetailDOS.stream()
                .map(StockTaskNoticeOrderAbnormalDetailConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    /**
     * 批量查询异常出库通知单明细
     * @param goodsSupplyOrderNoList 出库通知单列表
     * @return 返回异常出库通知单列表
     */
    @Override
    public List<StockTaskNoticeOrderAbnormalDetail> findByGoodsSupplyNoList(List<String> goodsSupplyOrderNoList) {
        if (CollectionUtils.isEmpty(goodsSupplyOrderNoList)) {
            return Lists.newArrayList();
        }
        List<StockTaskNoticeOrderAbnormalDetailDO> stockTaskNoticeOrderAbnormalDetailDOList =
                stockTaskNoticeOrderAbnormalDetailDAO.findByGoodsSupplyNoList(goodsSupplyOrderNoList);
        if (CollectionUtils.isEmpty(stockTaskNoticeOrderAbnormalDetailDOList)) {
            return Lists.newArrayList();
        }
        return stockTaskNoticeOrderAbnormalDetailDOList.stream()
                .map(StockTaskNoticeOrderAbnormalDetailConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<StockTaskNoticeOrderAbnormalDetail> findByIds(List<Long> abnormalIds) {
        if (CollectionUtils.isEmpty(abnormalIds)){
            return Lists.newArrayList();
        }
        List<StockTaskNoticeOrderAbnormalDetailDO> stockTaskNoticeOrderAbnormalDetailDOS = stockTaskNoticeOrderAbnormalDetailDAO.selectByIds(abnormalIds);
        return stockTaskNoticeOrderAbnormalDetailDOS.stream()
                .map(StockTaskNoticeOrderAbnormalDetailConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<StockTaskNoticeOrderAbnormalDetail> findByExceptTimeAndWarehouseStore(Integer warehouseNo, Integer storeNo, LocalDate expectTime, Integer type, Integer supplyMode, List<String> orderNos) {
        if (null == warehouseNo || null == storeNo || null == expectTime || null == type || null == supplyMode || CollectionUtils.isEmpty(orderNos)){
            return Lists.newArrayList();
        }
        List<StockTaskNoticeOrderAbnormalDetailDO> stockTaskNoticeOrderAbnormalDetailDOS = stockTaskNoticeOrderAbnormalDetailDAO.findByExceptTimeAndWarehouseStore(warehouseNo, storeNo, expectTime, type, supplyMode, orderNos);
        return stockTaskNoticeOrderAbnormalDetailDOS.stream()
                .map(StockTaskNoticeOrderAbnormalDetailConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<StockTaskNoticeOrderAbnormalDetail> findByStockTaskId(Long stockTaskId) {
        if (null == stockTaskId){
            return Lists.newArrayList();
        }
        List<StockTaskNoticeOrderAbnormalDetailDO> stockTaskNoticeOrderAbnormalDetailDOS = stockTaskNoticeOrderAbnormalDetailDAO.findByStockTaskId(stockTaskId);
        return stockTaskNoticeOrderAbnormalDetailDOS.stream()
                .map(StockTaskNoticeOrderAbnormalDetailConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

}
