package net.summerfarm.wms.infrastructure.repository.safeinventorylock.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockQueryRepository;
import net.summerfarm.wms.infrastructure.dao.safeinventorylock.WmsSafeInventoryLockMapper;
import net.summerfarm.wms.infrastructure.repository.safeinventorylock.converter.WmsSafeInventoryLockConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 安全库存锁定表查询仓储实现
 * <AUTHOR>
 * @date 2025-07-30
 */
@Repository
public class WmsSafeInventoryLockQueryRepositoryImpl implements WmsSafeInventoryLockQueryRepository {

    @Autowired
    private WmsSafeInventoryLockMapper wmsSafeInventoryLockMapper;

    @Override
    public PageInfo<WmsSafeInventoryLockEntity> getPage(WmsSafeInventoryLockQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WmsSafeInventoryLockEntity> entities = wmsSafeInventoryLockMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WmsSafeInventoryLockEntity selectById(Long id) {
        return WmsSafeInventoryLockConverter.toWmsSafeInventoryLockEntity(wmsSafeInventoryLockMapper.selectById(id));
    }

    @Override
    public List<WmsSafeInventoryLockEntity> selectByCondition(WmsSafeInventoryLockQueryParam param) {
        return wmsSafeInventoryLockMapper.selectByCondition(param);
    }

    @Override
    public WmsSafeInventoryLockEntity selectByLockNo(String lockNo) {
        return WmsSafeInventoryLockConverter.toWmsSafeInventoryLockEntity(wmsSafeInventoryLockMapper.selectByLockNo(lockNo));
    }

    @Override
    public List<WmsSafeInventoryLockEntity> selectByWarehouseNoAndSku(Integer warehouseNo, String sku) {
        return WmsSafeInventoryLockConverter.toWmsSafeInventoryLockEntityList(
                wmsSafeInventoryLockMapper.selectByWarehouseNoAndSku(warehouseNo, sku));
    }

    @Override
    public List<WmsSafeInventoryLockEntity> selectByWarehouseNoAndSkuAndLockStatus(Integer warehouseNo, String sku, Integer lockStatus) {
        return WmsSafeInventoryLockConverter.toWmsSafeInventoryLockEntityList(
                wmsSafeInventoryLockMapper.selectByWarehouseNoAndSkuAndLockStatus(warehouseNo, sku, lockStatus));
    }
}
