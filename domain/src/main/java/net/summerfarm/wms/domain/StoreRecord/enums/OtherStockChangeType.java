package net.summerfarm.wms.domain.StoreRecord.enums;

import net.summerfarm.wms.domain.areaStore.domainobject.StockChangeType;

/**
 * 除销售出库外的其他冻结库存类型
 */
public enum OtherStockChangeType implements StockChangeType {

    STOCK_ALLOCATION_AUDIT("库存调拨-审核通过"),
    STOCK_ALLOCATION_AUDIT_BALANCE("库存调拨-审核通过补差释放冻结"),
    STOCK_ALLOCATION_BALANCE("库存调拨-补差"),
    STOCK_ALLOCATION_DELIVERING("库存调拨-运输中"),
    STOCK_ALLOCATION_CANCEL("库存调拨-不调拨"),
    OPEN_SYNC("同步开关开启"),
    TRANSFORM_OUT("转换出库"),
    TRANSFORM_IN("转换入库"),
    MANUALLY_ADJUSTMENT("后台调整"),
    STOCK_TAKING("库存盘点"),
    TO_ZERO("采购单作废"),
    SAFE_STOCK_CHANGE("安全库存修改"),
    ONLINE_STOCK_CHANGE("虚拟库存修改"),
    //******************************************************//
//    STORE_ALLOCATION_IN( "调拨入库"),
    PURCHASE_IN("采购单入库"),
    PURCHASE_ION("采购单采购"),
    PURCHASE_NOT_IN("采购单拒收"),
    AFTER_SALE_IN("退货入库"),
    RETURN_IN("拒收入库"),
    LACK_GOODS_APPROVED("缺货入库"),
    //    RETURN_IN("拒收回库"),
    STORE_ALLOCATION_NOT_IN("调拨未收入库（实收+拒收小于实发的情况"),
    ALLOCATION_DAMAGE_OUT_TASK("调拨货损"),
    ALLOCATION_END_IN("调拨回库"),
    //    STOCK_TAKING_IN("盘盈入库"),
//    TRANSFER_IN("转换入库"),
//
//    STORE_ALLOCATION_OUT( "调拨出库"),
//    SALE_OUT("销售出库"),
    DEMO_OUT("出样出库"),
    DAMAGE_OUT("货损出库"),
    DAMAGE_AUDIT_FAIL("货损审核失败"),
    //    STOCK_TAKING_OUT( "盘亏出库"),
//    TRANSFER_OUT("转换出库");
    DEMO_SAVE("出样申请"),
    RECOVER_OUT("补货出库"),
    DEMO_CANCEL("出样取消"),
    PURCHASES_BACK_SAVE("生成采购退货"),
    PURCHASES_BACK_UPDATE("修改采购退货"),
    PURCHASES_BACK_OUT("采购退货出库"),
    PURCHASES_BACK_FINISH("采购退货出库补差"),
    PURCHASES_BACK_UN_STORE("未入库采购退货"),
    AREA_CHANGE_STORE("城市仓切换"),
    CANCEL_AFTER_ORDER("取消售后单"),
    PURCHASES_ADVANCE("预售数量变动"),
    VERIFICATION_ADVANCE("核销预售库存"),
    STOCK_TAKING_OUT("盘亏出库"),
    STOCK_ALLOCATION_LOCK("调拨备货冻结"),
    REVISION_ROAD_QUANTITY("系统调整在途库存"),

    PROCESSING_TASK_MATERIAL_REDUCE("加工出库"),
    PROCESSING_TASK_MATERIAL_INCREASE("加工回库"),
    PROCESSING_TASK_PRODUCT_INCREASE("加工入库"),


    CABINET_INVENTORY_SWITCH_OPEN("库位库存开关开启"),

    CABINET_INVENTORY_SWITCH_CLOSE("库位库存开关关闭"),

    OTHER_OUT("其他出库"),

    DEMO_SELF_OUT("自提出样出库"),

    SUPPLIER_ONLINE_CHANGE("供应商调整"),
    SUPPLIER_ONLINE_LOCK("供应商调整锁定"),
    SUPPLIER_ONLINE_UNLOCK("供应商调整释放"),

    OFC_PURCHASE_LOCK("履约转采-客户仅退冻结"),
    OFC_PURCHASE_RELEASE("履约转采-客户仅退释放冻结"),
    OFC_PURCHASE_REDUCE("履约转采-客户仅退扣减冻结"),


    MATERIAL_TASK_RECEIVE("物料领用"),
    MATERIAL_TASK_RESTORE("物料归还"),
    ;


    private final String typeName;

    OtherStockChangeType(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String getTypeName() {
        return typeName;
    }
}
