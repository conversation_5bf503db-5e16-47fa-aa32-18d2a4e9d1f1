package net.summerfarm.wms.api.h5.stocktask.dto.req;

import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/28
 */
@Data
public class StockTaskPickSkuDTO implements Serializable {

    private static final long serialVersionUID = 7726909854196011438L;

    /**
     * 出库任务id
     */
    private Long stockTaskId;

    /**
     * sku
     */
    private String sku;

    /**
     * 应出总数
     */
    private Integer quantity;

    /**
     * 生成拣货sku明细
     */
    @Valid
    private List<StockTaskPickSkuDetailDTO> stockTaskPickSkuDetails;
}
