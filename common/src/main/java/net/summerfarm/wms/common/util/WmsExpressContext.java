package net.summerfarm.wms.common.util;

import com.ql.util.express.IExpressContext;
import net.summerfarm.common.exceptions.DefaultServiceException;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

/**
 * wms express上下文
 *
 * <AUTHOR>
 */
public class WmsExpressContext extends HashMap<String, Object> implements IExpressContext<String, Object> {
    private ApplicationContext applicationContext;

    public WmsExpressContext(ApplicationContext context, Map<String, Object> ap) {
        super(ap);
        applicationContext = context;
    }

    /**
     * 根据key从容器里面获取对象
     *
     * @param key k
     * @return o
     */
    @Override
    public Object get(Object key) {
        Object object = super.get(key);
        try {
            if (object == null && this.applicationContext != null
                    && this.applicationContext.containsBean((String) key)) {
                object = this.applicationContext.getBean((String) key);
            }
        } catch (Exception e) {
            throw new DefaultServiceException("表达式容器获取对象失败", e);
        }
        return object;
    }

    /**
     * 把key-value放到容器里面去
     *
     * @param key   k
     * @param value v
     */
    @Override
    public Object put(String key, Object value) {
        return super.put(key, value);
    }
}
