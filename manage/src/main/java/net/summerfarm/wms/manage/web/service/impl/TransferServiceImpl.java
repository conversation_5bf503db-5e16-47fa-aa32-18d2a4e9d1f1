package net.summerfarm.wms.manage.web.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.enums.OpenSaleEnum;
import net.summerfarm.tms.util.DateUtils;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.initconfig.OnlineAutoTransferConfig;
import net.summerfarm.wms.domain.stockTransfer.StockTransferQueryRepository;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.manage.common.util.CommonFileUtils;
import net.summerfarm.wms.manage.dao.stockTransfer.StockTransferItemV1DAO;
import net.summerfarm.wms.manage.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.wms.manage.model.domain.*;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferCreateBO;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferFinishBO;
import net.summerfarm.wms.manage.model.domain.bo.StockTransferItemOpBO;
import net.summerfarm.wms.manage.model.domain.bo.TransferInInfoBO;
import net.summerfarm.wms.manage.model.dto.CabinetInventoryRecommendDTO;
import net.summerfarm.wms.manage.model.dto.CabinetInventoryRecommendOutFacadeRespDTO;
import net.summerfarm.wms.manage.model.dto.MailWorkBookDTO;
import net.summerfarm.wms.manage.model.dto.StockTaskPickDetailDTO;
import net.summerfarm.wms.manage.model.req.CabinetInventorySingleRecommendOutReqFacadeDTO;
import net.summerfarm.wms.manage.model.vo.StoreRecordVO;
import net.summerfarm.wms.manage.web.constants.TransferExportConstant;
import net.summerfarm.wms.manage.web.convert.CabinetInventoryRecommendConverter;
import net.summerfarm.wms.manage.web.enums.*;
import net.summerfarm.wms.manage.web.facade.CabinetBatchInventoryFacade;
import net.summerfarm.wms.manage.web.mapper.manage.*;
import net.summerfarm.wms.manage.web.resp.StockOutTaskPrintResp;
import net.summerfarm.wms.manage.web.resp.StockTaskDetailOutStatusResp;
import net.summerfarm.wms.manage.web.resp.StockTaskResult;
import net.summerfarm.wms.manage.web.service.*;
import net.summerfarm.wms.manage.web.utils.RedissonLockUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.retry.core.annotation.XmRetryAnnotation;
import net.xianmu.retry.core.annotation.XmRetryBackOffPolicy;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_UP;

/**
 * @author: dongcheng
 * @date: 2023/11/17
 */
@Service
@Slf4j
public class TransferServiceImpl implements TransferService, StockTaskStrategy {

    @Resource
    private StockTransferQueryRepository stockTransferQueryRepository;
    @Resource
    private StockTransferBizService stockTransferBizService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private ConversionSkuQuantityMapper conversionSkuQuantityMapper;
    @Resource
    private ConversionSkuConfigMapper conversionSkuConfigMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private StockTransferItemV1DAO stockTransferItemV1DAO;
    @Resource
    private GoodsLocationDetailService goodsLocationDetailService;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private CabinetBatchInventoryFacade cabinetBatchInventoryFacade;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private OnlineAutoTransferConfig onlineAutoTransferConfig;

    /**
     * 自动生成转换任务表格信息
     */
    @Override
    public void autoCreateTransferDate() {
        log.info("自动推送转换任务信息任务开始执行...");
        //获取所有的库存仓
        //遍历库存仓获取今天转换的信息
        List<StockTransferEntity> stockTransferDos = stockTransferQueryRepository.selectByCreatedAt(LocalDate.now(), TransferExportConstant.UNPACKING);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(stockTransferDos)) {
            log.info("无可发送的转换信息,date:{},自动推送转换任务信息任务执行结束...", LocalDate.now());
            return;
        }
        Set<Long> warehouseNoList = stockTransferDos.stream().map(StockTransferEntity::getWarehouseNo).collect(Collectors.toSet());
        Map<Long, List<StockTransferEntity>> transferMap = stockTransferDos.stream().collect(Collectors.groupingBy(StockTransferEntity::getWarehouseNo));

        //文件名信息
        List<String> filenameList = new ArrayList<>();
        Map<String, String> fileNameMap = Maps.newHashMap();
        for (Long warehouseNo : warehouseNoList) {
            List<StockTransferEntity> transferList = transferMap.get(warehouseNo);
            if (CollectionUtils.isEmpty(transferList)) {
                continue;
            }
            Workbook workbook = stockTransferBizService.excelTransferPool(warehouseNo,
                    transferList.stream().map(StockTransferEntity::getId).collect(Collectors.toList()));
            //生成导入报告
            String warehouseName = Global.warehouseMap.get(warehouseNo);
            String time = DateUtil.localDateToString(LocalDate.now(), null);
            String fileNameFormat = "%s,%s线下拆包数据汇总.xls";
            String fileName = String.format(fileNameFormat, time, warehouseName);
            //上传模版
            // CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
            File file = null;
            FileOutputStream out = null;
            try {
                file = new File(fileName);
                out = new FileOutputStream(file);
                // excel输出到文件
                workbook.write(out);
                OssUploadResult uploadResult = OssUploadUtil.uploadExpireThreeDay(fileName, file);
                fileNameMap.put(fileName, uploadResult.getUrl());
            } catch (Throwable e) {
                log.error("startUpload.异步上传外部存储异常,自动推送转换任务信息任务执行结束...", e);
            }
            filenameList.add(fileName);
        }

        try {
            Config config = configMapper.selectOne("un_parking_url");
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT));
            StringBuffer buffer = new StringBuffer();
            for (String fileName : filenameList) {
                // String url = "https://admin." + Global.TOP_DOMAIN_NAME + "/stock-task/transfer/download?fileName=" + fileName;
                String url = fileNameMap.get(fileName);
                buffer.append("\n").append("[")
                        .append(fileName).append("]").append("(")
                        .append(url).append(")");
            }
            if (config != null && !CollectionUtils.isEmpty(filenameList)) {
                // 飞书通知
                feishuNoticeTransferData(config.getValue(), date, buffer);
                log.info("自动推送转换任务信息任务执行结束...");
            } else {
                log.info("钉钉推送失败,配置为空,自动推送转换任务信息任务执行结束...");
            }
        } catch (Exception e) {
            log.error("钉钉消息推送失败 e={},自动推送转换任务信息任务执行结束...", e);
        }
    }

    @XmRetryAnnotation(maxRetryTimes = 3, backOffPolicy = XmRetryBackOffPolicy.EXPONENTIAL_RANDOM)
    public void feishuNoticeTransferData(String url, String date, StringBuffer buffer) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url) || org.apache.commons.lang3.StringUtils.isBlank(date) || null == buffer) {
            return;
        }
        String title = "当日自动生成转换任务汇总";
        String text = title + "\n" + "时间：" + date + "\n" + "内容：" + buffer + "\n线下请及时操作";
        CommonResult<Boolean> result = FeishuBotUtil.sendMarkdownMsgAndAtAll(url, text);
        if (!ResultStatusEnum.OK.getStatus().equals(result.getStatus())) {
            throw new RuntimeException("发送飞书消息失败");
        }
    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        return null;
    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result, String sku, Long pdId) {
        return null;
    }


    @Override
    public List<StockTaskPickDetailDTO> stockTaskDetailListByTaskIdList(List<StockTask> stockTaskList) {
        return null;
    }

    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {

    }

    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        return null;
    }

    @Override
    public AjaxResult inOutStore(Integer type, String data) {
        return null;
    }

    @Override
    public void closeTask(StockTask stockTask) {

    }

    @Override
    public StockOutTaskPrintResp printTaskDetail(StockTask stockTask) {
        return null;
    }

    @Override
    public StockTaskDetailOutStatusResp outStatusQuery(StockTask stockTask) {
        return null;
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public void inSkuAutoTransferTask() {
        ConversionSkuConfig skuConfig = new ConversionSkuConfig();
        skuConfig.setStatus(0);
        List<ConversionSkuConfig> skuConfigList = conversionSkuConfigMapper.selectConfig(skuConfig);
        if (CollectionUtils.isEmpty(skuConfigList)) {
            log.info("当前无生效状态的sku转换配置");
            return;
        }
        skuConfigList.forEach(config -> {
            String inSku = config.getInSku();
            Integer warehouseNo = config.getWarehouseNo();
            ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity();
            skuQuantity.setSku(inSku);
            skuQuantity.setWarehouseNo(warehouseNo);
            skuQuantity.setDate(LocalDate.now().minusDays(1));
            ConversionSkuQuantity result = conversionSkuQuantityMapper.selectDetail(skuQuantity);
            if (Objects.isNull(result)) {
                log.warn("未查询到sku销量统计数据:{}", JSONUtil.toJsonStr(skuQuantity));
                return;
            }
            // 无销量峰值或者为0
            BigDecimal maxSaleSeven = result.getMaxSaleSeven();
            if (Objects.isNull(maxSaleSeven)) {
                log.info("sku近七日无销量数据，{}", JSONUtil.toJsonStr(skuQuantity));
                return;
            }
            // 仓库库存信息
            AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSku(warehouseNo, inSku);
            if (Objects.isNull(areaStore)) {
                log.warn("未查询到仓库库存信息 warehouseNo:{}, inSku:{}", warehouseNo, inSku);
                return;
            }
            // 虚拟库存校验
            if (areaStore.getOnlineQuantity() > maxSaleSeven.divide(BigDecimal.valueOf(2), ROUND_UP)
                    .setScale(0, ROUND_UP).intValue()) {
                log.info("虚拟库存量大于等于近七日峰值一半 不触发拆包 areaStore：{}, maxSaleSeven:{}",
                        JSONUtil.toJsonStr(areaStore), maxSaleSeven);
                return;
            }
            try {
                this.autoTransferTask(warehouseNo, inSku, LocalDateTime.now(), config.getId());
            } catch (Exception e) {
                log.warn("自动拆包流程结束 原因:{}, areaNo:{}, sku:{}, configId:{}", e.getMessage(), warehouseNo, inSku, config.getId());
            }
        });


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoTransferTask(Integer storeNo, String sku, LocalDateTime outTime, Integer configId) throws Exception {
        log.info("自动转换执行，仓：{}，转入sku：{}，时间：{}，配置id：{}", storeNo, sku, outTime, configId);
        String key = RedisKeyEnum.AUTO_TRANSFER_TASK.getKey(storeNo, sku, configId);
        RLock lock = RedissonLockUtil.lock(key, 10);
        try {
            this.handleTransferTask(storeNo, sku, outTime, configId);
        } catch (Exception e) {
            log.warn("handleTransferTask fail request: store:{}, sku:{}, outTime:{}, configId:{}"
                    , storeNo, sku, outTime, configId, e);
            throw e;
        } finally {
            lock.unlock();
        }

    }

    public void handleTransferTask(Integer storeNo, String sku, LocalDateTime outTime, Integer configId) {
        log.info("自动触发转换任务,warehouseNo:{},转入sku:{},outTime:{},configId:{}", storeNo, sku, outTime, configId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(configId), "configId为空");
        ExceptionUtil.checkAndThrow(this.checkValidTime(), "当前时间不在自动转换生效范围内");
        ExceptionUtil.checkAndThrow(this.checkInSkuStatus(storeNo, sku), "sku非(可)上架状态");
        ConversionSkuConfig skuConfig = conversionSkuConfigMapper.selectConfigDetail(configId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuConfig), "未查询到相关sku转换配置信息");
        // sku为非鲜果类型
        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(storeNo);
        List<GoodsInfoDTO> goodsInfoDTOS = goodsReadFacade.listGoodsInfoBySkus(center.getWarehouseNo().longValue(), Lists.newArrayList(sku), null, null);
        if (CollectionUtils.isEmpty(goodsInfoDTOS)) {
            throw new BizException("sku:" + sku + "无货品信息");
        }
        GoodsInfoDTO goodsInfoDTO = goodsInfoDTOS.get(NumberUtils.INTEGER_ZERO);
        if (Objects.isNull(goodsInfoDTO) ||
                Objects.equals(CategoryTypeEnum.FRUIT.getType(), goodsInfoDTO.getCategoryType())) {
            log.info("sku为鲜果类型，不执行自动转换任务 sku:{}", sku);
            return;
        }
        //默认转换维度为库存转换 ，获取转入sku的销量信息
        Integer transferQuantity = this.calcInSkuTransferQuantity(skuConfig);
        log.info("获取到需转出数量：{}", transferQuantity);
        if (transferQuantity <= 0 && Objects.nonNull(skuConfig.getRates())) {
            ExceptionUtil.wrapAndThrow("已配置转换比例，可转换数量为0，不进行拆包操作");
        }
        Long stockTransferId = this.initStockTransferTask(storeNo, sku, transferQuantity);
        if (AreaTypeEnum.INTERNAL_AREA.getType().equals(center.getType())) {
            //触发自动完成转换逻辑 存在比例
            if (StringUtils.isNotBlank(skuConfig.getRates())) {
                StockTransferItemOpBO opBO = buildOpBO(skuConfig, transferQuantity, stockTransferId);
                if (CollectionUtils.isEmpty(opBO.getTransferOutInfos())) {
                    log.warn("无可转换批次,storeNo：{}, sku：{}", storeNo, sku);
                    stockTransferBizService.finishStockTransferTask(StockTransferFinishBO.builder()
                            .stockTransferId(stockTransferId).build());
                    return;
                }
                stockTransferBizService.operateStockTransferItem(opBO);
                stockTransferBizService.finishStockTransferTask(StockTransferFinishBO.builder()
                        .stockTransferId(stockTransferId).build());

                Workbook workbook = stockTransferBizService.excelTransfer(stockTransferId);
                try {
                    //生成导入报告/
                    String warehouseName = Objects.isNull(storeNo) ? "" : Global.warehouseMap.get(Long.valueOf(storeNo));
                    String time = DateUtil.localDateToString(LocalDate.now(), null);
                    String fileNameFormat = "%s,%s,%s需要线下拆包数据.xls";
                    String fileName = String.format(fileNameFormat, time, warehouseName, stockTransferId);
                    //上传模版
                    CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
                    //发送钉钉消息提醒
                    Config config = configMapper.selectOne("un_parking_url");
                    String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT));
                    if (config != null) {
                        long outNum = opBO.getTransferOutInfos().stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
                        //钉钉推送链接
                        String url = "https://admin." + Global.TOP_DOMAIN_NAME + "/summerfarm-wms/stock-task/transfer/download?fileName=" + fileName;
                        DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> {
                            Map<String, String> md = new HashMap<>();
                            String title = center.getWarehouseName() + "生成转换任务";
                            md.put("title", title);
                            md.put("text", "#### " + title + ":" + stockTransferId + "\n> ###### " +
                                    "时间：" + date + "\n> ###### " + "内容：" + goodsInfoDTO.getTitle() + "自动完成拆包转换" + "-" + goodsInfoDTO.getSpecification() + "转换数量为" + outNum
                                    + "\n> ###### " + "  线下请及时操作" +
                                    "\n> ###### " + "[" + fileName + "]" + "(" + url + ")");
                            return md;
                        }, null);
                    } else {
                        log.info("钉钉推送失败，配置为空");
                    }
                } catch (Exception e) {
                    log.error("生成文件推送失败 e={}", e.getMessage());
                }
            }
        }
    }

    private boolean checkValidTime() {
        String holdTime = onlineAutoTransferConfig.getHoldTime();
        log.info("配置自动转换限制时间：{}", holdTime);
        if (!org.apache.commons.lang3.StringUtils.isBlank(holdTime)) {
            boolean result = DateUtil.isAfter(new Date(), holdTime);
            log.info("配置自动转换限制结果：{}", result);
            return result;
        }
        return LocalDateTime.now().getHour() * 60 + LocalDateTime.now().getMinute() < 17 * 60 + 30;
    }

    /**
     * 校验转入sku状态
     *
     * @param storeNo 库存仓
     * @param sku     sku编码
     * <AUTHOR>
     * @Date 2022/12/2 14:43
     **/
    private boolean checkInSkuStatus(Integer storeNo, String sku) {
        // 获取城市编号areaNo,一个库存仓会对应多个运营区域？
        Set<Integer> areaNoSet = fenceService.selectAreaNosByWareNos(storeNo, sku);
        if (CollectionUtils.isEmpty(areaNoSet)) {
            log.warn("未查询到库存仓对应的运营区域 warehouseNo:{}, sku:{}", storeNo, sku);
            return false;
        }
        List<AreaSku> areaSkuList = areaSkuMapper.selectBySkuAndAreaNos(sku, areaNoSet);
        if (CollectionUtils.isEmpty(areaSkuList)) {
            return false;
        }
        List<Boolean> onSaleList = areaSkuList.stream().map(AreaSku::getOnSale).collect(Collectors.toList());
        List<Integer> openSaleList = areaSkuList.stream().map(AreaSku::getOpenSale).collect(Collectors.toList());
        if (onSaleList.contains(Boolean.TRUE)) {
            return true;
        }
        if (openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON.ordinal()) ||
                openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON_FOREVER.ordinal())) {
            return true;
        }
        return false;
    }

    /**
     * 计算转入sku数量
     *
     * @param skuConfig sku转换配置
     * <AUTHOR>
     * @Date 2022/12/5 11:52
     **/
    private Integer calcInSkuTransferQuantity(ConversionSkuConfig skuConfig) {
        log.info("计算转入数量配置：{}", JSON.toJSONString(skuConfig));
        String inSku = skuConfig.getInSku();
        String outSku = skuConfig.getOutSku();
        Integer warehouseNo = skuConfig.getWarehouseNo();
        // 校验转出商品库存信息
        AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSku(warehouseNo, outSku);
        ExceptionUtil.checkAndThrow(Objects.nonNull(areaStore), String.format("未查询到该sku库存信息 sku:%s, warehouseNo:%s", outSku, warehouseNo));
        ExceptionUtil.checkAndThrow(areaStore.getQuantity() > 0, String.format("该sku无库存信息 sku:%s, warehouseNo:%s", outSku, warehouseNo));
        Integer useQuantity = areaStore.getQuantity() - areaStore.getLockQuantity() - areaStore.getSafeQuantity();
        // 查询sku销量信息
        ConversionSkuQuantity param = new ConversionSkuQuantity();
        param.setSku(inSku);
        param.setDate(LocalDate.now().minusDays(1));
        param.setWarehouseNo(warehouseNo);
        ConversionSkuQuantity skuQuantity = conversionSkuQuantityMapper.selectDetail(param);
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuQuantity), "未查询到昨日库存销量信息");
        BigDecimal maxSaleSeven = skuQuantity.getMaxSaleSeven();
        log.info("仓库：{}，转入sku：{}，获取到近7天最大销售量：{}", warehouseNo, inSku, maxSaleSeven);
        // 校验大规格库存是否满足拆包
        if (Objects.isNull(skuConfig.getRates())) {
            return Objects.isNull(maxSaleSeven) ? 0 : maxSaleSeven.setScale(0, ROUND_UP).intValue();
        }
        // 1:12
        String[] split = skuConfig.getRates().split(":");
        // 转入比例
        Integer rate = new BigDecimal(split[1]).divide(new BigDecimal(split[0])).intValue();
        // 计算需要转入数量
        return calcTransferNeedInSkuNum(skuQuantity, useQuantity, rate);
    }

    public Long initStockTransferTask(Integer storeNo, String sku, Integer transferQuantity) {
        return stockTransferBizService.createStockTransferTask(StockTransferCreateBO.builder()
                .warehouseNo(storeNo.longValue())
                .transferDimension(TransferDimensionEnum.PEER_MOVE.getCode())
                .transferInInfos(Lists.newArrayList(TransferInInfoBO.builder()
                        .transferInNum(transferQuantity.longValue())
                        .transferInSku(sku)
                        .build()))
                .remark(TransferExportConstant.UNPACKING)
                .taskSource(StockTransferTaskSourceEnum.AUTO.getCode())
                .build());
    }

    private StockTransferItemOpBO buildOpBO(ConversionSkuConfig skuConfig, Integer transferQuantity, Long stockTransferId) {
        Boolean open = warehouseConfigService.openCabinetManagement(skuConfig.getWarehouseNo());
        List<TransferOutInfo> outInfos = Lists.newArrayList();
        List<GoodsLocationInfo> glInfos = Lists.newArrayList();
        Integer warehouseNo = skuConfig.getWarehouseNo();
        String rates = skuConfig.getRates();
        String outSku = skuConfig.getOutSku();
        Integer rate = Integer.valueOf(rates.split(":")[1]);
        boolean checkoutGoodsLocation = goodsLocationDetailService.checkoutGoodsLocation(warehouseNo);
        //货位批次信息
        StoreRecordVO storeRecordVO = new StoreRecordVO();
        storeRecordVO.setSku(outSku);
        storeRecordVO.setAreaNo(warehouseNo);
        storeRecordVO.setStoreQuantityMin(1);
        List<StoreRecordVO> recordList;
        //获取可以转换的批次信息
        if (checkoutGoodsLocation) {
            recordList = storeRecordMapper.selectLastJX(storeRecordVO);
        } else {
            recordList = storeRecordMapper.selectLastedVO(storeRecordVO);
        }
        log.info("仓库：{}，转出sku：{}，获取批次库存数据：{}", warehouseNo, outSku, JSON.toJSONString(recordList));
        //转出sku转出数量
        Integer outQuantity = BigDecimal.valueOf(transferQuantity)
                .divide(BigDecimal.valueOf(rate), ROUND_UP).setScale(0, RoundingMode.UP).intValue();
        log.info("转出sku：{}，计算转出数量：{}", outSku, outQuantity);
        Boolean rollback = true;

        // 库位推荐明细
        Map<String, List<CabinetInventoryRecommendDTO>> cabinetInventoryRecommendMap = new HashMap<>();
        if (Boolean.TRUE.equals(open)) {
            List<CabinetInventoryRecommendDTO> allCabinetInventoryRecommendList = new ArrayList<>();
            for (StoreRecordVO recordVO : recordList) {
                PageInfo<CabinetInventoryRecommendOutFacadeRespDTO> page = cabinetBatchInventoryFacade.queryRecommendOut(CabinetInventorySingleRecommendOutReqFacadeDTO.builder()
                        .pageIndex(1)
                        .pageSize(50)
                        .warehouseNo(warehouseNo)
                        .skuCode(recordVO.getSku())
                        .skuBatchCode(recordVO.getBatch())
                        .qualityDate(DateUtil.toDate(recordVO.getQualityDate()))
                        .produceDate(DateUtil.toDate(recordVO.getProductionDate()))
                        .build());
                List<CabinetInventoryRecommendOutFacadeRespDTO> list = page.getList();
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
                    log.error("自动转换仓库:{},sku:{}批次:{},生产日期:{},保质期:{}查询不到库位库存", warehouseNo, recordVO.getSku(), recordVO.getBatch(), recordVO.getProductionDate(), recordVO.getQualityDate());
                    continue;
                }
                for (CabinetInventoryRecommendOutFacadeRespDTO dto : list) {
                    CabinetInventoryRecommendDTO cabinetInventoryRecommendDTO = CabinetInventoryRecommendConverter.INSTANCE.convert(dto);
                    cabinetInventoryRecommendDTO.setBatchNo(recordVO.getBatch());
                    allCabinetInventoryRecommendList.add(cabinetInventoryRecommendDTO);
                }
            }
            log.info("获取库位库存：{}", JSON.toJSONStringWithDateFormat(allCabinetInventoryRecommendList, DateUtil.YYYY_MM_DD));
            cabinetInventoryRecommendMap = allCabinetInventoryRecommendList.stream().distinct().collect(Collectors.groupingBy(item -> getGroupKey(item.getSkuCode(), item.getProduceDate(), item.getQualityDate())));
        }

        for (StoreRecordVO recordVO : recordList) {
            Integer storeQuantity = recordVO.getStoreQuantity();
            if (transferQuantity <= 0) {
                break;
            }
            Integer newTransferQuantity = outQuantity - storeQuantity >= 0 ? storeQuantity : outQuantity;
            if (newTransferQuantity <= 0) {
                continue;
            }
            // 开启库位管理
            if (Boolean.TRUE.equals(open)) {
                List<CabinetInventoryRecommendDTO> cabinetInventoryRecommendDTOList = cabinetInventoryRecommendMap.get(getGroupKey(recordVO.getSku(), DateUtil.toDate(recordVO.getProductionDate()), DateUtil.toDate(recordVO.getQualityDate())));
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(cabinetInventoryRecommendDTOList)) {
                    log.error("自动转换仓库:{},sku:{}批次:{},生产日期:{},保质期:{}查询不到库位库存", warehouseNo, recordVO.getSku(), recordVO.getBatch(), recordVO.getProductionDate(), recordVO.getQualityDate());
                    continue;
                }
                // 排序
                List<CabinetInventoryRecommendDTO> sortedCabinetInventoryRecommendList = cabinetInventoryRecommendDTOList.stream().sorted(Comparator.comparing(CabinetInventoryRecommendDTO::getQualityDate).thenComparing(CabinetInventoryRecommendDTO::getCabinetPurpose).thenComparing(CabinetInventoryRecommendDTO::getCabinetCode)).collect(Collectors.toList());
                log.info("排序库位库存：{}", JSON.toJSONStringWithDateFormat(sortedCabinetInventoryRecommendList, DateUtil.YYYY_MM_DD));
                Long exeQuantity = newTransferQuantity.longValue();
                log.info("待执行数量：{}", exeQuantity);
                for (CabinetInventoryRecommendDTO cabinetInventoryRecommendDTO : sortedCabinetInventoryRecommendList) {
                    if (exeQuantity == 0) {
                        break;
                    }
                    long diffQuantity = exeQuantity - cabinetInventoryRecommendDTO.getAvailableQuantity().longValue() >= 0 ? cabinetInventoryRecommendDTO.getAvailableQuantity().longValue() : exeQuantity;
                    TransferOutInfo outInfo = TransferOutInfo.builder()
                            .transferOutNum(diffQuantity)
                            .transferOutBatch(cabinetInventoryRecommendDTO.getBatchNo())
                            .produceTime(DateUtil.toMill(cabinetInventoryRecommendDTO.getProduceDate()))
                            .shelfLife(DateUtil.toMill(cabinetInventoryRecommendDTO.getQualityDate()))
                            .transferOutSku(recordVO.getSku())
                            .transferOutCabinetNo(cabinetInventoryRecommendDTO.getCabinetCode())
                            .build();
                    outInfos.add(outInfo);
                    exeQuantity -= diffQuantity;
                }
                rollback = false;
            } else {
                TransferOutInfo outInfo = TransferOutInfo.builder()
                        .transferOutNum(newTransferQuantity.longValue())
                        .transferOutBatch(recordVO.getBatch())
                        .produceTime(DateUtil.toMill(recordVO.getProductionDate()))
                        .shelfLife(DateUtil.toMill(recordVO.getQualityDate()))
                        .transferOutSku(recordVO.getSku())
                        .build();
                outInfos.add(outInfo);
                rollback = false;
            }
            outQuantity = outQuantity - newTransferQuantity;
            GoodsLocationInfo glInfo = GoodsLocationInfo.builder()
                    .batch(recordVO.getBatch())
                    .sku(recordVO.getSku())
                    .produceTime(DateUtil.toMill(recordVO.getProductionDate()))
                    .shelfLife(DateUtil.toMill(recordVO.getQualityDate()))
                    .inGlNo(recordVO.getGlNo())
                    .outGlNo(recordVO.getGlNo())
                    .build();
            glInfos.add(glInfo);
            log.info("组装后转换数据：{}", JSON.toJSONString(outInfos));
        }
        if (Boolean.TRUE.equals(rollback)) {
            throw new BizException("无可转换库存数据");
        }
        List<StockTransferItemDO> stockTransferItemDOS = stockTransferItemV1DAO.listByStockTransferId(stockTransferId);
        return StockTransferItemOpBO.builder()
                .stockTransferItemId(stockTransferItemDOS.get(NumberUtils.INTEGER_ZERO).getId())
                .transferInSku(skuConfig.getInSku())
                .stockTransferId(stockTransferId)
                .transferOutInfos(outInfos)
                .warehouseNo(warehouseNo.longValue())
                .transferOutSku(skuConfig.getOutSku())
                .transferRatio(skuConfig.getRates())
                .glInfos(glInfos)
                .type(TransferOpEnum.ONE_WAY.getCode())
                .build();
    }

    private String getGroupKey(String sku, Date produceTime, Date shelfLife) {
        return sku + "_"  + produceTime + "_" + shelfLife;
    }

    /**
     * 计算需要转入数量
     * @param conversionSkuQuantity
     * @param useQuantity
     * @param rate
     * @return
     */
    private int calcTransferNeedInSkuNum(ConversionSkuQuantity conversionSkuQuantity, int useQuantity, Integer rate) {
        if (null == rate) {
            return 0;
        }
        BigDecimal maxSaleSeven = conversionSkuQuantity.getMaxSaleSeven();
        // 近七天有销量
        if (null != maxSaleSeven && maxSaleSeven.compareTo(BigDecimal.ZERO) > 0) {
            // 计算需要转出数量
            int needOutSkuNum = calcTransferNeedOutSkuNum(maxSaleSeven, conversionSkuQuantity.getSellOutNumOne(), conversionSkuQuantity.getSellOutNumSeven(), rate);
            return useQuantity > needOutSkuNum ? needOutSkuNum * rate : useQuantity * rate;
        }
        // 近三十天有销量
        BigDecimal maxSaleThirty = conversionSkuQuantity.getMaxSaleThirty();
        if (null != maxSaleThirty && maxSaleThirty.compareTo(BigDecimal.ZERO) > 0) {
            // 计算需要转出数量
            int needOutSkuNum = calcTransferNeedOutSkuNum(maxSaleThirty, conversionSkuQuantity.getSellOutNumOne(), conversionSkuQuantity.getSellOutNumThirty(), rate);
            return useQuantity > needOutSkuNum ? needOutSkuNum * rate : useQuantity * rate;
        }
        // 近六十天有销量
        BigDecimal maxSaleSixty = conversionSkuQuantity.getMaxSaleSixty();
        if (null != maxSaleSixty && maxSaleSixty.compareTo(BigDecimal.ZERO) > 0) {
            // 计算需要转出数量
            int needOutSkuNum = calcTransferNeedOutSkuNum(maxSaleSixty, conversionSkuQuantity.getSellOutNumOne(), conversionSkuQuantity.getSellOutNumSixty(), rate);
            return useQuantity > needOutSkuNum ? needOutSkuNum * rate : useQuantity * rate;
        }
        return rate;
    }

    /**
     * 计算需要转出数量
     * @param maxSaleQuantity
     * @param sellOutNumOne
     * @param sellOutNumOther
     * @param rate
     * @return
     */
    private int calcTransferNeedOutSkuNum(BigDecimal maxSaleQuantity, Integer sellOutNumOne, Integer sellOutNumOther, Integer rate) {
        if (null == maxSaleQuantity || null == rate) {
            return 0;
        }
        int needOutSkuNum = maxSaleQuantity.divide(BigDecimal.valueOf(rate), RoundingMode.UP).setScale(0, RoundingMode.UP).intValue();
        // 昨日有售罄
        if (null != sellOutNumOne && sellOutNumOne > 0) {
            needOutSkuNum = maxSaleQuantity.multiply(new BigDecimal("1.5")).divide(BigDecimal.valueOf(rate), RoundingMode.UP).setScale(0, RoundingMode.UP).intValue();
            return needOutSkuNum;
        }
        // 有售罄
        if (null != sellOutNumOther && sellOutNumOther > 0) {
            needOutSkuNum = maxSaleQuantity.multiply(new BigDecimal("1.5")).divide(BigDecimal.valueOf(rate), RoundingMode.UP).setScale(0, RoundingMode.UP).intValue();
        }
        return needOutSkuNum;
    }

}
