package net.summerfarm.wms.infrastructure.repository.stockRecord.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-08-03
 **/
@Data
public class StoreRecordDaySummaryDTO {
	/**
	 * 货品编码
	 */
	private Long pdId;

	/**
	 * 货品名称
	 */
	private String pdName;

	/**
	 * saas skuId
	 */
	private Long saasSkuId;

	/**
	 * 一级类目名称
	 */
	private String firstCategoryName;

	/**
	 * 二级类目名称
	 */
	private String secondCategoryName;

	/**
	 * 三级类目名称
	 */
	private String thirdCategoryName;

	/**
	 * 仓库号
	 */
	private Integer warehouseNo;

	/**
	 * 仓库名称
	 */
	private String warehouseName;

	/**
	 * 仓库服务商
	 */
	private String warehouseProvider;

	/**
	 * 货品单位
	 */
	private String unit;

	/**
	 * 货品规格
	 */
	private String weight;

	/**
	 * 期初数量
	 */
	private Integer openingQuantity;

	/**
	 * 期初金额
	 */
	private BigDecimal openingAmount;

	/**
	 * 期末数量
	 */
	private Integer endingQuantity;

	/**
	 * 期末金额
	 */
	private BigDecimal endingAmount;

	/**
	 * 调拨入库数量
	 */
	private Integer allocationInQuantity;

	/**
	 * 调拨入库金额
	 */
	private BigDecimal allocationInAmount;

	/**
	 * 采购入库数量
	 */
	private Integer purchaseInQuantity;

	/**
	 * 采购入库金额
	 */
	private BigDecimal purchaseInAmount;

	/**
	 * 退货入库数量
	 */
	private Integer afterSaleInQuantity;

	/**
	 * 退货入库金额
	 */
	private BigDecimal afterSaleInAmount;

	/**
	 * 盘盈入库数量
	 */
	private Integer stockTakingInQuantity;

	/**
	 * 盘盈入库金额
	 */
	private BigDecimal stockTakingInAmount;

	/**
	 * 转换入库数量
	 */
	private Integer transferInQuantity;

	/**
	 * 转换入库金额
	 */
	private BigDecimal transferInAmount;

	/**
	 * 调拨回库数量
	 */
	private Integer allocationAbnormalInQuantity;

	/**
	 * 调拨回库金额
	 */
	private BigDecimal allocationAbnormalInAmount;

	/**
	 * 其他入库数量
	 */
	private Integer otherInQuantity;

	/**
	 * 其他入库金额
	 */
	private BigDecimal otherInAmount;

	/**
	 * 入库合计数量
	 */
	private Integer inQuantity;

	/**
	 * 入库合计金额
	 */
	private BigDecimal inAmount;

	/**
	 * 调拨出库数量
	 */
	private Integer allocationOutQuantity;

	/**
	 * 调拨出库金额
	 */
	private BigDecimal allocationOutAmount;

	/**
	 * 销售出库数量
	 */
	private Integer saleOutQuantity;

	/**
	 * 销售出库金额
	 */
	private BigDecimal saleOutAmount;

	/**
	 * 货损出库数量
	 */
	private Integer damageOutQuantity;

	/**
	 * 货损出库金额
	 */
	private BigDecimal damageOutAmount;

	/**
	 * 盘亏出库数量
	 */
	private Integer stockTakingOutQuantity;

	/**
	 * 盘亏出库金额
	 */
	private BigDecimal stockTakingOutAmount;

	/**
	 * 转换出库数量
	 */
	private Integer transferOutQuantity;

	/**
	 * 转换出库金额
	 */
	private BigDecimal transferOutAmount;

	/**
	 * 采购退货出库数量
	 */
	private Integer purchaseBackOutQuantity;

	/**
	 * 采购退货出库金额
	 */
	private BigDecimal purchaseBackOutAmount;

	/**
	 * 补货出库数量
	 */
	private Integer supplyAgainOutQuantity;

	/**
	 * 补货出库金额
	 */
	private BigDecimal supplyAgainOutAmount;

	/**
	 * 自提销售出库数量
	 */
	private Integer ownSelfOutQuantity;

	/**
	 * 自提销售出库金额
	 */
	private BigDecimal ownSelfOutAmount;

	/**
	 * 其他出库数量
	 */
	private Integer otherOutQuantity;

	/**
	 * 其他出库金额
	 */
	private BigDecimal otherOutAmount;

	/**
	 * 出库合计数量
	 */
	private Integer outQuantity;

	/**
	 * 出库合计金额
	 */
	private BigDecimal outAmount;
}
