package net.summerfarm.wms.domain.instore.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.domainobject.query.StockStorageQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2022/11/15  18:56
 * 查询
 */
public interface StockTaskStorageQueryRepository {

    /**
     * 查询
     *
     * @return
     */
    PageInfo<StockTaskStorage> queryStockTaskStorage(StockStorageQuery stockStorageQuery);

    /**
     * 库位精细化查询
     *
     * @param stockStorageQuery query对象
     * @return 分页信息
     */
    PageInfo<StockTaskStorage> queryStockTaskStorageNoItem(StockStorageQuery stockStorageQuery);

    /**
     * 入库任务id
     *
     * @param taskId
     * @return
     */
    StockTaskStorage queryStockTaskStorageById(Long taskId);

    /**
     * 查询未入库的数量
     *
     * @param warehouseNo 仓库号
     * @param sku         sku
     * @param type        入库任务类型
     * @return 未入库的数量
     */
    Integer queryWaitInNumByType(Long warehouseNo, String sku, Integer type);

    /**
     * 查询未入库的数量
     *
     * @param warehouseNo 仓库号
     * @param skus        sku
     * @param type        入库任务类型
     * @return map<Sku, num></>
     */
    Map<String, Integer> mapWaitInNumByType(Long warehouseNo, List<String> skus, Integer type);


    /**
     * 批量查询
     *
     * @param taskIdList
     * @return
     */
    List<StockTaskStorage> selectStockStorageBatch(List<Long> taskIdList);


    /**
     * 入库任务id
     *
     * @param stockTaskStorage
     * @return
     */
    List<StockTaskStorage> queryStockTaskStorageBySourceId(StockTaskStorage stockTaskStorage);

    List<StockTaskStorage> queryStockTaskStorageByWarehouseNoAndSourceId(StockTaskStorage stockTaskStorage);


    /**
     * stockTaskId
     *
     * @param stockTaskId
     * @return
     */
    StockTaskStorage queryStockTaskStorageByOldId(Long stockTaskId);


    /**
     * 根据入库任务id
     *
     * @param stockTaskStorage
     * @return
     */
    StockTaskStorage queryStockTaskStorageMsg(StockTaskStorage stockTaskStorage);

    /**
     * 根据来源单号查询入库任务列表
     *
     * @param stockTaskStorage 入库任务对象
     * @return java.util.List<net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage>
     * <AUTHOR>
     * @date 2023/8/4 14:40
     */
    List<StockTaskStorage> selectListBySourceIdAndType(StockTaskStorage stockTaskStorage);

    /**
     * 根据来源单号查询入库任务
     *
     * @param sourceId
     * @param type
     * @return
     */
    StockTaskStorage selectBySourceIdAndType(String sourceId, Integer type);

    /**
     * 按照库存仓和期望入库时间和sku列表查询入库任务列表
     *
     * @param warehouseNo       库存仓列表
     * @param type              任务类型
     * @param expectInStoreTime 期望入库时间
     * @return 返回入库任务列表
     */
    List<StockTaskStorage> queryStockTaskStorage(Long warehouseNo, Integer type, LocalDateTime expectInStoreTime);

    /**
     * 按照库存仓和采购供应单号和sku列表查询入库任务列表
     *
     * @param warehouseNo 库存仓列表
     * @param typeList    任务类型
     * @param psoNo       采购供应单号
     * @return 返回入库任务列表
     */
    List<StockTaskStorage> queryStockTaskStorage(Long warehouseNo, List<Integer> typeList, String psoNo);

    /**
     * 查询入库任务列表
     * @param warehouseNo
     * @param typeList
     * @param psoNoList
     * @return
     */
    List<StockTaskStorage> queryStockTaskStorageByPsoNoList(Integer warehouseNo, List<Integer> typeList, List<String> psoNoList);

    /**
     * 根据条件查询唯一入库任务
     *
     * <AUTHOR>
     * @date 2023/11/1 11:41
     */
    StockTaskStorage selectOneByCondition(StockStorageQuery stockStorageQuery);


    /**
     * 按照幂等键值查询入库任务
     *
     * @param warehouseNo 仓库编号
     * @param taskType    任务类型
     * @param uniqueKey   幂等键信息
     * @return 返回查询到的任务
     */
    List<StockTaskStorage> queryByUniqueKey(Integer warehouseNo, Integer taskType, String uniqueKey);

    /**
     * 根据类型和来源id列表查询入库任务
     *
     * @param type
     * @param sourceIds
     * @return
     */
    List<StockTaskStorage> queryStockTaskStorageBySourceIdsAndType(Integer type, List<String> sourceIds);

    /**
     * 按照幂等键值批量查询入库任务
     *
     * @param warehouseNo   仓库编号
     * @param taskType      任务类型
     * @param uniqueKeyList 幂等键列表
     * @return 返回查询到的任务
     */
    List<StockTaskStorage> queryByUniqueKeyList(Integer warehouseNo, Integer taskType, List<String> uniqueKeyList);

    /**
     * stockTaskId
     *
     * @param stockTaskId
     * @return
     */
    Long queryIdStorageByOldId(Long stockTaskId);

    /**
     * 根据外部订单编号&类型查询入库任务
     *
     * <AUTHOR>
     * @date 2024/2/21 15:49
     * @param thirdOrderNo 外部订单号
     * @param type 类型
     * @return net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage
     */
    StockTaskStorage selectOneByThirdOrderNoAndType(String thirdOrderNo, Integer type);

    /**
     * 根据仓库号&类型&入库时间&采购模式查询入库任务
     * @param warehouseNos 仓库号集合
     * @param type 类型
     * @param expectTime 预约入库时间
     * @param purchaseMode 采购模式
     * @return 结果
     */
    List<StockTaskStorage> queryListByWreaNosAndTypeAndExTimeAndPuMode(List<Integer> warehouseNos, Integer type, LocalDate expectTime, Integer purchaseMode);

}
