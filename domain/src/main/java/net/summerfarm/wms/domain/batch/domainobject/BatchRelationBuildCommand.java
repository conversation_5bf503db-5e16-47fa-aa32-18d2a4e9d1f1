package net.summerfarm.wms.domain.batch.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;

/**
 * 批次关系建造对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BatchRelationBuildCommand {

    Long warehouseNo;

    String sku;

    String purchaseNo;

    Long sourceId;

    Integer sourceType;

    LocalDate produceAt;

    Long currentCostBatchId;
}
