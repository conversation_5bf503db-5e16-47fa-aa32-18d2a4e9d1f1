package net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class CabinetBatchInventoryRecommendOutResp implements Serializable {

    private static final long serialVersionUID = -4053311578229123077L;
    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private Integer warehouseNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String weightSpec;

    /**
     * 存储区域
     */
    @ApiModelProperty(value = "储存区域")
    private String storageLocation;

    /**
     * 存储区域id
     */
    private Integer storageLocationId;

    /**
     * 库区类型，所属温区（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    @ApiModelProperty(value = "库区类型，所属温区（1：冷冻，2：冷藏，3：恒温，4：常温）")
    private Integer zoneType;

    /**
     * 库区类型，所属温区（1：冷冻，2：冷藏，3：恒温，4：常温）
     */
    @ApiModelProperty(value = "库区类型，所属温区（1：冷冻，2：冷藏，3：恒温，4：常温）")
    private String zoneTypeDesc;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String packaging;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String cabinetCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date qualityDate;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer quantity;

    /**
     * 冻结数量
     */
    @ApiModelProperty(value = "冻结数量")
    private Integer lockQuantity;

    /**
     * 可用数量
     */
    @ApiModelProperty(value = "可用数量")
    private Integer availableQuantity;

    /**
     * 推荐数量
     */
    @ApiModelProperty(value = "推荐数量")
    private Integer recommendQuantity;

    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    private Integer cabinetPurpose;
}
