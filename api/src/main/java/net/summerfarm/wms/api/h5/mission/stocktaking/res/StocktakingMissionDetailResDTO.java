package net.summerfarm.wms.api.h5.mission.stocktaking.res;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.api.h5.mission.res.MissionItemPCResDTO;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StocktakingMissionDetailResDTO implements Serializable {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 业务单号
     */
    String sourceOrderNo;

    /**
     * 业务类型
     */
    Integer bizType;

    /**
     * 任务状态
     */
    Integer state;

    /**
     * 操作人
     */
    String operatorName;

    /**
     * 创建时间
     */
    String createTime;

    /**
     * 完成时间
     */
    String finishTime;
}
