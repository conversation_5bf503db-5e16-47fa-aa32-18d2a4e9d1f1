package net.summerfarm.wms.application.roadsale.listener.event;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseRoadSaleApplyEvent {

    /**
     * 采购单号
     */
    private String purchasesNo;

    /**
     * 预约单号
     */
    private String stockArrangeId;


    /**
     * 仓库
     */
    private Integer warehouseNo;


    /**
     * 预约时间
     */
    private LocalDate arrangeTime;


    /**
     * 操作人名称
     */
    private String operatorName;


    /**
     * 操作人id
     */
    private Long operatorId;


    /**
     * 采购在途详情
     */
    private List<PurchaseRoadSaleDetailEvent> purchaseRoadSaleDetailEventList;


    @Data
    public static class PurchaseRoadSaleDetailEvent {

        /**
         * 在途可售比例
         */
        private BigDecimal roadSaleRatio;


        /**
         * 预约数量
         */
        private Integer arrQuantity;


        /**
         * sku
         */
        private String sku;


    }


}
