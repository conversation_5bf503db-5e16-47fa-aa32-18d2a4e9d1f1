package net.summerfarm.wms.manage.web.service.impl;

import net.summerfarm.wms.manage.web.service.WarehouseStockExtService;
import net.summerfarm.wms.manage.model.domain.WarehouseStockExt;
import net.summerfarm.wms.manage.web.mapper.manage.WarehouseStockExtMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: dongcheng
 * @date: 2023/7/25
 */
@Service
public class WarehouseStockExtServiceImpl implements WarehouseStockExtService {

    @Resource
    private WarehouseStockExtMapper warehouseStockExtMapper;

    @Override
    public void updateStatus(WarehouseStockExt warehouseStockExt) {
        warehouseStockExtMapper.updateWarehouseStockExt(warehouseStockExt);
    }

    @Override
    public WarehouseStockExt selectWarehouseStockExt(WarehouseStockExt warehouseStockExt) {
        WarehouseStockExt resultExt = warehouseStockExtMapper.selectWarehouseStockExt(warehouseStockExt);
        return resultExt;
    }

    @Override
    public void initWarehouseStockExt(String sku) {
        warehouseStockExtMapper.initWarehouseStockExt(sku);
    }
}
