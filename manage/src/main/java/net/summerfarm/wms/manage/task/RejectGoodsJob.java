package net.summerfarm.wms.manage.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.initconfig.StockInWarehouseGreyConfig;
import net.summerfarm.wms.domain.instore.enums.NoticeStockTaskStorageOrderTypeEnum;
import net.summerfarm.wms.manage.web.service.StockTaskService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Component
@Slf4j
public class RejectGoodsJob extends XianMuJavaProcessorV2 {

    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private StockInWarehouseGreyConfig stockInWarehouseGreyConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        List<Integer> greyWarehouseNoList = stockInWarehouseGreyConfig.getWarehouseNoList();
        log.info("售后链路灰度仓列表：{}", greyWarehouseNoList);
        // 售后灰度链路
        if (!CollectionUtils.isEmpty(greyWarehouseNoList)) {
            stockTaskService.autoAfterSaleInForNotice(NoticeStockTaskStorageOrderTypeEnum.REJECT_SALE_IN.getAfterSaleOrderType(), false, greyWarehouseNoList);
        }
        if (null == greyWarehouseNoList) {
            greyWarehouseNoList = new ArrayList<>();
        }
        stockTaskService.autoRejectTask(greyWarehouseNoList);
        return new ProcessResult(true);
    }
}
