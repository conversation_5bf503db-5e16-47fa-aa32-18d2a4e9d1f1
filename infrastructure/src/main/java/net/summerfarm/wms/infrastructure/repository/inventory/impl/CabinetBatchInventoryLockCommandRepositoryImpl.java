package net.summerfarm.wms.infrastructure.repository.inventory.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventoryLockRelationEntity;
import net.summerfarm.wms.domain.inventory.domainobject.UpdateBatchInventoryLockRelation;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryLockCommandRepository;
import net.summerfarm.wms.infrastructure.dao.inventory.CabinetBatchInventoryLockRelationDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.UpdateBatchInventoryLockRelationDO;
import net.summerfarm.wms.infrastructure.repository.inventory.converter.CabinetBatchInventoryLockConverter;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Repository
@Slf4j
public class CabinetBatchInventoryLockCommandRepositoryImpl implements CabinetBatchInventoryLockCommandRepository {

    @Resource
    private CabinetBatchInventoryLockRelationDAO cabinetBatchInventoryLockRelationDAO;

    @Override
    public void insert(CabinetBatchInventoryLockRelationEntity lockRelation) {
        if (Objects.isNull(lockRelation)) {
            return;
        }
        CabinetBatchInventoryLockRelationDO convert = CabinetBatchInventoryLockConverter.INSTANCE.convert(lockRelation);
        cabinetBatchInventoryLockRelationDAO.insertSelective(convert);
    }

    @Override
    public void batchInsert(List<CabinetBatchInventoryLockRelationEntity> lockRelations) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInsert(UpdateBatchInventoryLockRelation update) {
        if (update.getChangeQuantity() == null || update.getWarehouseNo() == null || StringUtils.isEmpty(update.getBatch()) ||
                StringUtils.isEmpty(update.getSku()) || update.getProduceDate() == null || update.getQualityDate() == null ||
                StringUtils.isEmpty(update.getBizId()) || update.getBizType() == null) {
//            throw new BizException("库位批次占用记录更新失败,参数异常");
            log.warn("库位批次占用记录更新失败,参数异常");
            return;
        }
        log.info("批次锁定记录变动：{}", JSON.toJSONStringWithDateFormat(update, DateUtil.YYYY_MM_DD));
        // 暂时就采退接入
        if (!(update.getBizName().equals(CabinetInventoryChangeTypeEnum.PURCHASES_BACK_OUT.getTypeName()) ||
                update.getBizName().equals(CabinetInventoryChangeTypeEnum.INVENTORY_LOCK.getTypeName()))) {
            return;
        }
        UpdateBatchInventoryLockRelationDO convert = CabinetBatchInventoryLockConverter.INSTANCE.convert(update);
        // 查占用记录
        CabinetBatchInventoryLockRelationDO cabinetBatchInventoryLockRelationDO = cabinetBatchInventoryLockRelationDAO.selectByDupKey(convert);
        log.info("查询批次锁定记录变动：{}", JSON.toJSONStringWithDateFormat(cabinetBatchInventoryLockRelationDO, DateUtil.YYYY_MM_DD));
        // 为空则新增
        if (Objects.isNull(cabinetBatchInventoryLockRelationDO)) {
            if (convert.getChangeQuantity() > 0) {
                CabinetBatchInventoryLockRelationDO insert = CabinetBatchInventoryLockConverter.INSTANCE.convertInsert(update);
                cabinetBatchInventoryLockRelationDAO.insertSelective(insert);
            }
            return;
        }
        // 内存先校验一层
        if (cabinetBatchInventoryLockRelationDO.getLockQuantity() + update.getChangeQuantity() < 0) {
            throw new BizException("库位批次占用记录更新失败, 占用数量小于释放数量");
        }
        // 乐观删
        if (cabinetBatchInventoryLockRelationDO.getLockQuantity() + update.getChangeQuantity() == 0) {
            cabinetBatchInventoryLockRelationDAO.insertSelectForLog(convert);
            int i = cabinetBatchInventoryLockRelationDAO.deleteById(cabinetBatchInventoryLockRelationDO.getId(), System.currentTimeMillis());
            if (i <= 0) {
                throw new BizException("该库位批次占用已被释放,请勿重复操作");
            }
            return;
        }

        // 插新记录
        cabinetBatchInventoryLockRelationDAO.insertSelect(convert);

        // 删原记录
        int i = cabinetBatchInventoryLockRelationDAO.deleteById(cabinetBatchInventoryLockRelationDO.getId(), System.currentTimeMillis());
        if (i <= 0) {
            throw new BizException("该库位批次占用释放失败");
        }
    }

    @Override
    public int update(CabinetBatchInventoryLockRelationEntity lockRelation) {
        return 0;
    }

    @Override
    public void releaseLockByBiz(String bizId, String bizName) {
        log.info("释放批次占用：bizId:{}, bizName:{}", bizId, bizName);
        int i = cabinetBatchInventoryLockRelationDAO.releaseLockByBiz(bizId, bizName);
        if (i <= 0) {
            throw new BizException(bizId + bizName + "释放批次占用失败");
        }
    }
}
