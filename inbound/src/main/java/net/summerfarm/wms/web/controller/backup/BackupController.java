package net.summerfarm.wms.web.controller.backup;

import net.summerfarm.manage.client.wms.dto.dto.StockTaskSkuMsgDTO;
import net.summerfarm.wms.api.h5.backup.req.StorageItemDetailKeyCommand;
import net.summerfarm.wms.application.inventory.listener.WmsOutboundPackageCreateListener;
import net.summerfarm.wms.application.outstore.WmsOutboundPackageCreateCommandService;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.util.RedisUtil;
import net.summerfarm.wms.mq.stocktask.StockTaskFinishMsgDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 补偿入口
 * @date 2023/5/20
 */
@RestController
@RequestMapping(value = "/wms/backup")
public class BackupController {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private WmsOutboundPackageCreateCommandService wmsOutboundPackageCreateCommandService;

    @Autowired
    private WmsOutboundPackageCreateListener wmsOutboundPackageCreateListener;

    /**
     * 入库任务详情查询item_detail过渡key变更
     * @param storageItemDetailKeyCommand
     * @return
     */
    @PostMapping("/upsert/item-id-key")
    public CommonResult<String> upsertQueryStorageItemDetailKey(@RequestBody @Valid StorageItemDetailKeyCommand storageItemDetailKeyCommand) {
        redisUtil.set(RedisKeys.buildQueryStorageItemDetailKey(), storageItemDetailKeyCommand.getId());
        String value = redisUtil.get(RedisKeys.buildQueryStorageItemDetailKey());
        return CommonResult.ok(value);
    }

    /**
     * 补偿出库库存锁库处理
     * @param stockTaskSkuMsgDTO
     * @return
     */
    @PostMapping("/handleInventoryLockOut")
    public CommonResult<String> handleInventoryLockOut(@RequestBody @Valid StockTaskSkuMsgDTO stockTaskSkuMsgDTO) {
        wmsOutboundPackageCreateListener.handleInventoryLockOut(stockTaskSkuMsgDTO);
        return CommonResult.ok("ok");
    }


    /**
     * 补偿出库完成的锁库处理
     *
     * @param stockTaskFinishMsgDTO
     * @return
     */
    @PostMapping("/handleStockTaskFinish")
    public CommonResult<String> handleStockTaskFinish(@RequestBody @Valid StockTaskFinishMsgDTO stockTaskFinishMsgDTO) {
        wmsOutboundPackageCreateCommandService.handleStockTaskFinish(stockTaskFinishMsgDTO);
        return CommonResult.ok("ok");
    }

}
