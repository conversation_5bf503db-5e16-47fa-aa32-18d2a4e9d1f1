package net.summerfarm.wms.api.h5.outstore.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 拣货列表返回对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderPickingListPdaResDTO implements Serializable {

    private static final long serialVersionUID = 1496085670156637052L;
    /**
     * 任务号
     */
    String missionNo;

    /**
     * 货品种类
     */
    String cargoType;

    /**
     * 业务类型
     */
    List<Integer> bizTypeList;

    /**
     * 业务单号
     */
    List<String> sourceOrderNoList;

    /**
     * 已经拣货的sku数量
     */
    Integer pickSkuNum;

    /**
     * 总需要拣货的sku数量
     */
    Integer pickSkuTotalNum;

    /**
     * 已经拣货件数
     */
    Integer pickGoodsNum;

    /**
     * 总共需要拣货的商品件数
     */
    Integer pickGoodsTotalNum;

    /**
     * 城配仓编号
     */
    Long storeNo;

    /**
     * 城配仓名称
     */
    String storeName;

    /**
     * 城配仓列表
     */
    List<Long> storeNoList;

    /**
     * 城配仓名称列表
     */
    List<String> storeNameList;

    /**
     * 库仓
     */
    private Long targetWarehouseNo;

    /**
     * 出库仓名称
     */
    private String targetWarehouseName;

    /**
     * 出库仓列表
     */
    private List<Long> targetWarehouseNoList;

    /**
     * 出库仓名称列表
     */
    private List<String> targetWarehouseNoNameList;

    /**
     * 存储温区
     */
    String temperature;

    /**
     * 库区
     */
    String zone;

    /**
     * 预计出库日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate expectTime;

    /**
     * 任务状态
     */
    Integer state;

    /**
     * 业务类型返回字段
     */
    List<String> bizTypeNameList;

    /**
     * 业务标签 0默认 1顺鹿达
     */
    private Integer businessTag;

    public String bizTypeNameListStr() {
        if (CollectionUtils.isNotEmpty(bizTypeNameList)) {
            return StringUtils.join(bizTypeNameList, "/");
        }
        return "";
    }

    public String getStoreNameListStr() {
        if (CollectionUtils.isNotEmpty(storeNameList)) {
            return StringUtils.join(storeNameList, ",");
        }
        return "";
    }
}
