package net.summerfarm.wms.domain.print.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-03-20 17:04:21
 * @version 1.0
 *
 */
@Data
public class WmsPrintCodeInfoQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 商品名称
	 */
	private String pdName;

	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 外部专用条码
	 */
	private String externalCode;

	/**
	 * 外部专用名称
	 */
	private String externalName;

	/**
	 * 模版名称
	 */
	private String templateName;

	/**
	 * 分隔符
	 */
	private String sepCode;

	/**
	 * 是否删除（0:否，1:是）
	 */
	private Integer isDeleted;

	/**
	 * 自定义json
	 */
	private String customJson;

	
}