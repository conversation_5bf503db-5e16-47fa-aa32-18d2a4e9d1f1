package net.summerfarm.wms.manage.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MsgAdmin implements Serializable {
    private Integer id;

    private Integer msgBodyId;

    private Integer adminId;

    private Integer recvType;

    private Boolean read;

    private Date addtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMsgBodyId() {
        return msgBodyId;
    }

    public void setMsgBodyId(Integer msgBodyId) {
        this.msgBodyId = msgBodyId;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getRecvType() {
        return recvType;
    }

    public void setRecvType(Integer recvType) {
        this.recvType = recvType;
    }

    public Boolean getRead() {
        return read;
    }

    public void setRead(Boolean read) {
        this.read = read;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }
}
