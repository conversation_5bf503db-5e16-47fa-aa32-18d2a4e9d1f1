package net.summerfarm.wms.infrastructure.dao.mission.dataobject;

import com.google.common.collect.Lists;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.base.ValueObject;
import net.summerfarm.wms.domain.mission.enums.MissionCarrierTypeEnum;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 提交明细
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubmitDetailDO {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDate createTime;

    /**
     * update time
     */
    private LocalDate updateTime;

    /**
     * 任务编号
     */
    private String missionNo;

    /**
     * 执行单元编号
     */
    private String unitNo;

    /**
     * 来源载体类型
     */
    private Integer sourceCarrierType;

    /**
     * 来源载体编号
     */
    private String sourceCarrierCode;

    /**
     * 目标载体类型
     */
    private Integer targetCarrierType;

    /**
     * 目标载体编号
     */
    private String targetCarrierCode;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 生产日期
     */
    private LocalDate produceTime;

    /**
     * 保质期
     */
    private LocalDate shelfLife;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 货主
     */
    private String cargoOwner;

    /**
     * 操作人名称
     */
    private String opreatorName;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 任务类型
     */
    private Integer missionType;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * 拆分类型
     */
    private Integer splitType;

    /**
     * 拆分信息
     */
    private String splitInfo;

    public List<String> getContainCodeList() {
        ArrayList<String> containCodeList = Lists.newArrayList();
        if (MissionCarrierTypeEnum.CONTAINER.getCode().equals(sourceCarrierType)) {
            containCodeList.add(sourceCarrierCode);
        }
        if (MissionCarrierTypeEnum.CONTAINER.getCode().equals(targetCarrierType)) {
            containCodeList.add(targetCarrierCode);
        }
        return containCodeList;
    }
}
