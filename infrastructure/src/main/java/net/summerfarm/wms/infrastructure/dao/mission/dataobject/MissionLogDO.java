package net.summerfarm.wms.infrastructure.dao.mission.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * wms_mission_log
 * <AUTHOR>
@Data
public class MissionLogDO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 业务id
     */
    private String missionNo;

    /**
     * 业务类型
     */
    private Integer missionType;

    /**
     * 操作人id
     */
    private String operator;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作详情
     */
    private String operateInfo;

    private static final long serialVersionUID = 1L;
}