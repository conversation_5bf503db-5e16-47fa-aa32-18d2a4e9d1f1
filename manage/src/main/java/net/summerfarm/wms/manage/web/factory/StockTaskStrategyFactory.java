package net.summerfarm.wms.manage.web.factory;

import lombok.Data;
import net.summerfarm.wms.manage.web.service.StockTaskStrategy;

import java.util.Map;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
public class StockTaskStrategyFactory {

    private Map<Integer, StockTaskStrategy> strategyMap;

    public StockTaskStrategy getStrategy(Integer type) {
        return strategyMap.get(type);
    }

}
