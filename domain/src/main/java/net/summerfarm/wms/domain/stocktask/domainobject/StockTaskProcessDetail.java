package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@Deprecated
public class StockTaskProcessDetail {

    private Integer id;

    private Integer stockTaskProcessId;

    private Integer itemId;

    private String sku;

    private String listNo;

    private LocalDate qualityDate;

    private Integer quantity;

    private LocalDate productionDate;

    private String remark;

    private String transferSku;

    private Integer transferQuantity;

    private LocalDateTime createTime;

    private String creator;

    private String transferScale;
    /**
    * 转出货位编号
    */
    private String glNo;

    /**
     * 转入货位编号
     */
    private String inGlNo;

    private Integer status;

    /**
    * 转入批次
    */
    private String transferListNo;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    private Integer state;


    public StockTaskProcessDetail(){
    }

    public StockTaskProcessDetail(Integer stockTaskProcessId, Integer itemId, String sku, String listNo, LocalDate qualityDate, Integer quantity, LocalDate productionDate) {
        this.stockTaskProcessId = stockTaskProcessId;
        this.itemId = itemId;
        this.sku = sku;
        this.listNo = listNo;
        this.qualityDate = qualityDate;
        this.quantity = quantity;
        this.productionDate = productionDate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStockTaskProcessId() {
        return stockTaskProcessId;
    }

    public void setStockTaskProcessId(Integer stockTaskProcessId) {
        this.stockTaskProcessId = stockTaskProcessId;
    }

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getListNo() {
        return listNo;
    }

    public void setListNo(String listNo) {
        this.listNo = listNo;
    }

    public LocalDate getQualityDate() {
        return qualityDate;
    }

    public void setQualityDate(LocalDate qualityDate) {
        this.qualityDate = qualityDate;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDate getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(LocalDate productionDate) {
        this.productionDate = productionDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTransferSku() {
        return transferSku;
    }

    public void setTransferSku(String transferSku) {
        this.transferSku = transferSku;
    }

    public Integer getTransferQuantity() {
        return transferQuantity;
    }

    public void setTransferQuantity(Integer transferQuantity) {
        this.transferQuantity = transferQuantity;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getTransferScale() {
        return transferScale;
    }

    public void setTransferScale(String transferScale) {
        this.transferScale = transferScale;
    }

    public String getGlNo() {
        return glNo;
    }

    public void setGlNo(String glNo) {
        this.glNo = glNo;
    }

    public String getInGlNo() {
        return inGlNo;
    }

    public void setInGlNo(String inGlNo) {
        this.inGlNo = inGlNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTransferListNo() {
        return transferListNo;
    }

    public void setTransferListNo(String transferListNo) {
        this.transferListNo = transferListNo;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }
}
