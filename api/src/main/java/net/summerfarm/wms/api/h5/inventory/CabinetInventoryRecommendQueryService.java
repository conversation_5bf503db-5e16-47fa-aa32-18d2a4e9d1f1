package net.summerfarm.wms.api.h5.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryBatchRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventorySingleRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryRecommendOutResp;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryRecommendOutResp;
import net.xianmu.common.result.CommonResult;

import java.util.List;

public interface CabinetInventoryRecommendQueryService {

    /**
     * 查询推荐出库库位
     *
     * @param recommendOutReq
     * @return
     */
    CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOut(
            CabinetInventorySingleRecommendOutReq recommendOutReq);

    /**
     * 查询推荐出库批次库位
     * @param recommendOutReq
     * @return
     */
    CommonResult<PageInfo<CabinetBatchInventoryRecommendOutResp>> queryBatchRecommendOut(
            CabinetInventorySingleRecommendOutReq recommendOutReq);

    /**
     * 查询推荐出库库位
     * 排除临保效期
     *
     * @param recommendOutReq
     * @return
     */
    CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOutExcludeAbnormalPeriod(
            CabinetInventorySingleRecommendOutReq recommendOutReq);

    /**
     * 一键录入匹配
     *
     * @param queryRecommendReq
     * @return
     */
    CommonResult<List<CabinetInventoryRecommendOutResp>> matchBatchRecommendOut(CabinetInventoryBatchRecommendOutReq queryRecommendReq);

}
