package net.summerfarm.wms.upload.materialManage;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingCommandService;
import net.summerfarm.wms.domain.download.enums.FileDownloadCenterRecordEnum;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.upload.materialManage.dto.MaterialBindingUploadDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MaterialBindingUploadHandler extends DownloadCenterImportDefaultHandler<MaterialBindingUploadDTO> {

    @Resource
    private WmsMaterialBindingCommandService materialBindingCommandService;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private ProductRepository productRepository;


    @Override
    protected void dealExcelData(List<MaterialBindingUploadDTO> importDataList, DownloadCenterDataMsg msg) {
        if (CollectionUtils.isEmpty(importDataList)) {
            return;
        }

        Long bizUserId = msg.getAuthUser().getBizUserId();
        String bizNickName = msg.getAuthUser().getNickname();
        Long tenantId = msg.getAuthUser().getTenantId();

        // 查询货品
        List<String> skuCodeList = importDataList.stream()
                .map(MaterialBindingUploadDTO::getSkuCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, skuCodeList);

        // 查询物料
        List<String> materialSkuCodeList = importDataList.stream()
                .map(MaterialBindingUploadDTO::getMaterialSkuCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, materialSkuCodeList);

        // 按照仓库货品sku聚合分组
        Map<String, List<MaterialBindingUploadDTO>> importDataListGroup = new HashMap<>();
        for (MaterialBindingUploadDTO receiveUploadDTO : importDataList) {
            String key = receiveUploadDTO.getWarehouseName() + "_" + receiveUploadDTO.getSkuCode();
            List<MaterialBindingUploadDTO> list = importDataListGroup.get(key);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(receiveUploadDTO);
            importDataListGroup.put(key, list);
        }

        // 物料类目
        Long materialFirstCategory = productRepository.getMaterialFirstCategory();

        // 处理数据
        for (Map.Entry<String, List<MaterialBindingUploadDTO>> stringListEntry : importDataListGroup.entrySet()) {
            String key = stringListEntry.getKey();
            String[] value = key.split("_");
            String warehouseName = value[0];
            String sku = value[1];
            List<MaterialBindingUploadDTO> receiveUploadDTOList = stringListEntry.getValue();
            MaterialBindingUploadDTO materialBindingUploadDTO = receiveUploadDTOList.get(0);

            if (tenantId == null){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求租户不存在");
                }
                continue;
            }
            // 仓库参数
            WarehouseStorageCenterEntity warehouseStorageCenter = warehouseStorageRepository.selectByWarehouseName(
                    tenantId, warehouseName);
            if (warehouseStorageCenter == null){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求仓库不存在");
                }
                continue;
            }
            if (!tenantId.equals(warehouseStorageCenter.getTenantId())){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求仓库无权限访问");
                }
                continue;
            }
            Integer warehouseNo = warehouseStorageCenter.getWarehouseNo();

            // 货品
            GoodsInfoDTO goodsInfoDTO = goodsInfoDTOMap.get(sku);
            if (goodsInfoDTO == null){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求货品不存在");
                }
                continue;
            }
            // 请求货品存在不一致的货品比例
            if (receiveUploadDTOList.stream()
                    .map(MaterialBindingUploadDTO::getSkuRatio)
                    .distinct()
                    .count() > 1){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求货品存在不一致的货品比例");
                }
                continue;
            }
            // 请求物料sku重复
            if (receiveUploadDTOList.stream()
                   .map(MaterialBindingUploadDTO::getMaterialSkuCode)
                   .distinct()
                   .count() != receiveUploadDTOList.size()){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("请求物料sku存在重复");
                }
                continue;
            }

            // 填充明细
            List<WmsMaterialBindingDetailCommandInput> detailList = new ArrayList<>();
            Boolean checkMaterialSkuError = false;
            String checkMaterialSkuMsg = "";
            for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                // 物料
                GoodsInfoDTO materialGoodsInfoDTO = materialGoodsInfoDTOMap.get(uploadDTO.getMaterialSkuCode());
                if (materialGoodsInfoDTO == null) {
                    checkMaterialSkuMsg += "请求物料不存在" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                // 物资判断
                if (materialFirstCategory == null ||
                        !materialFirstCategory.equals(materialGoodsInfoDTO.getFirstCategoryId())) {
                    checkMaterialSkuMsg += "请求物料不是物料" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                if (uploadDTO.getSkuRatio() == null){
                    checkMaterialSkuMsg += "请求货品比例为空" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                if (uploadDTO.getMaterialSkuRatio() == null){
                    checkMaterialSkuMsg += "请求物资比例为空" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                if (uploadDTO.getSkuRatio().compareTo(BigDecimal.ZERO) <= 0){
                    checkMaterialSkuMsg += "请求货品比例不能小于等于0" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                if (uploadDTO.getMaterialSkuRatio().compareTo(BigDecimal.ZERO) <= 0){
                    checkMaterialSkuMsg += "请求物资比例不能小于等于0" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }
                // 货品比例不能非整数
                if (uploadDTO.getSkuRatio().remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0){
                    checkMaterialSkuMsg += "请求货品比例不能非整数" + uploadDTO.getMaterialSkuCode() + ";";
                    checkMaterialSkuError = true;
                    continue;
                }

                WmsMaterialBindingDetailCommandInput updateDto = new WmsMaterialBindingDetailCommandInput();
                updateDto.setMaterialSku(materialGoodsInfoDTO.getSku());
                updateDto.setMaterialSkuSaasId(materialGoodsInfoDTO.getSkuId());
                updateDto.setMaterialSkuRatio(uploadDTO.getMaterialSkuRatio());
                updateDto.setReuse(
                        materialGoodsInfoDTO.getPropertyValueByEnum(ProductsPropertyEnum.REUSE));
                updateDto.setAutoOutbound(
                        materialGoodsInfoDTO.getPropertyValueByEnum(ProductsPropertyEnum.AUTO_OUTBOUND));
                detailList.add(updateDto);
            }
            if (checkMaterialSkuError){
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg(checkMaterialSkuMsg);
                }
                continue;
            }

            // 填充主单
            WmsMaterialBindingCommandInput input = new WmsMaterialBindingCommandInput();
            input.setCreateTime(LocalDateTime.now());
            input.setCreator(bizNickName);
            input.setUpdateTime(LocalDateTime.now());
            input.setUpdater(bizNickName);
            input.setTenantId(tenantId);

            input.setWarehouseNo(warehouseNo);
            input.setSku(goodsInfoDTO.getSku());
            input.setSkuSaasId(goodsInfoDTO.getSkuId());
            input.setSkuRatio(materialBindingUploadDTO.getSkuRatio());
            input.setDetailCommandInputList(detailList);

            try {
                CommonResult<WmsMaterialBindingVO> commonResult =  materialBindingCommandService.insert(input);
                if (commonResult == null) {
                    for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                        uploadDTO.setErrorMsg("物料绑定发生异常");
                    }
                    continue;
                }
                if (!ResultStatusEnum.OK.getStatus().equals(commonResult.getStatus())) {
                    for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                        uploadDTO.setErrorMsg(commonResult.getMsg());
                    }
                }
            }
            catch (BizException e) {
                log.error("MaterialReceiveUploadHandler Throwable", e);
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg(e.getMessage());
                }
            }
            catch (Throwable e) {
                log.error("MaterialBindingUploadHandler Throwable", e);
                for (MaterialBindingUploadDTO uploadDTO : receiveUploadDTOList) {
                    uploadDTO.setErrorMsg("物料绑定失败，发生异常");
                }
            }
        }
    }

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.XIANMU;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadCenterRecordEnum.MATERIAL_BINDING_UPLOAD.getType();
    }
}