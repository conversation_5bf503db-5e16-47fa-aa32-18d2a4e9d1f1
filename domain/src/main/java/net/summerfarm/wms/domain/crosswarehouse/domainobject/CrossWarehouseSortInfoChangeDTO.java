package net.summerfarm.wms.domain.crosswarehouse.domainobject;

import lombok.Data;

/**
 * @author: dongcheng
 * @date: 2023/11/7
 */
@Data
public class CrossWarehouseSortInfoChangeDTO {

    private Long id;

    /**
     * 城配仓编码
     */
    private Integer storeNo;

    /**
     * 总需分拣的数量
     */
    private Integer totalQuantity;

    /**
     * 当前生成的分拣数量
     */
    private Integer currentGenerateQuantity;

    /**
     * 最初生成的分拣数量
     */
    private Integer sourceGenerateQuantity;

    /**
     * 剩余可分配数量
     *
     * @return 返回剩余可分配的数量
     */
    public Integer getRemainingAllocationQuantity() {
        return totalQuantity - currentGenerateQuantity;
    }

    public static CrossWarehouseSortInfoChangeDTO init(CrossWarehouseSortInfo crossWarehouseSortInfo) {
        if (crossWarehouseSortInfo == null) {
            return null;
        }
        CrossWarehouseSortInfoChangeDTO changeDTO = new CrossWarehouseSortInfoChangeDTO();

        changeDTO.setId(crossWarehouseSortInfo.getId());
        changeDTO.setTotalQuantity(crossWarehouseSortInfo.getTotalQuantity());
        changeDTO.setSourceGenerateQuantity(crossWarehouseSortInfo.getGenerateQuantity());
        changeDTO.setCurrentGenerateQuantity(crossWarehouseSortInfo.getGenerateQuantity());
        changeDTO.setStoreNo(crossWarehouseSortInfo.getStoreNo());
        return changeDTO;
    }
}
