package net.summerfarm.wms.manage.model.domain;

import lombok.Data;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Data
public class MerchantOuterDO extends BaseDO {

    /** 商家id */
    private Long mId;

    /** 商家外部编码 */
    private String outerNo;

    /** 备注 */
    private String remark;

    /**
     *  外部平台id
     */
    private Integer outerPlatformId;

    public Long getmId() {
        return this.mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getOuterNo() {
        return this.outerNo;
    }

    public void setOuterNo(String outerNo) {
        this.outerNo = outerNo;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOuterPlatformId() {
        return outerPlatformId;
    }

    public void setOuterPlatformId(Integer outerPlatformId) {
        this.outerPlatformId = outerPlatformId;
    }
}
