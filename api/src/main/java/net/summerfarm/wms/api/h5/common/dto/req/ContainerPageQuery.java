package net.summerfarm.wms.api.h5.common.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/3
 */
@Data
public class ContainerPageQuery implements Serializable {

    private static final long serialVersionUID = -1824192769063182869L;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 容器属性（1：收货，2：拣货，3：库内，4：交接，5：通用）
     */
    private Integer purpose;

    /**
     * 容器属性集合（1：收货，2：拣货，3：库内，4：交接，5：通用）
     */
    private List<Integer> purposes;

    /**
     * 容器状态（0：禁用，1：启用）
     */
    private Integer containerStatus;

    /**
     * 占用状态（0：未占用，1：已占用）
     */
    private Integer occupyStatus;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 库位编码模块查询
     */
    private String containerCodeLike;
}
