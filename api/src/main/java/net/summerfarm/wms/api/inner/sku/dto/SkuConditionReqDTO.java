package net.summerfarm.wms.api.inner.sku.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.api.h5.stocktaking.dto.SkuCabinetCondition;

import java.io.Serializable;
import java.util.List;

/**
 * 类目盘点条件
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SkuConditionReqDTO {

    /**
     * 仓库编号
     */
    Long warehouseNo;

    /**
     * skus
     */
    List<String> skus;

    /**
     * 库存情况
     * 1:>0,2:=0
     */
    Integer stockCd;


    /**
     * 上下架类型
     */
    Boolean saleType;

    /**
     * 上下架类型条件
     */
    List<Integer> saleTypeCd;

    /**
     * 上下操作
     */
    Boolean saleOp;

    /**
     * 上架情况
     */
    List<Integer> saleOpCd;

    /**
     * 温区
     */
    List<Integer> temperatureCd;

    /**
     * 类目
     */
    List<Long> categoryCd;

    /**
     * 库位盘点sku
     */
    List<SkuCabinetCondition> skuCabinetConditions;

    /**
     * 分页条件
     */
    Integer pageSize;

    Integer pageNum;


}
