package net.summerfarm.wms.manage.web.enums;

import lombok.Getter;

/**
 * @author: dongcheng
 * @date: 2023/12/1
 */
@Getter
public enum WechatMessageTemplateEnum {


    /**
     * 价格即将失效
     */
    OFFER_EXPIRE("YTSou_RZIB2meUy2NSqLScY1OnwfCfrFs4WWNRc0Kek"),
    /**
     * 修改报价审批结果
     */
    OFFER_MODIFY_RESULT("8rLmHMJSfS536tsJmrNPdORwCwC6DWKoDn5IlDrMS_s"),
    /**
     * 销售订单
     */
    SELL_ORDER("Jn4Dfoevand0hx980CSpMwudO3BmBQGF5ku6_u0HASg"),
    /**
     * 发货订单关闭或完成
     */
    ARRANGE_CLOSED("donn7Xz19qQwe04zsc28Fv9Go3x1PqDM8WHUGVamxoc");

    private final String templateId;


    WechatMessageTemplateEnum(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateId() {
        return templateId;
    }
}
