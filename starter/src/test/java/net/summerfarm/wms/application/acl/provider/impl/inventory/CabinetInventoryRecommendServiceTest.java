package net.summerfarm.wms.application.acl.provider.impl.inventory;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryRecommendQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryQueryRecommendOutDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryBatchRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventorySingleRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.SkuProductRangeReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryRecommendOutResp;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.starter.WmsApplication;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = WmsApplication.class)
@Slf4j
public class CabinetInventoryRecommendServiceTest {

    @Resource
    private CabinetInventoryRecommendQueryService recommendQueryService;

    @Test
    public void testqueryRecommendOut(){

        CabinetInventorySingleRecommendOutReq req = new CabinetInventorySingleRecommendOutReq();
//        req.setTenantId(1L);
        req.setWarehouseNo(1);

//        addDetailReq.setCabinetCode("Z99-01-01-01");
        req.setSkuCode("1029570672251");
//        req.setSkuBatchCode("20230615462637010");
//        req.setProduceDate(DateUtil.parseStartDate("2023-06-06", DateUtil.YYYY_MM_DD));
//        req.setQualityDate(DateUtil.parseStartDate("2023-07-05", DateUtil.YYYY_MM_DD));
//        req.setOccupyQuantity(1);
        req.setPageIndex(1);
        req.setPageSize(500);

        req.setSkuProductRangeInputs(Arrays.asList(
//                SkuProductRangeReq.builder()
//                        .produceDateGe(DateUtil.parseStartDate("2023-03-13", "yyyy-MM-dd"))
//                        .produceDateLe(DateUtil.parseStartDate("2023-03-15", "yyyy-MM-dd"))
//                        .build(),
                SkuProductRangeReq.builder()
                        .produceDateGe(DateUtil.parseStartDate("2023-06-01", "yyyy-MM-dd"))
                        .produceDateLe(DateUtil.parseStartDate("2023-06-30", "yyyy-MM-dd"))
                        .build()
        ));

        String a ="{\"pageIndex\":1,\"pageSize\":500,\"skuCode\":\"1029570672251\",\"warehouseNo\":1,\"skuProductRangeInputs\":[{\"produceDateLe\":\"2023-06-30\",\"produceDateGe\":\"2023-06-01\"}]}";
        req = JSONObject.parseObject(a, CabinetInventorySingleRecommendOutReq.class);
        CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> result = recommendQueryService.queryRecommendOut(req);
        System.out.println("result:" + JSONObject.toJSONString(result));
    }

    @Test
    public void testqueryBatchRecommendOut(){

        CabinetInventoryBatchRecommendOutReq req = new CabinetInventoryBatchRecommendOutReq();
        req.setTenantId(1L);
        req.setWarehouseNo(2);

        List<CabinetInventoryQueryRecommendOutDetailReq> addDetailReqList = new ArrayList<>();

        CabinetInventoryQueryRecommendOutDetailReq addDetailReq = new CabinetInventoryQueryRecommendOutDetailReq();
//        addDetailReq.setCabinetCode("Z99-01-01-01");
        addDetailReq.setSkuCode("1029025545867");
//        addDetailReq.setSkuBatchCode("20230315459935014");
//        addDetailReq.setProduceDate(DateUtil.parseStartDate("2023-03-15", DateUtil.YYYY_MM_DD));
//        addDetailReq.setQualityDate(DateUtil.parseStartDate("2023-07-14", DateUtil.YYYY_MM_DD));
        addDetailReq.setOccupyQuantity(1);

        addDetailReqList.add(addDetailReq);

        req.setRecommendDetailReqList(addDetailReqList);


        CommonResult<List<CabinetInventoryRecommendOutResp>> result = recommendQueryService.matchBatchRecommendOut(req);
        System.out.println(JSONObject.toJSONString(result));
    }
}
