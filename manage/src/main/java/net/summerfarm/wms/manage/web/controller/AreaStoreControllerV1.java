package net.summerfarm.wms.manage.web.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsPropertyDTO;
import net.summerfarm.wms.facade.inventory.SupplierWarehouseInventoryQueryFacade;
import net.summerfarm.wms.facade.inventory.dto.SupplierWarehouseInventory;
import net.summerfarm.wms.manage.model.domain.QuantityChangeRecord;
import net.summerfarm.wms.manage.model.vo.AreaStoreVO;
import net.summerfarm.wms.manage.web.enums.impl.OtherStockChangeTypeEnum;
import net.summerfarm.wms.manage.web.service.AreaStoreServiceV1;
import net.summerfarm.wms.manage.web.service.PurchasesConfigService;
import net.summerfarm.wms.manage.web.service.QuantityChangeRecordServiceV1;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: dongcheng
 * @date: 2023/11/14
 */
@Api(tags = "城市实际库存控制类")
@RestController
@RequestMapping(value = "/area-store")
public class AreaStoreControllerV1 {

    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AreaStoreServiceV1 areaStoreService;
    @Resource
    private PurchasesConfigService purchasesConfigService;

    @Resource
    private AreaStoreRepository areaStoreRepository;

    @Resource
    private QuantityChangeRecordServiceV1 quantityChangeRecordService;

    @Resource
    private SupplierWarehouseInventoryQueryFacade supplierWarehouseInventoryQueryFacade;

    public static final String KEY_ID = "lock:LockId:safeQuantity";

    /**
     * 修改安全库存
     * 需要前端替换为
     * net.summerfarm.wms.web.controller.areastore.AreaStoreController#updateSafeStockForXm
     *
     * @param updateKeys
     * @return
     */
    @ApiOperation(value = "修改安全库存",httpMethod = "POST",tags = "城市实际库存控制类")
    @RequiresPermissions(value = {"area-store:purchaser", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/safequantity", method = RequestMethod.POST)
    public AjaxResult updateSafeStockByStoreNo(AreaStoreVO updateKeys, BindingResult bindingResult) {
        // 方法禁用
        return AjaxResult.getError("当前功能已禁用，请前往新页面操作");

//        if (updateKeys == null || bindingResult == null ||
//                updateKeys.getAreaNo() == null || StringUtils.isBlank(updateKeys.getSku()) ||
//                updateKeys.getSafeQuantity() == null) {
//            throw new DefaultServiceException("参数异常！");
//        }
//
//        if (bindingResult.hasErrors()) {
//            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
//        }
//
//        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(
//                updateKeys.getAreaNo().longValue(), updateKeys.getSku());
//        if (areaStore == null){
//            return AjaxResult.getErrorWithMsg("请求仓库库存不存在");
//        }
//
//        // 屏蔽代销不入仓品的修改安全库存
//        Map<String, GoodsPropertyDTO> goodsPropertyDTOMap = goodsReadFacade
//                .mapGoodsPropertyBySkuList(Arrays.asList(updateKeys.getSku()));
//        GoodsPropertyDTO goodsPropertyDTO = goodsPropertyDTOMap.get(updateKeys.getSku());
//        if (goodsPropertyDTO != null && goodsPropertyDTO.getIsSale2PurchaseGoods()) {
//            SupplierWarehouseInventory supplierWarehouseInventory =  supplierWarehouseInventoryQueryFacade
//                    .queryQuantityByWnoAndSku(updateKeys.getAreaNo(), updateKeys.getSku());
//            // 修改数量不能超过供应商库存量
//            Integer maxChangeSafeQuantity = areaStore.getOnlineQuantity() -
//                    supplierWarehouseInventory.getQuantity();
//            Integer changeSafeQuantity = updateKeys.getSafeQuantity() - areaStore.getSafeQuantity();
//            if (changeSafeQuantity > maxChangeSafeQuantity){
//                return AjaxResult.getErrorWithMsg("代销不入仓货品最大可修改安全库存" + maxChangeSafeQuantity);
//            }
//            if (supplierWarehouseInventory.getSafeQuantity() > 0 &&
//                    updateKeys.getSafeQuantity() < supplierWarehouseInventory.getSafeQuantity()){
//                return AjaxResult.getErrorWithMsg("代销不入仓货品最小可修改安全库存" +
//                        supplierWarehouseInventory.getSafeQuantity());
//            }
//        }
//
//        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
//        if (Objects.isNull(updateKeys.getSafeQuantity())) {
//            throw new DefaultServiceException("请填写安全库存数量！");
//        }
//        if (Objects.isNull(updateKeys.getSku())) {
//            throw new DefaultServiceException("传参错误！");
//        }
//        if (Objects.isNull(updateKeys.getAreaNo())) {
//            throw new DefaultServiceException("传参错误！");
//        }
//        Long lockId = redisTemplate.opsForValue().increment(KEY_ID, 1L);
//        String key = Global.SAFE_QUANTITY + updateKeys.getAreaNo() + Global.REDIS_DELIMITER + updateKeys.getSku();
//        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(key, String.valueOf(lockId), 30, TimeUnit.SECONDS);
//        if (!tryLock) {
//            return AjaxResult.getError("已修改安全库存，勿重复提交");
//        }
//        try {
//            areaStoreService.updateSafeStockByStoreNoAndRemark(updateKeys.getSafeQuantity(), updateKeys.getSku(), updateKeys.getAreaNo(), OtherStockChangeTypeEnum.SAFE_STOCK_CHANGE, null, recordMap, updateKeys.getRemark());
//            quantityChangeRecordService.insertRecord(recordMap);
//            purchasesConfigService.msgArrival(updateKeys.getAreaNo(), updateKeys.getSku());
//        } finally {
//            redisTemplate.delete(key);
//        }
//        return AjaxResult.getOK();
    }

}
