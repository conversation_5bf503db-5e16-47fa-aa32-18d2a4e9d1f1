package net.summerfarm.wms.application.safeinventorylock.service.impl;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.safeinventorylock.assembler.WmsSafeInventoryLockAssembler;
import net.summerfarm.wms.application.safeinventorylock.input.query.WmsSafeInventoryLockQueryInput;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockQueryService;
import net.summerfarm.wms.application.safeinventorylock.vo.WmsSafeInventoryLockVO;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockQueryRepository;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 安全库存锁定表查询服务实现
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class WmsSafeInventoryLockQueryServiceImpl implements WmsSafeInventoryLockQueryService {

    @Autowired
    private WmsSafeInventoryLockQueryRepository wmsSafeInventoryLockQueryRepository;

    @Autowired
    private ProductRepository productRepository;

    @Override
    public CommonResult<PageInfo<WmsSafeInventoryLockVO>> getPage(WmsSafeInventoryLockQueryInput input) {
        if (input.getPdId() != null) {
            Map<String, Product> skuProductMap = productRepository.mapProductsByPdIdsOnlyGoods(input.getWarehouseNo().longValue(), Collections.singletonList(input.getPdId()));
            List<String> skus = new ArrayList<>(skuProductMap.keySet());
            if (CollectionUtils.isEmpty(skus)) {
                return CommonResult.ok(PageInfoConverter.toPageResp(new PageInfo<>(), WmsSafeInventoryLockAssembler::toWmsSafeInventoryLockVO));
            }
            input.setSkus(skus);
        }
        WmsSafeInventoryLockQueryParam queryParam = WmsSafeInventoryLockAssembler.toWmsSafeInventoryLockQueryParam(input);
        PageInfo<WmsSafeInventoryLockEntity> page = wmsSafeInventoryLockQueryRepository.getPage(queryParam);

        if (page == null || CollectionUtils.isEmpty(page.getList())) {
            return CommonResult.ok(PageInfoConverter.toPageResp(page, WmsSafeInventoryLockAssembler::toWmsSafeInventoryLockVO));
        }

        // 收集所有SKU
        List<String> skus = page.getList().stream()
                .map(WmsSafeInventoryLockEntity::getSku)
                .distinct()
                .collect(Collectors.toList());

        // 获取商品信息，使用第一个实体的仓库编码
        Long warehouseNo = page.getList().get(0).getWarehouseNo().longValue();
        Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(warehouseNo, skus);

        // 转换为VO并补充商品信息
        List<WmsSafeInventoryLockVO> resultList = page.getList().stream()
                .map(entity -> {
                    WmsSafeInventoryLockVO vo = WmsSafeInventoryLockAssembler.toWmsSafeInventoryLockVO(entity);
                    // 补充商品相关信息
                    Product product = productMap.get(entity.getSku());
                    if (product != null) {
                        vo.setPdName(product.getPdName());
                        vo.setSpecification(product.getSpecification());
                        vo.setPackaging(product.getPackaging());
                        vo.setCategoryType(product.getCategoryType());
                        vo.setSkuType(product.getSkuType());
                        vo.setIsDomestic(product.getIsDomestic());
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        return CommonResult.ok(PageInfoConverter.toPageResp(page, resultList));
    }

    @Override
    public WmsSafeInventoryLockEntity getById(Long id) {
        return wmsSafeInventoryLockQueryRepository.selectById(id);
    }

}
