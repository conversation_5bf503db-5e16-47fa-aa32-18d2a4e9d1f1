package net.summerfarm.wms.web.controller.outstore;

import net.summerfarm.wms.api.h5.outstore.OutStoreQueryService;
import net.summerfarm.wms.api.h5.outstore.req.OutStorePeriodAndBatchQuery;
import net.summerfarm.wms.api.h5.outstore.res.OutStorePeriodAndBatchResDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 出库任务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/out_store")
public class OutStoreController {

    @Resource
    private OutStoreQueryService outStoreQueryService;

    /**
     * 查询出库任务指定的效期或批次
     *
     * @param query q
     * @return res
     */
    @PostMapping(value = "/query/period_batch")
    public CommonResult<OutStorePeriodAndBatchResDTO> queryPeriodAndBatch(@RequestBody @Valid OutStorePeriodAndBatchQuery query){
        return CommonResult.ok(outStoreQueryService.queryPeriodAndBatch(query));
    }
}
