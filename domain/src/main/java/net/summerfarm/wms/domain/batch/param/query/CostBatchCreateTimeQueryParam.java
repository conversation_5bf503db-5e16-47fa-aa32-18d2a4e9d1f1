package net.summerfarm.wms.domain.batch.param.query;

import lombok.Data;

import java.util.List;

@Data
public class CostBatchCreateTimeQueryParam {
    /**
     * 仓库号
     */
    private Integer warehouseNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 采购批次号列表
     */
    private List<String> purchaseNoList;
    /**
     * 保质期列表
     */
    private List<Long> shelfLifeList;
    /**
     * 生产日期列表
     */
    private List<Long> produceAtList;
}
