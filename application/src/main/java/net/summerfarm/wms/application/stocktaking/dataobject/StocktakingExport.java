package net.summerfarm.wms.application.stocktaking.dataobject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StocktakingExport implements Serializable {
    private static final long serialVersionUID = 3126610124682328882L;

    @ExcelProperty("任务编号")
    Long taskId;

    @ExcelProperty("任务状态")
    String state;

    @ExcelProperty("仓库")
    String warehouseName;

    @ExcelProperty("任务类型")
    String cycle;

    @ExcelProperty("盘点维度")
    String dimension;

    @ExcelProperty("盘点方式")
    String method;

    @ExcelProperty("商品名称")
    String pdName;

    @ExcelProperty("sku")
    String sku;

    @ExcelProperty("规格")
    String specification;

    @ExcelProperty("类目")
    String category;

    @ExcelProperty("存储区域")
    String temperature;

    @ExcelProperty("包装")
    String packaging;

    @ExcelProperty("进口/国产")
    String origin;

    @ExcelProperty("商品归属")
    String goodsBelong;

    @ExcelProperty("采购批次")
    String batch;

    @ExcelProperty("库位编码")
    String cabinetCode;

    @ExcelProperty("生产日期")
    String produceAt;

    @ExcelProperty("保质期")
    String shelfLife;

    @ExcelProperty("批次库存")
    Integer batchNum;

    @ExcelProperty("盘点库存")
    Integer stocktakingNum;

    @ExcelProperty("盘盈/盘亏金额")
    String diffAmount;

    @ExcelProperty("盘点原因")
    String reason;

    @ExcelProperty("完成时间")
    String finishDate;

    @ExcelProperty("操作人")
    String operator;


}
