package net.summerfarm.wms.domain.prove.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.Entity;
import net.summerfarm.wms.domain.prove.ProveDelegate;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 证明
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Prove implements Entity {
    Long id;

    String sourceId;

    Integer type;

    String sku;

    /**
     * 证明所属信息
     */
    ProveBelong proveBelong;

    /**
     * 监管仓报告
     */
    SupervisedWarehouseProof supervisedWarehouseProof;

    /**
     * 农药残留报告
     */
    PesticideResidueReport pesticideResidueReport;

    /**
     * 核酸检测报告
     */
    NucleicAcidDetection nucleicAcidDetection;

    /**
     * 质检报告
     */
    InspectionReport inspectionReport;

    /**
     * 报关证明
     */
    CustomsDeclarationCertificate customsDeclarationCertificate;

    /**
     * 消毒证明
     */
    DisinfectionCertificate disinfectionCertificate;

    /**
     * 供应商证明是否齐全
     * 0-不齐全，1-齐全
     */
    Integer proofComplete;

    /**
     * 操作人
     */
    String operator;

    /**
     * 操作人名称
     */
    String operatorName;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * -----------------------------------------代理--------------------------------------
     **/
    ProveDelegate proveDelegate;

    LocalDate produceAt;

    LocalDate shelfLife;

    public void saveProve() {
        if (Objects.isNull(proveDelegate)) {
            return;
        }
        proveDelegate.saveProve(this);
    }

    /**
     * 获取质检报告的地址
     * @return 质检报告的地址
     */
    public String getInspectionReportUrl() {
        if (Objects.nonNull(inspectionReport)) {
            return inspectionReport.getReportUrl();
        }
        return null;
    }

    /**
     * 获取报关证明地址
     * @return 报关证明地址
     */
    public String getCustomsDeclarationCertificateReportUrl() {
        if (Objects.nonNull(customsDeclarationCertificate)) {
            return customsDeclarationCertificate.getProofUrl();
        }
        return null;
    }

    /**
     * 获取仓库监管证明地址
     * @return 仓库监管证明地址
     */
    public String getSupervisedWarehouseProofUrl() {
        if (Objects.nonNull(supervisedWarehouseProof)) {
            return supervisedWarehouseProof.getProofUrl();
        }
        return null;
    }

    /**
     * 获取核酸检测证明地址
     * @return 核酸检测证明地址
     */
    public String getNucleicAcidDetectionReportUrl() {
        if (Objects.nonNull(nucleicAcidDetection)) {
            return nucleicAcidDetection.getReportUrl();
        }
        return null;
    }

    /**
     * 获取消毒证明地址
     * @return 消毒证明地址
     */
    public String getDisinfectionCertificateReportUrl() {
        if (Objects.nonNull(disinfectionCertificate)) {
            return disinfectionCertificate.getProofUrl();
        }
        return null;
    }

}
