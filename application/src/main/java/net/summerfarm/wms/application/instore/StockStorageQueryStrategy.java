package net.summerfarm.wms.application.instore;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStoragePagePDAQuery;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStoragePageQuery;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStorageDetailPDAResDTO;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStorageDetailResDTO;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStorageListResDTO;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStoragePagePDAResDTO;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/16  15:56
 * 入库任务操作类入口
 */
public interface StockStorageQueryStrategy {

    /**
     * 获取任务类型id
     */
    Integer getTaskType();

    /**
     * 任务列表
     */
    PageInfo<StockStorageListResDTO> queryStockStorageListResList(StockStoragePageQuery storagePageQuery);

    /**
     * pda收货列表
     * @param stockStoragePagePDAQuery
     * @return
     */
    PageInfo<StockStoragePagePDAResDTO> queryListForPDA(StockStoragePagePDAQuery stockStoragePagePDAQuery);

    /**
     * 入库任务详情
     */
    StockStorageDetailResDTO queryStockStorageDetail(Long stockStorageId);

    /**
     * pda入库任务详情
     * @param stockStorageId
     * @return
     */
    StockStorageDetailPDAResDTO queryDetailForPDA(Long stockStorageId);
}
