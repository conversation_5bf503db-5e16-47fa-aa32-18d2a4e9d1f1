<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.offline.mapper.storealert.SafeStockWarningMapper">
    <!-- 结果集映射 -->
    <resultMap id="safeStockWarningDayResultMap"
               type="net.summerfarm.wms.infrastructure.offline.model.storealert.SafeStockWarning">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="warehouse_provider" property="warehouseProvider" jdbcType="VARCHAR"/>
        <result column="pd_id" property="pdId" jdbcType="NUMERIC"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="saas_sku_id" property="saasSkuId" jdbcType="NUMERIC"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="sku_tenant_id" property="skuTenantId" jdbcType="NUMERIC"/>
        <result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="stock_level_minimum" property="stockLevelMinimum" jdbcType="INTEGER"/>
        <result column="stock_level_maximum" property="stockLevelMaximum" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="day_tag" property="dayTag" jdbcType="INTEGER"/>
        <result column="use_flag" property="useFlag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="safeStockWarningDayColumns">
        t.id,
        t.create_time,
        t.update_time,
        t.warehouse_no,
        t.warehouse_name,
        t.warehouse_provider,
        t.pd_id,
        t.sku,
        t.saas_sku_id,
        t.category_id,
        t.sku_tenant_id,
        t.warehouse_tenant_id,
        t.quantity,
        t.stock_level_minimum,
        t.stock_level_maximum,
        t.status,
        t.day_tag,
        t.use_flag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseName != null and warehouseName !=''">
                AND t.warehouse_name = #{warehouseName}
            </if>
            <if test="warehouseProvider != null and warehouseProvider !=''">
                AND t.warehouse_provider = #{warehouseProvider}
            </if>
            <if test="pdId != null">
                AND t.pd_id = #{pdId}
            </if>
            <if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
            <if test="saasSkuId != null">
                AND t.saas_sku_id = #{saasSkuId}
            </if>
            <if test="categoryId != null">
                AND t.category_id = #{categoryId}
            </if>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND t.category_id IN
                <foreach collection="categoryIdList" item="category" open="(" close=")" separator=",">
                    #{category}
                </foreach>
            </if>
            <if test="skuTenantId != null">
                AND t.sku_tenant_id = #{skuTenantId}
            </if>
            <if test="warehouseTenantId != null">
                AND t.warehouse_tenant_id = #{warehouseTenantId}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
            <if test="stockLevelMinimum != null">
                AND t.stock_level_minimum = #{stockLevelMinimum}
            </if>
            <if test="stockLevelMaximum != null">
                AND t.stock_level_maximum = #{stockLevelMaximum}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="dayTag != null">
                AND t.day_tag = #{dayTag}
            </if>
            <if test="useFlag != null">
                AND t.use_flag = #{useFlag}
            </if>
        </trim>
    </sql>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.stockalert.param.query.SafeStockWarningQueryParam"
            resultType="net.summerfarm.wms.domain.stockalert.entity.SafeStockWarningEntity">
        SELECT
        t.id id,
        t.create_time createTime,
        t.update_time updateTime,
        t.warehouse_no warehouseNo,
        t.warehouse_name warehouseName,
        t.warehouse_provider warehouseProvider,
        t.pd_id pdId,
        t.sku sku,
        t.saas_sku_id saasSkuId,
        t.category_id categoryId,
        t.sku_tenant_id skuTenantId,
        t.warehouse_tenant_id warehouseTenantId,
        t.quantity quantity,
        t.stock_level_minimum stockLevelMinimum,
        t.stock_level_maximum stockLevelMaximum,
        t.status status,
        t.day_tag dayTag,
        t.use_flag useFlag
        FROM safe_stock_warning_day t
        <include refid="whereColumnBySelect"/>
        ORDER BY t.id DESC
    </select>

    <!-- 根据状态查询记录条数 -->
    <select id="countByStatus" parameterType="net.summerfarm.wms.domain.stockalert.param.query.StoreAlertCountByStatusQueryParam"
            resultType="net.summerfarm.wms.domain.stockalert.valueobject.StoreAlertCountByStatusValueObject">
        SELECT t.status, COUNT(1) AS totalCount
        FROM safe_stock_warning_day t
        <where>
            t.sku_tenant_id = #{skuTenantId} AND t.day_tag = #{dayTag}
            <if test="statusList != null and statusList.size() > 0">
                AND t.status IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="useFlag != null">
                AND t.use_flag = #{useFlag}
            </if>
        </where>
        GROUP BY t.status
    </select>

</mapper>