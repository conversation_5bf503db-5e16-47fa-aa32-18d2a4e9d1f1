package net.summerfarm.wms.api.h5.storealert.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * 存货周转预警查询
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreMaturityWarningPageQuery extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 5709775314398662797L;

    /**
     * 货品编码
     */
    private Long pdId;

    /**
     * SaaS skuId
     */
    private Long saasSkuId;

    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 储存状态，1：正常、2：临期、3：已滞库
     */
    private Integer status;

    /**
     * 异步导出的参数，查询时无需传入
     */
    private String params;

    /**
     * 用于注入租户id，前端页面上不应该传入该参数，即使传入也会被忽略
     */
    private Long tenantId;

    /**
     * 货品启用状态，0：停用、1：启用
     */
    private Integer useFlag;

}
