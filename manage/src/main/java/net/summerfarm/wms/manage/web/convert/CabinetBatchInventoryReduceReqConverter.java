package net.summerfarm.wms.manage.web.convert;

import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryReduceResp;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceDetailReqDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetBatchInventoryReduceRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Mapper
public interface CabinetBatchInventoryReduceReqConverter {

    CabinetBatchInventoryReduceReqConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryReduceReqConverter.class);

    CabinetBatchInventoryReduceReq convert(CabinetBatchInventoryReduceReqDTO req);

    CabinetBatchInventoryReduceDetailReq convertDetail(CabinetBatchInventoryReduceDetailReqDTO req);

    List<CabinetBatchInventoryReduceDetailReq> convertDetailList(List<CabinetBatchInventoryReduceDetailReqDTO> req);

    CabinetBatchInventoryReduceRespDTO convertResp(CabinetBatchInventoryReduceResp req);

}
