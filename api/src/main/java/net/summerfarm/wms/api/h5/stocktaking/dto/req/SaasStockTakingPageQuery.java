package net.summerfarm.wms.api.h5.stocktaking.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/6/28 17:59
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasStockTakingPageQuery implements Serializable {

    /**
     * 盘点开始时间
     */
    private LocalDate stockTakingStartTime;
    /**
     * 盘点结束时间
     */
    private LocalDate stockTakingEndTime;
    /**
     * 盘点任务编码
     */
    private Long stockTakingId;
    /**
     * 仓库
     */
    private Long warehouseNo;
    /**
     * 商品编码
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * saas skuId
     */
    private Long saasSkuId;
    /**
     * 创建人
     */
    private String creator;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 页号
     */
    private Integer pageIndex;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 入参查询字段
     */
    private String paramJson;

    public Integer getPageSize() {
        if (pageSize == null) {
            return 10;
        }
        if (pageSize > 100) {
            return 100;
        }
        return pageSize;
    }

    public Integer getPageIndex() {
        if (pageIndex == null || pageIndex < 1) {
            return 1;
        }
        return pageIndex;
    }

    /**
     * 获取下一天的日期，由于前端传过来的值是选定的时间
     *
     * @return 返回stockTakingEndTime的后一天
     */
    public LocalDate handlerStockTakingEndTime() {
        if (stockTakingEndTime != null) {
            return stockTakingEndTime.plusDays(1);
        }
        return null;
    }
}
