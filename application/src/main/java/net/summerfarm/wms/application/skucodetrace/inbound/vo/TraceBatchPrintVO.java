package net.summerfarm.wms.application.skucodetrace.inbound.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 批量打印返回<br/>
 * date: 2024/8/7 15:01<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceBatchPrintVO implements Serializable {

    /**
     * 入库任务次序编号-打印编码
     */
    private Integer stockTaskStorageSeq;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 批次唯一溯源码
     */
    private String skuBatchTraceCode;


    /**
     * sku编码
     */
    private String sku;

    /**
     * 货品spu id
     */
    private Long pdId;

    /**
     * 货品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批次日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd")
    private LocalDate batchDate;

    /**
     * SKU计划签收数量
     */
    private Integer skuPlanReceiptCount;

    /**
     * SKU当前商品次序
     */
    private Integer skuCurrentSeq;

    /**
     * 门店商品总数
     */
    private Integer merchantSkuTotalNum;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 线路编码
     */
    private String pathCode;

    /**
     * 点位在路线上的顺序
     */
    private Integer pathSequence;

    /**
     * 入库任务id
     */
    private Long stockTaskStorageId;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重人
     */
    private String weightPerson;

    /**
     * 称重完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weightTime;

    /**
     * 状态
     10：订单待分配
     20：路线待分配
     30：待称重
     40：已称重
     */
    private Integer state;

    /**
     * create time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * OFC采购供应单编号
     */
    private String psoNo;
    /**
     * 订单单号
     */
    private String orderNo;
    /**
     * 履约单号
     */
    private Long fulfillmentNo;

    /**
     * 城配仓no
     */
    private Integer storeNo;


    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     * 注意：鲜沐温区枚举和SAAS枚举值不同
     * {@link  net.summerfarm.goods.client.enums.StorageLocationEnum }
     */
    private Integer storageLocation;

    /**
     * 最近是否有配送过
     */
    private Boolean deliveredRecentlyOption;

    /**
     * 标签类型0POP，1POPT2
     */
    private Integer labelType;
}
