package net.summerfarm.wms.infrastructure.converter.qms;


import net.summerfarm.wms.domain.qms.entity.QmsInspectionTaskSnapshotEntity;
import net.summerfarm.wms.domain.qms.param.command.QmsInspectionTaskSnapshotCommandParam;
import net.summerfarm.wms.infrastructure.dao.qms.dataobject.QmsInspectionTaskSnapshot;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-04-18 11:01:07
 * @version 1.0
 *
 */
public class QmsInspectionTaskSnapshotConverter {

    private QmsInspectionTaskSnapshotConverter() {
        // 无需实现
    }




    public static List<QmsInspectionTaskSnapshotEntity> toQmsInspectionTaskSnapshotEntityList(List<QmsInspectionTaskSnapshot> qmsInspectionTaskSnapshotList) {
        if (qmsInspectionTaskSnapshotList == null) {
            return Collections.emptyList();
        }
        List<QmsInspectionTaskSnapshotEntity> qmsInspectionTaskSnapshotEntityList = new ArrayList<>();
        for (QmsInspectionTaskSnapshot qmsInspectionTaskSnapshot : qmsInspectionTaskSnapshotList) {
            qmsInspectionTaskSnapshotEntityList.add(toQmsInspectionTaskSnapshotEntity(qmsInspectionTaskSnapshot));
        }
        return qmsInspectionTaskSnapshotEntityList;
}


    public static QmsInspectionTaskSnapshotEntity toQmsInspectionTaskSnapshotEntity(QmsInspectionTaskSnapshot qmsInspectionTaskSnapshot) {
        if (qmsInspectionTaskSnapshot == null) {
             return null;
        }
        QmsInspectionTaskSnapshotEntity qmsInspectionTaskSnapshotEntity = new QmsInspectionTaskSnapshotEntity();
        qmsInspectionTaskSnapshotEntity.setId(qmsInspectionTaskSnapshot.getId());
        qmsInspectionTaskSnapshotEntity.setCreateTime(qmsInspectionTaskSnapshot.getCreateTime());
        qmsInspectionTaskSnapshotEntity.setUpdateTime(qmsInspectionTaskSnapshot.getUpdateTime());
        qmsInspectionTaskSnapshotEntity.setQmsInspectionTaskId(qmsInspectionTaskSnapshot.getQmsInspectionTaskId());
        qmsInspectionTaskSnapshotEntity.setInspRuleSnapshotType(qmsInspectionTaskSnapshot.getInspRuleSnapshotType());
        qmsInspectionTaskSnapshotEntity.setInspRuleSnapshot(qmsInspectionTaskSnapshot.getInspRuleSnapshot());
        qmsInspectionTaskSnapshotEntity.setInspScaleSnapshotType(qmsInspectionTaskSnapshot.getInspScaleSnapshotType());
        qmsInspectionTaskSnapshotEntity.setInspScaleSnapshot(qmsInspectionTaskSnapshot.getInspScaleSnapshot());
        return qmsInspectionTaskSnapshotEntity;
    }








    public static QmsInspectionTaskSnapshot toQmsInspectionTaskSnapshot(QmsInspectionTaskSnapshotCommandParam param) {
        if (param == null) {
            return null;
        }
        QmsInspectionTaskSnapshot qmsInspectionTaskSnapshot = new QmsInspectionTaskSnapshot();
        qmsInspectionTaskSnapshot.setId(param.getId());
        qmsInspectionTaskSnapshot.setCreateTime(param.getCreateTime());
        qmsInspectionTaskSnapshot.setUpdateTime(param.getUpdateTime());
        qmsInspectionTaskSnapshot.setQmsInspectionTaskId(param.getQmsInspectionTaskId());
        qmsInspectionTaskSnapshot.setInspRuleSnapshotType(param.getInspRuleSnapshotType());
        qmsInspectionTaskSnapshot.setInspRuleSnapshot(param.getInspRuleSnapshot());
        qmsInspectionTaskSnapshot.setInspScaleSnapshotType(param.getInspScaleSnapshotType());
        qmsInspectionTaskSnapshot.setInspScaleSnapshot(param.getInspScaleSnapshot());
        return qmsInspectionTaskSnapshot;
    }
}
