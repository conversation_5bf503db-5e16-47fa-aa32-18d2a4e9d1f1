package net.summerfarm.wms.domain.mission.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.Entity;
import net.summerfarm.wms.domain.base.ValueObject;

import java.time.LocalDate;

/**
 * 任务操作人信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionOperator implements Entity {
    /**
     * 任务编号
     */
    String missionNo;
    /**
     * 操作人状态
     */
    Integer state;
    /**
     * 操作人名称
     */
    String operatorName;
    /**
     * 操作人id
     */
    String operatorId;
    /**
     * 取消时间
     */
    Long cancelTime;
    /**
     * 创建时间
     */
    LocalDate createTime;
}
