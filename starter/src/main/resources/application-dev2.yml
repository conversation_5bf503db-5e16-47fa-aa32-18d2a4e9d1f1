# mybatis sql 打印
#logging:
#  level:
#    net.summerfarm.wms.infrastructure.dao: debug
#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: false
  supportMethodsArguments: true
  params: count=countSql
  ms:
    maximumSize: 200
    initialCapacity: 100
    expireAfterWrite: 10000

redis:
  host: test-redis.summerfarm.net
  jedis:
    pool:
      max-active: 40
      max-idle: 20
      max-wait: 5000
      min-idle: 5
  password: xianmu619
  port: 6379
  timeout: 6000
  database: 0
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://dev2h5.summerfarm.net
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C00
  accessKeySecret: zNwstNpZuurzzPHike39bEl7uUat0y00
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619


server:
  tomcat:
    accept-count: 500
    max-connections: 200
    max-threads: 2
    min-spare-threads: 2
  servlet:
    session:
      timeout: 3600
    encoding:
      charset: UTF-8
    context-path: /summerfarm-wms
  port: 80

mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: dev2
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: dev2

spring:
  application:
    id: summerfarm-wms
    name: summerfarm-wms
  servlet:
    multipart:
      max-file-size: 5MB
  datasource:
    druid:
      asyncInit: true
      dbType: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.jdbc.Driver
      initialSize: 8
      maxActive: 160
      maxWait: 6000
      minIdle: 8
      offline:
        password: xianmu619
        url: **************************************************************************************************
        username: dev2
      password: xianmu619
      testWhileIdle: true
      url: ******************************************************************************************
      username: dev2
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 4 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 4 # 连接池中的最大空闲连接
        min-idle: 4 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 4 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 4 # 连接池中的最大空闲连接
        min-idle: 4 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: d470595b-6520-4833-a158-239340a08eb2
    groupId: summerfarm-wms
    appKey: S/55ae25gyYDP+HZxvZdug==

xm:
  downloadcenter:
    consumer_group_suffix: wms
    consumer_tag_suffix: wms
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 800ed4d6-a4fd-4345-86dd-8a4cc70c9bda

saas:
  domain: https://dev2mall.cosfo.cn
  host: https://dev2mall.cosfo.cn