package net.summerfarm.wms.facade.msg.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.manage.client.msg.enums.MsgType;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MsgAdminFacadeDTO implements Serializable {
    private static final long serialVersionUID = 2655045813701069513L;
    /**
     * 模板id
     */
    @NotNull
    Integer msgTemplateId;
    /**
     * 接收人
     */
    AdminFacadeDTO adminDTO;

    /**
     * 主信息
     */
    String mainInfo;

    String keyword;

    /**
     * 文本
     */
    String content;

    /**
     * 文本附件
     */
    JSONObject jsonObject;

    /**
     * 短信类型
     *
     * @see MsgType
     */
    Integer msgType;
}
