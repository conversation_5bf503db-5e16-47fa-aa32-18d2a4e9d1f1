package net.summerfarm.wms.facade.process;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.ProcessEnum;
import net.summerfarm.common.client.provider.process.ProcessCommandProvider;
import net.summerfarm.common.client.provider.process.ProcessQueryProvider;
import net.summerfarm.common.client.req.process.ProcessCreateInstanceRequest;
import net.summerfarm.common.client.req.process.ProcessFormRequest;
import net.summerfarm.common.client.req.process.ProcessNodeRequest;
import net.summerfarm.common.client.req.process.ProcessQueryInstanceRequest;
import net.summerfarm.common.client.resp.process.ProcessCreateInstanceResponse;
import net.summerfarm.common.client.resp.process.ProcessInstanceDetailResponse;
import net.summerfarm.common.client.resp.process.ProcessOperateRecordResponse;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.client.ding.DingProcessProvider;
import net.summerfarm.manage.client.ding.dto.ProcessDetailResDTO;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.DubboResponseUtil;
import net.summerfarm.wms.facade.process.converter.ProcessConvert;
import net.summerfarm.wms.facade.process.dto.ProcessCreateFacadeDTO;
import net.summerfarm.wms.facade.process.dto.ProcessDetailFacadeDTO;
import net.summerfarm.wms.facade.process.dto.ProcessDetailFacadeResDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 飞书审批流内容
 *
 * @author: dongcheng
 * @date: 2023/8/10
 */
@Component
@Slf4j
public class ProcessFacade {

    @DubboReference
    private ProcessCommandProvider processCommandProvider;
    @DubboReference
    private ProcessQueryProvider processQueryProvider;
    @DubboReference
    private DingProcessProvider dingProcessProvider;

    /**
     * 创建审批流信息
     *
     * @param processCreateFacadeDTO 审批流内容
     */
    public void createProcess(ProcessCreateFacadeDTO processCreateFacadeDTO) {
        if (processCreateFacadeDTO == null) {
            log.info("创建审批流信息 createProcess: 入参内容为空");
            return;
        }
        if (Objects.isNull(processCreateFacadeDTO.getBizId())
                || Objects.isNull(processCreateFacadeDTO.getBizType())
                || Objects.isNull(processCreateFacadeDTO.getAdminId())
                || CollectionUtils.isEmpty(processCreateFacadeDTO.getDingdingForms())) {
            log.info("审批流请求参数错误:" + JSONUtil.toJsonStr(processCreateFacadeDTO));
            return;
        }
        ProcessCreateInstanceRequest request = new ProcessCreateInstanceRequest();
        request.setProcessType(processCreateFacadeDTO.getBizType());
        request.setBizId(processCreateFacadeDTO.getBizId());
        request.setAdminId(processCreateFacadeDTO.getAdminId().toString());
        if (CollectionUtils.isNotEmpty(processCreateFacadeDTO.getDingdingForms())) {
            request.setFormList(processCreateFacadeDTO.getDingdingForms().stream().map(data -> {
                ProcessFormRequest formRequest = new ProcessFormRequest();
                formRequest.setFormName(data.getFormName());
                formRequest.setFormValue(data.getFormValue());
                return formRequest;
            }).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(processCreateFacadeDTO.getCcList())) {
            String ccList = processCreateFacadeDTO.getCcList();
            List<String> asList = Arrays.asList(ccList.split(","));
            ProcessNodeRequest nodeRequest = new ProcessNodeRequest();
            nodeRequest.setAdminIdList(asList);
            request.setCcAdminList(Lists.newArrayList(nodeRequest));
        }
        if (StringUtils.isNotBlank(processCreateFacadeDTO.getApprovers())) {
            List<String> strings = Arrays.asList(processCreateFacadeDTO.getApprovers().split(","));
            ProcessNodeRequest nodeRequest = new ProcessNodeRequest();
            nodeRequest.setAdminIdList(strings);
            request.setApproversAdminList(Lists.newArrayList(nodeRequest));
        }
        request.setProcessAccountType(ProcessEnum.ProcessAccountEnum.XIAN_MU.name());
        DubboResponse<ProcessCreateInstanceResponse> dubboResponse = processCommandProvider.createInstanceCompatibility(request);
        DubboResponseUtil.isSuccess(dubboResponse);
        log.info(JSONUtil.toJsonStr(dubboResponse));
    }

    public String createInstance(ProcessCreateInstanceRequest request){
        DubboResponse<ProcessCreateInstanceResponse> instanceCompatibility = processCommandProvider.createInstanceCompatibility(request);
        if(!instanceCompatibility.isSuccess()){
            throw new BizException("创建审批失败");
        }
        ProcessCreateInstanceResponse data = instanceCompatibility.getData();

        return data.getPlatformInstanceId();

    }

    /**
     * 查询审批流信息内容
     *
     * @param processDetailFacadeDTO 查询审批流条件
     * @return 返回查询到的审批流内容
     */
    public List<ProcessDetailFacadeResDTO> processDetail(ProcessDetailFacadeDTO processDetailFacadeDTO) {
        if (processDetailFacadeDTO == null) {
            log.info("审批流信息查询 processDetail: 入参内容为空");
            return Lists.newArrayList();
        }
        if (Objects.isNull(processDetailFacadeDTO.getBizId())
                || Objects.isNull(processDetailFacadeDTO.getBizType())) {
            log.info("审批流信息查询请求参数错误 processDetail:" + JSONUtil.toJsonStr(processDetailFacadeDTO));
            return Lists.newArrayList();
        }
        ProcessQueryInstanceRequest processQueryInstanceRequest = new ProcessQueryInstanceRequest();
        processQueryInstanceRequest.setBizId(processDetailFacadeDTO.getBizId());
        processQueryInstanceRequest.setProcessType(processDetailFacadeDTO.getBizType());
        processQueryInstanceRequest.setHaveQueryOperateRecord(false);
        // 第一遍查询的只是审批类型 判断是否是飞书审批
        DubboResponse<ProcessInstanceDetailResponse> processDetail = processQueryProvider.queryProcessDetail(processQueryInstanceRequest);
        if (!processDetail.isSuccess()) {
            log.info("查询审批流信息类型失败req:" + JSONUtil.toJsonStr(processQueryInstanceRequest));
            log.info("查询审批流信息类型失败res:" + processDetail.getMsg());
            return Lists.newArrayList();
        }
        ProcessInstanceDetailResponse data = processDetail.getData();
        String platformType = data.getPlatformType();
        if (!ProcessEnum.ProcessPlatformEnum.FEI_SHU.name().equals(platformType)) {
            DubboResponse<List<ProcessDetailResDTO>> dingProcessDetail =
                    dingProcessProvider.getProcessDetail(ProcessConvert.INSTANCE.convert(processDetailFacadeDTO));
            if (!processDetail.isSuccess()) {
                log.info("查询钉钉审批流失败req:");
                return Lists.newArrayList();
            }
            List<ProcessDetailResDTO> processDetailResList = dingProcessDetail.getData();
            return processDetailResList.stream().map(ProcessConvert.INSTANCE::convert).collect(Collectors.toList());
        }
        // 查询飞书审批内容
        processQueryInstanceRequest.setHaveQueryOperateRecord(true);
        processDetail = processQueryProvider.queryProcessDetail(processQueryInstanceRequest);
        if (!processDetail.isSuccess()) {
            log.info("查询审批流信息失败req:" + JSONUtil.toJsonStr(processQueryInstanceRequest));
            log.info("查询审批流信息失败res:" + processDetail.getMsg());
            return Lists.newArrayList();
        }
        List<ProcessOperateRecordResponse> responseList = processDetail.getData().getOperateRecordResponseList();
        if (CollectionUtils.isEmpty(responseList)) {
            return Lists.newArrayList();
        }
        // 返回审批详情
        return processDetail.getData().getOperateRecordResponseList().stream().map(it -> ProcessDetailFacadeResDTO.builder()
                .name(it.getAdminName())
                .auditResult(it.getAuditResult())
                .auditRemark(it.getAuditRemark())
                .auditAt(it.getAuditAt() == null ? "" : DateUtil.formatDate(it.getAuditAt()))
                .isAuditor(it.getIsAuditor())
                .build()
        ).collect(Collectors.toList());
    }

}
