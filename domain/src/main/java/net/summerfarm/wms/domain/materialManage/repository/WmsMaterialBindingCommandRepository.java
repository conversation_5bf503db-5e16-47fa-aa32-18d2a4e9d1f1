package net.summerfarm.wms.domain.materialManage.repository;


import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialBindingCommandRepository {

    WmsMaterialBindingEntity insertSelective(WmsMaterialBindingCommandParam param);

    int updateSelectiveById(WmsMaterialBindingCommandParam param);

    int remove(Long id);

}