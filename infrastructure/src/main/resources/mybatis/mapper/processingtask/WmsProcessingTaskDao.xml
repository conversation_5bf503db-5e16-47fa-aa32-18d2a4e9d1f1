<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingTaskDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskDO" id="WmsProcessingTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="processingTaskCode" column="processing_task_code" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="finishRemark" column="finish_remark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingTaskMap">
        select
          id, warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task
        where id = #{id}
    </select>

    <!--根据加工任务编码查询加工任务-->
    <select id="queryByProcessingTaskCode" resultMap="WmsProcessingTaskMap">
        select
          id, warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task
        where processing_task_code = #{processingTaskCode}
    </select>

    <!--查询指定行数据-->
    <select id="queryPageByLimit" resultType="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskDO">
        SELECT
            t.id id,
            t.warehouse_no warehouseNo,
            t.processing_task_code processingTaskCode,
            t.type type,
            t.status status,
            t.finish_time finishTime,
            t.finish_remark finishRemark,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime,
            t.delete_flag deleteFlag
        FROM wms_processing_task t
        <where>
            <if test="warehouseNo != null">
                and t.warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and t.processing_task_code = #{processingTaskCode}
            </if>
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="typeList != null">
                <if test = "typeList.size() > 0">
                    and t.type in
                    <foreach collection="typeList" item="type1" open="(" close=")" separator=",">
                        #{type1}
                    </foreach>
                </if>
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="processingTaskCodeList != null">
                <if test = "processingTaskCodeList.size() > 0">
                    and t.processing_task_code in
                    <foreach collection="processingTaskCodeList" item="taskCode" open="(" close=")" separator=",">
                        #{taskCode}
                    </foreach>
                </if>
                <if test = "processingTaskCodeList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="productSkuCode!= null or productSkuName != null">
                and t.processing_task_code in (
                    select processing_task_code
                    from wms_processing_task_product
                    <where>
                        <if test="productSkuCode!= null">
                            and `product_sku_code` = #{productSkuCode}
                        </if>
                        <if test="productSkuName!= null">
                            and `product_sku_name` = #{productSkuName}
                        </if>
                    </where>
                )
            </if>
            <if test="materialSkuCode!= null or materialSkuName != null">
                and t.processing_task_code in (
                    select processing_task_code
                    from wms_processing_task_material
                    <where>
                        <if test="materialSkuCode!= null">
                            and `material_sku_code` = #{materialSkuCode}
                        </if>
                        <if test="materialSkuName!= null">
                            and `material_sku_name` = #{materialSkuName}
                        </if>
                    </where>
                )
            </if>
        </where>
        order by t.id desc
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsProcessingTaskMap">
        select
          id, warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="finishTime != null">
                and finish_time = #{finishTime}
            </if>
            <if test="finishRemark != null and finishRemark != ''">
                and finish_remark = #{finishRemark}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="finishTime != null">
                and finish_time = #{finishTime}
            </if>
            <if test="finishRemark != null and finishRemark != ''">
                and finish_remark = #{finishRemark}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task(warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag)
        values (#{warehouseNo}, #{processingTaskCode}, #{type}, #{status}, #{finishTime}, #{finishRemark}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task(warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.type}, #{entity.status}, #{entity.finishTime}, #{entity.finishRemark}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task(warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.type}, #{entity.status}, #{entity.finishTime}, #{entity.finishRemark}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        processing_task_code = values(processing_task_code),
        type = values(type),
        status = values(status),
        finish_time = values(finish_time),
        finish_remark = values(finish_remark),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_task
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                processing_task_code = #{processingTaskCode},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime},
            </if>
            <if test="finishRemark != null and finishRemark != ''">
                finish_remark = #{finishRemark},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_task where id = #{id}
    </delete>

    <select id="queryByTaskCodeList" resultMap="WmsProcessingTaskMap">
        select
            id, warehouse_no, processing_task_code, type, status, finish_time, finish_remark, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task
        <where>
            <if test="taskCodeList != null">
                <if test = "taskCodeList.size() > 0">
                    and processing_task_code in
                    <foreach collection="taskCodeList" item="taskCode1" open="(" close=")" separator=",">
                        #{taskCode1}
                    </foreach>
                </if>
                <if test = "taskCodeList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="taskCodeList == null">
                and 1 = 2
            </if>
        </where>
    </select>

    <update id="processingTask">
        update wms_processing_task
        <set>
            status = 2,
            updater = #{updater},
            update_time = now()
        </set>
        where processing_task_code = #{processingTaskCode}
        and status != 1
    </update>


    <update id="finishTask">
        update wms_processing_task
        <set>
            status = 1,
            finish_remark = #{remark},
            finish_time = now(),
            updater = #{updater},
            update_time = now()
        </set>
        where processing_task_code = #{processingTaskCode}
    </update>

    <select id="countUnfinishedProcessingTask" resultType="java.lang.Long">
        select count(*)
        from wms_processing_task
        where warehouse_no = #{warehouseNo}
        and status in (0, 2)
    </select>


    <select id="queryTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
        SELECT
            COUNT(DISTINCT wpt.processing_task_code) AS notFinishTaskNum,
            COUNT(DISTINCT CASE WHEN NOW() >= DATE(wpt.create_time) + INTERVAL 1 DAY THEN wpt.processing_task_code
            ELSE NULL
            END) AS notFinishInTimeTaskNum
        FROM wms_processing_task wpt
        WHERE wpt.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wpt.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wpt.status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        AND wpt.warehouse_no = #{warehouseNo}
    </select>
    <select id="queryTaskPanelNotFinishTask" resultType="java.lang.String">
        SELECT DISTINCT wpt.processing_task_code
        FROM wms_processing_task wpt
        WHERE wpt.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wpt.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wpt.status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        AND wpt.warehouse_no = #{warehouseNo}
    </select>
    <select id="queryTaskPanelNotFinishInTimeTask" resultType="java.lang.String">
        SELECT DISTINCT wpt.processing_task_code AS taskCode
        FROM wms_processing_task wpt
        WHERE wpt.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wpt.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wpt.status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        AND wpt.warehouse_no = #{warehouseNo}
        AND NOW() >= DATE(wpt.create_time) + INTERVAL 1 DAY
    </select>

</mapper>

