package net.summerfarm.wms.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/5
 */
@Data
public class OrderOccupyBySpecifyWarehouseReqDTO implements Serializable {

    private static final long serialVersionUID = 21874782071945487L;

    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单子编号，分段调度库存场景使用（省心送）
     */
    private String orderSubNo;

    /**
     * 订单类别
     */
    private String orderType;

    /**
     * 操作单号
     */
    private String operatorNo;

    /**
     * 幂等单号
     */
    private String idempotentNo;

    /**
     * 仓库编码
     */
    private Long warehouseNo;

    /**
     * 订单占用明细
     */
    private List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS;

    /**
     * 操作人名称
     */
    private String operatorName;
}
