<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.safeinventorylock.WmsSafeInventoryLockMapper">

    <!-- 结果集映射 -->
    <resultMap id="wmsSafeInventoryLockResultMap" type="net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="produce_date" property="produceDate" jdbcType="DATE"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="cabinet_code" property="cabinetCode" jdbcType="VARCHAR"/>
        <result column="lock_no" property="lockNo" jdbcType="VARCHAR"/>
        <result column="init_quantity" property="initQuantity" jdbcType="INTEGER"/>
        <result column="lock_quantity" property="lockQuantity" jdbcType="INTEGER"/>
        <result column="lock_type" property="lockType" jdbcType="INTEGER"/>
        <result column="lock_status" property="lockStatus" jdbcType="INTEGER"/>
        <result column="lock_reason" property="lockReason" jdbcType="VARCHAR"/>
        <result column="create_operator" property="createOperator" jdbcType="VARCHAR"/>
        <result column="update_operator" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
        <result column="create_operator_id" property="createOperatorId" jdbcType="NUMERIC"/>
        <result column="update_operator_id" property="updateOperatorId" jdbcType="NUMERIC"/>
        <result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, warehouse_no, sku, batch_no, produce_date, quality_date,
        cabinet_code, lock_no, init_quantity, lock_quantity, lock_type, lock_status, lock_reason,
        create_operator, update_operator, tenant_id, create_operator_id, update_operator_id, warehouse_tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="whereColumnBySelect">
        <where>
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseNos != null and warehouseNos.size() > 0">
                AND t.warehouse_no IN
                <foreach collection="warehouseNos" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sku != null and sku != ''">
                AND t.sku = #{sku}
            </if>
            <if test="skus != null and skus.size() > 0">
                AND t.sku IN
                <foreach collection="skus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="batchNo != null and batchNo != ''">
                AND t.batch_no = #{batchNo}
            </if>
            <if test="produceDate != null">
                AND t.produce_date = #{produceDate}
            </if>
            <if test="qualityDate != null">
                AND t.quality_date = #{qualityDate}
            </if>
            <if test="cabinetCode != null and cabinetCode != ''">
                AND t.cabinet_code = #{cabinetCode}
            </if>
            <if test="lockNo != null and lockNo != ''">
                AND t.lock_no = #{lockNo}
            </if>
            <if test="lockNos != null and lockNos.size() > 0">
                AND t.lock_no IN
                <foreach collection="lockNos" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lockType != null">
                AND t.lock_type = #{lockType}
            </if>
            <if test="lockTypes != null and lockTypes.size() > 0">
                AND t.lock_type IN
                <foreach collection="lockTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lockStatus != null">
                AND t.lock_status = #{lockStatus}
            </if>
            <if test="lockStatuses != null and lockStatuses.size() > 0">
                AND t.lock_status IN
                <foreach collection="lockStatuses" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="lockReason != null and lockReason != ''">
                AND t.lock_reason LIKE CONCAT('%', #{lockReason}, '%')
            </if>
            <if test="createOperator != null and createOperator != ''">
                AND t.create_operator = #{createOperator}
            </if>
            <if test="updateOperator != null and updateOperator != ''">
                AND t.update_operator = #{updateOperator}
            </if>
            <if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
            <if test="createTimeStart != null">
                AND t.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="updateTimeStart != null">
                AND t.update_time >= #{updateTimeStart}
            </if>
            <if test="updateTimeEnd != null">
                AND t.update_time &lt;= #{updateTimeEnd}
            </if>
            <if test="createOperatorId != null">
                AND t.create_operator_id = #{createOperatorId}
            </if>
            <if test="updateOperatorId != null">
                AND t.update_operator_id = #{updateOperatorId}
            </if>
            <if test="warehouseTenantId != null">
                AND t.warehouse_tenant_id = #{warehouseTenantId}
            </if>
        </where>
    </sql>

    <!-- 插入非空字段 -->
    <insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wms_safe_inventory_lock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="produceDate != null">
                produce_date,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="cabinetCode != null">
                cabinet_code,
            </if>
            <if test="lockNo != null">
                lock_no,
            </if>
            <if test="initQuantity != null">
                init_quantity,
            </if>
            <if test="lockQuantity != null">
                lock_quantity,
            </if>
            <if test="lockType != null">
                lock_type,
            </if>
            <if test="lockStatus != null">
                lock_status,
            </if>
            <if test="lockReason != null">
                lock_reason,
            </if>
            <if test="createOperator != null">
                create_operator,
            </if>
            <if test="updateOperator != null">
                update_operator,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="createOperatorId != null">
                create_operator_id,
            </if>
            <if test="updateOperatorId != null">
                update_operator_id,
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="batchNo != null">
                #{batchNo},
            </if>
            <if test="produceDate != null">
                #{produceDate},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="cabinetCode != null">
                #{cabinetCode},
            </if>
            <if test="lockNo != null">
                #{lockNo},
            </if>
            <if test="initQuantity != null">
                #{initQuantity},
            </if>
            <if test="lockQuantity != null">
                #{lockQuantity},
            </if>
            <if test="lockType != null">
                #{lockType},
            </if>
            <if test="lockStatus != null">
                #{lockStatus},
            </if>
            <if test="lockReason != null">
                #{lockReason},
            </if>
            <if test="createOperator != null">
                #{createOperator},
            </if>
            <if test="updateOperator != null">
                #{updateOperator},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="createOperatorId != null">
                #{createOperatorId},
            </if>
            <if test="updateOperatorId != null">
                #{updateOperatorId},
            </if>
            <if test="warehouseTenantId != null">
                #{warehouseTenantId},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新非空字段 -->
    <update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock">
        UPDATE wms_safe_inventory_lock
        <set>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="sku != null">
                sku = #{sku},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo},
            </if>
            <if test="produceDate != null">
                produce_date = #{produceDate},
            </if>
            <if test="qualityDate != null">
                quality_date = #{qualityDate},
            </if>
            <if test="cabinetCode != null">
                cabinet_code = #{cabinetCode},
            </if>
            <if test="lockNo != null">
                lock_no = #{lockNo},
            </if>
            <if test="initQuantity != null">
                init_quantity = #{initQuantity},
            </if>
            <if test="lockQuantity != null">
                lock_quantity = #{lockQuantity},
            </if>
            <if test="lockType != null">
                lock_type = #{lockType},
            </if>
            <if test="lockStatus != null">
                lock_status = #{lockStatus},
            </if>
            <if test="lockReason != null">
                lock_reason = #{lockReason},
            </if>
            <if test="updateOperator != null">
                update_operator = #{updateOperator},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="updateOperatorId != null">
                update_operator_id = #{updateOperatorId},
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id = #{warehouseTenantId},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除 -->
    <delete id="remove" parameterType="java.lang.Long">
        DELETE FROM wms_safe_inventory_lock WHERE id = #{id}
    </delete>

    <!-- 根据主键查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE id = #{id}
    </select>

    <!-- 根据条件查询列表 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam" 
            resultType="net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity">
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.warehouse_no warehouseNo,
            t.sku sku,
            t.batch_no batchNo,
            t.produce_date produceDate,
            t.quality_date qualityDate,
            t.cabinet_code cabinetCode,
            t.lock_no lockNo,
            t.init_quantity initQuantity,
            t.lock_quantity lockQuantity,
            t.lock_type lockType,
            t.lock_status lockStatus,
            t.lock_reason lockReason,
            t.create_operator createOperator,
            t.update_operator updateOperator,
            t.tenant_id tenantId,
            t.create_operator_id createOperatorId,
            t.update_operator_id updateOperatorId,
            t.warehouse_tenant_id warehouseTenantId
        FROM wms_safe_inventory_lock t
        <include refid="whereColumnBySelect"/>
        ORDER BY t.id DESC
    </select>

    <!-- 分页查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam" 
            resultType="net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity">
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.warehouse_no warehouseNo,
            t.sku sku,
            t.batch_no batchNo,
            t.produce_date produceDate,
            t.quality_date qualityDate,
            t.cabinet_code cabinetCode,
            t.lock_no lockNo,
            t.init_quantity initQuantity,
            t.lock_quantity lockQuantity,
            t.lock_type lockType,
            t.lock_status lockStatus,
            t.lock_reason lockReason,
            t.create_operator createOperator,
            t.update_operator updateOperator,
            t.tenant_id tenantId,
            t.create_operator_id createOperatorId,
            t.update_operator_id updateOperatorId,
            t.warehouse_tenant_id warehouseTenantId
        FROM wms_safe_inventory_lock t
        <include refid="whereColumnBySelect"/>
        ORDER BY t.id DESC
    </select>

    <!-- 根据锁定编号查询 -->
    <select id="selectByLockNo" parameterType="java.lang.String" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE lock_no = #{lockNo}
    </select>

    <!-- 根据仓库编码和SKU查询锁定记录 -->
    <select id="selectByWarehouseNoAndSku" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE warehouse_no = #{warehouseNo} AND sku = #{sku}
        ORDER BY id DESC
    </select>

    <!-- 根据仓库编码、SKU和锁定状态查询锁定记录 -->
    <select id="selectByWarehouseNoAndSkuAndLockStatus" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE warehouse_no = #{warehouseNo} AND sku = #{sku} AND lock_status = #{lockStatus}
        ORDER BY id DESC
    </select>

    <!-- 根据锁定状态查询锁定记录 -->
    <select id="selectByLockStatus" parameterType="java.lang.Integer" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE lock_status = #{lockStatus}
        ORDER BY id DESC
    </select>

    <!-- 根据仓库编码、锁定状态查询锁定记录 -->
    <select id="selectByLockStatusAndWarehouseNo" resultMap="wmsSafeInventoryLockResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM wms_safe_inventory_lock
        WHERE lock_status = #{lockStatus} AND warehouse_no = #{warehouseNo}
        ORDER BY id DESC
    </select>

    <!-- 根据锁定状态查询仓库编码 -->
    <select id="selectWarehouseNoByLockStatusAndWarehouseTenantId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT DISTINCT warehouse_no
        FROM wms_safe_inventory_lock
        WHERE lock_status = #{lockStatus}
        AND warehouse_tenant_id = #{warehouseTenantId}
    </select>

    <!-- 根据id更新锁定状态 -->
    <update id="updateLockStatusById">
        UPDATE wms_safe_inventory_lock
        SET lock_status = #{lockStatus}
        WHERE id = #{id}
    </update>

    <!-- 根据id更新锁定数量 -->
    <update id="unlockQuantityById">
        UPDATE wms_safe_inventory_lock
        SET lock_quantity = lock_quantity - #{unlockQuantity}, update_operator = #{updateOperator}
        WHERE id = #{id} AND lock_quantity = #{oldLockQuantity}
    </update>

</mapper>
