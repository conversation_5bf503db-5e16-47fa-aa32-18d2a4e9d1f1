package net.summerfarm.wms.application.stockdamage.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.stockdamage.StockDamageCommandService;
import net.summerfarm.wms.application.external.event.WarehouseExternalRouteStockDamageEvent;
import net.summerfarm.wms.application.external.factory.WarehouseExternalRouteCommandFactory;
import net.summerfarm.wms.application.external.input.WarehouseExternalRouteCommandInput;
import net.summerfarm.wms.application.external.service.WarehouseExternalRouteCommandService;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.BooleanEnum;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalRouteQueryRepository;
import net.summerfarm.wms.domain.outstore.domainobject.WmsDamageStockItem;
import net.summerfarm.wms.domain.outstore.repository.WmsDamageStockItemRepository;
import net.summerfarm.wms.openapi.stockdamage.xm.req.StockDamageCancelReq;
import net.summerfarm.wms.openapi.stockdamage.xm.req.StockDamageCommitReq;
import net.summerfarm.wms.openapi.stockdamage.xm.req.StockDamageCreateNoticeReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/24
 */
@Service
@Slf4j
public class StockDamageCommandServiceImpl implements StockDamageCommandService {

    @Resource
    private WarehouseExternalRouteQueryRepository warehouseExternalRouteQueryRepository;
    @Resource
    private WarehouseExternalRouteCommandService warehouseExternalRouteCommandService;
    @Resource
    private WarehouseExternalRouteCommandFactory warehouseExternalRouteCommandFactory;
    @Resource
    private WmsDamageStockItemRepository wmsDamageStockItemRepository;
    @Resource
    private MqProducer mqProducer;

    /**
     * 异步处理货损任务下发
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void handleExternalStockDamageNoticeAsync(Long stockDamageId, Integer warehouseNo, Integer taskType) {
        if (null == stockDamageId || null == warehouseNo || null == taskType) {
            return;
        }
        // 外部下发
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_CREATE_NOTICE)
                .orderType(taskType)
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        if (null != warehouseExternalRoute) {
            log.info("货损任务：{}，已配置外部路由，发送货损任务推送调用消息", stockDamageId);
            mqProducer.send(WmsConstant.MQ_EXTERNAL_TOPIC, WmsConstant.EXTERNAL_ROUTE_STOCK_DAMAGE_INVOKE, WarehouseExternalRouteStockDamageEvent.builder()
                    .idempotentNo(stockDamageId.toString())
                    .warehouseExternalRouteId(warehouseExternalRoute.getId())
                    .stockDamageId(stockDamageId)
                    .warehouseNo(warehouseNo)
                    .build());
        }
    }

    /**
     * 下发外部货损通知
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void exeExternalStockDamageNotice(Long stockDamageId, Integer warehouseNo) {
        if (null == stockDamageId || null == warehouseNo) {
            return;
        }
        List<WmsDamageStockItem> wmsDamageStockItemList = wmsDamageStockItemRepository.selectByDamageId(stockDamageId);
        if (CollectionUtils.isEmpty(wmsDamageStockItemList)) {
            throw new BizException("货损任务" + stockDamageId + "未获取货损明细");
        }
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_CREATE_NOTICE)
                .orderType(StockTaskTypeEnum.DAMAGE_OUT.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        // 对接下发
        if (null == warehouseExternalRoute) {
            return;
        }
        WarehouseExternalRouteCommandInput<StockDamageCreateNoticeReq> warehouseExternalRouteCommandInput = warehouseExternalRouteCommandFactory.buildStockDamageCreateNoticeCommand(wmsDamageStockItemList, stockDamageId, warehouseNo, warehouseExternalRoute);
        warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
    }

    /**
     * 异步处理货损任务提交
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void handleExternalStockDamageCommitAsync(Long stockDamageId, Integer warehouseNo) {
        if (null == stockDamageId || null == warehouseNo) {
            return;
        }
        // 外部下发
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_COMMIT)
                .orderType(StockTaskTypeEnum.DAMAGE_OUT.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        if (null != warehouseExternalRoute) {
            log.info("货损任务：{}，已配置外部路由，发送货损任务推送调用消息", stockDamageId);
            mqProducer.send(WmsConstant.MQ_EXTERNAL_TOPIC, WmsConstant.EXTERNAL_ROUTE_STOCK_DAMAGE_COMMIT_INVOKE, WarehouseExternalRouteStockDamageEvent.builder()
                    .idempotentNo(stockDamageId + "_" + warehouseExternalRoute.getId())
                    .warehouseExternalRouteId(warehouseExternalRoute.getId())
                    .stockDamageId(stockDamageId)
                    .warehouseNo(warehouseNo)
                    .build());
        }
    }

    /**
     * 下发外部货损提交
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void exeExternalStockDamageCommit(Long stockDamageId, Integer warehouseNo) {
        if (null == stockDamageId || null == warehouseNo) {
            return;
        }
        List<WmsDamageStockItem> wmsDamageStockItemList = wmsDamageStockItemRepository.selectByDamageId(stockDamageId);
        if (CollectionUtils.isEmpty(wmsDamageStockItemList)) {
            throw new BizException("货损任务" + stockDamageId + "未获取货损明细");
        }
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_COMMIT)
                .orderType(StockTaskTypeEnum.DAMAGE_OUT.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        // 对接下发
        if (null == warehouseExternalRoute) {
            return;
        }
        WarehouseExternalRouteCommandInput<StockDamageCommitReq> warehouseExternalRouteCommandInput = warehouseExternalRouteCommandFactory.buildStockDamageCommitCommand(stockDamageId, warehouseNo, warehouseExternalRoute);
        warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
    }

    /**
     * 异步处理货损任务取消
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void handleExternalStockDamageCancelAsync(Long stockDamageId, Integer warehouseNo) {
        if (null == stockDamageId || null == warehouseNo) {
            return;
        }
        // 外部下发
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_CANCEL)
                .orderType(StockTaskTypeEnum.DAMAGE_OUT.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        if (null != warehouseExternalRoute) {
            log.info("货损任务：{}，已配置外部路由，发送货损任务推送调用消息", stockDamageId);
            mqProducer.send(WmsConstant.MQ_EXTERNAL_TOPIC, WmsConstant.EXTERNAL_ROUTE_STOCK_DAMAGE_CANCEL_INVOKE, WarehouseExternalRouteStockDamageEvent.builder()
                    .idempotentNo(stockDamageId.toString())
                    .warehouseExternalRouteId(warehouseExternalRoute.getId())
                    .stockDamageId(stockDamageId)
                    .warehouseNo(warehouseNo)
                    .build());
        }
    }

    /**
     * 下发外部货损取消
     * @param stockDamageId
     * @param warehouseNo
     */
    @Override
    public void exeExternalStockDamageCancel(Long stockDamageId, Integer warehouseNo) {
        if (null == stockDamageId || null == warehouseNo) {
            return;
        }
        List<WmsDamageStockItem> wmsDamageStockItemList = wmsDamageStockItemRepository.selectByDamageId(stockDamageId);
        if (CollectionUtils.isEmpty(wmsDamageStockItemList)) {
            throw new BizException("货损任务" + stockDamageId + "未获取货损明细");
        }
        WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                .warehouseNo(warehouseNo)
                .abilityCode(ExternalCodeConstant.STOCK_DAMAGE_CANCEL)
                .orderType(StockTaskTypeEnum.DAMAGE_OUT.getId())
                .routeStatus(BooleanEnum.TRUE.getCode()).build());
        // 对接下发
        if (null == warehouseExternalRoute) {
            return;
        }
        WarehouseExternalRouteCommandInput<StockDamageCancelReq> warehouseExternalRouteCommandInput = warehouseExternalRouteCommandFactory.buildStockDamageCancelCommand(stockDamageId, warehouseNo, warehouseExternalRoute);
        warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
    }

}
