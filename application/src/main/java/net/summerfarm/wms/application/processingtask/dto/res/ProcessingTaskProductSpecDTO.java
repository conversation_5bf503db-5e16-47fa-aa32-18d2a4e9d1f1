package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 加工任务成品明细规格DTO
 * <AUTHOR>
 * @date 2023/02/15
 */
@Data
@Accessors(chain = true)
public class ProcessingTaskProductSpecDTO {

    /**
     * 加工成品规格id
     */
    private Long processingTaskProductSpecId;
    /**
     * 成品sku规格重量
     */
    private BigDecimal productSkuSpecWeight;
    /**
     * 成品sku规格单位
     */
    private String productSkuSpecUnit;
    /**
     * 成品sku规格预计加工数
     */
    private Integer productSkuSpecNeedQuantity;
    /**
     * 成品sku规格实际加工数量
     */
    private Integer productSkuSpecSubmitQuantity;
    /**
     * 成品打印次数
     */
    private Integer productSkuSpecPrintNumber;
}
