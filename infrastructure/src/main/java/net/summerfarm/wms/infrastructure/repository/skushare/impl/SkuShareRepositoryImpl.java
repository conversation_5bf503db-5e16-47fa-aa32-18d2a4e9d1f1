package net.summerfarm.wms.infrastructure.repository.skushare.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.skushare.domainobject.SkuShare;
import net.summerfarm.wms.domain.skushare.domainobject.valueObject.UpdateSkuShare;
import net.summerfarm.wms.domain.skushare.repository.SkuShareRepository;
import net.summerfarm.wms.infrastructure.dao.skushare.entity.InventorySkuShareTransferDO;
import net.summerfarm.wms.infrastructure.dao.skushare.mapper.InventorySkuShareTransferDAO;
import net.summerfarm.wms.infrastructure.repository.skushare.converter.SkuShareConvert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class SkuShareRepositoryImpl implements SkuShareRepository {

    @Autowired
    private InventorySkuShareTransferDAO skuShareDAO;

    @Override
    public SkuShare findSkuShare(Integer warehouseNo, String sku) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(sku), "sku不能为空");
        LambdaQueryWrapper<InventorySkuShareTransferDO> queryWrapper = new QueryWrapper<InventorySkuShareTransferDO>().lambda().select()
                .eq(InventorySkuShareTransferDO::getWarehouseNo, warehouseNo)
                .eq(InventorySkuShareTransferDO::getSku, sku);
        InventorySkuShareTransferDO skuShareDO = skuShareDAO.selectOne(queryWrapper);
        if (skuShareDO == null) {
            return null;
        }
        return SkuShareConvert.INSTANCE.convert(skuShareDO);
    }

    @Override
    public List<SkuShare> selectByWarehouseNoAndSkus(Integer warehouseNo, List<String> skus) {
        ExceptionUtil.checkAndThrow(warehouseNo != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(skus), "sku列表不能为空");
        LambdaQueryWrapper<InventorySkuShareTransferDO> queryWrapper = new QueryWrapper<InventorySkuShareTransferDO>().lambda().select()
                .eq(InventorySkuShareTransferDO::getWarehouseNo, warehouseNo)
                .in(InventorySkuShareTransferDO::getSku, skus);
        List<InventorySkuShareTransferDO> skuShareDOS = skuShareDAO.selectList(queryWrapper);
        return SkuShareConvert.INSTANCE.convert(skuShareDOS);
    }

    @Override
    public boolean saveSkuShare(UpdateSkuShare updateSkuShare) {
        ExceptionUtil.checkAndThrow(updateSkuShare.getWarehouseNo() != null, "仓库号不能为空");
        ExceptionUtil.checkAndThrow(StringUtils.isNotEmpty(updateSkuShare.getSku()), "sku不能为空");
        // 先查询，不存在则插入，存在则更新
        SkuShare skuShareInDb = findSkuShare(updateSkuShare.getWarehouseNo(), updateSkuShare.getSku());
        if (skuShareInDb == null) {
            InventorySkuShareTransferDO skuShareDO = InventorySkuShareTransferDO.builder()
                    .warehouseNo(updateSkuShare.getWarehouseNo())
                    .sku(updateSkuShare.getSku())
                    .remainingTransferOutQuantity(updateSkuShare.getRemainingTransferOutQuantityChange())
                    .remainingTransferInQuantity(updateSkuShare.getRemainingTransferInQuantityChange())
                    .creator(updateSkuShare.getOperator())
                    .operator(updateSkuShare.getOperator())
                    .build();
            skuShareDAO.insert(skuShareDO);
            return true;
        } else {
            int oldRemainingTransferOutQuantity = skuShareInDb.getRemainingTransferOutQuantity();
            int oldRemainingTransferInQuantity = skuShareInDb.getRemainingTransferInQuantity();
            InventorySkuShareTransferDO skuShareDO = InventorySkuShareTransferDO.builder()
                    .remainingTransferOutQuantity(oldRemainingTransferOutQuantity + updateSkuShare.getRemainingTransferOutQuantityChange())
                    .remainingTransferInQuantity(oldRemainingTransferInQuantity + updateSkuShare.getRemainingTransferInQuantityChange())
                    .operator(updateSkuShare.getOperator())
                    .updateTime(new Date())
                    .build();
            LambdaQueryWrapper<InventorySkuShareTransferDO> queryWrapper = new QueryWrapper<InventorySkuShareTransferDO>().lambda()
                    .eq(InventorySkuShareTransferDO::getWarehouseNo, updateSkuShare.getWarehouseNo())
                    .eq(InventorySkuShareTransferDO::getSku, updateSkuShare.getSku())
                    .eq(InventorySkuShareTransferDO::getRemainingTransferOutQuantity, oldRemainingTransferOutQuantity)
                    .eq(InventorySkuShareTransferDO::getRemainingTransferInQuantity, oldRemainingTransferInQuantity);
            return skuShareDAO.update(skuShareDO, queryWrapper) == 1;
        }
    }
}
