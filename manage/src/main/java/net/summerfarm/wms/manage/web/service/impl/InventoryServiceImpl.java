package net.summerfarm.wms.manage.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.wms.api.h5.filedown.DownCenterService;
import net.summerfarm.wms.api.h5.filedown.resp.FileDownResp;
import net.summerfarm.wms.api.h5.inventory.dto.enums.StorageLocationEnum;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorBizException;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.batch.domainobject.WarehouseCostBatch;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import net.summerfarm.wms.domain.download.domainobject.FileDownloadAsyncDTO;
import net.summerfarm.wms.domain.download.enums.FileDownloadCenterRecordEnum;
import net.summerfarm.wms.domain.download.enums.WmsFileDownloadMessageType;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.products.domainobject.query.QueryProduct;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.wms.api.h5.goods.enums.SkuTypeEnums;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.facade.warehouse.WarehouseWmsFacade;
import net.summerfarm.wms.manage.model.domain.*;
import net.summerfarm.wms.manage.model.vo.*;
import net.summerfarm.wms.manage.web.enums.FruitCategoryEnum;
import net.summerfarm.wms.manage.web.enums.InventorySubTypeEnum;
import net.summerfarm.wms.manage.web.enums.StorageLocation;
import net.summerfarm.wms.manage.web.mapper.manage.AreaSkuMapper;
import net.summerfarm.wms.manage.web.mapper.manage.AreaStoreMapper;
import net.summerfarm.wms.manage.web.mapper.manage.InventoryMapper;
import net.summerfarm.wms.manage.web.mapper.manage.SupplierMapper;
import net.summerfarm.wms.manage.web.reposity.AreaStoreRepositoryV1;
import net.summerfarm.wms.manage.web.req.AreaSkuQuery;
import net.summerfarm.wms.manage.web.service.InventoryService;
import net.summerfarm.wms.manage.web.service.WmsBuilderService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Service
@Slf4j
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private WmsBuilderService wmsBuilderService;
    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private WarehouseWmsFacade wmsFacade;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private AreaStoreRepositoryV1 areaStoreRepository;
    @Resource
    private CostBatchRepository costBatchRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private DownCenterService downCenterService;
    @Resource
    private MqProducer mqProducer;

    @Override
    public Map<String, InventoryWMSInfo> batchQueryWMSInfo(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Maps.newHashMap();
        }
        skuList = skuList.stream().distinct().collect(Collectors.toList());
        List<InventoryWMSInfo> infoList = inventoryMapper.batchSelectSkuWMSInfo(skuList);

        return infoList.stream().map(info -> {
            info.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
            info.setStorageArea(StorageLocation.getTypeById(info.getStorageLocation()));
            return info;
        }).collect(Collectors.toMap(InventoryWMSInfo::getSku, Function.identity()));
    }

    @Override
    public Map<String, InventoryWMSInfo> batchQueryWMSInfoFromGoodsCenter(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Maps.newHashMap();
        }
        Map<String, InventoryWMSInfo> result = Maps.newHashMap();
        Lists.partition(skuList, WmsConstant.PARTITION_SIZE).forEach(list -> {
            List<String> query = list.stream().distinct().collect(Collectors.toList());
            List<Product> productsFromGoodsCenter = productRepository.findProductsFromGoodsCenter(QueryProduct.builder().skus(query).build());

            Map<String, InventoryWMSInfo> collect = productsFromGoodsCenter.stream().map(info -> {
                InventoryWMSInfo inventoryWMSInfo = new InventoryWMSInfo();
                inventoryWMSInfo.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                inventoryWMSInfo.setStorageArea(StorageLocation.getTypeById(info.getTemperature()));
                inventoryWMSInfo.setPacking(info.getPackaging());
                inventoryWMSInfo.setSku(info.getSku());
                inventoryWMSInfo.setCategoryType(info.getCategoryType());
                inventoryWMSInfo.setSecondLevelCategory(info.queryFirstCategoryName() + info.querySecondCategoryName()
                        + info.queryThirdCategoryName());
                inventoryWMSInfo.setStorageLocation(info.getStorageLocation());
                return inventoryWMSInfo;
            }).collect(Collectors.toMap(InventoryWMSInfo::getSku, Function.identity()));
            result.putAll(collect);
        });
        return result;
    }

    @Override
    public Map<String, InventoryWMSInfo> batchQueryWMSInfoFromGoodsCenter(Long warehouseNo, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Maps.newHashMap();
        }
        Map<String, InventoryWMSInfo> result = Maps.newHashMap();
        Lists.partition(skuList, WmsConstant.PARTITION_SIZE).forEach(list -> {
            List<String> query = list.stream().distinct().collect(Collectors.toList());
            List<Product> productsFromGoodsCenter = productRepository.findProductsFromGoodsCenter(QueryProduct.builder()
                    .warehouseNo(warehouseNo)
                    .skus(query).build());

            Map<String, InventoryWMSInfo> collect = productsFromGoodsCenter.stream().map(info -> {
                InventoryWMSInfo inventoryWMSInfo = new InventoryWMSInfo();
                inventoryWMSInfo.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                inventoryWMSInfo.setStorageArea(StorageLocation.getTypeById(info.getTemperature()));
                inventoryWMSInfo.setPacking(info.getPackaging());
                inventoryWMSInfo.setSku(info.getSku());
                inventoryWMSInfo.setCategoryType(info.getCategoryType());
                inventoryWMSInfo.setSecondLevelCategory(info.getSecondCategory());
                inventoryWMSInfo.setStorageLocation(info.getStorageLocation());
                return inventoryWMSInfo;
            }).collect(Collectors.toMap(InventoryWMSInfo::getSku, Function.identity()));
            result.putAll(collect);
        });
        return result;
    }

    @Override
    public InventoryWMSInfo queryWMSInfo(String sku) {
        Product info = productRepository.findProductFromGoodsCenter(null, sku);
        if (Objects.isNull(info)) {
            return null;
        }
        InventoryWMSInfo inventoryWMSInfo = new InventoryWMSInfo();
        inventoryWMSInfo.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
        inventoryWMSInfo.setStorageArea(StorageLocation.getTypeById(info.getTemperature()));
        inventoryWMSInfo.setPacking(info.getPackaging());
        inventoryWMSInfo.setSku(info.getSku());
        inventoryWMSInfo.setCategoryType(info.getCategoryType());
        inventoryWMSInfo.setSecondLevelCategory(info.getSecondCategory());
        inventoryWMSInfo.setStorageLocation(info.getStorageLocation());
        return inventoryWMSInfo;
    }

    @Override
    public String getCategory(List<String> skus) {
        List<Product> productsFromGoodsCenter = productRepository.findProductsFromGoodsCenter(QueryProduct.builder().skus(skus).build());
        if (org.springframework.util.CollectionUtils.isEmpty(productsFromGoodsCenter)) {
            return null;
        }
        List<Integer> category = productsFromGoodsCenter.stream().map(Product::getCategoryType).collect(Collectors.toList());
        if (category.stream().allMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return FruitCategoryEnum.FRESH.getDesc();
        } else if (category.stream().noneMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return FruitCategoryEnum.FRESH_NOT.getDesc();
        } else {
            return FruitCategoryEnum.OTHER.getDesc();
        }
    }

    @Override
    public List<Inventory> selectByIds(List<Long> invIds) {
        return inventoryMapper.selectByIds(invIds);
    }

    @Override
    public Inventory selectById(Long id) {
        id = Optional.ofNullable(id).orElse(NumberUtils.LONG_ZERO);
        Inventory query = new Inventory();
        query.setInvId(id);
        return inventoryMapper.selectOne(query);
    }

    @Override
    public AjaxResult selectSkus(Integer storeNo, Integer characters, Integer categoryId, Integer noWarehouseFlag) {
        List<SKUVO> skus = null;
        if (Objects.equals(characters, 0)) {
            skus = inventoryMapper.selectSkus(storeNo, null, categoryId);
        }
        if (Objects.equals(characters, 1)) {
            skus = inventoryMapper.selectSkus(storeNo, 0, categoryId);
        }
        if (Objects.equals(noWarehouseFlag, 1) && CollectionUtil.isNotEmpty(skus)) {
            skus = skus.stream().filter(s -> !ObjectUtil.equal(s.getSubType(), InventorySubTypeEnum.CONSIGNMENT_NO_WAREHOUSE.getSubType())).collect(Collectors.toList());
        }
        wmsBuilderService.batchBuildWMSInfo(skus);
        return AjaxResult.getOK(skus);
    }

    /**
     * 这里需要优化，可以使用搜索引擎解决
     *
     * @return
     */
    @Override
    public AjaxResult selectSkusGroup(Integer storeNo, Integer characters, Integer categoryId) {
        List<SKUVO> skus = null;
        if (Objects.equals(characters, 0)) {
            skus = inventoryMapper.selectSkus(storeNo, null, categoryId);
        }
        if (Objects.equals(characters, 1)) {
            skus = inventoryMapper.selectSkus(storeNo, 0, categoryId);
        }

        wmsBuilderService.batchBuildWMSInfo(skus);
        return skusGroup(skus);
    }

    private AjaxResult skusGroup(List<SKUVO> skus) {

        List<SKUGroupVO> skuGroupVOS = new LinkedList<>();

        if (CollectionUtils.isEmpty(skus)) {
            return AjaxResult.getOK(skuGroupVOS);
        }
        List<Supplier> suppliers = supplierMapper.selectAll();

        // key对应productName value对应供应商
        HashMap<String, List<Supplier>> pdNameSuppliers = new HashMap<>();
        for (Supplier supplier : suppliers) {
            List<String> pdNames = pdNameArrayToList(supplier.getProductArray());
            for (String pdName : pdNames) {
                if (pdNameSuppliers.containsKey(pdName)) {
                    pdNameSuppliers.get(pdName).add(supplier);
                } else {
                    ArrayList<Supplier> supplierArrayList = new ArrayList<>();
                    supplierArrayList.add(supplier);
                    pdNameSuppliers.put(pdName, supplierArrayList);
                }
            }
        }
        Set<String> pdNames = skus.stream().map(SKUVO::getPdName).collect(Collectors.toSet());
        for (String pdName : pdNames) {
            SKUGroupVO skuGroupVO = new SKUGroupVO();
            skuGroupVOS.add(skuGroupVO);
            List<SKUVO> skuVOList = new LinkedList<>();
            skuGroupVO.setPdName(pdName);
            skuGroupVO.setSkuvos(skuVOList);
            Iterator<SKUVO> iterator = skus.iterator();
            while (iterator.hasNext()) {
                SKUVO skuvo = iterator.next();
                if (pdName.equals(skuvo.getPdName())) {
                    skuvo.setWeight(skuvo.getWeight());
                    skuVOList.add(skuvo);
                    skuGroupVO.setPdId(skuvo.getPdId());
                    skuGroupVO.setSpuPic(skuvo.getSpuPic());
                    skuGroupVO.setBrand(skuvo.getBrand());
                    skuGroupVO.setPdNo(skuvo.getPdNo());
                }
            }
            if (pdNameSuppliers.containsKey(pdName)) {
                skuGroupVO.setSuppliers(pdNameSuppliers.get(pdName));
            }
        }
        return AjaxResult.getOK(skuGroupVOS);
    }

    public ArrayList<String> pdNameArrayToList(String productArray) {
        ArrayList<String> pdNames = new ArrayList<>();
        if (!StringUtils.isEmpty(productArray)) {
            String[] pds = productArray.split(";");
            for (String pd : pds) {
                String[] pdName = pd.split("/");
                if (pdName.length == 2) {
                    pdNames.add(pdName[1]);
                }
            }
        }
        return pdNames;
    }

    /**
     * 根据skuList搜索
     * @date 2023/3/1 15:52
     * @param * @Param skuList:
     */
    @Override
    public List<Inventory> selectBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        return inventoryMapper.selectBySkuList(skuList);
    }

    @Override
    public AjaxResult selectStoreStock(int pageIndex, int pageSize, StockVO selectKeys, Long tenantId) {

        // 处理上下架状态sku搜索条件
        if (selectKeys.getOnSale() != null) {
            List<Integer> storeNoList = warehouseInventoryMappingMapper.selectAreaNo(selectKeys.getAreaNo());
            if (CollectionUtils.isEmpty(storeNoList)) {
                return AjaxResult.getErrorWithMsg("暂无配送仓使用该仓库存，无法根据上下架筛选");
            }
            if (Objects.equals(selectKeys.getOnSale(), true)) {
                selectKeys.setOnSaleSkus(areaSkuMapper.selectSku(selectKeys.getOnSale(), storeNoList, selectKeys.getAreaNo()));
            } else if (Objects.equals(selectKeys.getOnSale(), false)) {
                List<String> grounding = areaSkuMapper.selectSku(true, storeNoList, selectKeys.getAreaNo());
                List<String> notGrounding = areaSkuMapper.selectSku(false, storeNoList, selectKeys.getAreaNo());
                notGrounding.removeAll(grounding);
                selectKeys.setOnSaleSkus(notGrounding);
            }
        }

        // 仓库没选，按照租户隔离权限
        if (selectKeys.getAreaNo() == null && CollectionUtils.isEmpty(selectKeys.getAreaNoList())) {
            selectKeys.setAreaNoList(
                    wmsFacade.queryWarehouseCodeListByTenantId(tenantId)
            );
        }
        if (Objects.nonNull(selectKeys.getPdId())){
            Map<String, Product> productMap = productRepository.mapProductsByPdIdsOnlyGoods(selectKeys.getWarehouseNo(),
                    Lists.newArrayList(selectKeys.getPdId().longValue()));
            if (productMap.isEmpty() && Objects.nonNull(selectKeys.getPdId())){
                throw new BizException("货品中心不存在该品");
            }
            selectKeys.setSkusByPdId(Lists.newArrayList(productMap.keySet()));
        }

        PageInfo<StockVO> pageInfo = PageHelper.startPage(pageIndex, pageSize).doSelectPageInfo(() ->
                areaStoreMapper.selectStoreStock(selectKeys));
        List<StockVO> stockVOs = pageInfo.getList();
        Map<Integer, List<StockVO>> map = stockVOs.stream().collect(Collectors.groupingBy(StockVO::getAreaNo));
        map.forEach((areaNo,list)-> {
            List<String> skus = list.stream().map(StockVO::getSku).collect(Collectors.toList());
            Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(areaNo.longValue(), skus);
            list.forEach(item->{
                Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                item.setProductName(product.getPdName());
                item.setWeight(product.getSpecification());
                item.setExtType(product.getSkuExeType());
                item.setSkuType(product.getSkuType());
                item.setIsDomestic(product.getIsDomestic());
                item.setPdId(product.getId() != null ? product.getId().intValue() : null);
                item.setPicturePath(product.getPic());
                item.setStorageLocation(product.getStorageLocation());
                item.setNameRemakes(product.getBelong());
                item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                item.setStorageArea(StorageLocation.getTypeById(product.getTemperature()));
                item.setPacking(product.getPackaging());
                item.setSku(product.getSku());
                item.setSecondLevelCategory(product.queryFirstCategoryName() + product.querySecondCategoryName()
                        + product.queryThirdCategoryName());
                item.setStorageLocation(product.getStorageLocation());
            });
        });
        List<StockVO> objects = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        PageInfo<StockVO> res = new PageInfo<>(objects);
        res.setTotal(pageInfo.getTotal());
        return AjaxResult.getOK(res);
    }

    @Override
    public FileDownResp storeStockDownload(StockVO selectKeys, Long tenantId) {
        if (Objects.isNull(selectKeys.getAreaNo())) {
            throw new ParamsException("请选择库存仓");
        }
        // 处理上下架状态sku搜索条件
        if (selectKeys.getOnSale() != null) {
            List<Integer> storeNoList = warehouseInventoryMappingMapper.selectAreaNo(selectKeys.getAreaNo());
            if (CollectionUtils.isEmpty(storeNoList)) {
                throw new BizException("暂无配送仓使用该仓库存，无法根据上下架筛选");
            }
            if (Objects.equals(selectKeys.getOnSale(), true)) {
                selectKeys.setOnSaleSkus(areaSkuMapper.selectSku(selectKeys.getOnSale(), storeNoList, selectKeys.getAreaNo()));
            } else if (Objects.equals(selectKeys.getOnSale(), false)) {
                List<String> grounding = areaSkuMapper.selectSku(true, storeNoList, selectKeys.getAreaNo());
                List<String> notGrounding = areaSkuMapper.selectSku(false, storeNoList, selectKeys.getAreaNo());
                notGrounding.removeAll(grounding);
                selectKeys.setOnSaleSkus(notGrounding);
            }
        }
        List<String> skusByPdId = Lists.newArrayList();
        if (Objects.nonNull(selectKeys.getPdId())) {
            Map<String, Product> productMap = productRepository.mapProductsByPdIdsOnlyGoods(selectKeys.getWarehouseNo(),
                    Lists.newArrayList(selectKeys.getPdId().longValue()));
            if (productMap.isEmpty() && Objects.nonNull(selectKeys.getPdId())) {
                throw new BizException("货品中心查询不到该商品");
            }
            selectKeys.setSkusByPdId(Lists.newArrayList(productMap.keySet()));
        }

        // 导出文件
        log.info("出入库操作数据导出，操作人：{}，入参：{}", LoginInfoThreadLocal.getCurrentUserName(), JSON.toJSONString(selectKeys));
        String fileName = "出入库操作数据导出.xls";
        // 获取下载中心的uid
        Long uid = downCenterService.startUpload(fileName, FileDownloadCenterRecordEnum.IN_OUT_OPERATION_EXPORT.getType(),
                DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY, selectKeys.getParams());
        if (Objects.isNull(uid)) {
            throw new ErrorBizException(ErrorCodeNew.SAAS_START_DOWNLOAD_FAIL);
        }
        String exportJson = JSON.toJSONString(selectKeys);
        FileDownloadAsyncDTO fileDownloadAsyncDTO = FileDownloadAsyncDTO.builder()
                .dataJson(exportJson)
                .uid(uid.toString())
                .fileId(uid)
                .fileDownloadMessageType(WmsFileDownloadMessageType.IN_OUT_OPERATION_EXPORT.getCode())
                .fileName(fileName)
                .operator(LoginInfoThreadLocal.getCurrentUserName())
                .expireDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY)
                .build();
        log.info("出入库操作数据导出执行，入参：{}", JSON.toJSONString(fileDownloadAsyncDTO));
        mqProducer.send("topic_wms_stock_task", "tag_wms_download", fileDownloadAsyncDTO);
        return FileDownResp.builder().resId(uid).build();
    }

    @Override
    public void exportStoreStockHandler(StockVO selectKeys, Long fileId, String fileName, DownloadCenterEnum.FileExpiredDayEnum expiredDay) {
        downCenterService.endUpload(fileId, fileName, expiredDay, selectKeys, (file, q) -> {
            execExportStoreStock(q, file);
        });
    }

    public void execExportStoreStock(StockVO selectKeys, File file) {
        Workbook workbook = new HSSFWorkbook();
        FileOutputStream out = null;
        try {
            Sheet sheet1 = workbook.createSheet("批次数据");
            Row title1 = sheet1.createRow(0);
            title1.createCell(0).setCellValue("仓库");
            title1.createCell(1).setCellValue("一级类目");
            title1.createCell(2).setCellValue("二级类目");
            title1.createCell(3).setCellValue("商品编号");
            title1.createCell(4).setCellValue("商品名称");
            title1.createCell(5).setCellValue("规格");
            title1.createCell(6).setCellValue("存储区域");
            title1.createCell(7).setCellValue("包装");
            title1.createCell(8).setCellValue("商品归属");
            title1.createCell(9).setCellValue("进口/国产");
            title1.createCell(10).setCellValue("仓库库存");
            title1.createCell(11).setCellValue("批次");
            title1.createCell(12).setCellValue("生产日期");
            title1.createCell(13).setCellValue("保质期");
            title1.createCell(14).setCellValue("保鲜期");
            title1.createCell(15).setCellValue("批次库存");
            title1.createCell(16).setCellValue("安全库存");
            title1.createCell(17).setCellValue("冻结库存");
            title1.createCell(18).setCellValue("在途库存");
            title1.createCell(19).setCellValue("可用库存");
            title1.createCell(20).setCellValue("上架状态");

            Integer sheet1RowNum = NumberUtils.INTEGER_ONE;

            Sheet sheet2 = workbook.createSheet("仓库数据");
            Row title2 = sheet2.createRow(0);
            title2.createCell(0).setCellValue("仓库");
            title2.createCell(1).setCellValue("一级类目");
            title2.createCell(2).setCellValue("二级类目");
            title2.createCell(3).setCellValue("商品编号");
            title2.createCell(4).setCellValue("商品名称");
            title2.createCell(5).setCellValue("规格");
            title2.createCell(6).setCellValue("存储区域");
            title2.createCell(7).setCellValue("包装");
            title2.createCell(8).setCellValue("商品归属");
            title2.createCell(9).setCellValue("进口/国产");
            title2.createCell(10).setCellValue("仓库库存");
            title2.createCell(11).setCellValue("安全库存");
            title2.createCell(12).setCellValue("冻结库存");
            title2.createCell(13).setCellValue("在途库存");
            title2.createCell(14).setCellValue("可用库存");
            title2.createCell(15).setCellValue("上架状态");
            Integer sheet2RowNum = 1;


            Integer pageNum = 1;
            Integer pageSize = 500;

            Integer count = inventoryMapper.countStoreStock(selectKeys);
            Integer page = count / pageSize + 1;

            for (; pageNum <= page; pageNum++) {

                PageHelper.startPage(pageNum, pageSize);
                List<StockVO> stockVOs = inventoryMapper.selectStoreStockEx(selectKeys);
                if (stockVOs.size() == 0) {
                    break;
                }
                Map<Integer, List<StockVO>> map = stockVOs.stream().collect(Collectors.groupingBy(StockVO::getAreaNo));
                map.forEach((areaNo, list) -> {
                    List<String> skus = list.stream().map(StockVO::getSku).collect(Collectors.toList());
                    Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(areaNo.longValue(), skus);
                    list.forEach(item -> {
                        Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                        item.setProductName(product.getPdName());
                        item.setWeight(product.getSpecification());
                        item.setExtType(product.getSkuExeType());
                        item.setSkuType(product.getSkuType());
                        item.setIsDomestic(product.getIsDomestic());
                        item.setPdId(Objects.isNull(product.getId()) ? null : product.getId().intValue());
                        item.setPicturePath(product.getPic());
                        item.setStorageLocation(product.getStorageLocation());
                        item.setNameRemakes(product.getBelong());
                        item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                        item.setStorageArea(StorageLocationEnum.getDescByCode(product.getTemperature()));
                        item.setPacking(product.getPackaging());
                        item.setSku(product.getSku());
                        item.setSecondLevelCategory(product.queryFirstCategoryName() + product.querySecondCategoryName()
                                + product.queryThirdCategoryName());
                        item.setWarnTimePeriodDay(product.getWarnTimePeriodDay());
                        item.setFreshnessPeriodDay(product.getFreshnessPeriodDay());
                    });
                });

                // sheet1写入批次数据
                if (!CollectionUtils.isEmpty(stockVOs)) {
                    stockVOs = wmsBuilderService.sortedFruitPriority(stockVOs);

                    List<Integer> warehouseCodeList = stockVOs.stream().map(StockVO::getAreaNo).distinct().collect(Collectors.toList());
                    List<String> skuCodeList = stockVOs.stream().map(StockVO::getSku).distinct().collect(Collectors.toList());

                    AreaSkuQuery areaSkuQuery = new AreaSkuQuery();
                    areaSkuQuery.setSkuList(skuCodeList);
                    areaSkuQuery.setOnSale(Boolean.TRUE);
                    areaSkuQuery.setWarehouseNo(selectKeys.getAreaNo());

                    List<AreaSkuVO> areaSkuVOS = areaSkuMapper.selectByKeysGroupByAreaNoAndStoreNo(areaSkuQuery);
                    Set<String> onSaleSet = new HashSet<>();
                    if (!CollectionUtils.isEmpty(areaSkuVOS)) {
                        for (AreaSkuVO areaSkuVO : areaSkuVOS) {
                            String key = areaSkuVO.getSku() + ":" + areaSkuVO.getParentNo();
                            onSaleSet.add(key);
                        }
                    }

                    Map<String, AreaStore> areaStoreMap = areaStoreRepository.mapByWarehouseNoAndSkuCodeList(
                            warehouseCodeList, skuCodeList);

                    Map<String, List<WarehouseCostBatch>> warehouseCostBatchMap = costBatchRepository.mapByGroupWarehouseNoAndSkuCodeList(
                            warehouseCodeList, skuCodeList
                    );

                    //控制行数
                    for (int i = 0; i < stockVOs.size(); i++) {
                        Row row = sheet1.createRow(sheet1RowNum);
                        StockVO stockVO = stockVOs.get(i);
                        //获取批次信息
                        List<WarehouseCostBatch> warehouseCostBatchs = warehouseCostBatchMap.get(
                                costBatchRepository.mapKeyByWarehouseNoAndSkuCodeList(
                                        stockVO.getAreaNo(), stockVO.getSku())
                        );

                        //获取批次 > 0
                        List<WarehouseCostBatch> warehouseCostBatchList = CollectionUtils.isEmpty(warehouseCostBatchs) ? new ArrayList<>() :
                                warehouseCostBatchs.stream()
                                        .filter(x -> x.getQuantity() > NumberUtils.INTEGER_ZERO)
                                        .collect(Collectors.toList());

                        int size = warehouseCostBatchList.size() > 0 ? warehouseCostBatchList.size() : 1;

                        if (!Objects.equals(size, NumberUtils.INTEGER_ONE)) {
                            mergedRegion(sheet1, sheet1RowNum, size, 21);
                        }
                        for (WarehouseCostBatch record : warehouseCostBatchList) {
                            Row sheetRow = sheet1.createRow(sheet1RowNum);
                            String productionDate = Objects.isNull(record.getProductionDate()) ? "" : record.getProductionDate().toString();
                            String qualityDate = Objects.isNull(record.getQualityDate()) ? "" : record.getQualityDate().toString();
                            sheetRow.createCell(11).setCellValue(record.getPurchaseNo());
                            sheetRow.createCell(12).setCellValue(productionDate);
                            sheetRow.createCell(13).setCellValue(qualityDate);
                            if (stockVO.getFreshnessPeriodDay() == null){
                                sheetRow.createCell(14).setCellValue("");
                            } else {
                                LocalDate freshnessPeriodDay = record.getProductionDate()
                                        .plusDays(stockVO.getFreshnessPeriodDay());
                                String freshnessPeriodDayStr = freshnessPeriodDay.toString();
                                sheetRow.createCell(14).setCellValue(freshnessPeriodDayStr);
                            }

                            sheetRow.createCell(15).setCellValue(record.getQuantity().toString());
                            sheet1RowNum++;
                        }
                        //为空的也要展示

                        // 查询仓库库存
                        AreaStore areaStore = areaStoreMap.get(
                                areaStoreRepository.mapKeyByWarehouseNoAndSkuCodeList(
                                        stockVO.getAreaNo(), stockVO.getSku()));

                        String warehouseName = Objects.isNull(stockVO.getAreaNo()) ? "" :
                                Global.warehouseMap.get(Long.valueOf(stockVO.getAreaNo()));
                        row.createCell(0).setCellValue(warehouseName);
                        row.createCell(1).setCellValue(stockVO.getFirstLevelCategory());
                        row.createCell(2).setCellValue(stockVO.getSecondLevelCategory());
                        row.createCell(3).setCellValue(stockVO.getSku());
                        if (stockVO.getNameRemakes() != null) {
                            row.createCell(4).setCellValue(stockVO.getProductName() + "-" + stockVO.getNameRemakes());
                        } else {
                            row.createCell(4).setCellValue(stockVO.getProductName());
                        }
                        row.createCell(5).setCellValue(stockVO.getWeight());
                        row.createCell(6).setCellValue(stockVO.getStorageLocation() == null ? "无" : StorageLocation.getTypeById(stockVO.getStorageLocation()));
                        row.createCell(7).setCellValue(stockVO.getPacking());

                        // 自营还是代仓还是POP
                        row.createCell(8).setCellValue(SkuTypeEnums.getNameByType(stockVO.getSkuType()));

                        row.createCell(9).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, stockVO.getIsDomestic()) ? "进口" : "国产");
                        row.createCell(10).setCellValue(Objects.isNull(areaStore) ? "" : areaStore.getQuantity().toString());
                        //无可用批次展示0
                        if (CollectionUtils.isEmpty(warehouseCostBatchList)) {
                            sheet1RowNum++;
                            row.createCell(15).setCellValue(NumberUtils.INTEGER_ZERO);
                        }
                        row.createCell(16).setCellValue(stockVO.getSafeQuantity());
                        row.createCell(17).setCellValue(stockVO.getLockQuantity());
                        row.createCell(18).setCellValue(stockVO.getRoadQuantity());
                        row.createCell(19).setCellValue(stockVO.getStoreQuantity() - stockVO.getLockQuantity() - stockVO.getSafeQuantity());
                        String key = stockVO.getSku() + ":" + stockVO.getAreaNo();
                        row.createCell(20).setCellValue(onSaleSet.contains(key) ? "上架中" : "未上架");

                    }

                    // sheet写入仓库数据 拼装 仓库库存数据
                    storeSheet(workbook, stockVOs, onSaleSet, sheet2, sheet2RowNum);
                    sheet2RowNum += stockVOs.size();
                }
            }
            // excel输出到文件
            out = new FileOutputStream(file);
            workbook.write(out);
        } catch (IOException e) {
            log.error("storeStockDownload exception", e);
            throw new ProviderException("导出异常");
        } finally {
            try {
                workbook.close();
                if (Objects.nonNull(out)) {
                    out.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param index
     * @param length
     * @param size
     */
    private void mergedRegion(Sheet sheet, Integer index, Integer size, Integer length) {
        for (int i = 0; i < length; i++) {
            //不合并
            if (i > 10 && i < 16) {
                continue;
            }
            sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, i, i));
        }
    }

    /**
     * excel 拼装仓库数据
     *
     * @param workbook
     * @param stockVOs  下载数据
     * @param onSaleSet 上架sku
     */
    private void storeSheet(Workbook workbook, List<StockVO> stockVOs, Set<String> onSaleSet, Sheet sheet, Integer sheet2RowNum) {

        for (int i = 0; i < stockVOs.size(); i++) {
            Row row = sheet.createRow(sheet2RowNum);

            StockVO stockVO = stockVOs.get(i);
            if (stockVO.getAreaNo() == null) {
                row.createCell(0).setCellValue("");
            } else {
                row.createCell(0).setCellValue(Global.warehouseMap.get(Long.valueOf(stockVO.getAreaNo())));
            }
            row.createCell(1).setCellValue(stockVO.getFirstLevelCategory());
            row.createCell(2).setCellValue(stockVO.getSecondLevelCategory());
            row.createCell(3).setCellValue(stockVO.getSku());
            if (stockVO.getNameRemakes() != null) {
                row.createCell(4).setCellValue(stockVO.getProductName() + "-" + stockVO.getNameRemakes());
            } else {
                row.createCell(4).setCellValue(stockVO.getProductName());
            }
            row.createCell(5).setCellValue(stockVO.getWeight());
            row.createCell(6).setCellValue(stockVO.getStorageLocation() == null ? "无" : StorageLocation.getTypeById(stockVO.getStorageLocation()));
            row.createCell(7).setCellValue(stockVO.getPacking());
            // 自营还是代仓
            if (SkuTypeEnums.SELF_SUPPORT.getType().equals(stockVO.getSkuType())) {
                row.createCell(8).setCellValue(SkuTypeEnums.SELF_SUPPORT.getTypeName());
            } else if (SkuTypeEnums.SUBSTITUTE_WAREHOUSE.getType().equals(stockVO.getSkuType())) {
                row.createCell(8).setCellValue(SkuTypeEnums.SUBSTITUTE_WAREHOUSE.getTypeName());
            } else {
                row.createCell(8).setCellValue("");
            }
            row.createCell(9).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, stockVO.getIsDomestic()) ? "进口" : "国产");
            row.createCell(10).setCellValue(stockVO.getStoreQuantity());
            row.createCell(11).setCellValue(stockVO.getSafeQuantity());
            row.createCell(12).setCellValue(stockVO.getLockQuantity());
            row.createCell(13).setCellValue(stockVO.getRoadQuantity());
            row.createCell(14).setCellValue(stockVO.getStoreQuantity() - stockVO.getLockQuantity() - stockVO.getSafeQuantity());
            String key = stockVO.getSku() + ":" + stockVO.getAreaNo();
            row.createCell(15).setCellValue(onSaleSet.contains(key) ? "上架中" : "未上架");

            sheet2RowNum++;
        }
    }
}
