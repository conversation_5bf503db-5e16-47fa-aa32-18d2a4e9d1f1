package net.summerfarm.wms.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: dongcheng
 * @date: 2023/12/8
 */
@Data
public class RejectSaleNeedStockVo {

    private String sku;

    private Integer amount;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "售后单号")
    private String afterSaleOrderNo;

    @ApiModelProperty(value = "出库仓")
    private Integer areaNo;

    @ApiModelProperty(value = "城配仓")
    private Integer outStoreNo;

    private Long mId;


    /**
     * 获取订单号和仓库号
     * @return
     */
    public String getOrderNoStoreNo(){
        return this.getOrderNo()+this.getAreaNo();
    }



}

