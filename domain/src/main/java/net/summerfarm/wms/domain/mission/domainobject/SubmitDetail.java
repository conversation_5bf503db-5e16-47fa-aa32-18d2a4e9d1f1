package net.summerfarm.wms.domain.mission.domainobject;

import com.aliyun.odps.utils.StringUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.domain.base.ValueObject;
import net.summerfarm.wms.domain.mission.enums.MissionCarrierTypeEnum;

import java.time.LocalDate;

/**
 * 提交明细
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class SubmitDetail implements ValueObject {
    /**
     * 任务编号
     */
    String missionNo;

    /**
     * 任务类型
     */
    Integer missionType;

    /**
     * 执行单元编号
     */
    String unitNo;

    /**
     * 来源载体编号（容器编号）
     */
    String sourceCarrierCode;

    /**
     * 来源载体类型
     *
     * @see MissionCarrierTypeEnum
     */
    Integer sourceCarrierType;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * sku
     */
    String sku;

    /**
     * 生产日期
     */
    LocalDate produceTime;

    /**
     * 保质期
     */
    LocalDate shelfLife;

    /**
     * 采购批次
     */
    String batchNo;

    /**
     * 执行数量
     */
    Integer quantity;

    /**
     * 目标载体编号
     */
    String targetCarrierCode;

    /**
     * 目标载体类型(容器)
     *
     * @see MissionCarrierTypeEnum
     */
    Integer targetCarrierType;
    /**
     * 货主
     */
    String cargoOwner;
    /**
     * 操作人名称
     */
    String opreatorName;
    /**
     * 操作人id
     */
    String operatorId;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * 拆分类型
     */
    Integer splitType;

    /**
     * 拆分信息
     */
    String splitInfo;

    public String duplicateKey() {
        // 库位五要素,非库位四要素
        String carrier = MissionCarrierTypeEnum.CABINET.getCode().equals(sourceCarrierType) ? sourceCarrierCode : null;
        return sku + batchNo + produceTime + shelfLife + carrier + getSplitTypeForDp() + getSplitInfoForDp();
    }

    public String getSplitTypeForDp() {
        if (splitType == null) {
            return "";
        }
        return splitType.toString();
    }

    public String getSplitInfoForDp() {
        if (StringUtils.isBlank(splitInfo)) {
            return "";
        }
        return splitInfo.trim();
    }

    /**
     * 后台管理页面上三要素+仓为唯一键
     *
     * @return s
     */
    public String pcDuplicateKey() {
        return sku + produceTime + shelfLife;
    }

    public String countExeNumForPcKey() {
        return sourceCarrierCode + targetCarrierCode;
    }

    public String produceTimeString() {
        if (produceTime == null) {
            return "";
        }
        return "生产日期:" + produceTime;
    }

    public String shelfLifeString() {
        if (shelfLife == null) {
            return "";
        }
        return "保质期:" + shelfLife;
    }

    public String sourceCarrierCodeString() {
        if (sourceCarrierCode == null) {
            return "";
        }
        return MissionCarrierTypeEnum.convert(sourceCarrierType) + sourceCarrierCode;
    }
}
