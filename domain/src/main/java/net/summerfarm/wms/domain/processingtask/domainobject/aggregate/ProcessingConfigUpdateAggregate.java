package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;
import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigCommandParam;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingConfigUpdateAggregate implements Serializable {

    /**
     * 加工配置
     */
    private ProcessingConfig processingConfig;

    /**
     * 加工配置规格
     */
    private List<SkuSpec> skuSpecList;

    /**
     * 加工多原料
     */
    private List<WmsProcessingMaterialConfigCommandParam> processingMaterialConfigEntityList;

    /**
     * 追加配置id
     * @param processingConfigId
     */
    public void addProcessingConfigId(Long processingConfigId){
        processingConfig.setId(processingConfigId);
        if (skuSpecList != null) {
            for (SkuSpec skuSpec : skuSpecList) {
                skuSpec.setProcessingConfigId(processingConfigId);
            }
        }
        if (processingMaterialConfigEntityList != null) {
            for (WmsProcessingMaterialConfigCommandParam processingMaterialConfigEntity : processingMaterialConfigEntityList) {
                processingMaterialConfigEntity.setProcessingConfigId(processingConfigId);
            }
        }
    }
}
