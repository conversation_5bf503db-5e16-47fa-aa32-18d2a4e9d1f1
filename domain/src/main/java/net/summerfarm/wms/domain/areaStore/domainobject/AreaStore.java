package net.summerfarm.wms.domain.areaStore.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 暂时先放着，后续迁移者在做调整
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AreaStore {
    private Integer id;

    private Long warehouseNo;

    private String sku;

    private Integer quantity;

    private Date updateTime;

    private Integer adminId;

    private Integer leadTime;

    private int count;

    private BigDecimal costPrice;

    private BigDecimal marketPrice;

    private Integer priceStatus;

    /**
     * 预售库存
     */
    private Integer advanceQuantity;

    private Integer onlineQuantity;

    private Integer lockQuantity;

    private Integer roadQuantity;

    private Integer safeQuantity;

    private Integer saleLockQuantity;

    /**
     * 跟可用库存对比变化值
     */
    private Integer change;

    private Integer sync = 0;

    private Boolean autoTransfer;

    /**
     * 是否支持预留库存 0 不支持 1支持
     */
    private Integer supportReserved;

    private Integer reserveMaxQuantity;

    private Integer reserveMinQuantity;

    /**
     * 预留库存最大值大于0（支持预留）大客户下单就会累加
     * 在线库存展示逻辑： 实际使用 > 最大值减最小值 展示 最大值减最小值
     * 反之 展示预留库存实际使用数量
     */
    private Integer reserveUseQuantity;

    private Integer warningQuantity;

    /**
     * status状态
     */
    private Integer status;

    /**
     * 是否预警消息提醒过 0 未提醒 1 已提醒
     */
    private Integer sendWarningFlag;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    private Long warehouseTenantId;

    /**
     * 获取销售可用库存
     * @return
     */
    public Integer getSaleAbleQuantity(){
//        if (!Integer.valueOf(1).equals(this.sync) &&
//                this.getQuantity() != null &&
//                this.getLockQuantity() != null &&
//                this.getSafeQuantity() != null
//                ){
//            Integer saleAbleQuantity = this.getQuantity() -
//                    this.getLockQuantity() -
//                    this.getSafeQuantity();
//            return saleAbleQuantity < 0 ? 0 : saleAbleQuantity;
//        }

        Integer onlineQuantity = this.getOnlineQuantity();
        Integer reserveMaxQuantity = this.getReserveMaxQuantity();
        Integer reserveMinQuantity = this.getReserveMinQuantity();
        Integer reserveUseQuantity = this.getReserveUseQuantity();
        if (reserveMaxQuantity != null && reserveUseQuantity != null
                && reserveMinQuantity != null && onlineQuantity != null) {
            Integer reserveQuantity = reserveMaxQuantity - reserveMinQuantity;
            onlineQuantity = reserveUseQuantity > reserveQuantity ?
                    onlineQuantity - reserveMinQuantity :
                    onlineQuantity - (reserveMaxQuantity - reserveUseQuantity);
        }
        return onlineQuantity;
    }
}
