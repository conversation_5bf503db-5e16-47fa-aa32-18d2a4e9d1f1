package net.summerfarm.wms.api.h5.outstore.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/5/4 21:52
 * @<AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecheckOutStoreTaskQuery implements Serializable {

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 库存仓
     */
    private Integer warehouseNo;
    /**
     * 城配仓
     */
    private Integer storeNo;
    /**
     * 商品ID
     */
    private Long pdId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 预计出库时间
     */
    private LocalDate expectTime;

    private LocalDate endExpectTime;

    /**
     * 出库任务号
     */
    private Long outTaskId;


}
