package net.summerfarm.wms.domain.batch.factory;

import net.summerfarm.wms.domain.batch.domainobject.BatchRelation;
import net.summerfarm.wms.domain.batch.domainobject.CostBatch;
import net.summerfarm.wms.domain.batch.domainobject.CostBatchRecord;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流成本计算公式
 */
@Component
public class DeliveryCostFormulaFactory {

    private static final String FORMULA_SPLIT = "(";
    private static final String FORMULA_SPLIT_BACK = ")";
    private static final String FORMULA_MULTIPLY = "*";
    private static final String FORMULA_DIVIDE = "/";
    private static final String FORMULA_EMPTY = "";
    private static final String FORMULA_ADD = "+";
    private static final String FORMULA_PREFIX = "cost([";
    private static final String FORMULA_SUFFIX = "]";

    @Resource
    private CostBatchRepository costBatchRepository;

    public String transferDeliveryCostFormula(List<CostBatchRecord> records, BigDecimal ratio) {
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        List<CostBatch> costBatch = getCostBatches(records);

        // 转换条目对应的成本
        Map<String, List<CostBatch>> costBatchMap = costBatch.stream().collect(Collectors.groupingBy(CostBatch::getPurchaseNo));

        StringBuilder exp = new StringBuilder(FORMULA_SPLIT);
        costBatchMap.forEach((purchase, costBatches) -> {
            // 有计算公式的成本id
            List<String> costFormulas = costBatches.stream()
                    .map(CostBatch::getBatchRelations)
                    .flatMap(Collection::stream)
                    .map(BatchRelation::getCostFormula)
                    .distinct()
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            // 无计算公式的成本id
            List<Long> costBatchIdsWithOutFormula = costBatches.stream()
                    .filter(item -> item.getBatchRelations().stream()
                            .map(BatchRelation::getCostFormula)
                            .allMatch(StringUtils::isEmpty))
                    .map(CostBatch::getId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(costFormulas) && CollectionUtils.isEmpty(costBatchIdsWithOutFormula)) {
                return;
            }
            // 判断是不是第一次循环,添加"+"
            if (!StringUtils.equals(FORMULA_SPLIT, exp.toString())) {
                exp.append(FORMULA_ADD);
            }
            // 拼接公式
            StringBuilder dup = new StringBuilder(FORMULA_SPLIT);
            costFormulas.forEach(item -> dup.append(item).append(FORMULA_ADD));
            costBatchIdsWithOutFormula.forEach(item ->
                    dup.append(FORMULA_PREFIX).append(item).append(FORMULA_SUFFIX).append(FORMULA_SPLIT_BACK).append(FORMULA_ADD));
            dup.deleteCharAt(dup.lastIndexOf(FORMULA_ADD));
            if (!dup.toString().contains(FORMULA_ADD)) {
                // 只有一个批次的计算，去除(
                dup.deleteCharAt(0);
                exp.append(dup);
                return;
            }
            // 若是转换的采购批次扣减了多个成本批次，取均值
            int avg = costFormulas.size() + costBatchIdsWithOutFormula.size();
            if (avg != 1) {
                dup.append(FORMULA_SPLIT_BACK).append(FORMULA_DIVIDE).append(avg);
                exp.append(dup);
                return;
            }
        });


        if (!exp.toString().contains(FORMULA_ADD)) {
            exp.deleteCharAt(0);
            return exp.toString();
        }
        // 成本均值
        if (exp.lastIndexOf(FORMULA_ADD) == exp.length() - 1) {
            exp.deleteCharAt(0);
            exp.deleteCharAt(exp.lastIndexOf(FORMULA_ADD));
        }
        if (exp.toString().contains(FORMULA_ADD)) {
            exp.append(FORMULA_SPLIT_BACK).append(FORMULA_MULTIPLY);
            if (null != ratio) {
                exp.append(ratio);
            }
        } else {
            exp.deleteCharAt(0);
            if (ratio.intValue() != 1) {
                exp.append(FORMULA_MULTIPLY).append(ratio);
            }
        }

        if (costBatchMap.size() > 1) {
            exp.append(FORMULA_DIVIDE).append(costBatchMap.size() + FORMULA_EMPTY);
        }
        return exp.toString();
    }

    public String allocationDeliveryCostFormula(List<CostBatchRecord> records, Long currentCostBatchId) {
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        List<CostBatch> costBatch = getCostBatches(records);
        List<String> costFormula = costBatch.stream()
                .map(CostBatch::getBatchRelations)
                .flatMap(Collection::stream)
                .map(BatchRelation::getCostFormula)
                .distinct().collect(Collectors.toList());

        StringBuilder exp = new StringBuilder();
        if (CollectionUtils.isEmpty(costFormula)) {
            exp.append(FORMULA_PREFIX).append(currentCostBatchId).append(FORMULA_SUFFIX).append(FORMULA_SPLIT_BACK);
            return exp.toString();
        }
        costFormula.forEach(item -> exp.append(item).append(FORMULA_ADD));
        exp.append(FORMULA_PREFIX).append(currentCostBatchId).append(FORMULA_SUFFIX).append(FORMULA_SPLIT_BACK);

        return exp.toString();
    }

    private List<CostBatch> getCostBatches(List<CostBatchRecord> records) {
        List<Long> costBatchIds = records.stream().map(CostBatchRecord::getWarehouseCostBatchId).collect(Collectors.toList());
        return costBatchRepository.findCostBatch(costBatchIds);
    }

    public String processingTaskDeliveryCostFormula(List<CostBatchRecord> records, BigDecimal ratio) {
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        List<CostBatch> costBatches = getCostBatches(records);
        // 有计算公式的成本id
        List<String> costFormulas = costBatches.stream()
                .map(CostBatch::getBatchRelations)
                .flatMap(Collection::stream)
                .map(BatchRelation::getCostFormula)
                .distinct()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        // 无计算公式的成本id
        List<Long> costBatchIdsWithOutFormula = costBatches.stream()
                .filter(item -> item.getBatchRelations().stream()
                        .map(BatchRelation::getCostFormula)
                        .allMatch(StringUtils::isEmpty))
                .map(CostBatch::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(costFormulas) && CollectionUtils.isEmpty(costBatchIdsWithOutFormula)) {
            return null;
        }

        StringBuilder exp = new StringBuilder(FORMULA_SPLIT);
        costFormulas.forEach(item -> exp.append(item).append(FORMULA_ADD));
        costBatchIdsWithOutFormula.forEach(item ->
                exp.append(FORMULA_PREFIX).append(item).append(FORMULA_SUFFIX).append(FORMULA_SPLIT_BACK).append(FORMULA_ADD));
        exp.deleteCharAt(exp.lastIndexOf(FORMULA_ADD));
        int avg = costFormulas.size() + costBatchIdsWithOutFormula.size();
        if (avg != 1) {
            exp.append(FORMULA_SPLIT_BACK).append(FORMULA_DIVIDE).append(avg).append(FORMULA_MULTIPLY).append(ratio);
            return exp.toString();
        }
        exp.deleteCharAt(0);
        exp.append(FORMULA_MULTIPLY).append(ratio);
        return exp.toString();
    }
}

