package net.summerfarm.wms.api.h5.common.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/3
 */
@Data
public class CabinetUpdateCommand implements Serializable {

    private static final long serialVersionUID = 8218121251675368333L;

    /**
     * id
     */
    @NotNull
    private Long id;

    /**
     * 库位类型（1：高位货架，2：轻型货架，3：地堆）
     */
    private Integer cabinetType;

    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    private Integer purpose;

    /**
     * 长（单位：m）
     */
    private Double length;

    /**
     * 宽（单位：m）
     */
    private Double width;

    /**
     * 高（单位：m）
     */
    private Double high;

    /**
     * 是否允许混放sku（0：不允许，1：允许）
     */
    private Integer allowMixedSku;

    /**
     * 允许sku混放数量
     */
    private Integer allowMixedSkuQuantity;

    /**
     * 是否允许混放效期（0：不允许，1：允许）
     */
    private Integer allowMixedPeriod;

    /**
     * 允许效期混放数量
     */
    private Integer allowMixedPeriodQuantity;

    /**
     * 是否允许混放批次（0：不允许，1：允许）
     */
    private Integer allowMixedBatch;

    /**
     * 允许批次混放数量
     */
    private Integer allowMixedBatchQuantity;

    /**
     * 库位状态（0：禁用，1：启用）
     */
    private Integer cabinetStatus;

}
