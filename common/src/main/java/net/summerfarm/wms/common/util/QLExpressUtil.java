package net.summerfarm.wms.common.util;

import com.ql.util.express.ExpressRunner;
import com.ql.util.express.IExpressContext;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.dto.ExpressRunnerFuncDTO;
import net.summerfarm.wms.common.dto.QLExpressExeDTO;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class QLExpressUtil implements ApplicationContextAware {

    private ApplicationContext context;
    private ExpressRunner runner;

    public Object executeExpress(QLExpressExeDTO exe) {
        try {
            IExpressContext<String, Object> expressContext = new WmsExpressContext(this.context, exe.getParam());
            assembleRunner(exe, expressContext);
            return runner.execute(exe.getContent(), expressContext, null, exe.getIsCache(), exe.getIsTrace());
        } catch (Exception e) {
            log.error("qlExpress运行出错！", e);
        } finally {
            runner = new ExpressRunner();
        }
        return null;
    }

    private void assembleRunner(QLExpressExeDTO exe, IExpressContext<String, Object> expressContext) throws Exception {
        if (Objects.nonNull(exe.getExpressRunnerFunc())) {
            ExpressRunnerFuncDTO expressRunnerFunc = exe.getExpressRunnerFunc();
            if (Objects.nonNull(expressRunnerFunc.getFunction())) {
                runner.addFunctionOfServiceMethod(expressRunnerFunc.getFunction().getAlias(),
                        expressContext.get(expressRunnerFunc.getFunction().getServiceName()), expressRunnerFunc.getFunction().getFuncName(),
                        expressRunnerFunc.getFunction().getParameterClassTypes(), expressRunnerFunc.getFunction().getErrorInfo());
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
        runner = new ExpressRunner();
    }
}
