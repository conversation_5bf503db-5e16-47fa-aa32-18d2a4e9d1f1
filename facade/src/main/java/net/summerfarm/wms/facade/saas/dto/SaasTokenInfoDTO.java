package net.summerfarm.wms.facade.saas.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2022-12-15 11:09
 * @describe :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasTokenInfoDTO {
    /**
     * 手机号
     */
    private String phone;
    /**
     * 名称
     */
    private String name;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * adminId
     */
    private Long adminId;
    /**
     * SaaS登录账号id
     */
    private Long tenantAccountId;
    /**
     * SaaS登录账号名称
     */
    private String tenantAccountName;
    /**
     * 工商信息
     */
    private String companyName;
}