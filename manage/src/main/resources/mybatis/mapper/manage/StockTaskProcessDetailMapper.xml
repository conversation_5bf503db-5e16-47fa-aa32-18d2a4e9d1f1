<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockTaskProcessDetailMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetail" >
        INSERT INTO stock_task_process_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskProcessId != null">
                stock_task_process_id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="listNo != null">
                list_no,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="transferSku != null">
                transfer_sku,
            </if>
            <if test="transferQuantity != null">
                transfer_quantity,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="transferScale != null">
                transfer_scale,
            </if>
            <if test="glNo != null">
                gl_no,
            </if>
            <if test="inGlNo != null">
                in_gl_no,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="liableOwner != null">
                liable_owner,
            </if>
            <if test="cabinetCode != null">
                cabinet_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskProcessId != null">
                #{stockTaskProcessId},
            </if>
            <if test="itemId != null">
                #{itemId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="listNo != null">
                #{listNo},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="productionDate != null">
                #{productionDate},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="transferSku != null">
                #{transferSku},
            </if>
            <if test="transferQuantity != null">
                #{transferQuantity},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="transferScale != null">
                #{transferScale},
            </if>
            <if test="glNo != null">
                #{glNo},
            </if>
            <if test="inGlNo != null">
                #{inGlNo},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="liableOwner != null">
                #{liableOwner,jdbcType=VARCHAR},
            </if>
            <if test="cabinetCode != null">
                #{cabinetCode},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_task_process_detail(stock_task_process_id,item_id,sku,list_no,quality_date,quantity,production_date, transfer_sku, transfer_quantity, create_time, creator, transfer_scale,gl_no,in_gl_no,transfer_list_no
        , cabinet_code)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTaskProcessId},#{item.itemId},#{item.sku},#{item.listNo},#{item.qualityDate},#{item.quantity},#{item.productionDate}, #{item.transferSku}, #{item.transferQuantity}, #{item.createTime}, #{item.creator},
            #{item.transferScale},#{item.glNo},#{item.inGlNo},#{item.transferListNo}
            ,#{item.cabinetCode}
            )
        </foreach>
    </insert>

    <select id="selectByProcessId" resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        /*FORCE_MASTER*/ SELECT	stpd.id,p.pd_name pdName,i.pack,i.weight,stpd.sku,stpd.quantity,stpd.quality_date qualityDate,
                  stpd.production_date productionDate,stpd.list_no listNo,stpd.remark,stpe.is_complete isComplete,stpe.remark proveRemark
                ,ad.name_remakes nameRemakes,stpd.gl_no glNo,i.ext_type extType,i.inv_id skuId, stpd.liable_owner liableOwner,
                i.is_domestic isDomestic, i.type skuType, stpd.stock_task_process_id stockTaskProcessId
        FROM stock_task_process_detail stpd
                 LEFT JOIN stock_task_process_expand stpe on stpe.stock_task_process_detail_id= stpd.id
                 INNER JOIN inventory i ON stpd.sku=i.sku
                 INNER JOIN products p ON i.pd_id=p.pd_id
                 LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE  stpd.stock_task_process_id = #{stockTaskProcessId,jdbcType=INTEGER} and status = 1
    </select>

    <select id="selectByProcessIdWithGoods" resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        /*FORCE_MASTER*/ SELECT stpd.id,
               stpd.sku,
               stpd.quantity,
               stpd.quality_date    qualityDate,
               stpd.production_date productionDate,
               stpd.list_no         listNo,
               stpd.remark,
               stpe.is_complete     isComplete,
               stpe.remark          proveRemark
                ,
               stpd.gl_no           glNo,
               stpd.liable_owner    liableOwner
        FROM stock_task_process_detail stpd
                 LEFT JOIN stock_task_process_expand stpe on stpe.stock_task_process_detail_id = stpd.id
        WHERE stpd.stock_task_process_id = #{stockTaskProcessId,jdbcType=INTEGER}
          and status = 1
    </select>

    <select id="selectByTaskId" resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        SELECT stpd.id,
               stpd.sku sku,
               stpd.quantity quantity,
               stpd.quality_date    qualityDate,
               stpd.production_date productionDate,
               stpd.list_no         listNo
        FROM stock_task_process_detail stpd
                 inner join stock_task_process stp on stp.id = stpd.stock_task_process_id
        WHERE stp.stock_task_id = #{stockTaskId}
    </select>

    <select id="selectByStockTaskId"  resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        select stpd.id,
               stock_task_process_id stockTaskProcessId,
               item_id itemId,
               sku,
               list_no listNo,
               quality_date qualityDate,
               quantity,
               production_date productionDate,
               remark,
               transfer_sku transferSku,
               transfer_quantity transferQuantity,
               transfer_scale transferScale,
               create_time createTime,
               creator,
               gl_no glNo,
               in_gl_no inGlNo,
               status
        from stock_task_process stp
                 left join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id
        where stp.stock_task_id = #{stockTaskId} and stpd.sku=#{sku}
    </select>

    <select id="unFinishTemporaryTransferTask" resultType="boolean">
        select  count(1) > 0 from stock_task_process_detail stpd
        left join stock_task_process stp on stp.id = stpd.stock_task_process_id
        left join stock_task st on stp.stock_task_id = st.id
        where st.type = 82
        and st.state in (0, 1)
        and st.remark = '临保'
        and st.area_no = #{storeNo}
        and stpd.transfer_sku = #{sku}
        and stpd.list_no = #{listNo}
        and stpd.quality_date =  #{qualityDate}
        <if test="glNo != null">
            and stpd.gl_no = #{glNo}
        </if>
    </select>

    <select id="selectByProcessDetail" parameterType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO" resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        select stpd.quality_date qualityDate, sum(stpd.quantity) inQuantity, stpd.list_no listNo,stpd.gl_no glNo, stpd.sku,stpd.production_date productionDate
        from stock_task st
                 left join stock_task_process stp on st.id = stp.stock_task_id
                 inner join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id and stpd.list_no = #{listNo} and st.task_no = #{taskNo} and stpd.sku = #{sku} and stpd.quality_date = #{qualityDate}
        where st.type = 10
        group by stpd.quality_date,stpd.list_no,stpd.gl_no,stpd.sku
    </select>

    <select id="selectWarehouseByPurchaseNo" resultType="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetail">
        select stpd.sku,stpd.quantity
        from stock_task_process_detail stpd
                 left join stock_task_process stp on stpd.stock_task_process_id = stp.id
                 left join stock_task st on stp.stock_task_id = st.id
        where
            st.state in (1, 2)
          and stpd.status = 1
          and st.task_no = #{purchaseNo}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.wms.manage.model.domain.StockTaskProcessDetail">
        SELECT id,stock_task_process_id stockTaskProcessId,item_id itemId,sku,list_no listNo,quality_date qualityDate,quantity,
               add_time addTime,`state`
        FROM stock_task_process_detail
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectSkuTotalQuantityByStockTaskId"  resultType="net.summerfarm.wms.manage.model.vo.StockTaskProcessDetailVO">
        /*FORCE_MASTER*/ select stpd.sku, sum(stpd.quantity) quantity
        from stock_task_process stp
        inner join stock_task_process_detail stpd on stp.id = stpd.stock_task_process_id
        where stp.stock_task_id = #{stockTaskId}
        group by stpd.sku
    </select>

</mapper>