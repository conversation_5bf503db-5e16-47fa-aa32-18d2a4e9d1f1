package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * @Description
 * @Date 2023/5/22 10:46
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskItemCabinetOccupy {
    /**
     * primary key
     */
    private Long id;

    /**
     * 出库任务编码
     */
    private Long stockTaskId;

    /**
     * 出库任务明细编码
     */
    private Long stockTaskItemId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 保质期
     */
    private Date qualityDate;

    /**
     * 占用数量
     */
    private Integer occupyQuantity;

    /**
     * 拣货数量
     */
    private Integer pickQuantity;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 是否软删 0-正常 1-软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 释放数量
     */
    private Integer releaseQuantity;

    /**
     * 应拣（出）数量
     */
    private Integer shouldPickQuantity;

    /**
     * 实际拣货数量
     */
    private Integer actualPickQuantity;

    /**
     * 异常数量
     */
    private Integer abnormalQuantity;

    /**
     * 库区编码
     */
    private String zoneCode;

    /**
     * 出库任务类型
     */
    private Integer stockTaskType;

    public Integer getPickQuantity(){
        if (pickQuantity == null){
            return 0;
        }
        return pickQuantity;
    }

    public String dupKey() {
        LocalDate tmpProductionDate = null;
        if (productionDate != null) {
            tmpProductionDate = productionDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }
        LocalDate tmpQualityDate = null;
        if (qualityDate != null) {
            tmpQualityDate = qualityDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }
        return sku + tmpProductionDate + tmpQualityDate + cabinetCode;
    }
}
