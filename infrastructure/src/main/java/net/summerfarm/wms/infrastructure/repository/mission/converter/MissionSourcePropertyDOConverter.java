package net.summerfarm.wms.infrastructure.repository.mission.converter;

import com.google.common.collect.Lists;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class MissionSourcePropertyDOConverter {

    /**
     * 根据来源属性列表分组获取来源订单号列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单号列表
     */
    public static Map<String, List<String>> getSourceOrderNoListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            // 数据分组
            Map<String, List<String>> map = sourcePropertyList.stream().filter(it -> Objects.nonNull(it.getSourceOrderNo()))
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getSourceOrderNo, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<String> sourceOrderNoList = map.get(it);
                if (CollectionUtils.isNotEmpty(sourceOrderNoList)) {
                    sourceOrderNoList = sourceOrderNoList.stream().distinct().collect(Collectors.toList());
                    map.put(it, sourceOrderNoList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }

    /**
     * 根据来源属性列表分组获取来源订单来源类型列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单类型列表
     */
    public static Map<String, List<Integer>> getSourceTypeListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            // 数据分组
            Map<String, List<Integer>> map = sourcePropertyList.stream().filter(it -> Objects.nonNull(it.getSourceType()))
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getSourceType, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<Integer> sourceTypeList = map.get(it);
                if (CollectionUtils.isNotEmpty(sourceTypeList)) {
                    sourceTypeList = sourceTypeList.stream().distinct().collect(Collectors.toList());
                    map.put(it, sourceTypeList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }


    /**
     * 根据来源id列表分组获取来源订单来源id列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单id列表
     */
    public static Map<String, List<String>> getSourceIdListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            // 数据分组
            Map<String, List<String>> map = sourcePropertyList.stream().filter(it -> Objects.nonNull(it.getSourceId()))
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getSourceId, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<String> sourceIdList = map.get(it);
                if (CollectionUtils.isNotEmpty(sourceIdList)) {
                    sourceIdList = sourceIdList.stream().distinct().collect(Collectors.toList());
                    map.put(it, sourceIdList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }

    /**
     * 根据来源属性列表分组获取来源订单城配仓列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单类型列表
     */
    public static Map<String, List<Long>> getStoreNoListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            Map<String, List<Long>> map = sourcePropertyList.stream().filter(it -> Objects.nonNull(it.getStoreNo()))
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getStoreNo, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<Long> storeNoList = map.get(it);
                if (CollectionUtils.isNotEmpty(storeNoList)) {
                    storeNoList = storeNoList.stream().distinct()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    map.put(it, storeNoList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }

    /**
     * 根据来源属性列表分组获取来源订单出库仓列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单类型列表
     */
    public static Map<String, List<Long>> getTargetWarehouseNoListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            Map<String, List<Long>> map = sourcePropertyList.stream().filter(it -> Objects.nonNull(it.getTargetWarehouseNo()))
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getTargetWarehouseNo, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<Long> warehouseNoList = map.get(it);
                if (CollectionUtils.isNotEmpty(warehouseNoList)) {
                    warehouseNoList = warehouseNoList.stream().filter(Objects::nonNull)
                            .distinct().collect(Collectors.toList());
                    map.put(it, warehouseNoList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }


    /**
     * 获取 城配仓编号/调拨出库目标仓库编号
     *
     * @param storeNoList           城配仓
     * @param targetWarehouseNoList 库存仓列表
     * @return 返回仓库编号
     */
    public static Long getPageStoreNo(List<Long> storeNoList, List<Long> targetWarehouseNoList) {
        Long storeNo = CollectionUtils.isEmpty(storeNoList) ? null : storeNoList.get(0);
        if (CollectionUtils.isEmpty(targetWarehouseNoList)) {
            return storeNo;
        }
        return targetWarehouseNoList.get(0);
    }

    /**
     * 获取 城配仓编号/调拨出库目标仓库编号
     *
     * @param centerMap             库存仓内容
     * @param storeNoList           城配仓编号
     * @param targetWarehouseNoList 库存仓列表
     * @return 返回仓库编号
     */
    public static String getPageStoreName(Map<Long, WarehouseStorageCenterEntity> centerMap,
                                    List<Long> storeNoList,
                                    List<Long> targetWarehouseNoList) {
        Long storeNo = CollectionUtils.isEmpty(storeNoList) ? null : storeNoList.get(0);
        if (CollectionUtils.isEmpty(targetWarehouseNoList)) {
            return storeNo == null ? "" : Global.storeMap.get(storeNo.intValue());
        }
        WarehouseStorageCenterEntity center = centerMap.get(storeNo);
        return center == null ? "" :
                (center.getWarehouseName() == null ? "" : center.getWarehouseName());
    }

    /**
     * 获取 城配仓编号/调拨出库目标仓库编号
     *
     * @param storeNoList           城配仓
     * @param targetWarehouseNoList 库存仓列表
     * @return 返回仓库编号
     */
    public static List<Long> getPageStoreNoList(List<Long> storeNoList, List<Long> targetWarehouseNoList) {
        if (CollectionUtils.isEmpty(storeNoList) && CollectionUtils.isEmpty(targetWarehouseNoList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(targetWarehouseNoList)) {
            return storeNoList;
        }
        return targetWarehouseNoList;
    }

    /**
     * 获取 城配仓编号/调拨出库目标仓库编号
     *
     * @param centerMap             库存仓内容
     * @param storeNoList           城配仓编号
     * @param targetWarehouseNoList 库存仓列表
     * @return 返回仓库编号
     */
    public static List<String> getPageStoreNameList(Map<Long, WarehouseStorageCenterEntity> centerMap,
                                              List<Long> storeNoList,
                                              List<Long> targetWarehouseNoList) {
        ArrayList<String> storeNameList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(storeNoList) && CollectionUtils.isEmpty(targetWarehouseNoList)) {
            return storeNameList;
        }
        if (CollectionUtils.isEmpty(targetWarehouseNoList)) {
            for (Long storeNo : storeNoList) {
                if (Objects.nonNull(storeNo)) {
                    String storeName = Global.storeMap.get(storeNo.intValue());
                    if (StringUtils.isNotEmpty(storeName)) {
                        storeNameList.add(storeName);
                    }
                }
            }
            return storeNameList.stream().distinct().collect(Collectors.toList());
        }
        for (Long warehouseNo : targetWarehouseNoList) {
            WarehouseStorageCenterEntity center = centerMap.get(warehouseNo);
            if (Objects.nonNull(center)) {
                String warehouseName = center.getWarehouseName();
                if (StringUtils.isNotEmpty(warehouseName)) {
                    storeNameList.add(warehouseName);
                }
            }
        }
        return storeNameList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据来源属性列表分组获取业务标签列表
     *
     * @param sourcePropertyList 来源属性列表
     * @return 返回分组后的获取来源订单类型列表
     */
    public static Map<String, List<Integer>> getBusinessTagListMap(List<MissionSourcePropertyDO> sourcePropertyList) {
        if (CollectionUtils.isNotEmpty(sourcePropertyList)) {
            Map<String, List<Integer>> map = sourcePropertyList.stream()
                    .collect(Collectors.groupingBy(MissionSourcePropertyDO::getMissionNo,
                            Collectors.mapping(MissionSourcePropertyDO::getBusinessTag, Collectors.toList())));
            // 数据去重
            Set<String> missionNoSet = map.keySet();
            missionNoSet.forEach(it -> {
                List<Integer> businessTagList = map.get(it);
                if (CollectionUtils.isNotEmpty(businessTagList)) {
                    businessTagList = businessTagList.stream().filter(Objects::nonNull)
                            .distinct().collect(Collectors.toList());
                    map.put(it, businessTagList);
                }
            });
            return map;
        }
        return new HashMap<>();
    }

    public static Integer getBusinessTag(List<Integer> businessTagList) {
        if(CollectionUtils.isEmpty(businessTagList)){
            return null;
        }
        return businessTagList.get(0);
    }
}
