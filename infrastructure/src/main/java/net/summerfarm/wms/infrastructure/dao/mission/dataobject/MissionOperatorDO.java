package net.summerfarm.wms.infrastructure.dao.mission.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.Entity;

import java.time.LocalDate;

/**
 * 任务操作人信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MissionOperatorDO{
    Long id;
    /**
     * 任务编号
     */
    String missionNo;
    /**
     * 操作人状态
     */
    Integer state;
    /**
     * 操作人名称
     */
    String operatorName;
    /**
     * 操作人id
     */
    Long operatorId;
    /**
     * 取消时间
     */
    Long cancelTime;
    /**
     * 创建时间
     */
    LocalDate createTime;
    /**
     * 更新时间
     */
    LocalDate updateTime;
}
