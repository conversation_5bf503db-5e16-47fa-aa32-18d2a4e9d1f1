package net.summerfarm.wms.api.h5.instore.dto.res;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StorageTaskCategoryTagResDTO implements Serializable {
    /**
     * 类目名称
     */
    String categoryName;

    /**
     * 类目id
     */
    Integer categoryId;
}
