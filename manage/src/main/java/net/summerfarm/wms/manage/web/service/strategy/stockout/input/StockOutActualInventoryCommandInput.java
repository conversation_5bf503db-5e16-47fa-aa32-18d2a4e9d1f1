package net.summerfarm.wms.manage.web.service.strategy.stockout.input;

import lombok.Data;
import net.summerfarm.wms.manage.web.service.strategy.stockout.dto.StockOutActualInventoryDetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/28
 */
@Data
public class StockOutActualInventoryCommandInput implements Serializable {

    private static final long serialVersionUID = -480207713995545241L;

    /**
     * 出库任务id
     */
    private Long stockTaskId;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 出库入参
     */
    private String jsonData;

    /**
     * 出库明细
     */
    private List<StockOutActualInventoryDetail> stockOutActualInventoryDetails;

}
