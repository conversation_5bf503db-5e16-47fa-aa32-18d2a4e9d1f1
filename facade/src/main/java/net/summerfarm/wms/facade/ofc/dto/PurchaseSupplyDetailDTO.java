package net.summerfarm.wms.facade.ofc.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
public class PurchaseSupplyDetailDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 采购供应单编号
     */
    private String psoNo;

    /**
     * 来源订单号/售后单号
     */
    private String orderNo;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 库存仓号（冗余字段）
     */
    private Long warehouseNo;

    /**
     * 城配仓号
     */
    private Integer storeNo;

    /**
     * 鲜沐货品编码
     */
    private String skuCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 履约单号
     */
    private Long fulfillmentNo;
    /**
     * POP品买手花名
     */
    private String buyerName;
    /**
     * 门店名称
     */
    private String merchantName;
    /**
     * 门店id
     */
    private String merchantId;
    /**
     * 履约时间
     */
    private LocalDate fulfillmentTime;
    /**
     * 履约方式，0：干配，1：自提，2：干线，3：快递
     */
    private Integer fulfillmentWay;
}
