package net.summerfarm.wms.web.controller.instore;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.instore.StockStorageCommandService;
import net.summerfarm.wms.api.h5.instore.StockStorageQueryService;
import net.summerfarm.wms.api.h5.instore.dto.req.*;
import net.summerfarm.wms.api.h5.instore.dto.res.*;
import net.summerfarm.wms.application.instore.WmsStockTaskStorageNoticeOrderCommandService;
import net.summerfarm.wms.application.instore.input.ResolveInitInboundInput;
import net.summerfarm.wms.application.instore.input.StockTaskStorageCreateByNoticeCommandInput;
import net.summerfarm.wms.application.instore.vo.ResolveInitInboundResp;
import net.summerfarm.wms.application.common.InitInboundExcelDealService;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.api.h5.instore.dto.req.StockStorageArrivalCargoLimitInput;
import net.summerfarm.wms.api.h5.instore.dto.res.StockStorageArrivalCargoLimitVO;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.scheduler.PopAutoOutStoreJob;
import net.summerfarm.wms.scheduler.PopSaleOutStockCreateJOB;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2022/11/22  10:14
 * @folder 入库管理/入库任务
 */
@RestController
@RequestMapping("/in-store")
public class StockStorageController {

    @Resource
    StockStorageQueryService stockStorageQueryService;

    @Resource
    StockStorageCommandService stockStorageCommandService;

    @Resource
    PopSaleOutStockCreateJOB popSaleOutStockCreateJOB;

    @Resource
    PopAutoOutStoreJob popAutoOutStoreJob;

    @Resource
    private WmsStockTaskStorageNoticeOrderCommandService wmsStockTaskStorageNoticeOrderCommandService;

    @Resource
    private InitInboundExcelDealService initInboundExcelDealService;

    /**
     * 入库任务列表
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<StockStorageListResDTO>> queryStockStorageList(@Valid @RequestBody StockStoragePageQuery pageQuery) {
        PageInfo<StockStorageListResDTO> pageInfo = stockStorageQueryService.queryListResDTO(pageQuery);
        return CommonResult.ok(pageInfo);
    }

    /**
     * pda入库列表
     *
     * @param query
     * @return
     */
    @PostMapping("/query/list_pda")
    public CommonResult<PageInfo<StockStoragePagePDAResDTO>> queryListForPDA(@Valid @RequestBody StockStoragePagePDAQuery query) {
        PageInfo<StockStoragePagePDAResDTO> stockStoragePagePDAResDTOPageInfo = stockStorageQueryService.queryListForPDA(query);
        return CommonResult.ok(stockStoragePagePDAResDTOPageInfo);
    }

    /**
     * 打印详情
     *
     * @param query
     * @return
     */
    @PostMapping("/query/print_detail")
    public CommonResult<StockStoragePrintDetailResDTO> printDetail(@Valid @RequestBody StockStoragePrintDetailQuery query) {
        StockStoragePrintDetailResDTO stockStoragePrintDetailResDTO = stockStorageQueryService.printDetail(query);
        return CommonResult.ok(stockStoragePrintDetailResDTO);
    }


    /**
     * 校验货品条码是否存在于该任务中
     *
     * @param query q
     * @return r
     */
    @PostMapping("/query/cargo_code/exist")
    public CommonResult<Boolean> cargoCodeExistTask(@Valid @RequestBody StockStorageCheckCargoCodeQuery query) {
        Boolean exist = stockStorageQueryService.cargoCodeExistTask(query);
        return CommonResult.ok(exist);
    }

    /**
     * 根据货品条码/三级类目和任务id查询条目
     */
    @PostMapping("/query/pda_item")
    public CommonResult<List<StockStoragePdaItemDTO>> queryPdaItem(@Valid @RequestBody StockStoragePdaItemQuery query) {
        List<StockStoragePdaItemDTO> stockStoragePdaItems = stockStorageQueryService.queryPdaItem(query);
        return CommonResult.ok(stockStoragePdaItems);
    }

    /**
     * item sku的完成状态
     *
     * @param query q
     * @return StockTaskStorageSkuReceivingStateEnum
     */
    @PostMapping("/query/check_sku_item_state")
    public CommonResult<Integer> skuItemStateCheck(@Valid @RequestBody StockStoragePdaItemQuery query) {
        Integer state = stockStorageQueryService.skuItemStateCheck(query);
        return CommonResult.ok(state);
    }

    /**
     * 入库任务日志查询
     *
     * @param query q
     * @return res
     */
    @PostMapping("/query/storage_log")
    public CommonResult<List<StockStorageLogResDTO>> listStockTaskStorageLog(@Valid @RequestBody StockStorageLogQuery query) {
        List<StockStorageLogResDTO> stockStorageLogResDTO = stockStorageQueryService.listStockTaskStorageLog(query);
        return CommonResult.ok(stockStorageLogResDTO);
    }

    /**
     * 查询任务下的所有三级类目标签
     *
     * @param query
     * @return
     */
    @PostMapping("/query/storage_task/category_tag")
    public CommonResult<List<StorageTaskCategoryTagResDTO>> queryStorageTaskAllCategory(@Valid @RequestBody StorageTaskCategoryTagQuery query) {
        List<StorageTaskCategoryTagResDTO> storageTaskCategoryTagResDTOS = stockStorageQueryService.queryStorageTaskAllCategory(query);
        return CommonResult.ok(storageTaskCategoryTagResDTOS);
    }

    /**
     * 入库任务详情
     */
    @PostMapping("/query/detail")
    public CommonResult<StockStorageDetailResDTO> queryStockStorageDetail(@Valid @RequestBody StockStorageDetailQuery detailQuery) {
        StockStorageDetailResDTO stockStorageDetailResDTO = stockStorageQueryService.queryDetailDTO(detailQuery);
        return CommonResult.ok(stockStorageDetailResDTO);
    }

    /**
     * pda入库任务详情
     */
    @PostMapping("/query/detail_pda")
    public CommonResult<StockStorageDetailPDAResDTO> queryDetailForPDA(@Valid @RequestBody StockStorageDetailQuery detailQuery) {
        StockStorageDetailPDAResDTO stockStorageDetailPDAResDTO = stockStorageQueryService.queryDetailForPDA(detailQuery);
        return CommonResult.ok(stockStorageDetailPDAResDTO);
    }


    /**
     * 批量查询入库任务详情
     */
    @PostMapping("/list/detail")
    public CommonResult<List<StockStorageDetailListResDTO>> listStockStorageDetail(@Valid @RequestBody StockStorageDetailQuery detailQuery) {
        List<StockStorageDetailListResDTO> stockStorageDetailListResDTOS = stockStorageQueryService.listDetail(detailQuery);
        return CommonResult.ok(stockStorageDetailListResDTOS);
    }

    /**
     * 批量获取入库单id
     * *
     */
    @PostMapping("/task/getBatchStorageSkuDetail")
    public CommonResult<List<StockTaskStorageItemDTO>> getBatchStorageSkuDetail(@Valid @RequestBody BatchStorageSkuDetailDTO batchStorageSkuDetailDTO) {
        List<StockTaskStorageItemDTO> stockTaskStorageItemDTOS = stockStorageQueryService.queryBatchStorageSkuDetail(batchStorageSkuDetailDTO);
        return CommonResult.ok(stockTaskStorageItemDTOS);
    }

    /**
     * 新建入库任务
     */
    @PostMapping("/save")
    public CommonResult saveStockStorage(@Valid @RequestBody StockStorageCommandDTO stockStorageCommandDTO) {
        stockStorageCommandService.createStockStorage(stockStorageCommandDTO);
        return CommonResult.ok();
    }

    /**
     * 任务关闭
     */
    @PostMapping("/close")
    public CommonResult closeStockStorage(@Valid @RequestBody StockStorageCompleteReqDTO reqDTO) {
        stockStorageCommandService.closeStockStorage(reqDTO);
        return CommonResult.ok();
    }

    /**
     * 完成任务
     */
    @PostMapping("/complete")
    public CommonResult completeStockStorage(@Valid @RequestBody StockStorageCompleteReqDTO reqDTO) {
        stockStorageCommandService.completeStockStorage(reqDTO);
        return CommonResult.ok();
    }

    /**
     * 列表页面批量下载
     */
    @GetMapping("/down")
    public CommonResult downStockStorage(Long stockStorageId) {
        StockStorageDownReqDTO storageDownReqDTO = StockStorageDownReqDTO.builder().stockStorageId(stockStorageId).build();
        stockStorageCommandService.downStockStorage(storageDownReqDTO);
        return CommonResult.ok();
    }


    /**
     * 详情页面下载
     */
    @GetMapping("/batch/down")
    public CommonResult downBatchStockStorage(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ErrorCode.NOT_ID_PURCHASE_ERR.getMessage());
        }
        List<String> strings = Arrays.asList(ids.split(","));
        List<Long> idList = strings.stream().map(Long::valueOf).collect(Collectors.toList());
        StockStorageDownReqDTO downReqDTO = StockStorageDownReqDTO.builder()
                .stockStorageIdList(idList)
                .build();
        stockStorageCommandService.downBatchStockStorage(downReqDTO);
        return CommonResult.ok();
    }

    /**
     * 确认到货
     *
     * @param command c
     * @return r
     */
    @PostMapping("/upsert/arrival_cargo")
    public CommonResult<Boolean> arrivalCargo(@Valid @RequestBody CargoArrivalCommand command) {
        stockStorageCommandService.arrivalCargo(command);
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 确认到货时间限制
     *
     * @param input 查询条件
     * @return 返回限制时间内容
     */
    @PostMapping("/query/arrival_cargo_limit")
    public CommonResult<StockStorageArrivalCargoLimitVO> arrivalCargo(@RequestBody @Valid StockStorageArrivalCargoLimitInput input) {
        StockStorageArrivalCargoLimitVO stockStorageArrivalCargoLimitVO = stockStorageCommandService.arrivalCargoLimit(input);
        return CommonResult.ok(stockStorageArrivalCargoLimitVO);
    }

    /**
     * 完成本次到货
     *
     * @param command c
     * @return r
     */
    @PostMapping("/upsert/arrival_cargo_finish")
    public CommonResult<Boolean> arrivalCargoFinish(@Valid @RequestBody CargoArrivalCommand command) {
        command.setWebExe(true);
        stockStorageCommandService.arrivalCargoFinish(command);
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 查询仓下所有生产批次的效期信息
     *
     * @param query query
     * @return r
     */
    @PostMapping("/query/sku/validity")
    public CommonResult<List<StockStorageSkuValidityResDTO>> querySkuValidity(@RequestBody StockStorageSkuValidityQuery query) {
        List<StockStorageSkuValidityResDTO> resDTOList = stockStorageQueryService.querySkuValidity(query);
        return CommonResult.ok(resDTOList);
    }

    /**
     * 保存并自动完成入库
     */
    @PostMapping("/upsert/save/auto_complete")
    public CommonResult<Long> saveAndCompeteStockStorage(@Valid @RequestBody SaveCompeteStockStorageCommandDTO command) {
        command.setUserOperatorName(LoginInfoThreadLocal.getCurrentUserName());
        command.setAdminId(LoginInfoThreadLocal.getCurrentUserId());
        command.setTenantId(LoginInfoThreadLocal.getTenantId());
        Long taskId = stockStorageCommandService.saveAndCompeteStockStorage(command);
        return CommonResult.ok(taskId);
    }

    /**
     * 初始化越库出库任务
     * @param initCrossStockTaskInput
     * @return
     */
    @PostMapping("/upsert/init/cross_stock_task")
    public CommonResult<Boolean> initCrossStockTask(@RequestBody InitCrossStockTaskInput initCrossStockTaskInput) {
        stockStorageCommandService.initCrossStockTask(initCrossStockTaskInput.getWarehouseNo(), LocalDateTime.of(initCrossStockTaskInput.getExpectTime(), LocalTime.of(0, 0)));
        return CommonResult.ok(true);
    }

    /**
     * 入库完成消息补偿
     * @param stockTaskStorageFinishMsgSendInput
     * @return
     */
    @PostMapping("/upsert/finish_msg/stock_task_storage")
    public CommonResult<Boolean> initCrossStockTask(@RequestBody StockTaskStorageFinishMsgSendInput stockTaskStorageFinishMsgSendInput) {
        stockStorageCommandService.sendInStoreTaskFinish(stockTaskStorageFinishMsgSendInput.getStockTaskStorageId());
        return CommonResult.ok(true);
    }

    /**
     * POP 销售出库
     * @param deliveryTime
     * @return
     * @throws Exception
     */
    @PostMapping("/backdoor/pop_sale_generate_stock_out_task")
    public CommonResult<Boolean> popSaleGenerateStockOutTask(String deliveryTime) throws Exception {
        XmJobInput context = new XmJobInput();
        context.setInstanceParameters(deliveryTime);
        popSaleOutStockCreateJOB.processResult(context);
        return CommonResult.ok(true);
    }

    /**
     * POP自动出库定时任务
     * @param deliveryTime
     * @return
     * @throws Exception
     */
    @PostMapping("/backdoor/pop_auto_complete_stock_task")
    public CommonResult<Boolean> popAutoCompleteStockTask(String deliveryTime) throws Exception {
        XmJobInput context = new XmJobInput();
        context.setInstanceParameters(deliveryTime);
        popAutoOutStoreJob.processResult(context);
        return CommonResult.ok(true);
    }

    /**
     * 通过入库通知单生成入库任务工具
     * @param stockTaskStorageCreateByNoticeCommandInput
     * @return
     */
    @PostMapping("/upsert/create/stock_task_storage_by_notice")
    public CommonResult<Boolean> recreateStockTaskStorageByNotice(@RequestBody StockTaskStorageCreateByNoticeCommandInput stockTaskStorageCreateByNoticeCommandInput) {
        if (null == stockTaskStorageCreateByNoticeCommandInput || CollectionUtils.isEmpty(stockTaskStorageCreateByNoticeCommandInput.getGoodsRecycleOrderNos())) {
            return CommonResult.ok(false);
        }
        wmsStockTaskStorageNoticeOrderCommandService.createStockTaskStorage(stockTaskStorageCreateByNoticeCommandInput);
        return CommonResult.ok(true);
    }

    /**
     * 期初入库
     * @param resolveInitInboundInput
     * @return
     */
    @PostMapping("/resolve/init/inbound_file")
    public CommonResult<ResolveInitInboundResp> resolveInitInboundFile(@RequestBody ResolveInitInboundInput resolveInitInboundInput) {
        if (null == resolveInitInboundInput || StringUtils.isBlank(resolveInitInboundInput.getUrl()) || null == resolveInitInboundInput.getWarehouseNo()) {
            return CommonResult.ok(null);
        }
        ResolveInitInboundResp resp = initInboundExcelDealService.dealOss(resolveInitInboundInput.getUrl(), resolveInitInboundInput.getWarehouseNo());
        return CommonResult.ok(resp);
    }

}
