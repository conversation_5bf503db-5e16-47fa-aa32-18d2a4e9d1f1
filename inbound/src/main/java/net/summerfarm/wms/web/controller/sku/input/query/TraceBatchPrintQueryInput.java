package net.summerfarm.wms.web.controller.sku.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Description: 溯源码批量打印查询<br/>
 * date: 2024/8/7 14:54<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceBatchPrintQueryInput {
    /**
     * 分页条目
     */
    @NotNull(message = "请指定分页条目")
    private Integer pageIndex;

    /**
     * 分页数量
     */
    @NotNull(message = "请指定分页大小")
    private Integer pageSize;

    /**
     * 入库任务id
     */
    @NotNull(message = "入库任务id不能为空")
    private Integer stockTaskStorageId;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 打印编码查询起始值
     */
    private Integer beginStockTaskStorageSeq;

    /**
     * 打印编码查询结束值
     */
    private Integer endStockTaskStorageSeq;
}
