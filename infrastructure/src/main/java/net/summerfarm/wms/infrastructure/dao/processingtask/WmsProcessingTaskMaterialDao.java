package net.summerfarm.wms.infrastructure.dao.processingtask;

import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingTaskMaterialQueryParam;
import net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskMaterialDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 加工任务原料(WmsProcessingTaskMaterial)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-12 10:46:27
 */
public interface WmsProcessingTaskMaterialDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    WmsProcessingTaskMaterialDO queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WmsProcessingTaskMaterialDO> queryAllByLimit(WmsProcessingTaskMaterialQueryParam queryParam);

    /**
     * 统计总行数
     *
     * @param wmsProcessingTaskMaterialDO 查询条件
     * @return 总行数
     */
    long count(WmsProcessingTaskMaterialDO wmsProcessingTaskMaterialDO);

    /**
     * 新增数据
     *
     * @param wmsProcessingTaskMaterialDO 实例对象
     * @return 影响行数
     */
    int insert(WmsProcessingTaskMaterialDO wmsProcessingTaskMaterialDO);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WmsProcessingTaskMaterial> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WmsProcessingTaskMaterialDO> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<WmsProcessingTaskMaterial> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<WmsProcessingTaskMaterialDO> entities);

    /**
     * 修改数据
     *
     * @param wmsProcessingTaskMaterialDO 实例对象
     * @return 影响行数
     */
    int update(WmsProcessingTaskMaterialDO wmsProcessingTaskMaterialDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 增加废料损耗
     *
     * @param id
     * @param wasteLossWeight
     * @param updater
     */
    int addWasteLossWeight(@Param("id") Long id,
                            @Param("wasteLossWeight") BigDecimal wasteLossWeight,
                            @Param("updater") String updater);

    /**
     * 增加剩余原料重量
     * @param id
     * @param remainWeight
     * @param updater
     */
    int addRemainWeight(@Param("id") Long id,
                         @Param("remainWeight") BigDecimal remainWeight,
                         @Param("updater") String updater);

    /**
     * 增加归还原料
     *
     * @param id
     * @param materialSkuRestoreQuantity
     * @param materialSkuRestoreWeight
     * @param updater
     */
    int addRestoreQuantity(@Param("id") Long id,
                           @Param("materialSkuRestoreQuantity") Integer materialSkuRestoreQuantity,
                           @Param("materialSkuRestoreWeight") BigDecimal materialSkuRestoreWeight,
                           @Param("updater") String updater);

    int addMaterial(@Param("id")  Long id,
                        @Param("materialSkuReceiveQuantity") Integer materialSkuReceiveQuantity,
                        @Param("materialSkuReceiveWeight") BigDecimal materialSkuReceiveWeight,
                        @Param("updater") String updater);
}

