package net.summerfarm.wms.domain.common.domainobject;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 容器DO
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ContainerQuery {

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 容器属性（1：收货，2：拣货，3：库内，4：交接，5：通用）
     */
    private Integer purpose;

    /**
     * 容器属性集合（1：收货，2：拣货，3：库内，4：交接，5：通用）
     */
    private List<Integer> purposes;

    /**
     * 容器状态（0：禁用，1：启用）
     */
    private Integer containerStatus;

    /**
     * 占用状态（0：未占用，1：已占用）
     */
    private Integer occupyStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 长（单位：m）
     */
    private Double length;

    /**
     * 宽（单位：m）
     */
    private Double width;

    /**
     * 高（单位：m）
     */
    private Double high;

    /**
     * 重量（单位：kg）
     */
    private Double weight;

    /**
     * 承重量（单位：kg）
     */
    private Double loadWeight;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 库位编码模块查询
     */
    private String containerCodeLike;

    /**
     * 排除默认容器
     */
    private Boolean excludeDefault;
}
