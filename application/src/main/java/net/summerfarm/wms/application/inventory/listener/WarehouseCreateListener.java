package net.summerfarm.wms.application.inventory.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.wms.WarehouseProvider;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.WarehouseStockExtRepository;
import net.summerfarm.wms.storage.WarehouseNameRefreshProvider;
import net.summerfarm.wms.storage.dto.WarehouseNameRefreshDTO;
import net.summerfarm.wnc.client.mq.msg.WarehouseStorageCreateMsg;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@MqListener(topic = "topic_wnc_warehouse",
        tag = "tag_wnc_warehouse_create",
        consumerGroup = "GID_wms_wnc_warehouse")
@Slf4j
public class WarehouseCreateListener extends AbstractMqListener<WarehouseStorageCreateMsg> {

    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private WarehouseStockExtRepository warehouseStockExtRepository;
    @Resource
    private WarehouseNameRefreshProvider warehouseNameRefreshProvider;
    @Resource
    private WarehouseProvider warehouseProvider;

    @Override
    public void process(WarehouseStorageCreateMsg warehouseStorageCreateMsg) {
        log.info("接收wnc新增仓库通知，初始化库存信息，warehouseStorageCreateMsg：{}", JSONObject.toJSONString(warehouseStorageCreateMsg));
        if (warehouseStorageCreateMsg == null ||
                warehouseStorageCreateMsg.getWarehouseNo() == null ||
                warehouseStorageCreateMsg.getTenantId() == null) {
            log.info("WarehouseCreateListener process: " + JSONObject.toJSONString(warehouseStorageCreateMsg));
            return;
        }

        // 鲜沐仓
        if (WmsConstant.XIANMU_TENANT_ID.equals(warehouseStorageCreateMsg.getTenantId())) {
            // 初始化鲜沐仓库存信息
            areaStoreRepository.initAfterCreateXMWarehouse(
                    warehouseStorageCreateMsg.getTenantId(),
                    warehouseStorageCreateMsg.getWarehouseNo());
            warehouseStockExtRepository.initBatchXMWarehouseStockExt(
                    warehouseStorageCreateMsg.getWarehouseNo());
        } else {
            // 初始化仓库库存
            areaStoreRepository.initAfterCreateWarehouse(
                    warehouseStorageCreateMsg.getTenantId(),
                    warehouseStorageCreateMsg.getWarehouseNo());

            // 初始化仓库库存状态
            warehouseStockExtRepository.initBatchWarehouseStockExt(
                    warehouseStorageCreateMsg.getTenantId(),
                    warehouseStorageCreateMsg.getWarehouseNo());
        }

        // 刷新缓存
        WarehouseNameRefreshDTO warehouseNameRefreshDTO = new WarehouseNameRefreshDTO();
        warehouseNameRefreshDTO.setWarehouseName(warehouseStorageCreateMsg.getWarehouseName() + "");
        warehouseNameRefreshDTO.setWarehouseNo(warehouseStorageCreateMsg.getWarehouseNo() != null ?
                warehouseStorageCreateMsg.getWarehouseNo().longValue() :
                null);
        warehouseNameRefreshProvider.refreshWarehouseName(warehouseNameRefreshDTO);

        warehouseProvider.refreshWarehouseName(net.summerfarm.manage.client.wms.dto.req.WarehouseNameRefreshDTO.builder()
                .warehouseNo(warehouseNameRefreshDTO.getWarehouseNo())
                .warehouseName(warehouseNameRefreshDTO.getWarehouseName())
                .build());

    }
}
