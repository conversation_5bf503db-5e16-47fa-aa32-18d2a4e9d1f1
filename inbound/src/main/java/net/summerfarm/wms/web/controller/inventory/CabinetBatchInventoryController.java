package net.summerfarm.wms.web.controller.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.inventory.CabinetBatchInventoryQueryService;
import net.summerfarm.wms.api.h5.inventory.CabinetBatchInventoryRecommendQueryService;
import net.summerfarm.wms.api.h5.inventory.CabinetInventoryRecommendQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryExportFlowReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryExportInfoReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryQueryFlowReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryQueryInfoReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetInventoryQueryRecommendInReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryBatchRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryQueryRecommendOutDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventoryRecommendInDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetInventory.CabinetInventorySingleRecommendOutReq;
import net.summerfarm.wms.api.h5.inventory.dto.res.RecommendInResDTO;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.*;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryRecommendOutResp;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 安全库存精细化锁定锁定
 */
@RestController
@RequestMapping("/cabinetBatchInventory")
public class CabinetBatchInventoryController {

    @Autowired
    private CabinetBatchInventoryQueryService cabinetBatchInventoryQueryService;
    @Autowired
    private CabinetInventoryRecommendQueryService cabinetInventoryRecommendQueryService;
    @Autowired
    private CabinetBatchInventoryRecommendQueryService cabinetBatchInventoryRecommendQueryService;

    /**
     * 出库库位库存推荐
     *
     * @param queryRecommendReq
     * @return
     */
    @PostMapping(value = "/query/recommend/out")
    public CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOut(
            @Valid @RequestBody CabinetInventorySingleRecommendOutReq queryRecommendReq) {

        return cabinetInventoryRecommendQueryService.queryRecommendOut(queryRecommendReq);
    }

    /**
     * 出库批次库位库存推荐
     * @param queryRecommendReq
     * @return
     */
    @PostMapping(value = "/query/batch/recommend/out")
    public CommonResult<PageInfo<CabinetBatchInventoryRecommendOutResp>> queryBatchRecommendOut(
            @Valid @RequestBody CabinetInventorySingleRecommendOutReq queryRecommendReq) {

        return cabinetInventoryRecommendQueryService.queryBatchRecommendOut(queryRecommendReq);
    }

    /**
     * 转换出库库位库存推荐
     * 排除临保效期
     *
     * @param queryRecommendReq
     * @return
     */
    @PostMapping(value = "/query/recommend/out_exclude_abnormal_period")
    public CommonResult<PageInfo<CabinetInventoryRecommendOutResp>> queryRecommendOutExcludeAbnormalPeriod(
            @Valid @RequestBody CabinetInventorySingleRecommendOutReq queryRecommendReq) {
        return cabinetInventoryRecommendQueryService.queryRecommendOutExcludeAbnormalPeriod(queryRecommendReq);
    }

    /**
     * 出库库位库存批量推荐
     *
     * @param queryRecommendReq
     * @return
     */
    @PostMapping(value = "/query/recommend/batch/out")
    public CommonResult<List<CabinetInventoryRecommendOutResp>> queryRecommendBatchOut(
            @Valid @RequestBody CabinetInventoryBatchRecommendOutReq queryRecommendReq) {

        return cabinetInventoryRecommendQueryService.matchBatchRecommendOut(queryRecommendReq);
    }

    /**
     * 入库库位库存推荐
     *
     * @param recommendInDetailReq
     * @return
     */
    @PostMapping(value = "/query/recommend/in")
    public CommonResult<List<RecommendInResDTO>> queryRecommendIn(
            @Valid @RequestBody CabinetInventoryRecommendInDetailReq recommendInDetailReq) {
        return cabinetBatchInventoryRecommendQueryService.queryRecommendIn(recommendInDetailReq);
    }

    /**
     * 库位批次库存查询
     *
     * @param queryInfoReq
     * @return
     */
    @PostMapping(value = "/query/info")
    public CommonResult<PageInfo<CabinetBatchInventoryQueryInfoResp>> queryInfo(
            @Valid @RequestBody CabinetBatchInventoryQueryInfoReq queryInfoReq) {

        return cabinetBatchInventoryQueryService.queryInfo(queryInfoReq);
    }

    /**
     * 库位批次库存导出
     *
     * @param exportInfoReq
     * @return
     */
    @PostMapping(value = "/export-async/info")
    public CommonResult<CabinetBatchInventoryExportInfoResp> exportInfo(
            @Valid @RequestBody CabinetBatchInventoryExportInfoReq exportInfoReq) {

        return cabinetBatchInventoryQueryService.exportInfo(exportInfoReq);
    }

    /**
     * 库位批次库存流水查询
     *
     * @param queryFlowReq
     * @return
     */
    @PostMapping(value = "/query/flow")
    public CommonResult<PageInfo<CabinetBatchInventoryQueryFlowResp>> queryFlow(
            @Valid @RequestBody CabinetBatchInventoryQueryFlowReq queryFlowReq) {
        return cabinetBatchInventoryQueryService.queryFlow(queryFlowReq);
    }

    /**
     * 库位批次库存流水导出
     *
     * @param exportFlowReq
     * @return
     */
    @PostMapping(value = "/export-async/flow")
    public CommonResult<CabinetBatchInventoryExportFlowResp> exportFlow(
            @Valid @RequestBody CabinetBatchInventoryExportFlowReq exportFlowReq) {
        return cabinetBatchInventoryQueryService.exportFlow(exportFlowReq);
    }

    /**
     * 库位批次变动类型
     *
     * @return
     */
    @PostMapping(value = "/query/getAllTypeName")
    public CommonResult<List<String>> getAllTypeName() {
        return cabinetBatchInventoryQueryService.getAllTypeName();
    }

    /**
     * 库位操作变动类型
     *
     * @return
     */
    @PostMapping(value = "/query/getAllOperatorType")
    public CommonResult<Map<Integer, String>> getAllOperatorType(
            String typeName
    ) {
        return cabinetBatchInventoryQueryService.getAllOperatorType(typeName);
    }
}
