package net.summerfarm.wms.api.h5.stocktaking.dto.req;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import net.summerfarm.wms.api.base.BaseReq;
import net.summerfarm.wms.api.h5.stocktaking.dto.StockTakingCommitInfo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:盘点任务取消请求对象
 * date: 2023/10/23 14:44
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class StockTakingCancelCommand extends BaseReq implements Serializable {

    private static final long serialVersionUID = 3613457556316083205L;

    /**
     * 任务id
     */
    @NotNull(message = "任务Id不能为空")
    Long taskId;

    /**
     * 盘点单号
     */
    String stockTakingNo;

    /**
     * 仓库编号
     */
    Integer warehouseNo;

    /**
     * 操作人
     */
    String operator;
}
