package net.summerfarm.wms.application.safeinventorylock.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 安全库存锁定输入
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmsSafeInventoryLockInput implements Serializable {

    private static final long serialVersionUID = 146718161148646492L;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码不能为空")
    private Integer warehouseNo;

    /**
     * sku
     */
    @NotBlank(message = "SKU不能为空")
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定数量
     */
    @NotNull(message = "锁定数量不能为空")
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    @NotNull(message = "锁定类型不能为空")
    private Integer lockType;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;

    /**
     * 操作人
     */
    private String operator;
}
