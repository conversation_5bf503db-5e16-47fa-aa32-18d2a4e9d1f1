package net.summerfarm.wms.application.inventory.biz.converter;

import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryRecommendOutResp;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetInventory.CabinetInventoryRecommendOutResp;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetInventory;
import net.summerfarm.wms.domain.inventory.domainobject.aggregate.valueObject.CabinetInventoryRecommend;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CabinetInventoryRecommendOutRespConverter {

    CabinetInventoryRecommendOutRespConverter INSTANCE = Mappers.getMapper(CabinetInventoryRecommendOutRespConverter.class);

    CabinetInventoryRecommendOutResp convert(CabinetInventoryRecommend recommend);

    List<CabinetInventoryRecommendOutResp> convertList(List<CabinetInventoryRecommend> recommendList);

    @Mappings({
            @Mapping(target = "skuCode", source = "sku")
    })
    CabinetInventoryRecommendOutResp convert2(CabinetInventory recommend);

    List<CabinetBatchInventoryRecommendOutResp> convertBatchList(List<CabinetBatchInventory> cabinetInventoryList);

    List<CabinetInventoryRecommendOutResp> convertList2(List<CabinetInventory> cabinetInventoryList);

    List<CabinetInventoryRecommendOutResp> convertList3(List<CabinetBatchInventory> cabinetInventoryList);

    @Mappings({
            @Mapping(target = "skuCode", source = "sku")
    })
    CabinetInventoryRecommendOutResp convertResp(CabinetBatchInventory cabinetBatchInventory);

    @Mappings({
            @Mapping(target = "skuCode", source = "sku")
    })
    CabinetBatchInventoryRecommendOutResp convertBatchResp(CabinetBatchInventory cabinetBatchInventory);

}
