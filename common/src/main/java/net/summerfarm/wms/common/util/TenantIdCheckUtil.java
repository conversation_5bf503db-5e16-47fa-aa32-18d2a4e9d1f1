package net.summerfarm.wms.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.xianmu.common.exception.BizException;

import java.util.Objects;

@Slf4j
public class TenantIdCheckUtil {
    public static Boolean isXm(Long tenantId) {
        if (tenantId != null && tenantId > 1L) {
            return false;
        }
        return true;
    }

    public static void checkTenantMatch(Long loginTenantId, Long taskTenantId) {
        log.info("租户信息 loginTenantId:{}, taskTenantId:{} ", loginTenantId, taskTenantId);
        if (Objects.isNull(loginTenantId) || Objects.isNull(taskTenantId)) {
            log.error("未获取到租户信息 loginTenantId:{}, taskTenantId:{} \n", loginTenantId, taskTenantId);
            return;
        }
        // 鲜沐后台登录不做校验
        if (WmsConstant.XIANMU_TENANT_ID.equals(loginTenantId)) {
            return;
        }
        if (!loginTenantId.equals(taskTenantId)) {
            log.error("请求登录租户与业务单据租户信息不匹配，loginTenantId:{}, taskTenantId:{} \n", loginTenantId, taskTenantId);
            throw new BizException("请求单据不存在");
        }

    }

}
