package net.summerfarm.wms.domain.inventory.domainService.impl;

import net.summerfarm.wms.domain.StoreRecord.StoreRecordRepository;
import net.summerfarm.wms.domain.batch.repository.CostBatchDetailRepository;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import net.summerfarm.wms.domain.inventory.domainService.CostBatchDomainService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;


@Service
public class CostBatchDomainServiceImpl implements CostBatchDomainService {

    @Resource
    private CostBatchDetailRepository costBatchDetailRepository;
    @Resource
    private CostBatchRepository costBatchRepository;
    @Resource
    private StoreRecordRepository storeRecordRepository;

    private final Long PEEK_COUNT = 100L;

    @Override
    public Long updatePurchaseCost(Integer warehouseNo, String skuCode,
                                   String purchaseNo, BigDecimal singleCost) {
        Long changeTotal = 0L;

        if (Objects.isNull(warehouseNo) ||
                StringUtils.isEmpty(skuCode) ||
                StringUtils.isEmpty(purchaseNo) ||
                Objects.isNull(singleCost)
        ) {
            return changeTotal;
        }

        Long count;
        Integer num;
        for (count = PEEK_COUNT, num = 0; count >= PEEK_COUNT && num <= PEEK_COUNT; num++) {
            count = storeRecordRepository.updatePurchaseCost100(
                    warehouseNo, skuCode, purchaseNo, singleCost
            );
            changeTotal += count;
        }

        for (count = PEEK_COUNT, num = 0; count >= PEEK_COUNT && num <= PEEK_COUNT; num++) {
            count = costBatchRepository.updatePurchaseCost100(
                    warehouseNo, skuCode, purchaseNo, singleCost
            );
            changeTotal += count;
        }

        for (count = PEEK_COUNT, num = 0; count >= PEEK_COUNT && num <= PEEK_COUNT; num++) {
            count = costBatchDetailRepository.updatePurchaseCost100(
                    warehouseNo, skuCode, purchaseNo, singleCost
            );
            changeTotal += count;
        }
        return changeTotal;
    }
}
