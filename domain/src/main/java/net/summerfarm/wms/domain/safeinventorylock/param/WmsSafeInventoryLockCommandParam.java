package net.summerfarm.wms.domain.safeinventorylock.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 安全库存锁定表命令参数
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmsSafeInventoryLockCommandParam {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 初始化数量
     */
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定状态，1：锁定 0：释放
     */
    private Integer lockStatus;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;

    /**
     * 创建人ID
     */
    private Long createOperatorId;

    /**
     * 更新人ID
     */
    private Long updateOperatorId;

    /**
     * 仓库租户ID 1-鲜沐
     */
    private Long warehouseTenantId;
}
