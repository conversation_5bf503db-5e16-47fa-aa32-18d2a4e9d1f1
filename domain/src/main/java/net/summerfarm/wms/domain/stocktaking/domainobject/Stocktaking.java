package net.summerfarm.wms.domain.stocktaking.domainobject;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.enums.SystemSourceEnum;
import net.summerfarm.wms.domain.base.Entity;
import net.summerfarm.wms.domain.stocktaking.StocktakingDelegate;
import net.summerfarm.wms.domain.stocktaking.enums.StockTakingAuditState;
import net.summerfarm.wms.domain.stocktaking.enums.StockTakingCycle;
import net.summerfarm.wms.domain.stocktaking.enums.StockTakingState;
import net.summerfarm.wms.domain.stocktaking.enums.StocktakingDimension;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 盘点对象
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Stocktaking implements Entity {
    /**
     * 任务id
     */
    Long id;

    /**
     * 仓库编号
     */
    Long warehouseNo;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    Long tenantId;

    /**
     * 周期
     */
    StockTakingCycle cycle;

    /**
     * 盘点维度
     */
    StocktakingDimension dimension;

    /**
     * 任务状态
     */
    StockTakingState state;

    /**
     * 审核状态
     */
    StockTakingAuditState auditState;

    /**
     * 创建时间
     */
    Long createdAt;

    /**
     * 更新时间
     */
    Long updatedAt;

    /**
     * 操作人
     */
    String operator;

    /**
     * 盘点时间
     */
    LocalDate checkDate;

    /**
     * 备注
     */
    String remark;

    /**
     * 盘点方式
     * 10-明盘，20-盲盘
     */
    Integer method;

    /**
     * 实例信息
     */
    List<StockTakingItem> items;

    /**
     * 盘点单号
     */
    String stockTakingNo;

    /**
     * 系统来源 0-内部 1-外部
     */
    SystemSourceEnum systemSource;

    /**
     * 未完成盘点sku
     */
    List<String> unFinishSku;

    // 代理
    StocktakingDelegate stocktakingDelegate;

    public void update() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.updateStocktaking(this);
        }
    }

    /**
     * 更新实例详情
     * 其实应该提供领域服务的，但是盘点一期实例没有生命周期，为了方便直接实现
     */
    public void updateItemDetail() {
        if (Objects.nonNull(stocktakingDelegate)) {
            queryItems().forEach(item -> stocktakingDelegate.updateItemBatches(item.getBatchInfos()));
        }
    }

    public void dealSkuStockIfNeedAudit() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealSkuStockIfNeedAudit(this);
        }
    }

    public void dealSkuStockIfNeedAuditForCabinet() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealSkuStockIfNeedAuditForCabinet(this);
        }
    }

    public void dealSkuStockWhenAuditedSuccess(Boolean isAudit) {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealSkuStockIfAuditedSuccess(this, isAudit);
        }
    }

    public void dealSkuStockWhenAuditedFailed() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealSkuStockIfAuditedFailed(this);
        }
    }

    public void dealCabinetInventoryIfAuditedFailed() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealCabinetInventoryIfAuditedFailed(this);
        }
    }

    public void dealBatchStock() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealBatchStockIfAuditedSuccess(this);
        }
    }

    public void dealBatchStockWhenOpenCabinet(Map<String/*sku+batch+produceDate+qualityDate*/, List<Integer>> map) {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealBatchStockIfAuditedSuccessWhenOpenCabinet(this, map);
        }
    }

    public void dealCabinetInventoryForBatchStocktaking() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.dealCabinetInventoryForBatchStocktaking(this);
        }
    }

    public Map<String/*sku+batch+produceDate+qualityDate*/, List<Integer>> dealCabinetInventory() {
        if (Objects.nonNull(stocktakingDelegate)) {
            return stocktakingDelegate.dealCabinetInventoryIfAuditedSuccess(this);
        }
        return Maps.newHashMap();
    }

    /**
     * 获取盘点实例信息
     *
     * @return 盘点实例
     */
    public List<StockTakingItem> queryItems() {
        if (CollectionUtils.isNotEmpty(items) || Objects.isNull(stocktakingDelegate)) {
            return items;
        }
        return stocktakingDelegate.listStocktakingItems(this.id);
    }

    // 组装对象
    public void assemble() {
        if (Objects.nonNull(stocktakingDelegate)) {
            Stocktaking stocktaking = stocktakingDelegate.getStocktaking(id);
            this.updatedAt = stocktaking.getUpdatedAt();
            this.auditState = stocktaking.getAuditState();
            this.state = stocktaking.getState();
            this.dimension = stocktaking.getDimension();
            this.items = stocktaking.getItems();
            this.createdAt = stocktaking.getCreatedAt();
            this.cycle = stocktaking.getCycle();
            this.operator = stocktaking.getOperator();
            this.warehouseNo = stocktaking.getWarehouseNo();
            this.tenantId = stocktaking.getTenantId();
            this.systemSource = stocktaking.getSystemSource();
            this.stockTakingNo = stocktaking.getStockTakingNo();
        }
    }

    public Map<String/*sku*/, Integer/*realStock*/> reduceSkuMap() {
        Map<String, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.reduceSkuMap()));
        return result;
    }

    public Map<String/*sku_cabinetCode_quality*/, Integer/*realStock*/> reduceSkuMapForCabinet() {
        Map<String, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.reduceSkuMapForCabinet()));
        return result;
    }

    public Map<String/*sku_cabinetCode_produce*/, Integer/*realStock*/> releaseSkuMapForCabinet() {
        Map<String, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.releaseSkuMapForCabinet()));
        return result;
    }

    public Map<String/*sku*/, Integer> excessSkuMap() {
        Map<String, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.excessSkuMap()));
        return result;
    }

    public Map<Long/*produceBatch*/, Integer> existDiffBatch() {
        Map<Long, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.existDiffBatch()));
        return result;
    }

    public Map<Long/*produceBatch*/, List<Integer>> normalDiffBatch() {
        Map<Long, List<Integer>> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.normalDiffBatch()));
        return result;
    }

    public Map<Long/*cabinetInventoryId*/, Integer> existDiffBatchForCabinet() {
        Map<Long, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.existDiffBatchForCabinet()));
        return result;
    }

    public Map<Long/*cabinetInventoryId*/, Integer> normalDiffBatchForCabinet() {
        Map<Long, Integer> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.normalDiffBatchForCabinet()));
        return result;
    }

    public Map<Long, String/*采购单号*/> productBatchMap() {
        Map<Long, String> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.productBatchMap()));
        return result;
    }

    public Map<Long, String/*采购单号*/> cabinetInventoryBatchMap() {
        Map<Long, String> result = Maps.newHashMap();
        items.forEach(item -> result.putAll(item.cabinetInventoryBatchMap()));
        return result;
    }

    public boolean needAudit() {
        return items.stream().anyMatch(item -> {
            long diffAmount = item.diffAmount();
            return Math.abs(diffAmount) >= 500L;
        });
    }

    public String stocktakingMsg() {
        StringBuilder s = new StringBuilder();
        items.forEach(item -> {
            String reduceAmountReason = item.reduceAmountReason();
            if (StringUtils.isNotEmpty(reduceAmountReason)) {
                s.append(reduceAmountReason);
            }
            String excessAmountReason = item.excessAmountReason();
            if (StringUtils.isNotEmpty(excessAmountReason)) {
                s.append(excessAmountReason);
            }
        });
        return s.toString();
    }

    public String stocktakingReduceMsg() {
        StringBuilder s = new StringBuilder();
        items.forEach(item -> {
            String reduceAmountReason = item.reduceAmountReason();
            if (StringUtils.isNotEmpty(reduceAmountReason)) {
                s.append(reduceAmountReason);
            }
        });
        return s.toString();
    }

    public String stocktakingExcessMsg() {
        StringBuilder s = new StringBuilder();
        items.forEach(item -> {
            String excessAmountReason = item.excessAmountReason();
            if (StringUtils.isNotEmpty(excessAmountReason)) {
                s.append(excessAmountReason);
            }
        });
        return s.toString();
    }

    public void finish() {
        this.state = StockTakingState.SUCCESS;
        this.auditState = StockTakingAuditState.SUCCESS;
        this.updatedAt = System.currentTimeMillis();
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.finishStocktaking(this);
        }
    }

    public void underReview() {
        this.state = StockTakingState.STOCKTAKING;
        this.auditState = StockTakingAuditState.AUDITING;
        this.updatedAt = System.currentTimeMillis();
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.auditStocktaking(this);
        }
    }

    public void auditFailed() {
        this.auditState = StockTakingAuditState.FAILED;
        this.updatedAt = System.currentTimeMillis();
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.auditStocktakingFailed(this);
        }
    }

    public void checkCabinetInventoryOccupied() {
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.checkCabinetInventoryOccupied(this);
        }
    }

    public Map<String, Integer> reassemblyReduceSkuMap() {
        if (Objects.nonNull(stocktakingDelegate)) {
            return stocktakingDelegate.reassemblyReduceSkuMap(this);
        }
        return Maps.newHashMap();
    }

    public void cancel() {
        this.state = StockTakingState.CANCEL;
        //审核状态暂时不处理
        this.updatedAt = System.currentTimeMillis();
        if (Objects.nonNull(stocktakingDelegate)) {
            stocktakingDelegate.cancelStocktaking(this);
        }
    }
}
