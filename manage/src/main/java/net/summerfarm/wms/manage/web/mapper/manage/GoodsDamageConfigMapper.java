package net.summerfarm.wms.manage.web.mapper.manage;

import net.summerfarm.wms.manage.model.domain.GoodsDamageConfigPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Repository
public interface GoodsDamageConfigMapper {

    /**
     * 查询货损类型列表
     *
     * <AUTHOR>
     * @Date 2022/11/21 14:14
     * @return java.util.List<net.summerfarm.model.domain.GoodsDamageConfigPO>
     **/
    List<GoodsDamageConfigPO> selectConfigList();

}
