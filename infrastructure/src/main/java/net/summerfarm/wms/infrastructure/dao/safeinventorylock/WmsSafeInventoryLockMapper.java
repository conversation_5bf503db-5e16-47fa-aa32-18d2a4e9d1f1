package net.summerfarm.wms.infrastructure.dao.safeinventorylock;

import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam;
import net.summerfarm.wms.infrastructure.dao.safeinventorylock.dataobject.WmsSafeInventoryLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 安全库存锁定表Mapper接口
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface WmsSafeInventoryLockMapper {

    /**
     * 插入非空字段
     * @param record 记录
     * @return 影响行数
     */
    int insertSelective(WmsSafeInventoryLock record);

    /**
     * 根据主键更新非空字段
     * @param record 记录
     * @return 影响行数
     */
    int updateSelectiveById(WmsSafeInventoryLock record);

    /**
     * 根据主键删除
     * @param id 主键ID
     * @return 影响行数
     */
    int remove(@Param("id") Long id);

    /**
     * 根据主键查询
     * @param id 主键ID
     * @return 记录
     */
    WmsSafeInventoryLock selectById(@Param("id") Long id);

    /**
     * 根据条件查询列表
     * @param param 查询参数
     * @return 记录列表
     */
    List<WmsSafeInventoryLockEntity> selectByCondition(WmsSafeInventoryLockQueryParam param);

    /**
     * 分页查询
     * @param param 查询参数
     * @return 记录列表
     */
    List<WmsSafeInventoryLockEntity> getPage(WmsSafeInventoryLockQueryParam param);

    /**
     * 根据锁定编号查询
     * @param lockNo 锁定编号
     * @return 记录
     */
    WmsSafeInventoryLock selectByLockNo(@Param("lockNo") String lockNo);

    /**
     * 根据仓库编码和SKU查询锁定记录
     * @param warehouseNo 仓库编码
     * @param sku SKU
     * @return 记录列表
     */
    List<WmsSafeInventoryLock> selectByWarehouseNoAndSku(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);

    /**
     * 根据仓库编码、SKU和锁定状态查询锁定记录
     * @param warehouseNo 仓库编码
     * @param sku SKU
     * @param lockStatus 锁定状态
     * @return 记录列表
     */
    List<WmsSafeInventoryLock> selectByWarehouseNoAndSkuAndLockStatus(@Param("warehouseNo") Integer warehouseNo, 
                                                                       @Param("sku") String sku, 
                                                                       @Param("lockStatus") Integer lockStatus);

    /**
     * 根据id更新锁定状态
     * @param id id
     * @param lockStatus 锁定状态
     * @return 影响行数
     */
    int updateLockStatusById(@Param("id") Long id,
                                 @Param("lockStatus") Integer lockStatus);

    /**
     * 根据id更新锁定数量
     * @param id id
     * @param unlockQuantity 锁定编号
     * @param updateOperator 锁定数量
     * @param oldLockQuantity 更新人
     * @return 影响行数
     */
    int unlockQuantityById(@Param("id") Long id,
                                   @Param("unlockQuantity") Integer unlockQuantity,
                                   @Param("updateOperator") String updateOperator,
                                   @Param("oldLockQuantity") Integer oldLockQuantity);
}
