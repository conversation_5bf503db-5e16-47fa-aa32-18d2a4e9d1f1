package net.summerfarm.wms.application.external.converter;

import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.manage.model.input.AvailableInBatchQueryInput;
import net.summerfarm.wms.openapi.stockin.xm.req.InBoundOrderDetailDTO;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/7
 */
public class InBoundOrderDetailConverter {

    public static AvailableInBatchQueryInput convert(StockTaskStorage stockTaskStorage, InBoundOrderDetailDTO inBoundOrderDetailDTO) {
        AvailableInBatchQueryInput availableInBatchQueryInput = new AvailableInBatchQueryInput();
        availableInBatchQueryInput.setTaskId(stockTaskStorage.getId());
        availableInBatchQueryInput.setType(stockTaskStorage.getType());
        availableInBatchQueryInput.setWarehouseNo(stockTaskStorage.getInWarehouseNo());
        availableInBatchQueryInput.setSku(inBoundOrderDetailDTO.getSkuCode());
        availableInBatchQueryInput.setProductionDate(DateUtil.parseDateStringToLocalDate(inBoundOrderDetailDTO.getProductionDate()));
        availableInBatchQueryInput.setQualityDate(DateUtil.parseDateStringToLocalDate(inBoundOrderDetailDTO.getQualityDate()));
        return availableInBatchQueryInput;
    }

}
