package net.summerfarm.wms.application.inventory.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.tms.client.message.in.DistOrderDetailCancelMessage;
import net.summerfarm.tms.client.message.in.DistOrderMessage;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryCommand;
import net.summerfarm.wms.application.inventory.enums.WaveSafeInventoryLockEnum;
import net.summerfarm.wms.application.stocktask.enums.StockTaskStateEnum;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.areaStore.AreaStoreRepository;
import net.summerfarm.wms.domain.areaStore.domainobject.AreaStore;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.stocktask.StockTaskOrderSkuRepository;
import net.summerfarm.wms.domain.stocktask.StockTaskRepository;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskOrderSku;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.summerfarm.wms.initConfig.WaveSafeInventoryLockConfig;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@MqListener(topic = "topic-tms-dist-requirement",
        tag = "tag_dist_order_detail_cancel",
        consumerGroup = "GID_wms")
@Slf4j
public class DistOrderDetailCancelListener extends AbstractMqListener<DistOrderMessage> {

    @Resource
    private SafeInventoryCommandService safeInventoryCommandService;
    @Resource
    private StockTaskOrderSkuRepository stockTaskOrderSkuRepository;
    @Resource
    private StockTaskRepository stockTaskRepository;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private AdminUtil adminUtil;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private WaveSafeInventoryLockConfig waveSafeInventoryLockConfig;

    @Override
    public void process(DistOrderMessage distOrderMessage) {
        log.info("监听未到货退款，取消明细：{}", JSON.toJSONString(distOrderMessage));
        if (null == distOrderMessage || null == distOrderMessage.getDistOrderData()) {
            return;
        }
        DistOrderDetailCancelMessage distOrderDetailCancelMessage = JSONObject.parseObject(JSON.toJSONString(distOrderMessage.getDistOrderData()), DistOrderDetailCancelMessage.class);
        if (null == distOrderDetailCancelMessage || StringUtils.isBlank(distOrderDetailCancelMessage.getOuterOrderId()) || StringUtils.isBlank(distOrderDetailCancelMessage.getOuterItemId()) || null == distOrderDetailCancelMessage.getQuantity()) {
            return;
        }
        Integer lock = waveSafeInventoryLockConfig.getLock();
        log.info("波次后截单前退单自动锁安全配置：{}", lock);
        if (WaveSafeInventoryLockEnum.UNDO.getCode().equals(lock)) {
            log.info("波次后截单前退单自动锁安全未配置，过滤");
            return;
        }
        List<StockTaskOrderSku> stockTaskOrderSkuList = stockTaskOrderSkuRepository.selectListByOutOrderNoAndSku(distOrderDetailCancelMessage.getOuterOrderId(), distOrderDetailCancelMessage.getOuterItemId());
        if (CollectionUtils.isEmpty(stockTaskOrderSkuList)) {
            log.info("未查询到任务订单关联数据，过滤，订单：{}，sku：{}", distOrderDetailCancelMessage.getOuterOrderId(), distOrderDetailCancelMessage.getOuterItemId());
            return;
        }
        StockTaskOrderSku stockTaskOrderSku = stockTaskOrderSkuList.get(0);
        StockTask stockTask = stockTaskRepository.findOutStockTask(stockTaskOrderSku.getStockTaskId());
        if (null == stockTask) {
            log.info("未查询到出库任务数据，过滤，任务：{}", stockTaskOrderSku.getStockTaskId());
            return;
        }
        if (stockTaskOrderSku.getActualQuantity() > 0 || StockTaskStateEnum.isFinish(stockTask.getState())) {
            log.info("出库任务sku已操作，过滤，任务：{}，sku：{}", stockTaskOrderSku.getStockTaskId(), stockTaskOrderSku.getSku());
            return;
        }
        AreaStore areaStore = areaStoreRepository.querySkuStockBySku(stockTask.getAreaNo().longValue(), stockTaskOrderSku.getSku());
        Integer safeQuantity = areaStore.getSafeQuantity();
        // 安全库存操作
        SafeInventoryCommand safeInventoryCommand = SafeInventoryCommand.builder()
                .warehouseNo(stockTask.getAreaNo().longValue())
                .sku(stockTaskOrderSku.getSku())
                .safeQuantity(distOrderDetailCancelMessage.getQuantity() + safeQuantity)
                .remark("波次后截单前客户退单，系统自动锁定")
                .systemExe(true)
                .tenantId(areaStore.getTenantId())
                .build();
        // 默认兜底
        Long defaultAdminId = 1223L;
        String defaultAdminName = "杜金海";
        WarehouseStorageCenterEntity warehouseStorageCenterEntity = warehouseStorageRepository.selectByWarehouseNo(stockTask.getAreaNo());
        if (null != warehouseStorageCenterEntity && null != warehouseStorageCenterEntity.getManageAdminId()) {
            AdminFacadeDTO adminFacadeDTO = adminUtil.getAdmin(warehouseStorageCenterEntity.getManageAdminId().longValue());
            if (null != adminFacadeDTO) {
                safeInventoryCommand.setOperator(adminFacadeDTO.getRealName());
                safeInventoryCommand.setOperatorId(adminFacadeDTO.getAdminId());
            } else {
                safeInventoryCommand.setOperator(defaultAdminName);
                safeInventoryCommand.setOperatorId(defaultAdminId);
            }
        }
        // 更新安全库存
        safeInventoryCommandService.updateSafeInventory(safeInventoryCommand);
        // 飞书通知
        String robotUrl = configRepository.queryValueByKey("waveSafeLockRobotUrl");
        if (!StringUtils.isBlank(robotUrl)) {
            String title = "波次后截单前退单自动锁安全通知";
            String warehouseName = null != warehouseStorageCenterEntity ? warehouseStorageCenterEntity.getWarehouseName() : "";

            StringBuilder text = new StringBuilder("#### " + title + "\n");
            text.append("###### 仓库：").append(warehouseName).append("\n");
            Product productFromGoodsCenter = productRepository.findProductFromGoodsCenter(stockTask.getAreaNo().longValue(), stockTaskOrderSku.getSku());
            text.append("###### 内容：\n");
            text.append("###### ").append(stockTaskOrderSku.getSku()).append("-")
                    .append(productFromGoodsCenter.getPdName()).append("-")
                    .append(productFromGoodsCenter.getSpecification())
                    .append("，自动锁安全库存数量：").append(distOrderDetailCancelMessage.getQuantity())
                    .append("\n");
            text.append("###### 若实物未发出，请及时前往后台操作多出入库后并释放安全库存，避免有货不能售卖的情况！");
            DingTalkRobotUtil.sendMarkDownMsg(robotUrl, () -> {
                Map<String, String> md = new HashMap<>();
                md.put("title", title);
                md.put("text", text.toString());
                return md;
            }, null);
        }
        log.info("监听未到货退款处理完成，{}", JSON.toJSONString(safeInventoryCommand));
    }
}
