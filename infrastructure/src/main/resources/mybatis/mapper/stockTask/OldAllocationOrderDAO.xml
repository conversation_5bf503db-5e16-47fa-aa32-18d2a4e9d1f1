<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.OldAllocationOrderDAO" >

    <select id="selectOne" parameterType="string" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO">
        select t.id , t.list_no listNo, t.create_admin_name createAdminName, t.create_admin createAdmin, t.out_store_name outStoreName, t.in_store inStore, t.out_store outStore, t.in_store_name inStoreName, t.status, t.addtime, t.updatetime,t.order_type orderType,sr.sale_quantity_start saleQuantityStart,sr.sale_quantity_end saleQuantityEnd,
        t.in_store_admin inStoreAdmin, t.in_store_admin_name inStoreAdminName, t.out_store_admin outStoreAdmin, t.out_store_admin_name outStoreAdminName,sr.config_id configId,
        t.in_status inStatus, t.out_status outStatus,sr.cycle_type cycleType ,sr.sale_partake salePartake,sr.logistics_time logisticsTime,
        t.audit_admin auditAdmin, t.audit_admin_name auditAdminName, t.out_time outTime, t.in_time inTime, t.expect_time expectTime, t.transport, t.tracking_no trackingNo,sr.interval_days intervalDays,sr.purchase_partake purchasePartake,t.expect_out_time expectOutTime,t.plan_list_no planListNo,t.next_day_arrive nextDayArrive,t.plan_list_id planListId,t.trunk_flag trunkFlag,t.tenant_id tenantId
        FROM stock_allocation_list t
        left join stock_allocation_config_record sr on sr.list_no = t.list_no
        WHERE t.list_no = #{listNo}
    </select>

    <select id="selectList" parameterType="string" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO">
        select t.id , t.list_no listNo, t.create_admin_name createAdminName, t.create_admin createAdmin, t.out_store_name outStoreName, t.in_store inStore, t.out_store outStore, t.in_store_name inStoreName, t.status, t.addtime, t.updatetime,t.order_type orderType,sr.sale_quantity_start saleQuantityStart,sr.sale_quantity_end saleQuantityEnd,
               t.in_store_admin inStoreAdmin, t.in_store_admin_name inStoreAdminName, t.out_store_admin outStoreAdmin, t.out_store_admin_name outStoreAdminName,sr.config_id configId,
               t.in_status inStatus, t.out_status outStatus,sr.cycle_type cycleType ,sr.sale_partake salePartake,sr.logistics_time logisticsTime,
               t.audit_admin auditAdmin, t.audit_admin_name auditAdminName, t.out_time outTime, t.in_time inTime, t.expect_time expectTime, t.transport, t.tracking_no trackingNo,sr.interval_days intervalDays,sr.purchase_partake purchasePartake,t.expect_out_time expectOutTime,t.plan_list_no planListNo,t.next_day_arrive nextDayArrive,t.plan_list_id planListId,t.trunk_flag trunkFlag,t.tenant_id tenantId
        FROM stock_allocation_list t
                 left join stock_allocation_config_record sr on sr.list_no = t.list_no
        WHERE t.list_no IN
        <foreach collection="listNos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectInStoreByListNoList" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO">
        select t.list_no listNo, t.in_store inStore, t.expect_out_time expectOutTime
        FROM stock_allocation_list t
        WHERE t.list_no IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO" useGeneratedKeys="true" keyProperty="id">
        insert into stock_allocation_list
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="listNo != null" >
                list_no,
            </if>
            <if test="createAdmin != null" >
                create_admin,
            </if>
            <if test="createAdminName != null" >
                create_admin_name,
            </if>
            <if test="auditAdmin != null" >
                audit_admin,
            </if>
            <if test="outStore != null" >
                out_store,
            </if>
            <if test="inStore != null" >
                in_store,
            </if>
            <if test="outTime != null" >
                out_time,
            </if>
            <if test="expectOutTime != null" >
                expect_out_time,
            </if>
            <if test="expectTime != null" >
                expect_time,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="inTime != null" >
                in_time,
            </if>
            <if test="transport != null" >
                transport,
            </if>
            <if test="trackingNo != null" >
                tracking_no,
            </if>
            <if test="auditAdminName !=null">
                audit_admin_name,
            </if>
            <if test="outStoreName != null">
                out_store_name,
            </if>
            <if test="inStoreName != null">
                in_store_name,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="inStoreAdmin != null">
                in_store_admin,
            </if>
            <if test="inStoreAdminName != null">
                in_store_admin_name,
            </if>
            <if test="outStoreAdmin != null">
                out_store_admin,
            </if>
            <if test="outStoreAdminName != null">
                out_store_admin_name,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="planListNo != null">
                plan_list_no,
            </if>
            <if test="nextDayArrive != null">
                next_day_arrive,
            </if>
            <if test="planListId != null">
                plan_list_id,
            </if>
            <if test="trunkFlag != null">
                trunk_flag,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="listNo != null" >
                #{listNo,jdbcType=VARCHAR},
            </if>
            <if test="createAdmin != null" >
                #{createAdmin,jdbcType=INTEGER},
            </if>
            <if test="createAdminName != null" >
                #{createAdminName},
            </if>
            <if test="auditAdmin != null" >
                #{auditAdmin,jdbcType=INTEGER},
            </if>
            <if test="outStore != null" >
                #{outStore,jdbcType=INTEGER},
            </if>
            <if test="inStore != null" >
                #{inStore,jdbcType=INTEGER},
            </if>
            <if test="outTime != null" >
                #{outTime},
            </if>
            <if test="expectOutTime != null" >
                #{expectOutTime},
            </if>
            <if test="expectTime != null" >
                #{expectTime},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="inTime != null" >
                #{inTime},
            </if>
            <if test="transport != null" >
                #{transport,jdbcType=INTEGER},
            </if>
            <if test="trackingNo != null" >
                #{trackingNo,jdbcType=VARCHAR},
            </if>
            <if test="auditAdminName !=null">
                #{auditAdminName},
            </if>
            <if test="outStoreName != null">
                #{outStoreName},
            </if>
            <if test="inStoreName != null">
                #{inStoreName},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="inStoreAdmin != null">
                #{inStoreAdmin},
            </if>
            <if test="inStoreAdminName != null">
                #{inStoreAdminName},
            </if>
            <if test="outStoreAdmin != null">
                #{outStoreAdmin},
            </if>
            <if test="outStoreAdminName != null">
                #{outStoreAdminName},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="orderType != null">
                #{orderType},
            </if>
            <if test="planListNo != null">
                #{planListNo},
            </if>
            <if test="nextDayArrive != null">
                #{nextDayArrive},
            </if>
            <if test="planListId != null">
                #{planListId},
            </if>
            <if test="trunkFlag != null">
                #{trunkFlag}
            </if>
            <if test="tenantId != null">
                #{tenantId}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO" >
        update stock_allocation_list
        <set >
            <if test="auditAdmin != null" >
                audit_admin = #{auditAdmin,jdbcType=INTEGER},
            </if>
            <if test="auditAdminName != null">
                audit_admin_name =#{auditAdminName},
            </if>
            <if test="outStore != null" >
                out_store = #{outStore,jdbcType=INTEGER},
            </if>
            <if test="outStoreName != null" >
                out_store_name = #{outStoreName},
            </if>
            <if test="inStore != null" >
                in_store = #{inStore},
            </if>
            <if test="inStoreName != null" >
                in_store_name = #{inStoreName},
            </if>
            <if test="outTime != null" >
                out_time = #{outTime},
            </if>
            <if test="expectTime != null" >
                expect_time = #{expectTime},
            </if>
            <if test="expectOutTime != null" >
                expect_out_time = #{expectOutTime},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="inTime != null" >
                in_time = #{inTime},
            </if>
            <if test="transport != null" >
                transport = #{transport,jdbcType=INTEGER},
            </if>
            <if test="trackingNo != null" >
                tracking_no = #{trackingNo,jdbcType=VARCHAR},
            </if>
            <if test="inStoreAdmin != null">
                in_store_admin = #{inStoreAdmin},
            </if>
            <if test=" inStoreAdminName!= null">
                in_store_admin_name= #{inStoreAdminName},
            </if>
            <if test=" outStoreAdmin!= null">
                out_store_admin= #{outStoreAdmin},
            </if>
            <if test=" outStoreAdminName!= null">
                out_store_admin_name = #{outStoreAdminName},
            </if>
            <if test=" outStatus!= null">
                out_status= #{outStatus},
            </if>
            <if test=" inStatus!= null">
                in_status= #{inStatus},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="nextDayArrive != null">
                next_day_arrive = #{nextDayArrive},
            </if>
            <if test="trunkFlag != null">
                trunk_flag = #{trunkFlag},
            </if>
        </set>
        where list_no = #{listNo}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.AllocationOrderDO" >
        update stock_allocation_list
        set list_no = #{listNo,jdbcType=VARCHAR},
            create_admin = #{createAdmin,jdbcType=INTEGER},
            audit_admin = #{auditAdmin,jdbcType=INTEGER},
            out_store = #{outStore,jdbcType=INTEGER},
            in_store = #{inStore,jdbcType=INTEGER},
            out_time = #{outTime},
            expect_time = #{expectTime},
            status = #{status,jdbcType=INTEGER},
            in_time = #{inTime},
            transport = #{transport,jdbcType=INTEGER},
            tracking_no = #{trackingNo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>