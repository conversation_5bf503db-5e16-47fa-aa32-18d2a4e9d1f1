package net.summerfarm.wms.domain.inventory.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Data
@Builder
@AllArgsConstructor
public class WmsBatchFrozen {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 库存仓id
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次
     */
    private String batch;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 类型（53 货损出库）
     */
    private Integer type;

    /**
     * 关联的类型id
     */
    private Long typeId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 原因
     */
    private String reason;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    public WmsBatchFrozen() {
    }

    public WmsBatchFrozen(Integer warehouseNo, String sku, String batch, Integer lockQuantity, Integer type, Long typeId, Integer status, String reason, LocalDate qualityDate) {
        this.warehouseNo = warehouseNo;
        this.sku = sku;
        this.batch = batch;
        this.lockQuantity = lockQuantity;
        this.type = type;
        this.typeId = typeId;
        this.status = status;
        this.reason = reason;
        this.qualityDate = qualityDate;
    }

    private static final long serialVersionUID = 1L;
}
