package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProcessingTask implements Serializable {

    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;
    /**
     * 任务状态：0未加工、1已加工、2部分加工
     */
    private Integer status;
    /**
     * 完成时间
     */
    private Date finishTime;
    /**
     * 完成备注
     */
    private String finishRemark;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;
}
